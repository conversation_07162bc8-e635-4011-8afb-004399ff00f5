class TableauApiClient
  include ClientApiBuilder::Router

  API_VERSION = '3.15'

  attr_accessor :x_tableau_auth

  base_url 'https://tableau.evertrue.com'

  header 'Content-Type', 'application/json'
  header 'Accept', 'application/json'

  section :auth do
    header 'Content-Type', 'application/json'
    header 'Accept', 'application/json'

    route :create_token, "/api/#{API_VERSION}/auth/signin", body: {
            credentials: {
              name: :name,
              password: :password,
              site: {
                contentUrl: :content_url
              }
            }
          }
  end

  section :resources do
    header 'Content-Type', 'application/json'
    header 'Accept', 'application/json'
    header 'X-Tableau-Auth', :x_tableau_auth

    route :get_sites, "/api/#{API_VERSION}/sites"
    route :get_groups, "/api/#{API_VERSION}/sites/:site_id/groups"
    route :get_site_user, "/api/#{API_VERSION}/sites/:site_id/users/:user_id"
    route :get_site_users, "/api/#{API_VERSION}/sites/:site_id/users", query: {pageNumber: :page_number, pageSize: :page_size}
    route :find_site_users, "/api/#{API_VERSION}/sites/:site_id/users", query: {filter: :filter}
    route :get_user_groups, "/api/#{API_VERSION}/sites/:site_id/users/:user_id/groups"
    route :get_projects, "/api/#{API_VERSION}/sites/:site_id/projects"
    route :get_views, "/api/#{API_VERSION}/sites/:site_id/views"
    route :get_view, "/api/#{API_VERSION}/sites/:site_id/views", query: {filter: :filter}
    route :get_view_image, "/api/#{API_VERSION}/sites/:site_id/views/:view_id/image", stream: :io
    route :get_view_preview_image, "/api/#{API_VERSION}/sites/:site_id/workbooks/:workbook_id/views/:view_id/previewImage", stream: :io
    route :get_workbook, "/api/#{API_VERSION}/sites/:site_id/workbooks/:workbook_id"

    route :remove_user_from_site, "/api/#{API_VERSION}/sites/:site_id/users/:user_id", method: :delete
    route :remove_user_from_group, "/api/#{API_VERSION}/sites/:site_id/groups/:group_id/users/:user_id", method: :delete
  end

  section :xml_resources do
    header 'Content-Type', 'application/xml'
    header 'Accept', 'application/json'
    header 'X-Tableau-Auth', :x_tableau_auth
    
    route :add_user_to_site, "/api/#{API_VERSION}/sites/:site_id/users", method: :post
    route :add_user_to_group, "/api/#{API_VERSION}/sites/:site_id/groups/:group_id/users", method: :post
  end

  def login(username, password, content_url)
    result = auth.create_token(
      name: username,
      password: password,
      content_url: content_url
    )
    @x_tableau_auth = result['credentials']['token']
  end

  def self.create_client_with_credentials(username, password, content_url)
    client = new
    client.login(username, password, content_url)
    client
  end

  def self.create_client_with_default_credentials(content_url = '')
    create_client_with_credentials(
      ENV['TABLEAU_USERNAME'],
      ENV['TABLEAU_PASSWORD'],
      content_url
    )
  end

  def find_site_user_by_name(site_id, name)
    resources.find_site_users(site_id: site_id, filter: "name:eq:#{name}")
  end

  def to_ts_request(body)
    formatted_no_decl = Nokogiri::XML::Node::SaveOptions::NO_DECLARATION + Nokogiri::XML::Node::SaveOptions::FORMAT
    builder = Nokogiri::XML::Builder.new

    builder.tsRequest {
      builder.user(body)
    }
    builder.to_xml(save_with: formatted_no_decl)
  end

  def add_user_to_site(site_id:, name:, site_role:)
    xml_resources.add_user_to_site(site_id: site_id, body: to_ts_request(name: name, siteRole: site_role))
  end

  def add_user_to_group(site_id:, group_id:, user_id:)
    xml_resources.add_user_to_group(site_id: site_id, group_id: group_id, body: to_ts_request(id: user_id))
  end

  def get_view_by_path(site_id:, path:)
    filter = 'contentUrl:eq:' + path.sub('/', '').sub('/', '/sheets/')
    views = resources.get_view(site_id: site_id, filter: filter)['views']['view']
    views.size == 1 ? views.first : nil
  end

  def get_image_view_by_path(site_id:, path:)
    filter = 'contentUrl:eq:Sample' + path.sub('/', '').sub('/', '/sheets/')
    views = resources.get_view(site_id: site_id, filter: filter)['views']['view']
    views.size == 1 ? views.first : nil
  end
end
