class InteractionSchema
  include Schema::All

  attribute :id, :integer
  attribute :oid, :integer
  attribute :remote_id, :string
  attribute :target_id, :integer
  attribute :target_type, :string
  attribute :creator_user_id, :integer
  attribute :date_occurred, :integer
  attribute :text, :string
  attribute :application, :string
  attribute :summary, :string
  attribute :deleted, :boolean
  attribute :update_source, :string
  attribute :interaction_type, :string
  attribute :created_at, :integer
  attribute :updated_at, :integer

  has_one(:author) do
    attribute :name, :string
    attribute :remote_user_id, :string
    attribute :user_id, :integer
  end

  has_many(:solicitor) do
    attribute :name, :string
    attribute :remote_user_id, :string
    attribute :user_id, :integer
  end

  has_many(:user_mention) do
    attribute :alias, :string
    attribute :user_id, :integer
  end

  has_many(:contact_mention) do
    attribute :alias, :string
    attribute :contact_id, :integer
  end

  has_many(:label) do
    attribute :name, :string
    attribute :value, :string
  end

  has_many(:custom_field) do
    attribute :id, :integer
    attribute :value, :string
  end

  has_many(:secondary_target) do
    attribute :target_id, :integer
    attribute :remote_id, :string
  end
end
