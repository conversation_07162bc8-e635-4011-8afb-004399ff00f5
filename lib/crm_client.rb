# frozen_string_literal: true

class CRMClient < BaseApiClient
  def update_nango_configuration(oid, connection_id, provider_config_key)
    payload = {
      connection_id: connection_id,
      provider_config_key: provider_config_key
    }

    request(:post, "/crm/integrations/admin/configure/nango?oid=#{oid}&app_key=#{@app_key}&auth=#{@auth_token}&auth_provider=#{@provider}", payload)
  end

  def get_nango_configuration(oid)
    request(:get, "/crm/integrations/admin/configure/nango?oid=#{oid}&app_key=#{@app_key}&auth=#{@auth_token}&auth_provider=#{@provider}")
  end
end
