require 'net/http'

class GithubClient
  BASE_URL = 'https://api.github.com'
  DEFAULT_OWNER = 'evertrue'
  # You need to have an access token with repo permissions
  # goto:  https://github.com/settings/tokens to create one
  GITHUB_ENV_VARIABLE_NAMES = [
    'GITHUB_REPOS_ACCESS_TOKEN',
    'GITHUB_PERSONAL_ACCESS_TOKEN'
  ]

  attr_reader :owner

  def initialize(owner=nil)
    @owner = owner || DEFAULT_OWNER
  end

  def branches(repo)
    uri = URI(BASE_URL + "/repos/#{owner}/#{repo}/branches?per_page=100")
    res = HTTPClient.get(uri, req_headers)
    JSON.parse(res.body)
  end

  def last_commit(repo, branch_name='master')
    branches(repo).each do |branch|
      next unless branch['name'] == branch_name

      return branch['commit']['sha']
    end

    nil
  end

  def repositories
    repos = []
    page = 1
    while true
      uri = URI(BASE_URL + "/orgs/#{owner}/repos?type=all&per_page=100&page=#{page}")
      res = HTTPClient.get(uri, req_headers.merge('Accept' => 'application/vnd.github.v3+json'))
      cnt = 0
      JSON.parse(res.body).each do |repo|
        cnt += 1
        next if repo['archived'] || repo['disabled'] || repo['fork']

        repos << repo.slice('name', 'ssh_url')
      end
      break if cnt < 100
      page += 1
    end
    repos
  end

  def get_repositories_by_type(type='all')
    repos = []
    page = 1
    while true
      uri = URI(BASE_URL + "/orgs/#{owner}/repos?type=#{type}&per_page=100&page=#{page}")
      res = HTTPClient.get(uri, req_headers.merge('Accept' => 'application/vnd.github.v3+json'))
      cnt = 0
      JSON.parse(res.body).each do |repo|
        cnt += 1
        next if repo['disabled'] || repo['fork']

        repos << repo
      end
      break if cnt < 100
      page += 1
    end
    repos
  end

  def get_file_content(repo, path)
    uri = URI(BASE_URL + "/repos/#{owner}/#{repo}/contents/#{path}")
    res = HTTPClient.get(uri, req_headers)
    Base64.decode64(JSON.parse(res.body)['content'])
  end

  def update_repo(repo, settings)
    uri = URI(BASE_URL + "/repos/#{owner}/#{repo}")
    res = HTTPClient.patch(uri, settings.to_json, req_headers.merge('Accept' => 'application/vnd.github.v3+json'))
    JSON.parse(res.body)
  end

  private

  def access_token
    unless defined?(@access_token)
      @access_token = GITHUB_ENV_VARIABLE_NAMES.map { |name| ENV[name] }.compact.first
      raise("no git hub access token found in environement variables #{GITHUB_ENV_VARIABLE_NAMES.join(', ')}")  unless @access_token
    end
    @access_token
  end

  def req_headers
    {
      'Authorization' =>  "token  #{access_token}"
    }
  end
end
