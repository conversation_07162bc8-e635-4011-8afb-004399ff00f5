# frozen_string_literal: true

module <PERSON><PERSON>el<PERSON>
  def time_in_words(time, start_time = nil)
    diff = ((start_time || Time.now) - time).to_i
    years = diff / (365 * 86_400)
    diff -= years * (365 * 86_400)
    days = diff / 86_400
    diff -= (days * 86_400)
    hours = diff / 3_600
    diff -= hours * 3_600
    minutes = diff / 60
    diff -= minutes * 60
    seconds = diff

    parts = []
    parts << "#{years}y" if years > 0
    parts << "#{days}d" if days > 0
    parts << "#{hours}h" if hours > 0
    parts << "#{minutes}m" if minutes > 0

    if parts.empty?
      'less than 1 minutes'
    else
      parts[0, 2].join(' ')
    end
  end
end
