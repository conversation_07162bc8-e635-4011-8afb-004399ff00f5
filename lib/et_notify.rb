# Wrapper for ETSlack, adds messaging frequency and script completion notification
class ETNotify
  include Singleton

  DEFAULT_MESSAGE_FREQUENCY = 600 # 10 minutes

  # - slack_ids: can be a channel, user or profile_id
  # channel: #general
  # user: user name from config/slack.yml, user name should match system user name <PERSON>NV['USER']
  # profile_id: Slack Profile ID
  # - message_frequency: custom time in seconds between slack notifications
  attr_accessor :slack_ids,
                :message_frequency

  # - last_message_at: last time a slack notification was sent
  attr_reader :last_message_at

  def self.configure
    yield instance
  end

  # Sends a slack notification when script execution finishes
  def self.notify_when_finished(message = 'finished')
    at_exit do
      ETNotify.message_now(message)
    end
  end

  # Can be used to send progress updates,
  #  by default only allow messages to be sent every 5 minutes
  def self.message(message)
    instance.message(message)
  end

  # Skips message frequency check
  #  useful for sending out the last progress updates
  def self.message_now(message)
    instance.message_now(message)
  end

  def et_slack
    @et_slack ||= ETSlack.new(ENV['SLACK_API_TOKEN'])
  end

  # time in seconds between slack notifications, default 5 minutes
  def message_frequency
    @message_frequency || DEFAULT_MESSAGE_FREQUENCY
  end

  # Drops messages if time between calls is to frequent
  def message(message)
    return if last_message_at && (Time.now.to_i - last_message_at.to_i) < message_frequency

    message_now(message)
  end

  # Sends slack notification immediately if configured
  def message_now(message)
    return unless slack_ids
    return unless et_slack.configured?

    begin
      slack_ids.each do |slack_id|
        et_slack.message(
          SLACK_CONFIG['users'].key?(slack_id) ? SLACK_CONFIG['users'][slack_id] : slack_id,
          message
        )
      end
    rescue Exception => e
      LOG.error "failed to send slack message, #{e.inspect}"
    end

    @last_message_at = Time.now
  end
end
