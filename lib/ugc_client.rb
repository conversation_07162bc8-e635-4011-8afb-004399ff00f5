# frozen_string_literal: true

class UgcClient < BaseApiClient
  def fetch_note_by_id(oid, note_id)
    request(:get, "/ugc/v2/interaction/#{note_id}?oid=#{oid}&includeSoftDeletes=true")
  end
  alias fetch_interaction_by_id fetch_note_by_id

  def sync_interaction_to_es(oid, interaction_id)
    request(:post, "/ugc/v2/admin/interaction/#{interaction_id}/es/sync?oid=#{oid}")
  end

  def fetch_interactions_for_target(oid, target_type, target_id)
    request(:get, "/ugc/v2/interaction/#{target_type}/#{target_id}?oid=#{oid}")
  end

  def fetch_interactions_in_bulk(oid, interaction_ids, include_deleted=false)
    ids_params = interaction_ids.map { |interaction_id| "ids[]=#{interaction_id}" }.join('&')
    request(:get, "/ugc/v2/interaction/bulk?oid=#{oid}&#{ids_params}&include_deleted=true")
  end

  def post_targets(interaction)
    request(:post, "/ugc/v2/admin/interaction/#{interaction['id']}/targets?oid=#{interaction['oid']}", interaction)
  end

  def post_solicitors(interaction)
    request(:post, "/ugc/v2/admin/interaction/#{interaction['id']}/solicitors?oid=#{interaction['oid']}", interaction)
  end

  def post_labels(interaction)
    request(:post, "/ugc/v2/admin/interaction/#{interaction['id']}/labels?oid=#{interaction['oid']}", interaction)
  end

  def update_dxo_date_occurred_interactions(oid, id)
    request(:post, "/ugc/v2/admin/interaction/#{id}/date_occurred?oid=#{oid}")
  end

  def upsert_most_recent_interaction(oid, solicitor_contact_id, prospect_contact_id)
    query_string = "oid=#{oid}&solicitorContactId=#{solicitor_contact_id}&prospectContactId=#{prospect_contact_id}"
    request(:put, "/ugc/v2/admin/interaction/most_recent?" + query_string)
  end

  def refresh_most_recent_interaction(oid, solicitor_contact_id, prospect_contact_id)
    query_string = "oid=#{oid}&solicitor_contact_id=#{solicitor_contact_id}&prospect_contact_id=#{prospect_contact_id}"
    request(:post, "/ugc/v2/interactions/most_recent/refresh?" + query_string)
  end

  def fetch_note_by_target(oid, target_type, target_id, note_id)
    request(:get, "/ugc/v2/note/#{target_type}/#{target_id}/#{note_id}?oid=#{oid}")
  end

  def options_request(oid, origin)
    request(:options, "/ugc/v2/note?oid=#{oid}", nil, {'Origin' => origin})
  end

  def fetch_note_by_remote_id(oid, remote_id)
    request(:get, "/ugc/v2/note/remote_id/#{remote_id}?oid=#{oid}")
  end

  def upsert_note_by_remote_id(oid, remote_id, payload)
    request(:post, "/ugc/v2/note/remote_id/#{remote_id}?oid=#{oid}", payload)
  end

  def update_note_by_target(oid, target_type, target_id, note_id, payload, message_id = nil)
    path = "/ugc/v2/note/#{target_type}/#{target_id}/#{note_id}?oid=#{oid}"
    path += "&messageId=#{message_id}" if message_id
    request(:put, "/ugc/v2/note/#{target_type}/#{target_id}/#{note_id}?oid=#{oid}&messageId=1", payload)
  end

  def delete_note_by_target(oid, target_type, target_id, note_id)
    request(:delete, "/ugc/v2/note/#{target_type}/#{target_id}/#{note_id}?oid=#{oid}")
  end

  def update_note_by_payload(payload, message_id = nil)
    update_note_by_target(
      payload['oid'],
      payload['target_type'],
      payload['target_id'],
      payload['id'],
      payload,
      message_id
    )
  end

  def fetch_proposal(oid, proposal_id)
    request(:get, "/ugc/v2/proposal/#{proposal_id}?oid=#{oid}")
  end

  def fetch_proposals_in_bulk(oid, ids)
    ids_params = ids.map { |id| "ids=#{id}" }.join('&')
    request(:get, "/ugc/v2/proposal/?oid=#{oid}&#{ids_params}")
  end

  def sync_proposal_to_es(oid, proposal_id)
    request(:post, "/ugc/v2/admin/proposal/#{proposal_id}/es/sync?oid=#{oid}")
  end

  def archive_proposal(oid, proposal_id, delete_record)
    request(:post, "/ugc/v2/admin/proposal/#{proposal_id}/archive?oid=#{oid}&delete_record=#{delete_record}")
  end

  def fetch_proposal_by_contact_and_id(oid, contact_id, proposal_id)
    request(:get, "/ugc/v2/proposal/#{contact_id}/#{proposal_id}?oid=#{oid}")
  end

  def fetch_proposals_by_contact(oid, contact_id)
    request(:get, "/ugc/v2/proposal/contact/#{contact_id}?oid=#{oid}")
  end

  def fetch_proposal_by_remote_id(oid, remote_id)
    request(:get, "/ugc/v2/proposal/remote_id/#{remote_id}?oid=#{oid}")
  end

  def fetch_proposal_id_by_remote_id(oid, remote_id)
    request(:get, "/ugc/v2/proposal/remote_id/#{remote_id}/proposal_id?oid=#{oid}")
  end

  def update_proposal(oid, proposal_id, payload)
    request(:put, "/ugc/v2/proposal/#{proposal_id}?oid=#{oid}", payload)
  end

  def update_proposal_by_payload(payload)
    update_proposal(
      payload['oid'],
      payload['id'],
      payload
    )
  end

  def fetch_interaction_custom_fields(oid)
    request(:get, "/ugc/v2/interactions/custom_fields?oid=#{oid}")
  end

  def search_proposal(oid, payload)
    request(:post, "/ugc/v2/search/proposal?oid=#{oid}", payload)
  end

  def fetch_status
    request(:get, '/ugc/v1/status?oid=1')
  end

  def fetch_status_deps
    request(:get, '/ugc/v1/status/deps?oid=1')
  end

  def fetch_interaction_custom_field_values_form_values(oid)
    request(:get, "/ugc/v2/interactions/custom_field_values/form_values?oid=#{oid}")
  end

  def fetch_interaction_custom_field_values_form_values_by_custom_field(oid, custom_field_id)
    request(:get, "/ugc/v2/interactions/custom_field_values/form_values/#{custom_field_id}?oid=#{oid}")
  end

  def fetch_interaction_custom_field_values_by_custom_field(oid, custom_field_id)
    request(:get, "/ugc/v2/interactions/custom_field_values/#{custom_field_id}?oid=#{oid}")
  end

  def fetch_interaction_custom_field_values_by_custom_field_and_value(oid, custom_field_id, custom_field_value_id)
    request(:get, "/ugc/v2/interactions/custom_field_values/#{custom_field_id}/#{custom_field_value_id}?oid=#{oid}")
  end

  def fetch_interaction_types_by_id(oid, interaction_type_id)
    request(:get, "/ugc/v2/interactions/types/#{interaction_type_id}?oid=#{oid}")
  end

  def fetch_interaction_types(oid)
    request(:get, "/ugc/v2/interactions/types?oid=#{oid}")
  end

  def put_interaction_type(interaction_type)
    request(:put, "/ugc/v2/interactions/types/#{interaction_type['id']}?oid=#{interaction_type['oid']}", interaction_type)
  end

  def fetch_ugc_types_by_data_type(oid, data_type)
    request(:get, "/ugc/v2/types/#{data_type}?oid=#{oid}")
  end

  def put_ugc_type(data_type, ugc_type)
    request(:put, "/ugc/v2/types/#{data_type}/#{ugc_type['id']}?oid=#{ugc_type['oid']}", ugc_type)
  end

  def fetch_proposal_custom_fields(oid)
    request(:get, "/ugc/v2/proposal/custom_fields?oid=#{oid}")
  end

  def fetch_proposal_custom_field_picklist_by_propery_and_id(oid, property_id, picklist_id)
    request(:get, "/ugc/v2/proposal/custom_fields/picklist/#{property_id}/#{picklist_id}?oid=#{oid}")
  end

  def fetch_proposal_custom_field_picklists_by_propery(oid, property_id)
    request(:get, "/ugc/v2/proposal/custom_fields/picklist/#{property_id}?oid=#{oid}")
  end

  def fetch_proposal_stage_by_id(oid, stage_id)
    request(:get, "/ugc/v2/proposal/stage/#{stage_id}?oid=#{oid}")
  end

  def fetch_proposal_stages(oid)
    request(:get, "/ugc/v2/proposal/stage?oid=#{oid}")
  end

  def fetch_proposal_stages_by_group(oid, group_id)
    request(:get, "/ugc/v2/proposal/stage/group/#{group_id}/stages?oid=#{oid}")
  end

  def fetch_proposal_stage_group(oid, group_id)
    request(:get, "/ugc/v2/proposal/stage/group/#{group_id}?oid=#{oid}")
  end

  def fetch_proposal_stage_groups(oid)
    request(:get, "/ugc/v2/proposal/stage/group?oid=#{oid}")
  end

  def fetch_reminder(oid, reminder_id)
    request(:get, "/ugc/v2/reminder/#{reminder_id}?oid=#{oid}")
  end

  def fetch_reminders_by_task_id(oid, task_id)
    request(:get, "/ugc/v2/reminder/reminders?oid=#{oid}&task_id=#{task_id}")
  end

  def fetch_task(oid, task_id)
    request(:get, "/ugc/v2/tasks/#{task_id}?oid=#{oid}")
  end

  def refresh_remote_user_cache(oid, user_id)
    request(:get, "/ugc/v2/remoteusercache/refresh?oid=#{oid}&user_id=#{user_id}")
  end

  def migrate_interaction_custom_field(oid, custom_field_id)
    request(:post, "/ugc/v2/admin/custom_fields/interaction/#{custom_field_id}/migrate?oid=#{oid}")
  end

  def get_designations(oid, data_type)
    request(:get, "/ugc/v2/designations/#{data_type}?oid=#{oid}")
  end

  def create_designation(oid, data_type, name)
    request(:post, "/ugc/v2/designations/#{data_type}?oid=#{oid}", {
      name: name,
      active: true
    })
  end

  def delete_designation(oid, data_type, id)
    request(:delete, "/ugc/v2/designations/#{data_type}/#{id}?oid=#{oid}")
  end

  def v3_delete_proposal(oid, id)
    request(:delete, "/ugc/v3/proposal/#{id}?oid=#{oid}")
  end

  def update_interaction(oid, interaction_id, payload, message_id = nil)
    path = "/ugc/v3/interaction/#{interaction_id}?oid=#{oid}"
    path += "&messageId=#{message_id}" if message_id
    request(:put, path, payload)
  end

  def v3_get_interaction(oid, interaction_id)
    request(:get, "/ugc/v3/interaction/#{interaction_id}?oid=#{oid}")
  end

  def get_interaction_type_form_values(oid)
    request(:get, "/ugc/v2/interactions/types/form_values?oid=#{oid}")
  end

  def v3_delete_interaction(oid, interaction_id)
    request(:delete, "/ugc/v3/interaction/#{interaction_id}?oid=#{oid}")
  end

  def v3_delete_proposal(oid, proposal_id)
    request(:delete, "/ugc/v3/proposal/#{proposal_id}?oid=#{oid}")
  end
end
