class SearchClient < BaseApiClient
  def search_contacts(oid, payload)
    request(:post, "/search/v2/contacts/search?oid=#{oid}", payload)
  end

  def create_index(oid, shards)
    request(:post, "/search/v2/index/?oid=#{oid}&shards=#{shards}")
  end

  def reindex(oid, num_shards, num_replicas, destination_index_name)
    body = {
      num_shards: num_shards,
      num_replicas: num_replicas,
      destination_index_name: destination_index_name
    }
    request(:post, "/search/v2/index/reindex?oid=#{oid}", body)
  end
end
