#!/usr/bin/env ruby

# shared script options for ai scripts

require_relative '../config/script_options'

def int_or_nil(string)
  Integer(string || '')
rescue ArgumentError
  nil
end

AI_LOCAL_URL = 'http://localhost:8085'

class AiScriptOptions < ScriptOptions
  def add_default_options
    super
    
    on('-l', '--local', "Use the local api endpoint #{AI_LOCAL_URL}'")  do |v|
      options[:use_local_api] = true
    end
  end

  def add_prompt_options
    on('-p', '--prompt-key PROMPT_KEY', 'Prompt key to use for completion. \
        Prompt keys define server-side prompts.') do |v|
      options[:prompt_key] = v
    end

    on('-t', '--template TEMPLATE', 'Raw prompt template path. Instead of loading \
      a server side prompt, you can use this to provide a path to a local file containg\
      prompt template data.') do |v|
      options[:template] = v
      options[:template_data] = File.read(options[:template])
    end

    on('-a', '--arg NAME=VALUE', 'A prompt argument, these are dependent on the data queries \
      you use in your prompt.') do |v|
      split = v.split('=').map &:strip
      name = split[0]
      value = split[1]
      # attempt to convert to int, if that fails assume it should be a string
      value = int_or_nil(value) || value

      options[:args] ||= {}
      options[:args][name] = value
    end
  end

  def after_env_load!
    if options[:prompt_key] || options[:template] || options[:args]
      options[:prompt] = AiClient::Prompt.new(options[:prompt_key], options[:template_data], options[:args])
    end

    if options[:use_local_api]
      options[:ai_client] = AiClient.create_app_client('outreach', AI_LOCAL_URL)
    else
      options[:ai_client] = AiClient.create_app_client('outreach')
    end
  end
end

def ai_client
  SCRIPT_OPTIONS[:ai_client]
end

def ai_mysql
  @ai_mysql ||= MysqlHelpers.create_client('ai') 
end