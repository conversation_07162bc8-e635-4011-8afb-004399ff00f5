require 'aws-sdk-ecs'

class ECSTaskRunner
  attr_reader :task_definition_name,
              :task_options

  def initialize(task_definition_name)
    @task_definition_name = task_definition_name
    @task_options = {}
  end

  def run(script_options)
    @task_options[:cluster] = cluster
    @task_options[:task_definition] = task_definition_arn

    puts "RUNNING IN ECS"
    print JSON.pretty_generate(@task_options) + "\n"

    resp = ecs.run_task(@task_options)

    print "\nECS RUN TASK RESPONSE:\n"
    print JSON.pretty_generate(resp.to_h) + "\n"
  end

  private

  def ecs
    @ecs ||= ECSHelpers.create_client
  end

  def cluster
    ECS_CONFIG['ecs']['cluster']['worker']
  end

  def cluster_arn
    @cluster_arn ||= ecs.describe_clusters(clusters: [cluster]).clusters.first.cluster_arn
  end

  def task_definition_arn
    @task_definition_arn ||= ecs.describe_task_definition(task_definition: task_definition_name).task_definition.task_definition_arn
  end
end
