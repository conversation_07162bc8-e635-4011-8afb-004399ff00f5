# frozen_string_literal: true

# AwsSecretSearch finds secrets based on a search regex
class AwsSecretSearch
  def initialize(aws_client)
    @aws_client = aws_client
  end

  def search(search_regex, namespace_regex=nil, only_keys=false, only_values=false)
    each_secret do |entry, secret, secret_string|
      next if namespace_regex && !namespace_regex.match(entry.name)
      next if only_keys && !match?(search_regex, JSON.parse(secret_string).keys)
      next if only_values && !match?(search_regex, JSON.parse(secret_string).values)
      next if !only_keys && !only_values && !search_regex.match(secret_string)

      yield entry, secret, secret_string
    end
  end

  def each_secret
    next_token = nil

    while(true)
      list = @aws_client.list_secrets(next_token: next_token)
      list.secret_list.each do |entry|
        secret = @aws_client.get_secret_value(secret_id: entry.name)
        yield entry, secret, secret.secret_string
      end

      break unless (next_token = list.next_token)
    end
  end

  private

  def match?(search_regex, values)
    values.any? { |value| value.is_a?(String) && search_regex.match(value) }
  end
end
