
module MySQLHelpers
  module_function

  def create_client(database_name)
    Mysql2::Client.new(get_config(database_name))
  end

  def get_config(database_name)
    database_env_name = "DATABASE_URL_#{database_name.to_s.upcase}"
    raise("database environment variable #{database_env_name} not configured") unless ENV[database_env_name]

    database_uri = URI(ENV[database_env_name])

    {
      host: database_uri.host,
      username: Addressable::URI.unescape(database_uri.user.to_s),
      password: Addressable::URI.unescape(database_uri.password.to_s),
      database: database_uri.path[1..-1]
    }
  end

  def cache_query_results(client, query, cache_file)
    dir = TMP_DIR.join('cache').join(ENV['RAILS_ENV']).join('mysql_query_cache')
    FileUtils.mkdir_p(dir) unless File.exist?(dir)

    file = dir.join(cache_file)
    return JSON.parse(File.read(file)) if File.exist?(file)

    data = client.query(query).to_a

    File.open(file, 'wb') { |f| f.write data.to_json }

    data
  end
end
