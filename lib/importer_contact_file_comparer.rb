class ImporterContactFileComparer < Struct.new(
        :org,
        :import_file,
        :schema_class,
        :model_class,
        :remote_id_field_name,
        :save_output_files,
        keyword_init: true
      )

  FOUND = 'FOUND'
  NOT_FOUND = 'NOT_FOUND'
  UNKNOWN_REMOTE_ID = 'UNKNOWN_REMOTE_ID'
  MISSING = 'MISSING'

  attr_writer :importer_file_type

  attr_reader :file_results_report,
              :missing_records_report

  def importer_file_type
    @importer_file_type || model_class.table_name.pluralize
  end

  def setup!
    base_path = TMP_DIR.join('importer', 'validations', org.id.to_s).to_s
    FileUtils.mkdir_p(base_path)

    @missing_records_report = CSVUtils::CSVReport.new("#{base_path}/org-#{org.id}-#{org.slug}-missing-#{importer_file_type}-report-#{Time.now.strftime('%Y-%m-%d')}.csv", schema_class)

    @file_results_report = CSVUtils::CSVReport.new("#{base_path}/org-#{org.id}-#{org.slug}-#{importer_file_type}-validation-report-#{Time.now.strftime('%Y-%m-%d')}.csv", schema_class)
  end

  def upload!
    RepairshopFileUploader.upload_file_and_notify(missing_records_report.csv.path)
    RepairshopFileUploader.upload_file_and_notify(file_results_report.csv.path)
  end

  def input_csv_file
    @input_csv_file ||= CSVUtils::CSVIterator.new(import_file)
  end

  def from_row(remote_id, row)
    schema = schema_class.from_hash(row)
    schema.et_contact_remote_id = remote_id
    schema.normalize
    schema
  end

  def record_as_json(record)
    record.as_json
  end

  def from_record(identity, record)
    schema = schema_class.from_hash(record_as_json(record))
    schema.et_contact_remote_id = identity.value
    schema.normalize
    schema
  end

  def import_schemas
    @import_schemas ||=
      begin
        schemas = {}
        input_csv_file.each do |row|
          remote_id = row[remote_id_field_name]
          (schemas[remote_id] ||= []) << from_row(remote_id, row)
        end
        schemas
      end
  end

  def get_identities(remote_ids)
    ContactDB::Identity
      .where(
        oid: org.id,
        type: 1,
        value: remote_ids
      )
      .index_by(&:value)
  end

  def get_records(identities)
    model_class
      .where(
        contact_id: identities.values.map(&:contact_id)
      )
      .to_a
      .group_by(&:contact_id)
  end

  def has_matching_record?(schema, other_schemas)
    other_schemas.any? { |contact_record| schema == contact_record }
  end

  def compare_and_report
    setup!

    STATS[:type] = importer_file_type

    STATS.total = import_schemas.size

    LOG.info("Starting comparison process for #{importer_file_type}")

    import_schemas.keys.in_groups_of(10_000, false) do |remote_ids|
      identities = get_identities(remote_ids)
      records = get_records(identities)

      remote_ids.each do |remote_id|
        STATS.inc_and_notify

        unless (identity = identities[remote_id])
          import_schemas[remote_id].each do |schema|
            schema.comparison_result = UNKNOWN_REMOTE_ID
            file_results_report << schema
          end

          STATS.update(:unknown_remote_ids)
          next
        end

        record_schemas = (records[identity.contact_id] || []).map { |record| from_record(identity, record) }
        import_schemas[remote_id].each do |schema|
          schema.comparison_result =
            if has_matching_record?(schema, record_schemas)
              STATS.update(:matching_records)
              FOUND
            else
              STATS.update(:not_found_records)
              NOT_FOUND
            end

          file_results_report << schema
        end

        record_schemas.each do |record_schema|
          next if has_matching_record?(record_schema, import_schemas[remote_id])

          STATS.update(:missing_records)
          record_schema.comparison_result = 'MISSING'
          missing_records_report << record_schema
        end
      end
    end

    missing_records_report.close
    file_results_report.close

    upload! if save_output_files
  end
end
