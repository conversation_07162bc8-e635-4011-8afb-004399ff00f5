class ImporterFileComparer < Struct.new(
        :org,
        :import_file,
        :schema_class,
        :model_class,
        :remote_id_field_name,
        :save_output_files,
        keyword_init: true
      )

  MATCHES = 'MATCHES'
  DIFFERENT = 'DIFFERENT'
  NOT_FOUND = 'NOT_FOUND'

  attr_writer :importer_file_type

  attr_reader :validations_report

  def importer_file_type
    @importer_file_type || model_class.table_name.pluralize
  end

  def setup!
    base_path = TMP_DIR.join('importer', 'validations', org.id.to_s).to_s
    FileUtils.mkdir_p(base_path)

    @validations_report = CSVUtils::CSVReport.new("#{base_path}/org-#{org.id}-#{org.slug}-validation-#{importer_file_type}-report-#{Time.now.strftime('%Y-%m-%d')}.csv", schema_class)
  end

  def upload!
    RepairshopFileUploader.upload_file_and_notify(validations_report.csv.path)
  end

  def input_csv_file
    @input_csv_file ||= CSVUtils::CSVIterator.new(import_file)
  end

  def from_row(row)
    row.reject! { |_, v| v.nil? || v.empty? }
    schema = schema_class.from_hash(row)
    schema.normalize
    schema
  end

  def record_as_json(record)
    record.as_json
  end

  def from_record(record)
    schema = schema_class.from_hash(record_as_json(record))
    schema.normalize
    schema
  end

  def get_records(remote_ids)
    model_class
      .where(
        oid: org.id,
        remote_id_field_name => remote_ids
      )
      .index_by(&remote_id_field_name)
  end

  def compare_and_report
    setup!

    STATS[:type] = importer_file_type

    STATS.total = input_csv_file.size

    LOG.info("Starting comparison process for #{importer_file_type}")

    rows = []
    compare_rows_proc = proc do
      import_schemas = rows.map { |row| from_row(row) }.index_by(&remote_id_field_name)
      records = get_records(import_schemas.keys)

      import_schemas.each do |remote_id, import_schema|
        STATS.inc_and_notify

        unless (record = records[remote_id])
          import_schema.comparison_result = NOT_FOUND
          validations_report << import_schema
          STATS.update(:not_found_records)
          next
        end

        import_schema.comparison_result =
          if import_schema == from_record(record)
            STATS.update(:matching_records)
            MATCHES
          else
            STATS.update(:different_records)
            DIFFERENT
          end

        validations_report << import_schema
      end

      rows = []
    end

    input_csv_file.each do |row|
      rows << row
      compare_rows_proc.call if rows.size >= 10_000
    end

    compare_rows_proc.call if rows.size > 0

    validations_report.close

    upload! if save_output_files
  end
end
