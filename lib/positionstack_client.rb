class PositionstackClient < Struct.new(:access_key)
  include ClientApiBuilder::Router

  base_url 'http://api.positionstack.com'

  query_param :access_key, :access_key

  route :forward, '/v1/forward', query: {query: :query}

  def get_coordinates(query)
    payload = forward(query: query)
    location = payload['data'].first
    return nil if location.nil? || location.empty?

    location.slice('latitude', 'longitude')
  end
end
