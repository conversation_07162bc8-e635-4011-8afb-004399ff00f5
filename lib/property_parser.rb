class PropertyParser
  attr_reader :properties

  def initialize
    @properties = {}
  end

  def self.read(file)
    parser = new
    File.open(file, 'rb') { |f| parser.parse(f) }
    parser.properties
  end

  def parse(io)
    section = nil

    while !io.eof?
      line = io.readline.strip

      if line =~ /\[(.*?)\]/
        section = $1
        @properties[section] ||= {}
      elsif line =~ /=/
        name, value = line.split('=', 2).map(&:strip)

        if section
          @properties[section][name] = value
        else
          @properties[name] = value
        end
      end
    end
  end
end
