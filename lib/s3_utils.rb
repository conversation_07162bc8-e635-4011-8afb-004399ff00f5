# Purpose: generic S3 logic
module S3Utils
  module_function

  # fetches all files in an s3 folder
  def list_objects(s3_client, bucket, prefix, max_keys=1_000)
    resp = s3_client.list_objects(
      {
        bucket: bucket,
        prefix: prefix,
        max_keys: max_keys
      }
    )

    objects = resp.contents

    while resp.contents.size == max_keys
      marker = objects.last.key

      resp = s3_client.list_objects(
        {
          bucket: bucket,
          prefix: prefix,
          max_keys: max_keys,
          marker: marker
        }
      )

      objects += resp.contents
    end

    objects
  end

  # deletes an S3 folder and all of its contents
  def delete_folder(s3_client, bucket, prefix)
    objects = list_objects(s3_client, bucket, prefix)

    objects.each_slice(1_000) do |objects_to_delete|
      objects_to_delete = objects_to_delete.map { |obj_to_delete| {key: obj_to_delete.key} }

      s3_client.delete_objects(
        {
          bucket: bucket,
          delete: {
            objects: objects_to_delete,
            quiet: false
          }
        }
      )
    end
  end

  def size(s3_client, bucket, key)
    resp = s3_client.list_objects(
      {
        bucket: bucket,
        prefix: key,
        max_keys: 1
      }
    )

    return nil if resp.contents.empty?

    resp.contents[0].size
  end

  def exists?(s3_client, bucket, key)
    !! size(s3_client, bucket, key)
  end
end
