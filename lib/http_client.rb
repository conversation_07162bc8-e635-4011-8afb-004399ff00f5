# Utility class for making http calls
require 'net/http'

module HTTPClient
  module_function

  # based on net/http/requests.rb
  module CustomHTTP
    class List < Net::HTTPRequest
      METHOD = 'LIST'
      REQUEST_HAS_BODY = false
      RESPONSE_HAS_BODY = true
    end
  end

  METHOD_TO_NET_HTTP_CLASS = {
    delete: Net::HTTP::Delete,
    get: Net::HTTP::Get,
    head: Net::HTTP::Head,
    options: Net::HTTP::Options,
    patch: Net::HTTP::Patch,
    post: Net::HTTP::Post,
    put: Net::HTTP::Put,
    list: HTTPClient::CustomHTTP::List
  }

  def request(method, uri, body, headers, connection_options)
    request = METHOD_TO_NET_HTTP_CLASS[method].new(uri.request_uri, headers)
    request.body = body if body

    retries = 0
    begin
      Net::HTTP.start(uri.hostname, uri.port, connection_options.merge(use_ssl: uri.scheme == 'https')) do |http|
        http.request(request)
      end
    rescue Net::OpenTimeout => e
      LOG.error(e.inspect)
      retries += 1
      raise e if retries >= 5
      LOG.info("retrying request, attempt #{retries}")
      sleep 1
      retry
    end
  end

  def get(uri, headers = {}, connection_options = {})
    request(:get, uri, nil, headers, connection_options)
  end

  def post(uri, body, headers = {}, connection_options = {})
    request(:post, uri, body, headers, connection_options)
  end

  def put(uri, body, headers = {}, connection_options = {})
    request(:put, uri, body, headers, connection_options)
  end

  def delete(uri, body = nil, headers = {}, connection_options = {})
    request(:delete, uri, body, headers, connection_options)
  end

  def patch(uri, body, headers = {}, connection_options = {})
    request(:patch, uri, body, headers, connection_options)
  end

  def request_and_follow_redirect(method, uri, body, headers, connection_options, max_redirects = 10)
    res = nil

    max_redirects.times do
      res = request(method, uri, body, headers, connection_options)

      return res unless res.kind_of?(Net::HTTPRedirection)

      uri = URI(res['Location'])
    end

    res
  end
end
