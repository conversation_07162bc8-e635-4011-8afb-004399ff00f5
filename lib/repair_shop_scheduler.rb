require 'aws-sdk-eventbridge'

class RepairShopScheduler < RepairShopECSTaskRunner
  SCHEDULE_ARGS = [
    '--schedule',
    '--schedule-name',
    '--schedule-description'
  ]

  def schedule(script_options, name, description, cron_expression)
    raise('no name specified') unless name
    raise('no cron_expression specified') unless cron_expression

    update_rule(name, description, cron_expression)
    resp = update_rule_target(name, script_options)

    puts "schedule[#{name}] #{command(script_options)}"
  end

  def unschedule(name)
    delete_target(name)
    delete_rule(name)

    puts "unschedule[#{name}]"
  end

  def self.get_default_schedule_name(script_options)
    name = 'repairshop-' + File.basename(script_options.program_name)
    if script_options[:oid]
      org = AuthDB::Organization.find(script_options[:oid])
      name += '-for-' + org.slug + '-' + org.id.to_s
    end
    name
  end

  private

  def aws_eventbridge_client
    @aws_eventbridge_client ||= AwsEventsHelper.create_client
  end

  def delete_target(name)
    aws_eventbridge_client.remove_targets(
      rule: name,
      event_bus_name: event_bus_name,
      ids: [target_id(name)],
      force: false,
    )
  end

  def delete_rule(name)
    aws_eventbridge_client.delete_rule(
      name: name,
      event_bus_name: event_bus_name,
      force: false
    )
  end

  def update_rule(name, description, cron_expression)
    aws_eventbridge_client.put_rule(
      name: name,
      schedule_expression: cron_expression,
      state: 'ENABLED',
      description: description || name,
      role_arn: role_arn,
      tags: [
        {
          key: 'product',
          value: 'et_repair_shop'
        },
        {
          key: 'environment',
          value: ENV['RAILS_ENV']
        }
      ],
      event_bus_name: event_bus_name
    )
  end

  def update_rule_target(name, script_options)
    aws_eventbridge_client.put_targets(
      rule: name,
      event_bus_name: event_bus_name,
      targets: [
        {
          id: target_id(name),
          arn: cluster_arn,
          role_arn: role_arn,
          ecs_parameters: {
            task_definition_arn: task_definition_arn,
            task_count: 1,
          },
          input: target_input(script_options)
        }
      ]
    )
  end

  # update to remove schedule arguments
  def get_program_arguments(script_options)
    arguments = super(script_options)

    SCHEDULE_ARGS.each do |arg|
      next unless idx = arguments.index(arg)

      arguments.delete_at(idx)
      arguments.delete_at(idx)
    end

    arguments
  end

  def role_arn
    @role_arn ||= AwsIAMHelper.get_role_arn('eventbridge-repairshop-role')
  end

  def event_bus_name
    'default'
  end

  def target_id(name)
    name + '-target'
  end

  def target_input(script_options)
    {
      containerOverrides: [
        {
          name: container_name,
          command: command(script_options),
          environment: [
            {
              name: 'LOG',
              value: 'stdout',
            }
          ]
        }
      ]
    }.to_json
  end
end
