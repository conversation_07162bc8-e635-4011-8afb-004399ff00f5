class PresenceKS
  KEYSPACE_NAME = 'presence'

  def self.session
    @session ||= CassandraConnection.instance.get_session(KEYSPACE_NAME)
  end

  def self.keyspace
    @keyspace ||= CassandraHelpers.get_keyspace(CassandraConnection.instance.cluster, KEYSPACE_NAME)
  end

  def self.presence_table
    @presence_table ||= CassandraHelpers.get_table(keyspace, 'presence')
  end

  attr_reader :oid

  def initialize(oid)
    @oid = oid
  end

  def each_presence_records(type)
    cql = "SELECT * FROM #{Cassandra::Util.escape_name(self.class.presence_table.name)} WHERE oid = #{Cassandra::Util.encode_object(oid)} AND type = #{Cassandra::Util.encode_object(type)}"
    CassandraHelpers.each_record(self.class.session, cql) do |record|
      yield record
    end
  end

  def delete_presence(type, sub_type, remote_id)
    CassandraHelpers.delete_record(
      self.class.session,
      self.class.presence_table,
      'oid' => oid,
      'type' => type,
      'sub_type' => sub_type,
      'remote_id' => remote_id
    )
  end

  def create_presence(type, sub_type, remote_id, job_id, updated_at)
    sql = <<SQL
INSERT INTO #{Cassandra::Util.escape_name(self.class.presence_table.name)}
  (oid, type, sub_type, remote_id, job_id, updated_at)
  VALUES(
    #{Cassandra::Util.encode_object(oid)},
    #{Cassandra::Util.encode_object(type)},
    #{Cassandra::Util.encode_object(sub_type)},
    #{Cassandra::Util.encode_object(remote_id)},
    #{Cassandra::Util.encode_object(job_id)},
    #{Cassandra::Util.encode_object(updated_at)}
  )
SQL

    CassandraHelpers.execute_query(self.class.session, sql)
  end
end
