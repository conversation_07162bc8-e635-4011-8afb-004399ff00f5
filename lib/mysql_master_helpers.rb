require 'active_support/core_ext/hash'

module MySQLMasterHelpers
  module_function

  def et_one_password
    @et_one_password ||= ETOnePassword.new
  end

  def create_client(database_name)
    config = et_one_password.db_config(database_name)
    config['username'] = config.delete('user')
    Mysql2::Client.new(config.symbolize_keys)
  end

  def create_rds_client(rds_name)
    config = et_one_password.rds_config(rds_name)
    config['username'] = config.delete('user')
    config['database'] = 'mysql'
    Mysql2::Client.new(config.symbolize_keys)
  end

  def mysql_version(client)
    version_string = client.query('SELECT VERSION()').first.values.first
    version_string.split('.').map(&:to_i)
  end

  def use_new_mysql_syntax?(client)
    version = mysql_version(client)
    version[0] >= 8
  end

  def create_read_only_user(client, user, password)
    if use_new_mysql_syntax?(client)
      client.query("CREATE USER IF NOT EXISTS '#{Mysql2::Client.escape(user)}'@'%' IDENTIFIED BY '#{Mysql2::Client.escape(password)}'")
      client.query("GRANT SELECT, PROCESS, SHOW VIEW ON *.* TO '#{Mysql2::Client.escape(user)}'@'%'")
    else
      client.query("GRANT SELECT, PROCESS, SHOW VIEW ON *.* TO '#{Mysql2::Client.escape(user)}'@'%' IDENTIFIED BY '#{Mysql2::Client.escape(password)}'")
    end
  end

  def update_read_only_user(client, user)
    client.query("GRANT SELECT, PROCESS, SHOW VIEW ON *.* TO '#{Mysql2::Client.escape(user)}'@'%'")
  end

  def create_user(client, user, password)
    if use_new_mysql_syntax?(client)
      client.query("CREATE USER IF NOT EXISTS '#{Mysql2::Client.escape(user)}'@'%' IDENTIFIED BY '#{Mysql2::Client.escape(password)}'")
    else
      client.query("GRANT USAGE ON *.* TO '#{Mysql2::Client.escape(user)}'@'%' IDENTIFIED BY '#{Mysql2::Client.escape(password)}'")
    end
  end

  def create_full_user(client, user, password, database)
    if use_new_mysql_syntax?(client)
      client.query("CREATE USER IF NOT EXISTS '#{Mysql2::Client.escape(user)}'@'%' IDENTIFIED BY '#{Mysql2::Client.escape(password)}'")
      client.query("GRANT ALL ON #{Mysql2::Client.escape(database)}.* TO '#{Mysql2::Client.escape(user)}'@'%'")
    else
      client.query("GRANT ALL ON #{Mysql2::Client.escape(database)}.* TO '#{Mysql2::Client.escape(user)}'@'%' IDENTIFIED BY '#{Mysql2::Client.escape(password)}'")
    end
  end

  def grant_all_to_user(client, user, database)
    client.query("GRANT ALL ON #{Mysql2::Client.escape(database)}.* TO '#{Mysql2::Client.escape(user)}'@'%'")
  end

  def create_slow_log_user(client, user, password)
    if use_new_mysql_syntax?(client)
      client.query("CREATE USER IF NOT EXISTS '#{Mysql2::Client.escape(user)}'@'%' IDENTIFIED BY '#{Mysql2::Client.escape(password)}'")
      client.query("GRANT SELECT ON mysql.slow_log TO '#{Mysql2::Client.escape(user)}'@'%'")
    else
      client.query("GRANT SELECT ON mysql.slow_log TO '#{Mysql2::Client.escape(user)}'@'%' IDENTIFIED BY '#{Mysql2::Client.escape(password)}'")
    end
  end

  def get_user(client, user)
    client.query("SELECT * FROM mysql.user WHERE User = '#{Mysql2::Client.escape(user)}'").first
  end

  def delete_user(client, user)
    client.query("DROP USER '#{Mysql2::Client.escape(user)}'@'%'")
  end

  def all_users(client)
    client.query('SELECT User FROM mysql.user', as: :array).to_a.flatten
  end

  def update_user_authentication_string(client, user, authentication_string)
    client.query("UPDATE mysql.user SET authentication_string = '#{Mysql2::Client.escape(authentication_string)}' WHERE User = '#{Mysql2::Client.escape(user)}'")
    client.query('FLUSH PRIVILEGES')
  end
  def verify_rds!
    seen_dbs = []
    et_one_password.config['rds'][ENV['RAILS_ENV']].each do |rds_name, settings|
      settings['databases'].each do |database|
        puts "Verifying RDS: #{rds_name}, DB: #{database}"
  
        raise("Already seen #{database}") if seen_dbs.include?(database)
  
        client = create_client(database)
        result = client.query('SHOW DATABASES')
        
        # Make sure the database exists by checking the result
        database_list = result.map { |row| row.values.first }
        unless database_list.include?(database)
          raise "Database #{database} not found in RDS #{rds_name}"
        end
  
        seen_dbs << database
      end
    end
  end
end      