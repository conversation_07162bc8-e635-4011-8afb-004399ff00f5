class FacebookCollector
  attr_reader :facebook_client,
              :limit,
              :stats

  def initialize(facebook_client, limit = 25, stats = STATS)
    @facebook_client = facebook_client
    @limit = limit
    @stats = stats
  end

  # a Facebook access_token should have access to posts, comments and reactions
  def valid_facebook_token?(page_id)
    post_payload = facebook_client.get_page_posts(page_id: page_id)

    # assume the access_token is valid
    return true if post_payload['data'].size == 0

    post_id = post_payload['data'].first['id']

    facebook_client.get_comments(post_id: post_id)
    facebook_client.get_reactions(post_id: post_id)
    true
  rescue ClientApiBuilder::UnexpectedResponse => e
    false
  end

  def each_post(page_id)
    LOG.info("fetching posts for page #{page_id}")

    payload = facebook_client.get_page_posts(page_id: page_id, query: {limit: limit})

    payload['data'].each do |fb_post|
      stats.update(:total_posts)
      yield fb_post
    end

    while (next_url = payload.dig('paging', 'next'))
      unless (next_url = fix_resource_url(next_url, page_id))
        stats.update(:next_posts_url_invalid)
        break
      end

      LOG.info("fetch next set of posts with #{next_url}")
      payload = JSON.parse(HTTPClient.get(URI(next_url)).body)

      payload['data'].each do |fb_post|
        stats.update(:total_posts)
        yield fb_post
      end
    end
  rescue ClientApiBuilder::UnexpectedResponse => e
    LOG.error("received exception #{e.inspect}/#{e.response.body} while fetching posts for page #{page_id}")
    handle_unexpected_response(e)
  end

  def each_comment(post_id)
    LOG.info("fetching comments for post #{post_id}")

    payload = facebook_client.get_comments(post_id: post_id, query: {limit: limit})

    payload['data'].each do |fb_comment|
      stats.update(:total_comments)
      yield fb_comment
    end

    while (next_url = payload.dig('paging', 'next'))
      unless (next_url = fix_resource_url(next_url, post_id))
        stats.update(:next_comments_url_invalid)
        break
      end

      LOG.info("fetch next set of comments with #{next_url}")
      payload = JSON.parse(HTTPClient.get(URI(next_url)).body)

      unless payload['data']
        LOG.error("no comment data return for post_id #{post_id}, next_url #{next_url}")
        return []
      end

      payload['data'].each do |fb_comment|
        stats.update(:total_comments)
        yield fb_comment
      end
    end
  rescue ClientApiBuilder::UnexpectedResponse => e
    LOG.error("received exception #{e.inspect}/#{e.response.body} while fetching comments for post #{post_id}")
    handle_unexpected_response(e)
  end

  def each_reaction(post_id)
    LOG.info("fetching reactions for post #{post_id}")

    payload = facebook_client.get_reactions(post_id: post_id, query: {limit: limit})

    payload['data'].each do |fb_reaction|
      stats.update(:total_reactions)
      yield fb_reaction
    end

    while (next_url = payload.dig('paging', 'next'))
      unless (next_url = fix_resource_url(next_url, post_id))
        stats.update(:next_reactions_url_invalid)
        break
      end

      LOG.info("fetch next set of reactions with #{next_url}")
      payload = JSON.parse(HTTPClient.get(URI(next_url)).body)

      unless payload['data']
        LOG.error("no reaction data return for post_id #{post_id}, next_url #{next_url}")
        return []
      end

      payload['data'].each do |fb_reaction|
        stats.update(:total_reactions)
        yield fb_reaction
      end
    end
  rescue ClientApiBuilder::UnexpectedResponse => e
    LOG.error("received exception #{@e.inspect}/#{e.response.body} while fetching reactions for post #{post_id}")
    handle_unexpected_response(e)
  end

  def fix_resource_url(url, remote_id)
    unless url.match?(/after=[^&]+/)
      LOG.error("next url #{url} missing or invalid after param for #{remote_id}")
      stats.update(:next_url_missing_after)
      return nil
    end

    url =~ /v\d+\.\d+\/(.*?)\//
    current_remote_id = $1

    if current_remote_id == remote_id
      stats.update(:valid_next_url_id)
    else
      stats.update(:invalid_next_url_id)
      LOG.error("next url #{url} invalid resource id current_remote_id #{current_remote_id} should be #{remote_id}")
      url = url.sub(current_remote_id, remote_id)
    end

    url
  end

  def handle_unexpected_response(e)
    unless e.response.code == '400'
      raise e
    end
  end
end
