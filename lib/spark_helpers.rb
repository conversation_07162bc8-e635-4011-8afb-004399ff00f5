module SparkHelpers
  module_function

  def spark_master_host
    SPARK_CONFIG['hosts'].first
  end

  def get_applications
    HTTPClient.get(URI("http://#{spark_master_host}:#{SPARK_CONFIG['master_port']}/api/v1/applications"))
  end

  # this route returns worker information
  def get_json
    uri = URI("http://#{spark_master_host}:#{SPARK_CONFIG['master_port']}/json/")
    res = HTTPClient.get(uri)
    attempts = 0
    while res.body.include?('<!DOCTYPE html>')
      attempts += 1
      raise('failed to get spark worker information') if attempts > 4
      sleep(0.2)
      res = HTTPClient.get(uri)
    end
    JSON.parse(res.body)
  end

  def get_historical_applications(min_date, max_date, status = 'completed')
    HTTPClient.get(URI("http://#{spark_master_host}:#{SPARK_CONFIG['history_port']}/api/v1/applications?minDate=#{min_date}&maxDate=#{max_date}&status=#{status}"))
  end

  def num_running_jobs
    get_json['activeapps'].size
  end
end
