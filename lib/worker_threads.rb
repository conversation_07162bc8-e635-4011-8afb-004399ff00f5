class WorkerThreads < Thread::Queue
  attr_reader :threads

  attr_accessor :max_size

  def initialize(num_threads = 1)
    super()

    @threads = num_threads.times.map do
      Thread.new { yield self }
    end
  end

  def each
    while msg = deq
      if (pause_duration = calculate_pause_duration)
        slept = sleep(pause_duration)
      end

      yield msg
    end
  end

  def shutdown
    threads.size.times { enq(nil) }
    threads.each(&:join)
  end

  def process
    yield self
    shutdown
  end

  def pause(duration)
    now = Time.now
    @threads.each do |thread|
      thread[:thread_pause_occurred_at] = now
      thread[:thread_pause_duration] = duration
    end
  end

  def enq(payload)
    while max_size && size >= max_size
      sleep(0.1)
    end

    super(payload)
  end

  private

  def calculate_pause_duration
    if Thread.current[:thread_pause_occurred_at] && Thread.current[:thread_pause_duration]
      duration = Thread.current[:thread_pause_duration] - (Time.now - Thread.current[:thread_pause_occurred_at])
      Thread.current[:thread_pause_occurred_at] = nil
      Thread.current[:thread_pause_duration] = nil
      duration > 0.0 ? duration : nil
    end
  end
end
