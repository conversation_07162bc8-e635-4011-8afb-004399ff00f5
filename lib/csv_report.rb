require 'csv'

class CSVReport
  attr_reader :csv_file_name,
              :headers,
              :wrote_headers

  def initialize(csv_file_name, headers = nil)
    @csv_file_name = csv_file_name
    @headers = headers
  end

  def open
    @csv = CSV.open(csv_file_name, 'wb')
    write_headers(headers) if headers
    @csv
  end

  def <<(row)
    @csv << row
  end

  def close
    @csv.close
  end

  def generate
    open
    yield self
    close
  end

  def from_mysql_query(db_client, query)
    generate do |csv|
      db_client.query(query).each do |result|
        write_headers(result.keys) unless wrote_headers
        csv << result.values
      end
    end
  end

  def write_headers(headers)
    @csv << headers
    @wrote_headers = true
  end
end
