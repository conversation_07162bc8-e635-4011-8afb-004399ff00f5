module ImporterMemcacheHelpers
  module_function

  def create_clients
    MEMCACHE_CONFIG['importer_hosts'].map do |host|
      client = Dalli::Client.new(host + ':' + MEMCACHE_CONFIG['port'].to_s)
      client.stats
      client
    end
  end

  def get_cache_key(oid, remote_id, importer_data_type)
    [
      oid,
      escape_remote_id(remote_id),
      importer_data_type
    ].join(':')
  end

  def get_checksum_cache_key(oid, remote_id, importer_data_type)
    [
      oid,
      escape_remote_id(remote_id),
      importer_data_type,
      'checksum'
    ].join(':')
  end

  def get_checksum_cache_key_with_mapping_hash(oid, remote_id, mapping_hash_id, importer_data_type)
    [
      oid,
      escape_remote_id(remote_id),
      mapping_hash_id,
      importer_data_type,
      'checksum'
    ].join(':')
  end

  def get_note_cache_key(oid, remote_id)
    get_checksum_cache_key(oid, remote_id, 'NOTE')
  end

  def get_proposal_cache_key(oid, remote_id)
    get_checksum_cache_key(oid, remote_id, 'PROPOSAL')
  end

  def escape_remote_id(remote_id)
    remote_id.gsub(' ', '+')
  end

  # attempts to delete the key from every client
  # if deleted returns true
  def delete_key(clients, key)
    clients.any? do |client|
      client.delete(key)
    end
  end
end
