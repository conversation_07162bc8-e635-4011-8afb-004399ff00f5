# frozen_string_literal: true

module CommandLineHelpers
  def highlight_string(str)
    "\033[1m#{str}\033[0m"
  end
  alias bold_string highlight_string

  def underline_string(str)
    "\033[4m#{str}\033[0m"
  end

  def green_string(str)
    "\033[32m#{str}\033[0m"
  end

  def red_string(str)
    "\033[31m#{str}\033[0m"
  end

  def yes_no(prompt)
    answer = nil
    while !answer
      print prompt + ' (y/n): '
      case $stdin.readline.downcase.strip
      when 'y',
           'yes'
        answer = :yes
      when 'n',
           'no'
        answer = :no
      end
    end
    answer
  end

  def get_input(prompt)
    res = nil
    while res.nil? || res.empty?
      print prompt + ': '
      res = $stdin.readline.downcase.strip
    end
    res
  end

  def get_input_or_default(prompt, default)
    res = nil
    print prompt + ': '
    res = $stdin.readline.downcase.strip
    (res.nil? || res.empty?) ? default : res
  end

  def run_command(cmd)
    LOG.info "running: #{cmd}"
    started_at = Time.now
    result = `#{cmd}`
    LOG.info "took #{Time.now - started_at} to run #{cmd}"
    raise("command failed #{cmd}") unless $?.success?
    result
  end
end
