require 'shellwords'

# Purpose: integrate with 1Password, avoid having to create config files or use secrets manager for administrative level passwords
# Requirements:
# * Install: brew install --cask 1password/tap/1password-cli
# * Sign In: op signin --account evertrue
# *  - Secret Key can be found in 1Password > Preferences > Accounts
# Future sign ins: op signin
class OnePassword
  OP_CMD = 'op' # name of the binary
  attr_reader :subdomain

  def initialize(subdomain)
    @subdomain = subdomain
    validate_session_token!
  end

  def validate_session_token!
    return if @already_validated_session_token

    list_vaults
    @already_validated_session_token = true
  end

  def get_item(uuid, fields='label=username,label=password,label=hostname,label=port,label=database')
    run_cmd(Shellwords.join([OP_CMD, 'item', 'get', uuid, '--format', 'json', '--fields', fields]))
  end

  def list_vaults
    run_cmd(Shellwords.join([OP_CMD, 'vaults', 'list', '--format', 'json']))
  end

  def run_cmd(cmd)
    output = `#{cmd}`
    raise("command failed: #{cmd}, try signing in again: eval $(op signin #{subdomain})") unless $?.success?
    JSON.parse(output)
  end

  def display_item(name_or_uuid)
    item = get_item(name_or_uuid)
    print JSON.pretty_generate(item) + "\n"
  end
end
