module Windfall
  module_function

  def get_latest_windfall_enriched_s3_file_path(oid)
    oid = oid.to_s

    each_file do |file|
      next unless file =~ /windfall_enriched_(\d+)_\d{8}\.csv$/
      next unless $1 == oid

      return file
    end

    nil
  end

  def get_previous_windfall_enriched_s3_file_path(oid)
    oid = oid.to_s
    latest_windfall_enrichment_file = nil

    each_file do |file|
      next unless file =~ /windfall_enriched_(\d+)_\d{8}\.csv$/
      next unless $1 == oid

      if latest_windfall_enrichment_file.nil?
        latest_windfall_enrichment_file = File.basename(file)
        next
      end

      # skip over duplicate files
      next if File.basename(file) == latest_windfall_enrichment_file

      return file 
    end

    nil
  end

  def each_file
    files = []
    S3Utils.list_objects(create_s3_client, windfall_s3_bucket, base_windfall_s3_path).each do |object|
      next unless object.key =~ /^windfall\/\d{8}\/.*\.csv$/

      files << object.key
    end

    files.sort!
    files.reverse!

    files.each do |file|
      yield file
    end
  end

  def get_file(key)
    create_s3_client.get_object(
      {
        bucket: windfall_s3_bucket,
        key: key
      }
    )
  end

  def upload_file(file, key)
    File.open(file, 'rb') do |body|
      create_s3_client.put_object(
        bucket: windfall_s3_bucket,
        key: key,
        body: body
      )
    end

    "s3://#{windfall_s3_bucket}/#{key}"
  end

  def get_career_moves_update_path(oid, date)
    "export/windfall_career_updates/oid=#{oid}/windfall_updates/#{date}/Career_Moves_Updates_Export_#{date}.csv"
  end

  def get_latest_career_moves_update_path(oid)
    latest_windfall_enrichment_file = get_latest_windfall_enriched_s3_file_path(oid)
    return unless latest_windfall_enrichment_file

    latest_windfall_enrichment_file =~ /windfall_enriched_\d+_(\d{8})\.csv$/
    get_career_moves_update_path(oid, $1)
  end

  def get_csv(key)
    CSV.new(get_file(key).body, liberal_parsing: true)
  end

  def get_csv_iterator(key)
    CSVUtils::CSVIterator.new(get_csv(key))
  end

  def create_s3_client
    Aws::S3::Client.new(EnvironmentLoader.instance.aws_config)
  end

  def base_windfall_s3_path
    'windfall'
  end

  def windfall_s3_bucket
    'et-enrichment-' + APP_ENV_PREFIX
  end
end
