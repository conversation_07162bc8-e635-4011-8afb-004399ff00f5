# frozen_string_literal: true

module DynamicActiveModelGenerator
  module_function

  DATABASES = %w[
    ai
    auth
    buoys
    census
    chat
    contacts
    crm_integrations
    emma
    ems
    enrichment
    events
    exporter
    gifts
    give
    graduway
    hub
    importer
    integrations
    integrations_v2
    journeys
    landing_pages
    outreach
    search
    sodas
    suggestions
    tags
    trips
    ugc
    volunteers
  ].freeze

  def create_database(database_name, mod, skip_tables = [], relationships = {})
    require 'dynamic-active-model'

    database_url_env_name = "DATABASE_URL_#{database_name.upcase}"
    unless ENV[database_url_env_name]
      raise("#{database_url_env_name} is incorrect or missing, check the config/image.yml")
    end

    connection_options = {
      'url' => ENV[database_url_env_name],
      pool: 30
    }

    DynamicActiveModel::Explorer.explore(mod, connection_options, skip_tables, relationships)
  end

  def create_database_models(database_name, skip_tables = [], relationships = {})
    module_name = (database_name.singularize.classify + 'DB').to_sym
    return Object.const_get(module_name).database if Object.const_defined?(module_name) && Object.const_get(module_name)&.database

    database = create_database(database_name, Object.const_set(module_name, Module.new), skip_tables, relationships)
    mod = Object.const_get(module_name)
    mod.singleton_class.attr_accessor :database
    disable_inheritance_column(database)
    mod.database = database
  end

  def create_dynamic_db_files
    DATABASES.each do |database_name|
      File.open("lib/dynamic_db/#{database_name.singularize}_db.rb", 'wb') do |f|
        f.write <<STR
# frozen_string_literal: true

DynamicActiveModelGenerator.create_database_models('#{database_name}')
STR
      end
      puts "autoload :#{database_name.singularize.classify}DB, 'dynamic_db/#{database_name.singularize}_db.rb'"
    end
  end

  def create_database_models_for_backup(database_name, backup_host, skip_tables = [], relationships = {})
    require 'dynamic-active-model'

    database_url_env_name = "DATABASE_URL_#{database_name.upcase}"
    unless ENV[database_url_env_name]
      raise("#{database_url_env_name} is incorrect or missing, check the config/image.yml")
    end

    backup_url = ENV[database_url_env_name].sub(/@.*?:/, "@#{backup_host}:")
    ENV[database_url_env_name + '_BACKUP'] = backup_url

    create_database_models(database_name + '_backup', skip_tables, relationships)
  end

  def disable_inheritance_column(database)
    database.models.each do |model|
      next unless model.attribute_names.include?('type')

      model.inheritance_column = :_type_disabled
    end
  end
end
