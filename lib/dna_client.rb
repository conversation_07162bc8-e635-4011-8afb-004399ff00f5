# frozen_string_literal: true

class DNAClient < BaseApiClient
  PLATFORMS = %i(service web ios android)

  KEEP_LEADING_ZEROS = 'ET.Importer.KeepRemoteIDLeadingZeros'
  LEADING_ZERO_PADDING_LENGTH = 'ET.Importer.RemoteIDZeroPaddingLength'
  INGESTION_MODE = 'ET.Importer.IngestionMode'
  GATE_SUPER_POWERS = 'super_powers'
  USER_EDIT_POWERS = 'user_edit_powers'
  AFFILIATION_ATTRIBUTE_POWERS = 'affiliation_attribute_powers'
  NOTE_EXPORT_END_OF_RECORD = 'note-export-end-of-record'
  PROPOSAL_EXPORT_END_OF_RECORD = 'proposal-export-end-of-record'
  TIME_ZONE = 'ET.Importer.TimeZone'
  FISCAL_YEAR = 'ET.Org.FiscalYear'

  def get_setting_value(oid, name)
    res = request(:get, "/dna/setting_values/#{name}?oid=#{oid}")
    JSON.parse(res.body)
  end

  def get_bulk_setting_value(name)
    res = request(:get, "/dna/bulk/setting_values/#{name}")
    JSON.parse(res.body)
  end

  def get_bulk_gate(name)
    res = request(:get, "/dna/bulk/gates/#{name}")
    JSON.parse(res.body)
  end

  def get_gate(oid, name)
    res = request(:get, "/dna/gates/#{name}?oid=#{oid}")
    JSON.parse(res.body)
  end

  def get_gate_for_platform(oid, name, platform)
    res = request(:get, "/dna/gates/#{name}?oid=#{oid}&platform=#{platform}")
    JSON.parse(res.body)
  end

  def get_user_gate(user_id, name)
    res = request(:get, "/dna/gates/#{name}?user_id=#{user_id}")
    JSON.parse(res.body)
  end

  def get_org_user_gate(oid, user_id, name)
    res = request(:get, "/dna/gates/#{name}?oid=#{oid}&user_id=#{user_id}")
    JSON.parse(res.body)
  end

  def get_user_gates(user_id)
    res = request(:get, "/dna/gates?user_id=#{user_id}")
    JSON.parse(res.body)
  end

  def update_setting_value(name, value, scopes = {})
    res = request(:put, "/dna/setting_values/#{name}", scopes.merge(value: value))

    raise("failed to update dna setting #{name} to #{value.inspect} for scopes #{scopes} #{res}/#{res.body}") unless res.kind_of?(Net::HTTPSuccess)

    JSON.parse(res.body)
  end

  def create_setting(name)
    res = request(:post, "/dna/settings", key: name, permissions: 'write')

    raise("failed to create dna setting #{name}, reason #{res.body}") unless res.kind_of?(Net::HTTPSuccess)

    JSON.parse(res.body)
  end

  def is_big_customer?(oid)
    get_setting_value(oid, 'is-big-customer').dig('settings', 'is-big-customer', 'value') == true
  end

  def all_big_customer_oids
    results = get_bulk_setting_value('is-big-customer')
    results['setting_values'].select { |setting_value| setting_value['value'] }.map { |setting_value| setting_value['oid'] }
  end

  def keep_remote_id_leading_zeros?(oid)
    get_setting_value(oid, KEEP_LEADING_ZEROS).dig('settings', KEEP_LEADING_ZEROS, 'value') == true
  end

  def update_gate(name, enabled, scopes = {})
    res = request(:put, "/dna/gates/#{name}", scopes.merge(enabled: enabled))

    raise("failed to update dna gate #{name} to #{enabled.inspect} for scopes #{scopes}") unless res.kind_of?(Net::HTTPSuccess)

    JSON.parse(res.body)
  end

  def update_gate_default(name, enabled)
    res = request(:put, "/dna/gates/#{name}/default", enabled: enabled)

    raise("failed to update dna gate #{name} to #{enabled.inspect} for scopes #{scopes}") unless res.kind_of?(Net::HTTPSuccess)

    JSON.parse(res.body)
  end

  def delete_gate(name, scopes = {})
    request(:delete, "/dna/gates/#{name}", scopes)
  end

  def delete_gate_default(name)
    request(:delete, "/dna/gates/#{name}/default")
  end

  def delete_setting_value(name, scopes = {})
    request(:delete, "/dna/setting_values/#{name}", scopes)
  end
end
