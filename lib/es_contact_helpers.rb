# Helper methods for fetching data from the contact index
module ESContactHelpers
  module_function

  MAPPING_TO_OID_PROPERTY ={
    '.percolator': :oid,
    appeal: :oid,
    assignment_analytics: :oid,
    contact: :oid,
    event: :oid,
    list: :oid,
    submission: :oid,
    suggested_update: :oid,
    # child docs of contact
    assignment: :contact_oid,
    contact_activity: :contact_oid,
    contact_appeal: :contact_oid,
    contact_engagement: :contact_oid,
    contact_event_engagement: :oid,
    contact_note: :oid,
    gift: :contact_oid,
    proposal: :oid,
    social: :contact_oid,
    contact_tag: :oid
  }

  def dna_client
    @dna_client ||= DNAClient.create_app_client
  end

  def is_big_customer?(oid)
    @is_big_customer ||= {}
    return @is_big_customer[oid] if @is_big_customer.key?(oid)

    @is_big_customer[oid] = dna_client.is_big_customer?(oid)
  end

  def url_for_doc(mapping_name, id, oid)
    path = "/#{index_name_for_oid(oid)}/#{mapping_name}/#{id}"

    if !is_big_customer?(oid)
      path += "?routing=#{oid}"
    end

    ESHelpers.url_for_path(path)
  end

  def request_doc_by_id(mapping_name, id, oid)
    HTTPClient.get(URI(url_for_doc(mapping_name, id, oid)))
  end

  def query_mapping_by_oid(mapping_name, oid)
    query = {
      'bool' => {
        'must' => [
          {
            'term' => {
              MAPPING_TO_OID_PROPERTY[mapping_name.to_sym].to_s => {
                'value' => oid.to_s
              }
            }
          }
        ]
      }
    }

    path = "/#{index_name_for_oid(oid)}/#{mapping_name}/_search"

    ElasticSearchScanner.new(ESHelpers.url_for_path(path), query)
  end

  def count_by_oid(mapping_name, oid, index_name_override=nil)
    path = "/#{index_name_override || index_name_for_oid(oid)}/#{mapping_name}/_count?q=#{MAPPING_TO_OID_PROPERTY[mapping_name.to_sym]}:#{oid}"
    res = HTTPClient.get(ESHelpers.uri_for_path(path))
    JSON.parse(res.body)['count']
  end

  def index_prefix
    case ENV['RAILS_ENV']
    when 'production'
      'prod'
    when 'staging'
      'stage'
    else
      'local'
    end
  end

  def index_name_for_oid(oid)
    "#{index_prefix}-contacts-oid-#{oid}"
  end
end
