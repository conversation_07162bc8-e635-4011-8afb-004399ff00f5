# frozen_string_literal: true

class VaultClient
  attr_accessor :client_token

  def self.login!
    new.tap(&:login!)
  end

  def login!
    raise('no GITHUB_PERSONAL_ACCESS_TOKEN found in environment variable') unless ENV['GITHUB_PERSONAL_ACCESS_TOKEN']

    headers = {
      'Content-Type' => 'application/json'
    }

    payload = {
      token: ENV['GITHUB_PERSONAL_ACCESS_TOKEN']
    }

    url = base_url + '/v1/auth/github/login'

    res = HTTPClient.request_and_follow_redirect(
      :post,
      URI(url),
      payload.to_json,
      headers,
      {}
    )

    raise("github login request failed with #{res.code}/#{res.body}") unless res.kind_of?(Net::HTTPSuccess)

    @client_token = JSON.parse(res.body)['auth']['client_token']
  end

  def list_secrets
    res = request(:list, '/v1/secret/default')

    raise("list secrets request failed with #{res.code}/#{res.body}") unless res.kind_of?(Net::HTTPSuccess)

    JSON.parse(res.body)['data']['keys']
  end

  def get_secret(name)
    res = request(:get, "/v1/secret/default/#{name}")

    raise("get secret #{name} request failed with #{res.code}/#{res.body}") unless res.kind_of?(Net::HTTPSuccess)

    JSON.parse(res.body)['data']
  end

  def update_secret(name, secrets)
    res = request(:put, "/v1/secret/default/#{name}", secrets)

    raise("update secret #{name} request failed with #{res.code}/#{res.body}") unless res.code == '204'
  end

  def request(action, path, payload = nil, headers = {}, connection_options = {})
    url = base_url + path

    res = HTTPClient.request_and_follow_redirect(
      action,
      URI(url),
      payload ? payload.to_json : nil,
      req_headers.merge(headers),
      connection_options
    )

  end

  def base_url
    "#{VAULT_CONFIG['scheme']}://#{VAULT_CONFIG['host']}:#{VAULT_CONFIG['port']}"
  end

  private

  def req_headers
    raise('no client token, login! first') unless @client_token

    {
      'Content-Type' => 'application/json',
      'X-Vault-Token' => @client_token
    }
  end
end
