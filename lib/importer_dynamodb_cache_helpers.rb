require 'aws-sdk-dynamodb'

module ImporterDynamodbCacheHelpers
  module_function

  CACHE_TABLE_NAMES = {
    assignments: "importer-assignment-cache",
    contacts: "importer-contact-cache",
    interactions: "importer-interaction-cache",
    proposals: "importer-proposal-cache",
    gifts: "importer-transactional-gift-cache"
  }

  def get_table_name(type)
    CACHE_TABLE_NAMES[type.to_sym]
  end

  def create_dynamodb_client
    Aws::DynamoDB::Client.new(aws_config)
  end

  def aws_config
    EnvironmentLoader.instance.aws_config
  end

  def get_contact_cache_item(dynamodb_client, oid, contact_id)
    dynamodb_client.get_item(
      table_name: CACHE_TABLE_NAMES[:contacts],
      key: {
        oid: oid,
        evertrue_id: contact_id
      }
    )
  end
end
