require 'aws-sdk-eventbridge'

module AwsEventsHelper
  module_function

  def create_client
    Aws::EventBridge::Client.new(aws_events_config)
  end

  def list_rules(client, event_bus_name)
    rules = []
    resp = client.list_rules(event_bus_name: event_bus_name)
    rules += resp.rules

    while resp.next_token
      resp = client.list_rules(
        event_bus_name: event_bus_name,
        next_token: resp.next_token
      )

      rules += resp.rules
    end

    rules
  end

  def aws_events_config
    {
      region: EnvironmentLoader.instance.aws_region_name,
      profile: EnvironmentLoader.instance.aws_profile
    }
  end
end
