# frozen_string_literal: true

require 'et_logger'
require 'fileutils'

# ProcessLogger creates a log file base on the process name and the environment
class ProcessLogger
  attr_writer :log_dir_name,
              :log_dir,
              :log_file

  def initialize(environment, base_dir)
    @environment = environment
    @base_dir = base_dir
    @log_dir_name = 'log'
  end

  def create_logger(formatter = nil)
    logger = ETLogger.new(log_file)
    logger.formatter = formatter || Logger::Formatter.new
    logger
  end

  private

  def log_file
    if ENV['LOG'] == 'stdout'
      $stdout
    else
      name = @environment + '.log'
      name = process_name + '-' + name if process_name
      log_dir + '/' + name
    end
  end

  def log_dir
    dir = @base_dir.to_s + '/' + @log_dir_name
    FileUtils.mkdir(dir) unless File.exist?(dir)
    dir
  end

  def process_name
    if $PROGRAM_NAME =~ /(?:bin|script|irb)/
      File.basename($PROGRAM_NAME)
    elsif <PERSON>['_'] =~ /(?:bin|script|irb)/
      File.basename(ENV['_'])
    end
  end
end
