class JourneyClient < BaseApiClient

  def close_contact_journey(payload)
    response = request(:post, "/journeys/v2/contact-journeys?oid=#{payload[:oid]}", payload)
    case response
    when Net::HTTPSuccess
      p "Successfully closed Journey: #{payload[:old_journey][:id]}, User: #{payload[:user_id]} Contact: #{payload[:contact_id]}"
    else 
      p "ERROR! #{response.body}"
    end
  end 

  def migrate_dxo(payload, errors)
    response = request(:post, "/journeys/v1/onboard/dxo/migrate?oid=#{payload[:oid]}", payload)
    case response
    when Net::HTTPNoContent
      return 1
    else
      errors[payload[:user_id]] = response.body
      return 0
    end
  end

  def create_action(payload)
    response = request(:post, "/journeys/v1/actions?oid=#{payload[:oid]}", payload)
    case response
    when Net::HTTPSuccess
      p "Successfully created action #{response.body}"
    else 
      p "ERROR! #{response.body}"
    end
  end
end

