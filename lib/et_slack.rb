require 'socket'

# Wrapper for Slack::Web::Client provides a message_prefix when sending out notifications
class ETSlack
  attr_writer :message_prefix
  attr_reader :slack_api_token,
              :channel_threads

  def initialize(slack_api_token)
    @slack_api_token = slack_api_token
    @channel_threads = {}
  end

  def app_name
    $0.split('/').last
  end

  def message_prefix
    "_`#{ENV['RAILS_ENV']}`_ *`#{app_name}[#{running_identifier}]:`*"
  end

  def client
    @client ||= Slack::Web::Client.new(token: slack_api_token)
  end

  def configured?
    slack_api_token
  end

  def message(channel, message)
    if (thread_ts = channel_threads[channel])
      client.chat_postMessage(channel: channel, text: message, icon_emoji: icon_emoji, username: username, thread_ts: thread_ts)
    else
      channel_threads[channel] = client.chat_postMessage(channel: channel, text: "#{message_prefix} #{message}", icon_emoji: icon_emoji, username: username).message.ts
    end
  end

  # returns the ECS task_id or the PID
  def running_identifier
    EnvironmentLoader.instance.ecs_task_id ||
      Process.pid
  end

  def username
    ENV['SLACK_USERNAME'] || 'Repair Shop'
  end

  def icon_emoji
    ENV['SLACK_ICON_EMOJI'] || ':hammer_and_wrench:'
  end
end
