require 'singleton'

class RepairshopFileUploader
  include Singleton

  DEFAULT_PRESIGNED_EXPIRES_IN = 86_400

  def self.upload_file(path, dest = nil)
    instance.upload_file(path.is_a?(File) ? path.path : path, dest)
  end

  def self.upload_file_and_notify(path, dest = nil, message = 'output file:')
    path = path.is_a?(File) ? path.path : path
    dest = upload_file(path, dest)
    LOG.notify("#{message} #{dest}", true)
    dest
  end

  def self.download_file(path, dest = nil)
    instance.download_file(path, dest)
    LOG.info("downloaded #{path} from S3 to #{dest || path}")
    dest || path
  end

  def self.upload_input_file(path)
    s3_url = instance.upload_input_file(path)
    LOG.info("uploaded #{path} to #{s3_url}")
    s3_url
  end

  def self.download_input_file(s3_url)
    dest = instance.download_input_file(s3_url)
    LOG.info("downloaded #{s3_url} to #{dest}")
    dest
  end

  def self.is_input_file?(s3_url)
    instance.is_input_file?(s3_url)
  end

  def s3_client
    @s3_client ||= Aws::S3::Client.new(
      region: 'us-east-1',
      access_key_id: ENV['REPAIRSHOP_AWS_ACCESS_KEY'],
      secret_access_key: ENV['REPAIRSHOP_AWS_SECRET_KEY']
    )
  end

  def s3_presigner
    @s3_presigner || Aws::S3::Presigner.new(client: s3_client)
  end

  def upload_file(path, dest = nil)
    unless dest
      dest = path.sub(TMP_DIR.to_s + '/', '')
      dest = dest.sub(BASE_DIR.to_s + '/', '')
    end

    File.open(path, 'rb') do |file|
      s3_client.put_object(
        bucket: s3_bucket_name,
        key: dest,
        body: file
      )
    end

    LOG.info("uploaded file #{path} to s3://#{s3_bucket_name}/#{dest}")

    dest
  end

  def download_file(path, dest = nil)
    dest ||= File.basename(path)

    s3_client.get_object(
      bucket: s3_bucket_name,
      key: path,
      response_target: dest
    )
  end

  def s3_bucket_name
    case ENV['RAILS_ENV']
    when 'production'
      'et-repair-shop-prod'
    else
      'et-repair-shop-stage'
    end
  end

  def presigned_url(path, expires_in = DEFAULT_PRESIGNED_EXPIRES_IN)
    s3_presigner.presigned_url(
      :get_object,
      bucket: s3_bucket_name,
      key: path,
      expires_in: expires_in
    )
  end

  def download_input_file(s3_url)
    path = s3_url.sub("s3://#{s3_bucket_name}/", '')
    dest = (TMP_DIR + path).to_s
    FileUtils.mkdir_p(File.dirname(dest))
    download_file(path, dest)
    dest
  end

  def upload_input_file(path)
    now = Time.now
    dest = "inputs/#{now.strftime('%Y-%m')}/#{(now.to_f * 1000).to_i}-#{File.basename(path)}"
    upload_file(path, dest)
    "s3://#{s3_bucket_name}/#{dest}"
  end

  def is_input_file?(s3_url)
    s3_url.include?("s3://#{s3_bucket_name}/")
  end
end
