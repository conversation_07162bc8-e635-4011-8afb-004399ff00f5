# frozen_string_literal: true

class AuthClient < BaseApiClient
  def get_me()
    request(:get, '/auth/users/me')
  end

  def get_user_by_remote_id(oid, remote_user_id)
    res = request(:get, "/auth/users/remote_user_id/#{remote_user_id}?oid=#{oid}")
    JSON.parse(res.body)
  end

  def create_affiliation(oid, payload)
    res = request(:post, "/auth/affiliations?oid=#{oid}", payload)
    JSON.parse(res.body)
  end

  def update_affiliation(oid, affiliation_id, payload)
    res = request(:put, "/auth/affiliations/#{affiliation_id}?oid=#{oid}", payload)
    JSON.parse(res.body)
  end

  def delete_affiliation(oid, affiliation_id)
    request(:delete, "/auth/affiliations/#{affiliation_id}?oid=#{oid}")
  end

  def get_integration(oid, integration_id)
    res = request(:get, "/auth/integrations/#{integration_id}?oid=#{oid}")
    JSON.parse(res.body)
  end

  def create_integration(oid, payload)
    request(:post, "/auth/integrations?oid=#{oid}", payload)
  end

  def get_integration_api_credentials(oid, platform, integration_id)
    res = request(:get, "/auth/integrations/#{integration_id}/#{platform}/credentials?oid=#{oid}")
    JSON.parse(res.body)
  end

  def get_blackbaud_integration(oid)
    res = request(:get, "/auth/integrations/blackbaud/renxt?oid=#{oid}")
    JSON.parse(res.body)
  end

  def get_blackbaud_redirect(oid)
    res = request(:get, "/auth/integrations/blackbaud/renxt/redirect?oid=#{oid}")
    JSON.parse(res.body)
  end

  def authorize_blackbaud(oid, authorization_code)
    res = request(:post, "/auth/integrations/blackbaud/renxt/authenticate?oid=#{oid}", {authorization_code: authorization_code})
    JSON.parse(res.body)
  end

  def get_blackbaud_access_token(oid)
    res = request(:post, "/auth/integrations/blackbaud/renxt/access_token?oid=#{oid}")
    JSON.parse(res.body)['access_token']
  end

  def create_user(oid, payload)
    request(:post, "/auth/users?oid=#{oid}", payload)
  end

  def update_user(oid, user_id, payload)
    request(:put, "/auth/users/#{user_id}?oid=#{oid}", payload)
  end

  def restore_user(user_id)
    request(:post, "/auth/users/#{user_id}/restore")
  end

  def delete_user(user_id)
    request(:post, "/auth/users/#{user_id}/soft_delete")
  end

  def hard_delete_user(user_id)
    request(:delete, "/auth/users/#{user_id}")
  end

  def disable_mfa(user_id)
    request(:post, "/auth/users/#{user_id}/mfa/remove")
  end

  def update_mfa(user_id, otp_mobile)
    request(:post, "/auth/users/#{user_id}/mfa/update", {otp_mobile: otp_mobile})
  end

  def lookup_by_email(email)
    request(:get, "/auth/users/email/#{email}")
  end

  def get_org(oid)
    res = request(:get, "/auth/organizations/#{oid}.json")
    JSON.parse(res.body)
  end

  def get_org_default_roles(oid)
    get_org(oid).dig("roles").select{ |role| role["default"] }.map{ |role| role["id"]}
  end

  def get_user_tableua_groups(oid, user_id)
    request(:get, "/auth/admin/tableau/users/#{user_id}/groups?oid=#{oid}")
  end

  def manually_add_user_to_tableua_groups(oid, user_id, group)
    request(:post, "/auth/admin/tableau/users/#{user_id}/groups?oid=#{oid}", {group: group})
  end

  def manually_remove_user_from_tableua_groups(oid, user_id, group)
    request(:delete, "/auth/admin/tableau/users/#{user_id}/groups/#{group}?oid=#{oid}")
  end

  def get_org_tableua_groups(oid)
    request(:get, "/auth/admin/tableau/groups?oid=#{oid}")
  end

  def add_org_to_tableua_groups(oid, roles, group, expires_on = nil)
    request(:post, "/auth/admin/tableau/groups?oid=#{oid}", {group: group, roles: roles, expires_on: expires_on})
  end

  def remove_org_from_tableua_groups(oid, group)
    request(:delete, "/auth/admin/tableau/groups/#{group}?oid=#{oid}")
  end

  def get_tableau_dashboards
    request(:get, "/auth/admin/tableau/dashboards")
  end

  def add_tableau_dashboard(group, name, path, image_url = nil, active = true)
    payload = {
      group: group,
      name: name,
      path: path,
      image_url: image_url,
      active: active
    }
    request(:post, "/auth/admin/tableau/dashboards", payload)
  end

  def update_tableau_dashboard(tableau_dashboard_id, name, path, image_url = nil, active = true)
    payload = {
      group: group,
      name: name,
      path: path,
      image_url: image_url,
      active: active
    }
    request(:put, "/auth/admin/tableau/dashboards/#{tableau_dashboard_id}", payload)
  end

  def delete_tableau_dashboard(tableau_dashboard_id)
    request(:delete, "/auth/admin/tableau/dashboards/#{tableau_dashboard_id}")
  end

  def get_tableau_dashboard_exclusions(oid)
    request(:get, "/auth/admin/tableau/dashboard/exclusions?oid=#{oid}")
  end

  def add_tableau_dashboard_exclusion(oid, path)
    request(:post, "/auth/admin/tableau/dashboards/exclusions?oid=#{oid}", {path: path})
  end

  def delete_tableau_dashboard_exclusion(oid, path)
    request(:delete, "/auth/admin/tableau/dashboards/exclusions?oid=#{oid}", {path: path})
  end

  def get_organizations
    JSON.parse(request(:get, '/auth/organizations').body)
  end

  def get_user_by_id(user_id, oid)
    request(:get, "/auth/users/#{user_id}?oid=#{oid}")
  end

  def create_identity_provider(oid, payload)
    request(:post, "/auth/identity_providers?oid=#{oid}", payload)
  end

  def update_identity_provider(oid, identity_provider_id, payload)
    request(:put, "/auth/identity_providers/#{identity_provider_id}?oid=#{oid}", payload)
  end

  def get_identity_providers(oid)
    request(:get, "/auth/identity_providers?oid=#{oid}")
  end

  def delete_identity_provider(oid, identity_provider_id)
    request(:delete, "/auth/identity_providers/#{identity_provider_id}?oid=#{oid}")
  end

  def create_restriction(oid, payload)
    request(:post, "/auth/restrictions?oid=#{oid}", payload)
  end

  def update_organization(oid, payload)
    request(:put, "/auth/organizations/#{oid}", payload)
  end

  def new_purge(oid)
    request(:get, "/auth/organizations/#{oid}/purge")
  end

  def delete_org(oid, purge_token, captcha_secret)
    request(:delete, "/auth/organizations/#{oid}", {purge_token: purge_token, captcha_secret: captcha_secret})
  end

  def get_users_by_role(oid, roles = nil, super_user='false')
    roles_param = roles && roles.map { |n| 'roles[]=' + CGI.escape(n) }.join('&')
    request(:get, "/auth/affiliations/users?oid=#{oid}&super_user=#{super_user}&#{roles_param}")
  end

  def create_forumbee_sso_login(oid)
    request(:post, "/auth/forumbee?oid=#{oid}")
  end

  def match_remote_ids(oid, remote_user_ids)
    request(:post, "/auth/users/bulk_fetch/ids?oid=#{oid}", {remote_user_ids: remote_user_ids})
  end

  def match_emails(oid, emails)
    request(:post, "/auth/users/bulk_fetch/ids?oid=#{oid}", {emails: emails})
  end
end
