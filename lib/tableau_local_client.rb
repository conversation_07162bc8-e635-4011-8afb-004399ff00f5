require 'openssl'

class TableauLocalClient
  include ClientApiBuilder::Router

  attr_reader :cookies,
              :xsrf_token

  base_url 'https://tableau.evertrue.com'

  header 'Accept', 'application/json, text/plain, */*'
  header 'Content-Type', 'application/json;charset=UTF-8'
  header 'Cookie', :cookies
  header 'X-Xsrf-Token', :xsrf_token
        
  route :generate_public_key, '/vizportal/api/web/v1/generatePublicKey', method: :post, body: {
          method: 'generatePublicKey',
          params: {}
        }
  
  route :login, '/vizportal/api/web/v1/login', method: :post, body: {
          method: 'login',
          params: {
            username: :username,
            encryptedPassword: :encrypted_password,
            keyId: :key_id
          }
        }

  route :session_info, '/vizportal/api/web/v1/getSessionInfo', method: :post, body: {
          method: 'getSessionInfo',
          params: {}
        }

  # response: {"result"=>{"users"=>[{"username"=>"testuser", "displayName"=>"Test User", "id"=>"19"}], "results"=>[{"id"=>"19", "codes"=>[10]}]}}
  route :create_local_server_user, '/vizportal/api/web/v1/createLocalServerUser', method: :post, body: {
          method: 'createLocalServerUser',
          params: {
                      username: :username,
                      displayName: :display_name,
                      email: :email, # optional but set to empty string if not using
                      encryptedPassword: :encrypted_password,
                      keyId: :key_id
                    }
        }

  route :update_users_site_memberships, '/vizportal/api/web/v1/updateServerUsersSiteMembership', method: :post, body: {
          method: 'updateServerUsersSiteMembership',
          params: {
            userIds: :user_ids, # ["14"]
            addToSiteRoles: :add_site_roles, # [{"siteId":"2","siteRole":"Viewer"}]
            removeFromSiteIds: :remove_site_roles # []
          }
        }

  route :get_sites, '/vizportal/api/web/v1/getSites', method: :post, body: {
          method: 'getSites',
          params: {
            order: [
              {
                field: 'name',
                ascending: true
              }
            ],
            page: {
              startIndex: 0,
              maxItems: 100
            }
          }
        }

  route :get_site_names, '/vizportal/api/web/v1/getSiteNames', method: :post, body: {
          method: 'getSiteNames',
          params: {
            page:
              {
                startIndex: 0,
                maxItems: 1000
              }
          }
        }

  route :get_users, '/vizportal/api/web/v1/getServerUsers', method: :post, body: {
          method: 'getServerUsers',
          params: {
            order: [
              {
                field: 'displayName',
                ascending: true
              }
            ],
            page:
              {
                startIndex: :start_index, # default 0
                maxItems: :max_items # default 100
              }
          }
        }

  def login_and_save_cookies(username, password)
    public_key = generate_public_key

    encrypted_password = encrypt(
      public_key['result']['key']['n'],
      public_key['result']['key']['e'],
      password
    )

    login_payload = login(
      username: username,
      key_id: public_key['result']['keyId'],
      encrypted_password: encrypted_password
    )

    @cookies = response.header['set-cookie'].split(', ').map { |cookie| cookie.sub(/; .*/, '') }.join('; ')
    @xsrf_token = response.header['set-cookie'].split(', ').map { |cookie| cookie.sub(/; .*/, '') }.detect { |cookie| cookie =~ /^XSRF-TOKEN=/ }.sub('XSRF-TOKEN=', '')
  end

  def create_user(username, password, display_name, email = '')
    public_key = generate_public_key

    encrypted_password = encrypt(
      public_key['result']['key']['n'],
      public_key['result']['key']['e'],
      password
    )

    create_local_server_user(
      username: username,
      display_name: display_name,
      email: email,
      encrypted_password: encrypted_password,
      key_id: public_key['result']['keyId']
    )
  end

  def encrypt(modulus, exponent, str)
    rsa = OpenSSL::PKey::RSA.new
    rsa.set_key(modulus.to_i(16), exponent.to_i(16), nil)
    rsa.public_encrypt(str).unpack('H*').first
  end
end
