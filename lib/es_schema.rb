class ESSchema
  attr_reader :base_url

  def initialize(base_url)
    @base_url = base_url
  end

  def mappings
    @mappings ||= ESHelpers.get_mappings
  end

  def docs
    @docs ||=  ESHelpers.get_docs(mappings)
  end

  def small_index_name
    mappings.keys.detect { |index_name| index_name =~ /small/ }
  end

  def big_index_name
    mappings.keys.detect { |index_name| index_name =~ /big/ }
  end

  def org_index_name(oid)
    mappings.keys.detect { |index_name| index_name =~ /-contacts-oid-#{oid}-/}
  end

  def get_doc_count(doc, query)
    path = '/' + doc[:index_name] + '/' + doc[:mapping_name] + '/_count?q=' + query
    ESHelpers.get_mapping_count(path)['count']
  end

  def get_search_url(doc)
    base_url + '/' + doc[:index_name] + '/' + doc[:mapping_name] + '/_search'
  end

  def get_max_timestamp(doc, oid)
    get_max_value_for(doc, oid, '_timestamp')
  end

  def get_max_value_for(doc, oid, field)
    payload = {
      "size" => 0,
      "_source" => false,
      "aggs" => {
        "max_#{field}" => {
          "max" => {
            "field" => field
          }
        }
      },
      "query" => {
        "term" => {
          doc[:oid_property] => oid
        }
      }
    }

    uri = URI(get_search_url(doc))
    headers = {'Content-Type' => 'application/json'}
    res = HTTPClient.post(uri, payload.to_json, headers)
    JSON.parse(res.body)['aggregations']["max_#{field}"]['value']
  end

  def get_index_for_alias(alias_name)
      uri = URI(base_url + "/_alias/#{alias_name}")
      http = Net::HTTP.new(uri.host, uri.port)
      req = Net::HTTP::Get.new(uri)
      res = http.request(req)

      if res.code.to_i == 200
        index = JSON.parse(res.body).keys
        return index
      else
        raise "Error fetching alias information: #{res.body}"
      end
  end
end
