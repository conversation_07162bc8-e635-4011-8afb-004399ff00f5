require 'resolv'
require 'uri'

module DBUtils
  module_function

  def translate_database_url(database_url)
    uri = URI(database_url)
    translate_uri(uri).to_s
  end

  def translate_uri(uri)
    uri.host = cname_lookup(uri.host)
    uri
  end

  def cname_lookup(url)
    result = Resolv::DNS.open do |dns|
      dns.getresource(url, Resolv::DNS::Resource::IN::CNAME)
    end
    result.name.to_s
  rescue Resolv::ResolvException => e
    LOG.error("cname look failed for #{url} with #{e.inspect}")
    url
  end
end
