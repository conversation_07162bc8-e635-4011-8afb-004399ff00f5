# frozen_string_literal: true

class AssignmentsClient < BaseApiClient

  def update_assignment_last_dates(oid, assignment)
    path = "/assignments-java/v2/assignments/#{assignment.id}/last_dates?oid=#{oid}"
    path += "&last_visit_date=#{assignment.last_visit_date}&last_contact_date=#{assignment.last_contact_date}"
    request(:put, path)
  end

  def write_records_to_delete(oid, job_id, pool_type) # copied name from AssignmentsClient
    request(:post, "/assignments-java/v2/import/diff/#{job_id}/records?oid=#{oid}&pool_type=#{pool_type}", nil, {}, read_timeout: 600)
  end

  def get_pools(oid, offset=0, limit=20, pool_types=[])
    pool_types_str = pool_types.join("&pool_type[]=") || ""
    pool_types_str = "&pool_type[]=#{pool_types_str}" if pool_types_str != ""
    request(:get, "/assignments-java/v2/pools?oid=#{oid}&offset=#{offset}&limit=#{limit}#{pool_types_str}")
  end

  def get_all_pools(oid, limit=20, pool_types=[])
    pools = []

    resp = get_pools(oid, 0, limit, pool_types)
    raise("failed to fetch pools for oid #{oid}") unless resp.kind_of?(Net::HTTPSuccess)

    data = JSON.parse(resp.body)
    pools += data['items']
    total = data['total']

    while pools.size < total
      resp = get_pools(oid, pools.size, limit, pool_types)
      raise("failed to fetch pools for oid #{oid}") unless resp.kind_of?(Net::HTTPSuccess)

      pools += JSON.parse(resp.body)['items']
    end

    pools
  end

  def post_pool(oid, pool_type, name, stage_group_id)
    request(:post, "/assignments-java/v2/pools?oid=#{oid}", {
      name: name,
      pool_type: pool_type,
      stage_group_id: stage_group_id
    })
  end

  def delete_pool(oid, pool_id)
    request(:delete, "/assignments-java/v2/pools/#{pool_id}?oid=#{oid}")
  end

  def delete_assignment(oid, assignment_id)
    request(:delete, "/assignments-java/v2/assignments/#{assignment_id}?oid=#{oid}")
  end

  def sync_assignment_to_es(oid, assignment_id)
    request(:post, "/assignments-java/v2/assignments/#{assignment_id}/refresh?oid=#{oid}")
  end

  def bulk_update_assignments(oid, assignments)
    request(:post, "/assignments-java/v3/assignments/bulk?oid=#{oid}", assignments)
  end

  def delete_solicitor(oid, solicitor_id, pool_type)
    request(:delete, "/assignments-java/v2/solicitors/#{solicitor_id}?oid=#{oid}&pool_type=#{pool_type}")
  end

  def update_assignment_stage(oid, assignment_id, stage, stage_start_date)
    request(:put, "/assignments-java/v2/assignments/#{assignment_id}/stage?oid=#{oid}", {stage: stage, stage_start_date: stage_start_date})
  end
end
