
class ContactsKS
  KEYSPACE_NAME = 'contacts'

  def self.session
    @session ||= CassandraConnection.instance.get_session(KEYSPACE_NAME)
  end

  def self.keyspace
    @keyspace ||= CassandraHelpers.get_keyspace(CassandraConnection.instance.cluster, KEYSPACE_NAME)
  end

  def self.contact_table
    @contact_table ||= CassandraHelpers.get_table(keyspace, 'contact')
  end

  def self.find_contact_by_id(contact_id)
    CassandraHelpers.find_record(
      self.class.session,
      self.class.contact_table,
      'id' => contact_id
    )
  end
end
