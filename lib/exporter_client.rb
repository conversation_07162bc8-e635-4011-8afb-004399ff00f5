# frozen_string_literal: true

class ExporterClient < BaseApiClient
  def export_interactions(oid, search)
    request(:post, "/exporter/v1/submit/interaction?oid=#{oid}", {name: 'interactions', search: search})
  end

  def export_all_interactions(oid)
    search = {
      must: [
        {
          updated_at: {
            gt: 0
          }
        }
      ]
    }

    export_interactions(oid, search)
  end

  def restart_export(oid, export_id)
    request(:post, "/exporter/v1/exports/#{export_id}/restart?oid=#{oid}")
  end

  def spark_status
    request(:get, "/exporter/v1/spark/status?oid=1")
  end
end
