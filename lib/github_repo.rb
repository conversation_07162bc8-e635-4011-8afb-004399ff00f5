require 'active_support/core_ext/hash'

class GithubRepo
  ORG_DIR = "#{ENV['HOME']}/github/evertrue"

  attr_reader :repo_name

  def self.all_repos
    Dir.glob(ORG_DIR + '/*').map do |file|
      next unless File.directory?(file)
      next unless File.directory?(file + '/.git')

      new File.basename(file)
    end
  end

  def initialize(repo_name)
    @repo_name = repo_name
  end

  def pom?
    !root_pom_file.nil?
  end

  def root_pom
    @root_pom ||= Hash.from_xml(File.read(root_pom_file))
  end

  def base_pom_version
    if root_pom.dig('project', 'parent', 'groupId') == 'com.evertrue' &&
       root_pom.dig('project', 'parent', 'artifactId') == 'basepom'
      root_pom.dig('project', 'parent', 'version')
    else
      nil
    end
  end

  def dir
    ORG_DIR + '/' + repo_name
  end

  def chdir
    Dir.chdir(dir) do
      yield
    end
  end

  def root_pom_file
    pom_files.sort_by(&:size).first
  end

  def root_pom_dir
    File.dirname(root_pom_file)
  end

  def pom_files
    @pom_files ||= Dir.glob(dir + '/**/**/pom.xml')
  end

  def poms
    @poms ||= pom_files.each_with_object({}) do |file, hsh|
      hsh[file.sub(dir + '/', '')] = Hash.from_xml(File.read(file))
    end
  end

  def pom_dependency?(group_id, artifact_id)
    poms.any? do |_, pom|
      dependencies = pom.dig('project', 'dependencies', 'dependency')
      next unless dependencies

      dependencies = [dependencies] unless dependencies.is_a?(Array)
      dependencies.any? { |dependency| dependency['groupId'] == group_id && dependency['artifactId'] == artifact_id }
    end
  end

  def git_commit
    chdir { `git log -1 --pretty=%H` }
  end

  def git_date
    chdir { `git log -1 --pretty=%ai`.split(' ', 2).first }
  end

  def dir
    ORG_DIR + '/' + repo_name
  end

  def exists?
    File.exist?(dir)
  end

  def last_update_commit
    File.exist?(last_update_commit_file) &&
      File.read(last_update_commit_file).strip
  end

  def update_last_update_commit!
    File.open(last_update_commit_file, 'wb') { |f| f.write git_commit }
  end

  def last_update_commit_file
    dir + '/last-update-commit.txt'
  end

  def has_new_updates?
    git_commit != last_update_commit
  end

  def mvn_dependency_tree_file
    ORG_DIR + '/' + repo_name + '/mvn-dependencies.txt'
  end

  def run_mvn_dependency_tree
    Dir.chdir(root_pom_dir) do
      `mvn dependency:tree > #{mvn_dependency_tree_file}`
    end
  end

  def git_update!
    chdir do
      `git pull origin #{head_branch}`
    end
  end

  def update!
    LOG.info "git updating #{repo_name}"

    git_update!

    return unless has_new_updates?
    return unless pom?

    LOG.info "creating dependencies.txt for #{repo_name}"
    if run_mvn_dependency_tree
      update_last_update_commit!
    end
  end

  def dependency_versions(group_id, artifact_id)
    regex = Regexp.new(Regexp.escape(group_id + ':' + artifact_id + ':') + '.+?:(.+?):')
    File.read(mvn_dependency_tree_file).scan(regex).flatten.uniq
  end

  def artifact_ids(group_id)
    regex = Regexp.new(Regexp.escape(group_id) + ':(.*?):')
    File.read(mvn_dependency_tree_file).scan(regex).flatten.uniq
  end

  def head_branch
    chdir do
      `git remote show origin | grep "HEAD branch" | cut -d ":" -f 2`
    end
  end
end
