
class ContactsIndexKS
  KEYSPACE_NAME = 'contacts_index'
  OID_INDEX_NUM_SHARDS = 20

  def self.session
    @session ||= CassandraConnection.instance.get_session(KEYSPACE_NAME)
  end

  def self.keyspace
    @keyspace ||= CassandraHelpers.get_keyspace(CassandraConnection.instance.cluster, KEYSPACE_NAME)
  end

  def self.oid_index_table
    @contact_table ||= CassandraHelpers.get_table(keyspace, 'oid_index')
  end

  attr_reader :oid

  def initialize(oid)
    @oid = oid
  end

  def find_contact_by_id(contact_id)
    cql = "SELECT * FROM #{Cassandra::Util.escape_name(self.class.oid_index_table.name)} WHERE id IN(#{shards.join(',')}) AND oid = #{Cassandra::Util.encode_object(oid)} AND contact_id = #{Cassandra::Util.encode_object(contact_id)}"
    result = CassandraHelpers.execute_query(self.class.session, cql)
    result.first ? ::Contacts::Contact.decode(result.first['contact']) : nil
  end

  private

  def shards
    @shards ||= OID_INDEX_NUM_SHARDS.times.map { |i| Cassandra::Util.encode_string("#{oid}_shard_#{i}") }
  end
end
