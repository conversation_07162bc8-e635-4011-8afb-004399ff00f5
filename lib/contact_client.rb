# frozen_string_literal: true

class ContactClient < BaseApiClient
  def get_contact(oid, contact_id)
    res = fetch_contact_by_id(oid, contact_id)
    JSON.parse(res.body)
  end

  def fetch_contact_by_id(oid, contact_id)
    request(:get, "/contacts/v1/contacts/#{contact_id}?oid=#{oid}")
  end

  def create_contact(oid, payload)
    request(:post, "/contacts/v1/contacts?oid=#{oid}", payload)
  end

  def update_contact(oid, contact_id, payload)
    request(:put, "/contacts/v1/contacts/#{contact_id}?oid=#{oid}", payload)
  end

  def delete_contact(oid, contact_id)
    res = request(:delete, "/contacts/v1/contacts/#{contact_id}?oid=#{oid}")

    case res
    when Net::HTTPOK
      LOG.info("deleted #{contact_id} from org #{oid}")
      true
    else
      LOG.error("failed to delete #{contact_id} from org #{oid}, response #{res.inspect}")
      false
    end
  end

  def refresh_contact(oid, contact_id)
    request(:post, "/contacts/v1/contacts/#{contact_id}/refresh?oid=#{oid}")
  end

  def refresh_contacts(oid, contact_ids)
    contact_ids.each do |contact_id|
      refresh_contact(oid, contact_id)
    end
  end

  # response is Net::HTTPNoContent on success
  # response is Net::HTTPNotFound if identity does not exists
  def delete_identity(oid, contact_id, type, value)
    request(:delete, "/contacts/v1/identities/contact/#{contact_id}?oid=#{oid}", value: value, type: type)
  end

  def delete_email_identity(oid, contact_id, email)
    delete_identity(oid, contact_id, 'EMAIL', email)
  end

  def delete_remote_id_identity(oid, contact_id, remote_id)
    delete_identity(oid, contact_id, 'REMOTE_ID', remote_id)
  end

  # response is Net::HTTPNoContent on success
  # response is Net::HTTPNotFound if identity already exists
  def add_identity(oid, contact_id, type, value)
    request(:post, "/contacts/v1/identities/contact/#{contact_id}?oid=#{oid}", value: value, type: type)
  end

  def add_email_identity(oid, contact_id, email)
    add_identity(oid, contact_id, 'EMAIL', email)
  end

  def add_remote_id_identity(oid, contact_id, remote_id)
    add_identity(oid, contact_id, 'REMOTE_ID', remote_id)
  end

  def change_remote_id(oid, contact_id, old_value, new_value)
    delete_identity(oid, contact_id, 'REMOTE_ID', old_value) &&
      add_identity(oid, contact_id, 'REMOTE_ID', new_value)
  end

  def get_contact_ids_for_identities(oid, identities, type = 'REMOTE_ID')
    identity_params = identities.map { |identity| "identity[]=#{Addressable::URI.escape(identity)}" }.join('&')
    request(:get, "/contacts/v1/contacts/identities/bulk/id?oid=#{oid}&provider=#{type}&#{identity_params}")
  end

  def fetch_status
    request(:get, '/contacts/v1/status?oid=1')
  end

  def get_bulk_contacts_by_ids(oid, contact_ids, load_identities=false)
    id_params = contact_ids.map { |id| "id[]=#{id}" }.join('&')
    request(:get, "/contacts/v1/contacts/bulk?oid=#{oid}&#{id_params}&loadIdentities=#{load_identities}")
  end

  def get_bulk_contacts_by_ids_using_post(oid, contact_ids, load_identities=false)
    request(:post, "/contacts/v1/contacts/bulk?oid=#{oid}&loadIdentities=#{load_identities}", contact_ids)
  end

  def get_bulk_contacts_by_identities(oid, identities, provider='REMOTE_ID')
    identity_params = identities.map { |id| "identity[]=#{Addressable::URI.escape(id)}" }.join('&')
    request(:get, "/contacts/v1/contacts/identities/bulk?oid=#{oid}&#{identity_params}&provider=#{provider}")
  end

  def get_bulk_contacts_by_identities_using_post(oid, identities, provider='REMOTE_ID')
    request(:post, "/contacts/v1/contacts/identities/bulk?oid=#{oid}&provider=#{provider}", identities)
  end

  def get_contact_identities_match(oid, identities, provider='REMOTE_ID')
    request(:post, "/contacts/v1/contacts/identities/match?oid=#{oid}&provider=#{provider}", identities)
  end

  def stream_contact_remote_ids(oid, cursor=0, limit=10_000)
    request(:get, "/contacts/v1/contacts/remote_ids?oid=#{oid}&cursor=#{cursor}&limit=#{limit}")
  end

  def fetch_contact_by_id_out_of_oid_index(oid, contact_id)
    request(:get, "/contacts/v1/contacts/#{contact_id}/oid_index?oid=#{oid}")
  end

  def fetch_contact_remtoe_id(oid, contact_id)
    request(:get, "/contacts/v1/contacts/#{contact_id}/remote_id?oid=#{oid}")
  end

  def contact_exists(oid, contact_id)
    request(:get, "/contacts/v1/contacts/exists/#{contact_id}?oid=#{oid}")
  end

  def get_bulk_contact_ids_by_identities(oid, identities, provider='REMOTE_ID')
    identity_params = identities.map { |id| "identity[]=#{Addressable::URI.escape(id)}" }.join('&')
    request(:get, "/contacts/v1/contacts/identities/bulk/id?oid=#{oid}&#{identity_params}&provider=#{provider}")
  end

  def get_all_properties(oid)
    request(:get, "/contacts/v1/properties?oid=#{oid}")
  end

  def get_all_gift_properties(oid)
    request(:get, "/contacts/v1/gift/properties?oid=#{oid}")
  end

  def get_gift_by_id(oid, gift_id, unused_contact_id = 1)
    request(:get, "/contacts/v1/gifts/#{gift_id}/contact/#{unused_contact_id}?oid=#{oid}")
  end

  def get_gifts_by_contact_id(oid, contact_id)
    request(:get, "/contacts/v1/gifts/contact/#{contact_id}?oid=#{oid}")
  end

  def get_gift_by_remote_id(oid, gift_remote_id, gift_type)
    request(:get, "/contacts/v1/gifts/remote_id/#{gift_remote_id}?oid=#{oid}&type=#{gift_type}")
  end

  def sync_gift_to_es(oid, gift_id)
    request(:post, "/contacts/v1/gifts/#{gift_id}/refresh?oid=#{oid}")
  end

  def get_recent_profile_views(oid)
    request(:get, "/contacts/v1/profile_views/recent?oid=#{oid}")
  end

  def get_lists(oid)
    request(:get, "/contacts/v1/lists?oid=#{oid}")
  end

  def get_list(oid, list_id)
    request(:get, "/contacts/v1/lists/#{list_id}?oid=#{oid}")
  end

  def update_list(oid, list_id, payload)
    request(:put, "/contacts/v1/lists/#{list_id}?oid=#{oid}", payload)
  end

  def delete_list(oid, list_id)
    request(:delete, "/contacts/v1/lists/#{list_id}?oid=#{oid}")
  end

  def update_roles_override(oid, contact_id, role_ids, updated_at)
    request(:put, "/contacts/v1/roles/override/contact/#{contact_id}?oid=#{oid}", {roles_override: role_ids, updated_at: updated_at}, {'ET-Update-Source' => 'console'})
  end

  def get_latest_scheduled_exports(oid)
    request(:get, "/contacts/v2/exports/latest-scheduled?oid=#{oid}")
  end

  def percolate(oid, contact_ids)
    request(:post, "/contacts/v1/contacts/percolate?oid=#{oid}", {contact_ids: contact_ids})
  end

  def update_properties_schema(oid)
    request(:post, "/contacts/v1/properties/schema?oid=#{oid}")
  end
end
