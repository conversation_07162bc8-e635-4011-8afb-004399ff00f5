require 'aws-sdk-ecs'

class RepairShopECSTaskRunner < ECSTaskRunner
  DEFAULT_SLACK_NOTIFICATION_CHANNEL = '#task_notifications'

  def initialize
    super(ECS_CONFIG['ecs']['task_definition']['name'])
  end

  def run(script_options)
    resp = ecs.run_task(
      cluster: cluster,
      task_definition: task_definition_arn,
      overrides: {
        container_overrides: [
          {
            name: container_name,
            command: command(script_options),
            environment: [
              {
                name: 'LOG',
                value: 'stdout',
              }
            ]
          }
        ]
      }
    )

    print JSON.pretty_generate(resp.to_h) + "\n"
  end

  private

  def container_name
    ECS_CONFIG['ecs']['task_definition']['container_name']
  end

  def command(script_options)
    [get_program_name(script_options)] + get_program_arguments(script_options)
  end

  def get_program_arguments(script_options)
    arguments = script_options.program_arguments

    arguments.reject! { |arg| arg == '--run-remote' }

    environment_idx = arguments.index('-e') || arguments.index('--environment')
    if environment_idx
      arguments.delete_at(environment_idx) # delete the flag
      arguments.delete_at(environment_idx) # delete the value
    end

    slack_notify_idx = arguments.index('--slack-notify')
    if slack_notify_idx
      slack_notify_value_idx = slack_notify_idx + 1

      # if no value was specified, set it to notify the user
      slack_notify_value = arguments[slack_notify_value_idx]
      arguments.insert(slack_notify_value_idx, ENV['USER']) if slack_notify_value.nil? || slack_notify_value =~ /^-/

      arguments[slack_notify_value_idx] += ',' + DEFAULT_SLACK_NOTIFICATION_CHANNEL
    else
      arguments << '--slack-notify'
      arguments << DEFAULT_SLACK_NOTIFICATION_CHANNEL
      arguments << '--slack-notify-on-startup'
    end

    arguments
  end

  def get_program_name(script_options)
    script_options.program_name
  end
end
