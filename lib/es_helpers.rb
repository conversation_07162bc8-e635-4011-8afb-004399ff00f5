require 'json'
require 'net/http'

module ESHelpers
  module_function

  OID_PROPERTY_NAMES = ['oid', 'contact_oid', 'organization_id', 'content_oid']

  def get_mappings
    res = HTTPClient.get(uri_for_path('/_mappings'))
    JSON.parse(res.body)
  end

  def get_aliases
    res = HTTPClient.get(uri_for_path('/_aliases'))
    JSON.parse(res.body)
  end

  def get_docs(mappings)
    docs = []
    mappings.each do |index_name, info|
      next if index_name =~ /^\./

      info['mappings'].each do |mapping_name, mapping_info|
        next if mapping_name =~ /^\./ && mapping_name != '.percolator'
        next unless mapping_info['properties']
        next unless (oid_property = mapping_info['properties'].keys.detect { |name| OID_PROPERTY_NAMES.include?(name) })

        docs << {
          index_name: index_name,
          mapping_name: mapping_name,
          oid_property: oid_property,
          parent_type: mapping_info.dig('_parent', 'type')
        }
      end
    end
    docs
  end

  def get_mapping_count(path)
    res = HTTPClient.get(uri_for_path(path))
    JSON.parse(res.body)
  end

  def get_search_shards(index_name, routing)
    uri = uri_for_path('/' + index_name + '/_search_shards?routing=' + routing.to_s)
    res = HTTPClient.get(uri)
    JSON.parse(res.body)
  end

  def get_aliases
    res = HTTPClient.get(uri_for_path('/_aliases'))
    JSON.parse(res.body)
  end

  def get_settings(index_name)
    res = HTTPClient.get(uri_for_path("/#{index_name}/_settings"))
    JSON.parse(res.body)
  end

  def get_indexes
    res = HTTPClient.get(uri_for_path('/_cat/indices?format=JSON'))
    JSON.parse(res.body)
  end

  def get_index_stats(index_name)
    res = HTTPClient.get(uri_for_path("/#{index_name}/_stats"))
    JSON.parse(res.body)
  end

  def bulk_request(actions)
    body = actions.map(&:to_json).join("\n") + "\n"
    HTTPClient.post(uri_for_path('/_bulk'), body)
  end

  def block_writes(index_name)
    body = {
      index: {
        'blocks.write' => true
      }
    }.to_json

    res = HTTPClient.put(uri_for_path("/#{index_name}/_settings"), body)

    JSON.parse(res.body)
  end

  def resume_writes(index_name)
    body = {
      index: {
        'blocks.write' => false
      }
    }.to_json

    res = HTTPClient.put(uri_for_path("/#{index_name}/_settings"), body)

    JSON.parse(res.body)
  end

  def url_for_path(path)
    ES_URL + path
  end

  def uri_for_path(path)
    URI(url_for_path(path))
  end

  def url_for_doc(doc)
    path = "/#{doc['_index']}/#{doc['_type']}/#{doc['_id']}"
    path += "?routing=#{doc['_routing']}" if doc['_routing']
    url_for_path(path)
  end
end
