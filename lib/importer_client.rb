# frozen_string_literal: true

class ImporterClient < BaseApiClient
  def get_mapping(oid, type, hash_id)
    path = '/importer/v2/mapping/'
    path += "#{type}/" if type
    path += "#{hash_id}?oid=#{oid}"

    res = request(:get, path)
    JSON.parse(res.body)
  end

  def update_mapping(oid, type, hash_id, payload)
    path = '/importer/v2/mapping/'
    path += "#{type}/" if type
    path += "#{hash_id}?oid=#{oid}"

    res = request(:put, path, payload)

    case res
    when Net::HTTPOK
      true
    else
      false
    end
  end

  def create_job(oid, payload)
    res = request(:post, "/importer/v1/jobs?oid=#{oid}", payload)
    JSON.parse(res.body)
  end

  def get_job(oid, job_id)
    res = request(:get, "/importer/v1/jobs/#{job_id}?oid=#{oid}")
    JSON.parse(res.body)
  end

  def queue_job(oid, job_id)
    res = request(:post, "/importer/v1/jobs/queue/#{job_id}?oid=#{oid}")
    JSON.parse(res.body)
  end

  def queue_import_job_v3(oid, job_id)
    res = request(:post, "/importer/v3/jobs/#{job_id}/import?oid=#{oid}")
    JSON.parse(res.body)
  end

  def cancel_import_job_v3(oid, job_id)
    res = request(:post, "/importer/v3/jobs/#{job_id}/cancel?oid=#{oid}")
    JSON.parse(res.body)
  end

  def get_download_link_v3(oid, job_id, worker_type=nil, file_type=nil)
    res = request(:get, "/importer/v3/jobs/#{job_id}/download?oid=#{oid}&worker_type=#{worker_type}&file_type=#{file_type}")
    JSON.parse(res.body)
  end
end
