# frozen_string_literal: true

# VaultSecretSearch finds secrets based on a search regex
class VaultSecretSearch
  def initialize
    @vault_client = VaultClient.login!
  end

  def search(search_regex, only_keys=false, only_values=false)
    each_secret do |secret_name, data, data_json|
      next if only_keys && !match?(search_regex, data.keys)
      next if only_values && !match?(search_regex, data.values)
      next if !only_keys && !only_values && !search_regex.match(data_json)

      yield secret_name, data, data_json
    end
  end

  def each_secret
    @vault_client.list_secrets.each do |secret_name|
      data = @vault_client.get_secret(secret_name)
      yield secret_name, data, data.to_json
    end
  end

  private

  def match?(search_regex, values)
    values.any? { |value| value.is_a?(String) && search_regex.match(value) }
  end
end
