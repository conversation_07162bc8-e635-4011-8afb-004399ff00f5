# frozen_string_literal: true

require 'yaml'

=begin
Configures the application based on environment and where it is running

Running locally: use the .aws folder to fetch secrets for staging and production
Running locally in container, use the AWS_<key|secret|token> to fetch secret for staging and production
 - checks for a AWS_SESSION_TOKEN
Running in ECS container, can rely on passenger container logic to load secrets into environment variables
 - if the AWS_SESSION_TOKEN is not present and no .aws folder in the home diretory
=end

# EnvironmentLoader loads environment variables for either secrets manager or .env file
class EnvironmentLoader
  include Singleton

  DEFAULT_REGION_NAME = 'us-east-1'
  IMAGE_YAML_FILE = 'config/image.yml'
  AWS_TOOL_PROFILE_NAME = 'evertruetools'

  def self.use_profile_file?
    ENV.has_key?('HOME') && File.exist?("#{ENV['HOME']}/.aws")
  end

  def self.load
    if self.use_profile_file?
      %w[AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_REGION].each do |name|
        ENV.delete(name)
      end
    end
    instance.load
  end

  # the section under aws_secrets_env
  def load
    case ENV['RAILS_ENV']
    when 'production',
         'staging',
         'old_production',
         'old_staging'
      load_aws_secrets
    when 'test'
      load_devenv('.env.test')
    else
      load_devenv
    end
  end

  def aws_profile
    case ENV['RAILS_ENV']
    when 'production',
         'old_production'
      'evertrueprod'
    when 'staging',
         'old_staging'
      'evertruestage'
    when 'thankview'
      'thankview'
    else
      'default'
    end
  end

  def aws_role_arn
    PropertyParser.read("#{ENV['HOME']}/.aws/config")["profile #{aws_profile}"]['role_arn']
  end

  def aws_region_name
    DEFAULT_REGION_NAME
  end

  def create_aws_client
    require 'aws-sdk-secretsmanager'

    Aws::SecretsManager::Client.new(aws_config)
  end

  def aws_config
    if self.class.use_profile_file?
      {
        region: aws_region_name,
        profile: aws_profile
      }
    elsif ENV['AWS_SESSION_TOKEN']
      {
        region: aws_region_name,
        access_key_id: ENV['AWS_ACCESS_KEY_ID'],
        secret_access_key: ENV['AWS_SECRET_ACCESS_KEY'],
        session_token: ENV['AWS_SESSION_TOKEN']
      }
    else
      {
      }
    end
  end

  def get_secret_value(aws_client, section)
    JSON.parse(aws_client.get_secret_value(secret_id: get_secret_name(section)).secret_string)
  end

  def ecs_container_metadata
    @ecs_container_metadata ||=
      if ENV['ECS_CONTAINER_METADATA_URI_V4']
        require 'net/http'
        JSON.parse(Net::HTTP.get_response(URI(ENV['ECS_CONTAINER_METADATA_URI_V4'])).body)
      else
        {}
      end
  end

  def ecs_task_id
    unless defined?(@ecs_task_id)
      task_arn = ecs_container_metadata.dig('Labels', 'com.amazonaws.ecs.task-arn')
      @ecs_task_id = task_arn ? task_arn.split('/').last : nil
    end

    @ecs_task_id
  end

  private

  def load_aws_secrets
    return unless (aws_client = create_aws_client)

    image_config['aws_secrets_env'].keys.each do |section|
      load_config(get_secret_value(aws_client, section))
    end
  end

  def load_devenv(file = '.env')
    require 'dotenv'

    Dotenv.load(file)
  end

  def get_secret_name(section)
    case ENV['RAILS_ENV']
    when 'production',
         'old_production'
      "prod/#{section}"
    when 'staging',
         'old_staging'
      "stage/#{section}"
    else
      raise "unhandled RAILS_ENV #{ENV['RAILS_ENV']}"
    end
  end

  def image_config
    @image_config ||= load_image_yaml
  end

  def load_image_yaml
    YAML.load_file(IMAGE_YAML_FILE)
  end

  def load_config(config)
    config.each do |name, value|
      ENV[name] = value
    end
  end
end
