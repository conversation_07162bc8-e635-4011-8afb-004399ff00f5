class FacebookClient < Struct.new(
        :access_token,
        :version
      )

  include ClientApiBuilder::Router

  base_url 'https://graph.facebook.com'

  query_param(:access_token) { access_token }

  route :get_me, '/me'
  # https://developers.facebook.com/docs/graph-api/reference/user/accounts/
  route :get_accounts, '/:user_id/accounts', query: {fields: 'id, name, access_token'}
  # https://developers.facebook.com/docs/graph-api/reference/page/
  route :get_page, '/:page_id', query: {fields: 'id, name, picture.type(normal), has_transitioned_to_new_page_experience'}
  # https://developers.facebook.com/docs/graph-api/reference/post/
  route :get_post, '/:post_id'
  # https://developers.facebook.com/docs/graph-api/reference/v12.0/page/feed
  route :get_posts, '/:page_id/feed'
  # https://developers.facebook.com/docs/graph-api/reference/v12.0/object/comments
  route :get_comments, '/:post_id/comments'
  # https://developers.facebook.com/docs/graph-api/reference/v12.0/object/reactions
  route :get_reactions, '/:post_id/reactions'
  # https://developers.facebook.com/docs/graph-api/reference/v14.0/debug_token
  route :debug_token, '/debug_token', query: {input_token: :input_token}
  # seems to be just the page Feed API, but this is the route the FB warming job uses
  route :get_page_posts, '/:page_id/posts', query: {fields: 'id, message, status_type, created_time, updated_time, permalink_url'}
  route :get_attachments, '/:post_id/attachments'

  # need for using the debug_token route
  def self.create_with_app_access_token(client_id:, client_secret:)
    response = Net::HTTP.get(URI("#{base_url}/oauth/access_token?client_id=#{client_id}&client_secret=#{client_secret}&grant_type=client_credentials"))
    payload = JSON.parse(response)
    new(payload['access_token'])
  end

  def self.create_default_app_client
    create_with_app_access_token(
      client_id: ENV['FACEBOOK_APP_ID'],
      client_secret: ENV['FACEBOOK_APP_SECRET']
    )
  end

  # override base method to dynamically change API version
  def build_uri(path, query, options)
    uri = URI(base_url + build_path(path))
    uri.query = build_query(query, options)
    uri
  end

  def build_path(path)
    return path unless version

    '/' + version + path
  end
end
