# frozen_string_literal: true

class ImporterS3Client
  def create_copy(slug, src_file)
    dest_file = (Time.now.to_f * 1000).to_i.to_s + '-' + src_file
    copy(slug, src_file, dest_file)
    dest_file
  end

  def copy(slug, src_file, dest_file)
    copy_source = "#{bucket_name}/#{slug}/data/#{src_file}"
    target_key = "#{slug}/data/#{dest_file}"
    s3_client.copy_object(bucket: bucket_name, copy_source: copy_source, key: target_key)
  end

  def s3_client
    @s3_client ||= create_s3_client
  end

  def create_s3_client
    Aws::S3::Client.new(
      region: 'us-east-1',
      access_key_id: config['com.et.importer.awsAccessKey'],
      secret_access_key: config['com.et.importer.awsSecretKey']
    )
  end

  def get_object(src_file)
    s3_client.get_object(
      bucket: bucket_name,
      key: src_file
    )
  end

  def download(job, path)
    s3_client.get_object(
      bucket: job.bucket_name,
      key: job.s3_key,
      response_target: path
    )
    path
  end

  private

  def bucket_name
    config['com.et.importer.awsS3Bucket']
  end

  def config
    @config ||= EnvironmentLoader.instance.get_secret_value(EnvironmentLoader.instance.create_aws_client, 'importer')
  end
end
