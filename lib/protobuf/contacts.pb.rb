# frozen_string_literal: true

##
# This file is auto-generated. DO NOT EDIT!
#
require 'protobuf'

module Contacts
  ::Protobuf::Optionable.inject(self) { ::Google::Protobuf::FileOptions }

  ##
  # Enum Classes
  #
  class GiftType < ::Protobuf::Enum
    define :HARD_CREDIT, 0
    define :SOFT_CREDIT, 1
    define :PLEDGE, 2
  end

  class FieldMatch < ::Protobuf::Enum
    define :EMAIL, 0
    define :NAME, 1
    define :ORGANIZATION, 2
  end

  class UpdateSource < ::Protobuf::Enum
    define :NONE, 0
    define :LINKEDIN, 1
    define :RAISERS_EDGE, 2
    define :CONSOLE, 3
    define :CSV_IMPORTER, 4
    define :FACEBOOK, 5
    define :TWITTER, 6
    define :LEGACY_MIGRATION, 7
    define :AUTH, 8
    define :API, 9
    define :COMMUNITY, 10
    define :HADOOP, 11
    define :PAYMENTS, 12
    define :GIVING_TREE, 13
    define :<PERSON>NRICHED, 14
  end

  class ActivityType < ::Protobuf::Enum
    define :PROPERTY_CHANGE, 0
    define :ROL<PERSON>_CHANGE, 1
    define :IDEN<PERSON><PERSON>_CHANGE, 2
    define :PROPERTY_DELETE, 3
    define :PROPERTY_SUPPRESSION, 4
    define :CONTACT_SUPPRESSION, 5
    define :PROPERTY_ADD, 6
    define :ROLE_OVERRIDE_CHANGE, 7
    define :EMS_MESSAGE_SENT, 8
  end

  ##
  # Message Classes
  #
  class ContactChangeEvent < ::Protobuf::Message
    class Type < ::Protobuf::Enum
      define :CREATE, 0
      define :READ, 1
      define :UPDATE, 2
      define :DELETE, 3
      define :REFRESH, 4
    end
  end

  class Contact < ::Protobuf::Message
    class Type < ::Protobuf::Enum
      define :CONTACT, 0
      define :ORGANIZATION, 1
    end
  end

  class Gift < ::Protobuf::Message; end
  class GiftLabel < ::Protobuf::Message; end
  class Identity < ::Protobuf::Message
    class Type < ::Protobuf::Enum
      define :EMAIL, 0
      define :REMOTE_ID, 1
      define :LEGACY_PERSON_ID, 2
      define :LINKEDIN_ID, 3
      define :USER_ID, 4
      define :FACEBOOK_ID, 5
      define :FULLCONTACT_ID, 6
      define :TWITTER_ID, 7
      define :EVENTBRITE_ID, 8
    end
  end

  class NameValueObject < ::Protobuf::Message; end
  class NameValueObjectList < ::Protobuf::Message; end
  class NameValuePair < ::Protobuf::Message; end
  class Property < ::Protobuf::Message
    class DataType < ::Protobuf::Enum
      define :STRING, 0
      define :NUMBER, 1
      define :OBJECT, 2
      define :LIST, 3
      define :DATE_STRING, 4
      define :DATETIME, 5
      define :BOOLEAN, 6
      define :CURRENCY, 7
    end
  end

  class PropertyPermission < ::Protobuf::Message; end
  class SuggestedUpdate < ::Protobuf::Message; end
  class Activity < ::Protobuf::Message; end
  class DonorIndexRollup < ::Protobuf::Message; end
  class ListFilter < ::Protobuf::Message; end
  class ShareSearch < ::Protobuf::Message; end
  class ListCollaborator < ::Protobuf::Message; end
  class ContactList < ::Protobuf::Message
    class Type < ::Protobuf::Enum
      define :DYNAMIC, 0
      define :USER_LIST, 1
    end
  end

  class ContactListChangeEvent < ::Protobuf::Message
    class Type < ::Protobuf::Enum
      define :CREATE, 0
      define :UPDATE, 1
      define :REFRESH, 2
      define :DELETE, 3
    end
  end

  class ListGroupChangeEvent < ::Protobuf::Message
    class Type < ::Protobuf::Enum
      define :CREATE, 0
      define :UPDATE, 1
      define :REFRESH, 2
      define :DELETE, 3
    end
  end

  class ListContactChangeEvent < ::Protobuf::Message
    class Type < ::Protobuf::Enum
      define :CREATE, 0
      define :UPDATE, 1
      define :REFRESH, 2
      define :DELETE, 3
    end
  end

  class ContactListMembership < ::Protobuf::Message; end
  class ContactListMembershipChangeEvent < ::Protobuf::Message
    class Type < ::Protobuf::Enum
      define :CREATE, 0
      define :DELETE, 1
      define :REFRESH, 2
      define :UPDATE, 3
    end
  end

  class Export < ::Protobuf::Message
    class State < ::Protobuf::Enum
      define :STARTED, 0
      define :FINISHED_SUCCESS, 1
      define :FINISHED_ERROR, 2
      define :ENQUEUED, 3
      define :JOB_STARTED, 4
      define :PROCESSING, 5
      define :UPLOADING, 6
      define :SENDING_EMAIL, 7
    end
  end

  class ListActivity < ::Protobuf::Message
    class Target < ::Protobuf::Enum
      define :CONTACT, 0
      define :COLLABORATOR, 1
    end

    class Type < ::Protobuf::Enum
      define :ADDED, 0
      define :DELETED, 1
    end
  end

  class EventAttendance < ::Protobuf::Message
    class Status < ::Protobuf::Enum
      define :RSVP, 0
      define :ATTENDED, 1
      define :CANCELLED, 2
    end
  end

  class ListGroup < ::Protobuf::Message
    class Type < ::Protobuf::Enum
      define :POOL, 0
    end
  end

  class ListLabel < ::Protobuf::Message
    class ObjectType < ::Protobuf::Enum
      define :LIST, 0
      define :LIST_GROUP, 1
      define :LIST_CONTACT, 2
    end
  end

  class ListContact < ::Protobuf::Message; end

  ##
  # File Options
  #
  set_option :java_outer_classname, 'ContactsProtoBuf'

  ##
  # Message Fields
  #
  class ContactChangeEvent
    optional ::Contacts::ContactChangeEvent::Type, :type, 1
    optional ::Contacts::Contact, :current, 2
    optional ::Contacts::Contact, :previous, 3
    optional :string, :app_key, 4
    optional :int64, :created_at, 5
    optional :int32, :authed_user_id, 6
    optional :int32, :oid, 7
  end

  class Contact
    optional :int64, :id, 1
    optional :int32, :oid, 2
    optional :int64, :created_at, 3
    optional :int64, :updated_at, 4
    repeated ::Contacts::NameValuePair, :name_value_pair, 5
    repeated ::Contacts::NameValueObject, :name_value_object, 6
    repeated ::Contacts::NameValueObjectList, :name_value_object_list, 7
    optional :bool, :private, 8
    repeated :int32, :role_id, 9
    repeated ::Contacts::Identity, :identity, 10
    optional ::Contacts::UpdateSource, :privatized_source, 11
    repeated :int32, :roles_override, 12
    optional :bool, :deleted, 13
    optional :int64, :highest_db_write_time_micros, 14
    optional ::Contacts::Contact::Type, :type, 15
  end

  class Gift
    optional :int64, :id, 1
    optional :float, :amount, 2
    optional :string, :remote_id, 3
    optional :int64, :related_gift_id, 4
    optional ::Contacts::GiftType, :type, 5
    optional :int64, :occurred_at, 6
    repeated ::Contacts::GiftLabel, :label, 7
    optional ::Contacts::UpdateSource, :update_source, 8
    optional :string, :related_gift_remote_id, 9
    optional ::Contacts::GiftType, :related_gift_type, 10
    optional :bool, :deleted, 11
    repeated ::Contacts::NameValuePair, :name_value_pair, 12
    repeated ::Contacts::NameValueObject, :name_value_object, 13
    repeated ::Contacts::NameValueObjectList, :name_value_object_list, 14
  end

  class GiftLabel
    optional :string, :value, 1
  end

  class Identity
    optional :int64, :id, 1
    optional :string, :value, 2
    optional ::Contacts::Identity::Type, :type, 3
    repeated ::Contacts::FieldMatch, :field_match, 4
  end

  class NameValueObject
    optional ::Contacts::Property, :property, 1
    repeated ::Contacts::NameValuePair, :name_value_pair, 2
    optional :string, :guid, 3
  end

  class NameValueObjectList
    optional ::Contacts::Property, :property, 1
    repeated ::Contacts::NameValueObject, :name_value_object, 2
    optional :string, :containing_object_name, 3
  end

  class NameValuePair
    optional :int64, :id, 1
    optional ::Contacts::Property, :property, 2
    optional :string, :value, 3
    optional ::Contacts::UpdateSource, :update_source, 4
    optional :int64, :created_at, 5
    optional :int64, :updated_at, 6
    optional :bool, :private, 7
    optional ::Contacts::UpdateSource, :privatized_source, 8
    optional :bool, :deleted, 9
  end

  class Property
    optional :int32, :id, 1
    optional :string, :name, 2
    optional ::Contacts::Property::DataType, :data_type, 3
    optional :string, :description, 4
    optional :int64, :created_at, 5
    optional :int64, :updated_at, 6
    optional :bool, :default, 7
    optional :int32, :parent_id, 8
    repeated ::Contacts::PropertyPermission, :permission, 9
    optional :int32, :oid, 10
    repeated :int32, :app_key, 11
    optional :bool, :deleted, 12
    optional :bool, :reserved, 13
    optional :bool, :visible, 14
    optional :bool, :filterable, 15
  end

  class PropertyPermission
    optional :int32, :actor_role_id, 1
    optional :int32, :subject_role_id, 2
    optional :bool, :writable, 3
  end

  class SuggestedUpdate
    optional :int32, :user_id, 1
    optional :int64, :target_contact_id, 2
    optional :string, :suggestion, 3
    optional :int64, :created_at, 4
    optional :int64, :updated_at, 5
    optional :int32, :oid, 6
    optional :int32, :id, 7
    optional :bool, :skip_email_notification, 8
    optional :string, :suggested_updates, 9
  end

  class Activity
    optional :int64, :id, 1
    optional :int64, :created_at, 2
    optional :int64, :timestamp, 3
    optional :int64, :contact_id, 4
    optional :int32, :oid, 5
    optional :string, :prev_val, 6
    optional :string, :current_val, 7
    optional :int32, :app_key_id, 8
    optional :int32, :user_id, 9
    optional ::Contacts::ActivityType, :type, 10
    optional :int32, :property_id, 11
  end

  class DonorIndexRollup
    optional :int32, :recenecy_score, 1
    optional :int32, :frequency_score, 2
    optional :int32, :monetary_value_score, 3
    optional ::Contacts::Contact, :contact, 4
    optional :bool, :giving_freq_changed, 5
  end

  class ListFilter
    optional :int32, :id, 1
    optional :string, :filter_id, 2
    optional :string, :label, 3
    repeated :string, :platform, 4
    optional :int32, :version, 5
    optional :string, :data_type, 6
  end

  class ShareSearch
    optional :int32, :id, 1
    optional :string, :hash_id, 2
    optional :int32, :oid, 3
    optional :int32, :user_id, 4
    optional :string, :criteria, 5
    optional :string, :filter, 6
    optional :int64, :created_at, 7
    optional :int64, :updated_at, 8
  end

  class ListCollaborator
    optional :int32, :id, 1
    optional :int64, :created_at, 2
    optional :int64, :updated_at, 3
    optional :int32, :list_id, 4
    optional :int32, :oid, 5
    optional :int32, :collaborator_user_id, 6
    optional :int32, :added_by_user_id, 7
  end

  class ContactList
    optional :int32, :id, 1
    optional :int32, :oid, 2
    optional :int64, :created_at, 3
    optional :int64, :updated_at, 4
    optional :int32, :user_id, 5
    optional ::Contacts::ContactList::Type, :type, 6
    optional :string, :criteria, 7
    optional :string, :name, 8
    optional :string, :alias, 9
    optional :string, :filter, 10
    optional :bool, :private, 11
    optional :bool, :deleted, 12
    optional :int32, :app_key_id, 13
    optional :string, :remote_id, 14
    optional :int32, :owner_group_id, 15
    optional :bool, :hidden, 16
    optional :string, :slug, 17
    optional :bool, :global, 18
    repeated ::Contacts::ListLabel, :list_label, 19
  end

  class ContactListChangeEvent
    optional ::Contacts::ContactListChangeEvent::Type, :type, 1
    optional ::Contacts::ContactList, :current, 2
    optional ::Contacts::ContactList, :previous, 3
    optional :int64, :created_at, 4
  end

  class ListGroupChangeEvent
    optional ::Contacts::ListGroupChangeEvent::Type, :type, 1
    optional ::Contacts::ListGroup, :current, 2
    optional ::Contacts::ListGroup, :previous, 3
    optional :int64, :created_at, 4
  end

  class ListContactChangeEvent
    optional ::Contacts::ListContactChangeEvent::Type, :type, 1
    optional ::Contacts::ListContact, :current, 2
    optional ::Contacts::ListContact, :previous, 3
    optional :int64, :created_at, 4
  end

  class ContactListMembership
    optional :int32, :list_id, 1
    optional ::Contacts::ContactList::Type, :list_type, 2
    optional :int32, :user_id, 3
    optional :int64, :created_at, 4
    optional :int32, :group_id, 5
    optional :bool, :initial, 6
  end

  class ContactListMembershipChangeEvent
    optional :int64, :contact_id, 1
    optional ::Contacts::ContactListMembershipChangeEvent::Type, :type, 2
    optional ::Contacts::ContactListMembership, :contact_list_membership, 3
    optional :int64, :created_at, 4
    optional :int32, :oid, 5
    optional :int32, :group_id, 6
  end

  class Export
    optional :int32, :id, 1
    optional :int64, :created_at, 2
    optional :int64, :updated_at, 3
    optional :int32, :oid, 4
    optional :string, :external_path, 5
    optional :int32, :user_id, 6
    optional :string, :file_name, 7
    optional :int64, :last_downloaded_at, 8
    optional :double, :percent_complete, 9
    optional ::Contacts::Export::State, :state, 10
    optional :int32, :list_id, 11
    optional :string, :title, 12
    optional :int32, :app_key_id, 13
    optional :string, :remote_id, 14
    optional :string, :type, 15
  end

  class ListActivity
    optional :int32, :id, 1
    optional :int32, :list_id, 2
    optional :int32, :oid, 3
    optional :int64, :created_at, 4
    optional :int32, :user_id, 5
    optional ::Contacts::ListActivity::Target, :target, 6
    optional ::Contacts::ListActivity::Type, :type, 7
    optional :int64, :target_id, 8
  end

  class EventAttendance
    optional :string, :event_id, 1
    optional :string, :remote_id, 2
    optional ::Contacts::EventAttendance::Status, :status, 3
  end

  class ListGroup
    optional :int32, :id, 1
    optional :int32, :oid, 2
    optional :string, :name, 3
    optional :string, :slug, 4
    optional ::Contacts::ListGroup::Type, :type, 5
    optional :int32, :owner_list_id, 6
    optional :bool, :unique_constraint_on_contact, 7
    optional :int64, :created_at, 8
    optional :int64, :updated_at, 9
  end

  class ListLabel
    optional :int32, :id, 1
    optional ::Contacts::ListLabel::ObjectType, :object_type, 2
    optional :int32, :object_id, 3
    optional :string, :key, 4
    optional :string, :value, 5
    optional :int64, :created_at, 6
    optional :int64, :updated_at, 7
  end

  class ListContact
    optional :int32, :id, 1
    optional :int32, :list_id, 2
    optional :int32, :constraint_group_id, 3
    optional :int32, :user_id, 4
    optional :int32, :oid, 5
    optional :int64, :contact_id, 6
    optional :int64, :created_at, 7
    optional :int64, :updated_at, 8
  end
end
