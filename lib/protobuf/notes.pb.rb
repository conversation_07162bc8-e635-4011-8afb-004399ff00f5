# frozen_string_literal: true

##
# This file is auto-generated. DO NOT EDIT!
#
require 'protobuf'

module Notes
  ::Protobuf::Optionable.inject(self) { ::Google::Protobuf::FileOptions }

  ##
  # Enum Classes
  #
  class Application < ::Protobuf::Enum
    define :EVERTRUE_APP, 0
    define :VOLUNTEER_APP, 1
  end

  class NoteTargetType < ::Protobuf::Enum
    define :CONTACT, 0
    define :LIST, 1
  end

  class NoteCategory < ::Protobuf::Enum
    define :NOTE, 0
    define :PHONECALL, 1
    define :MEETING, 2
    define :MAILING, 3
    define :TASK, 4
    define :OTHER, 5
    define :EMAIL, 6
    define :GIVINGTREE_COMMENT, 7
  end

  class UpdateSource < ::Protobuf::Enum
    define :IMPORTER, 0
    define :EVERTRUE, 1
  end

  ##
  # Message Classes
  #
  class Note < ::Protobuf::Message; end
  class CustomField < ::Protobuf::Message; end
  class Author < ::Protobuf::Message; end
  class Solicitor < ::Protobuf::Message; end
  class SecondaryTarget < ::Protobuf::Message; end
  class UserMention < ::Protobuf::Message; end
  class ContactMention < ::Protobuf::Message; end
  class Label < ::Protobuf::Message; end

  ##
  # Message Fields
  #
  class Note
    optional :int64, :id, 1
    optional :int32, :oid, 4
    optional :string, :remote_id, 3
    optional :int64, :target_id, 6
    optional ::Notes::NoteTargetType, :target_type, 7
    optional ::Notes::NoteCategory, :category, 2
    optional :int32, :creator_user_id, 8
    optional :int64, :date_occurred, 9
    optional :int64, :created_at, 10
    optional :int64, :updated_at, 11
    optional :string, :text, 12
    repeated ::Notes::UserMention, :user_mention, 13
    repeated ::Notes::ContactMention, :contact_mention, 14
    repeated ::Notes::Label, :label, 15
    optional ::Notes::Author, :author, 16
    repeated ::Notes::Solicitor, :solicitor, 17
    optional :string, :summary, 18
    optional :bool, :deleted, 19
    optional ::Notes::UpdateSource, :update_source, 20
    optional :int32, :trip_id, 21
    optional :int32, :meeting_id, 22
    optional :string, :interaction_type, 23
    optional :int64, :proposal_id, 24
    repeated ::Notes::CustomField, :custom_field, 25
    optional ::Notes::Application, :application, 26, default: ::Notes::Application::EVERTRUE_APP
    repeated ::Notes::SecondaryTarget, :secondary_target, 27
  end

  class CustomField
    optional :int64, :id, 1
    optional :string, :value, 2
  end

  class Author
    optional :string, :name, 1
    optional :string, :remote_user_id, 2
    optional :int32, :user_id, 3
  end

  class Solicitor
    optional :string, :name, 1
    optional :string, :remote_user_id, 2
    optional :int32, :user_id, 3
  end

  class SecondaryTarget
    optional :int64, :target_id, 1
    optional :string, :remote_id, 2
  end

  class UserMention
    optional :string, :alias, 1
    optional :int32, :user_id, 3
  end

  class ContactMention
    optional :string, :alias, 1
    optional :int64, :contact_id, 4
  end

  class Label
    optional :string, :name, 1
    optional :string, :value, 2
  end
end
