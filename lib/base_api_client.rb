# frozen_string_literal: true

class BaseApiClient
  attr_accessor :impersonating_user_id,
                :update_source
  
  def initialize(app_key, auth_token, base_url, provider)
    @app_key = app_key
    @auth_token = auth_token
    @base_url = base_url
    @provider = provider
    @update_source = 'csv_importer'
  end

  def request(action, path, payload = nil, headers = {}, connection_options = {})
    url = @base_url + path + tracing_info(path)

    HTTPClient.request(
      action,
      URI(url),
      payload ? payload.to_json : nil,
      req_headers.merge(headers),
      connection_options
    )
  end

  def self.create_app_client(app_name='csv_importer', base_url_override=nil)
    mysql_client = MySQLHelpers.create_client(:auth)
    begin
      results = mysql_client.query("SELECT `key`, lazy_token FROM applications WHERE name = '#{app_name}'").first
      new(results['key'], results['lazy_token'], base_url_override || base_url, 'EvertrueAppToken')
    ensure
      mysql_client.close
    end
  end
  class << self; alias create_client_with_app_creds create_app_client; end

  def self.create_client_with_user_creds(user_auth_token, app_name='givingtree')
    mysql_client = MySQLHelpers.create_client(:auth)
    begin
      results = mysql_client.query("SELECT `key` FROM applications WHERE name = '#{app_name}'").first
      new(results['key'], user_auth_token, base_url, 'EvertrueAuthToken')
    ensure
      mysql_client.close
    end
  end

  def self.create_client_for_logged_in_user(oid, email=nil, app_name='givingtree')
    email ||= ENV['EVERTRUE_EMAIL']
    unless email
      $stderr.puts("Configure your EVERTRUE_EMAIL environment variable")
      exit 1
    end

    app = AuthDB::Application.find_by!(name: app_name)
    user = AuthDB::User.find_by!(email: email)
    org = AuthDB::Organization.find(oid)
    recent_session = user.sessions.where(application_id: app.id, organization_id: org.id, session_type: 'SCOPED').where('expire_at > ? AND lazy_token IS NOT NULL', Time.now()).last
    recent_session ||= user.sessions.where(application_id: app.id).where('expire_at > ? AND lazy_token IS NOT NULL', Time.now()).last!
    new(app.key, recent_session.lazy_token, base_url, 'EvertrueAuthToken')
  rescue
    $stderr.puts("Login or goto #{app_url} to create a new session with org #{org.name}/#{org.id}")
    exit 1
  end

  def self.base_url
    case ENV['RAILS_ENV']
    when 'production'
      ENV['EVERTRUE_PROD_BASE_URL'] || 'https://api.evertrue.com'
    when 'staging'
      ENV['EVERTRUE_STAGE_BASE_URL'] || 'https://stage-api.evertrue.com'
    else
      'http://localhost:3000'
    end
  end

  def self.app_url
    case ENV['RAILS_ENV']
    when 'production'
      'https://app.evertrue.com'
    when 'staging'
      'https://stage-app.evertrue.com'
    else
      'http://localhost:3000'
    end
  end

  def tracing_info(path)
    (path =~ /\?/ ? '&' : '?') + 
      "script=#{Addressable::URI.escape($0)}&developer=#{Addressable::URI.escape(ENV['USER'])}"
  end

  def req_headers
    headers = {
      'Content-Type' => 'application/json',
      'Accept' => 'application/json',
      'Application-Key' => @app_key,
      'Authorization-Provider' => @provider,
      'Authorization' => @auth_token,
      'ET-Update-Source' => @update_source
    }
    headers['Authorization-Impersonate'] = impersonating_user_id.to_s if impersonating_user_id
    headers
  end
end

