module AwsAlbHelper
  module_function

  def create_client
    Aws::ElasticLoadBalancingV2::Client.new(aws_alb_config)
  end

  def load_balancers(client)
    client.describe_load_balancers[:load_balancers]
  end

  def listeners(client, load_balancer_arn)
    client.describe_listeners(load_balancer_arn: load_balancer_arn)[:listeners]
  end

  def rules(client, listener_arn)
    client.describe_rules(listener_arn: listener_arn)[:rules]
  end

  def aws_alb_config
    {
      region: EnvironmentLoader.instance.aws_region_name,
      profile: EnvironmentLoader.instance.aws_profile
    }
  end
end
