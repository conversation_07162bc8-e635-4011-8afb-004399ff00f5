module AwsAppflowHelper
  module_function

  def create_client
    Aws::Appflow::Client.new(aws_appflow_config)
  end

  def each_flow(client)
    next_token = nil
    max_results = 20

    while (resp = client.list_flows(next_token: next_token, max_results: max_results))
      resp.flows.each do |flow|
        yield flow
      end

      break unless (next_token = resp.next_token)
    end
  end

  def aws_appflow_config
    {
      region: EnvironmentLoader.instance.aws_region_name,
      profile: EnvironmentLoader.instance.aws_profile
    }
  end
end
