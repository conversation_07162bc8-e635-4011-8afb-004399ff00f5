class BlackbaudRENXTClient < Struct.new(
        :access_token
      )
  include ClientApiBuilder::Router

  base_url 'https://api.sky.blackbaud.com'

  header 'Content-Type', 'application/json'
  header 'Accept', 'application/json'
  header 'Authorization', :bearer_access_token
  header('Bb-Api-Subscription-Key') { ENV['BLACKBAUD_SUBSCRIPTION_KEY'] }

  query_builder(
    proc do |query|
      pairs = []
      query.each do |n, value|
        case value
        when Array
          pairs += value.map { |entry| "#{n}=#{entry}" }
        else
          pairs << "#{n}=#{value}"
        end
      end
      pairs.join('&')
    end
  )

  route :get_constituents, '/constituent/v1/constituents', query: {limit: :limit, offset: :offset}
  route :search_constituents, '/constituent/v1/constituents/search', query: {search_text: :search_text, limit: :limit, offset: :offset}
  route :get_gifts, '/gift/v1/gifts', query: {limit: :limit, offset: :offset}

  def self.get_access_token(auth_client, oid)
    payload = auth_client.get_blackbaud_integration(oid)
    access_token_expires_on = payload.dig('authentication', 'access_token_expires_on')
    diff = access_token_expires_on - (Time.now.to_i * 1000)

    # if the current access_token is good for 10 minutes, keep it
    return payload.dig('authentication', 'access_token') if diff > (10 * 60 * 1000)

    auth_client.get_blackbaud_access_token(oid)
  end

  private

  def bearer_access_token
    "Bearer #{access_token}"
  end
end
