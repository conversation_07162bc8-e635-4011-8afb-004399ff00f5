class AiClient < BaseApiClient
  def self.parse_response(raw_response, include_id=false)
    response = ""

    parse_chunk = Proc.new { |chunk|
       # add request uuid only on first chunk
       if response == "" && include_id
        response = "request_id: #{chunk['metadata']['request_id']}\nresponse: "
      end
      
      response = response + chunk['data']['response'] if chunk['data']
    }

    chunks = raw_response.split('CHUNK_TERMINATOR')
    chunks.each do |chunk_raw|
      chunk = JSON.parse(chunk_raw)
      parse_chunk.call(chunk)

      if chunk['event'] == 'CHUNK'
        next
      elsif chunk['event'] == 'DONE'
        return response
      elsif chunk['event'] == 'ERROR'
        puts "error encountered: #{chunk}"
        raise response
      end

      raise "Receieved chunk with unknown event: #{chunk}"
    end
    response
  end

  def complete(oid, prompt)
    response = request(:post, "/ai/v1/completions/?oid=#{oid}", {
      prompt: prompt.to_h,
    })
    case response
    when Net::HTTPOK
      return AiClient.parse_response(response.body)
    else
      return "#{response.body}"
    end
  end

  def create_chat(oid)
    response = request(:post, "/ai/v1/chats?oid=#{oid}")
    case response
    when Net::HTTPOK
      return JSON.parse(response.body)
    else
      raise response.body
    end
  end

  def submit_chat_message(oid, chat_id, message, prompt)
    response = request(:post, "/ai/v1/chats/#{chat_id}/messages?oid=#{oid}", {
      message: message,
      prompt: prompt.to_h
    })
    case response
    when Net::HTTPOK
      return AiClient.parse_response(response.body)
    else
      raise response.body
    end
  end

  def get_prompt(oid, prompt_key)
    request(:get, "/ai/v1/prompts/#{prompt_key}?oid=#{oid}").body
  end

  def list_prompts(oid, filter)
    request(:get, "/ai/v1/prompts/?oid=#{oid}&filter=#{filter}").body
  end

  def render_prompt(oid, prompt)
    request(:post, "/ai/v1/prompts/render?oid=#{oid}", {
      prompt: prompt.to_h
    }).body
  end

  class Prompt
    def initialize(prompt_key=nil, template_data=nil, args=nil)
      if prompt_key || template_data
        raise "provide either prompt_key or template, but not both" unless !!prompt_key ^ !!template_data
      elsif !args
        raise "provide at least one of prompt_key, template, or args"
      end
  
      @prompt_key = prompt_key
      @template_data = template_data
      @args = args
    end

    def to_h
      {
        key: @prompt_key,
        template_data: @template_data,
        args: @args
      }
    end
  end
end

