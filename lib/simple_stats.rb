require 'time'

class SimpleStats < Hash
  DEFAULT_SEPARATOR = ', '

  attr_reader :started_at,
              :semaphore

  attr_writer :counter_display_name,
              :notifier

  attr_accessor :total,
                :notification_interval,
                :cnt

  def initialize
    super(0)
    @notification_interval = 10_000
    @semaphore = Mutex.new
    reset
  end

  def reset
    @started_at = Time.now
    @cnt = 0
    clear
  end

  def elapsed_time
    Time.now - started_at
  end

  def counter_display_name
    @counter_display_name || 'cnt'
  end

  def stats
    ([[counter_display_name, cnt]] + to_a + [['elapsed_time', elapsed_time], ['percent_complete', percent_complete_display]]).reject { |(_, v)| v.nil? }
  end

  def percent_complete
    (cnt.to_f / total.to_f) if cnt && total && total > 0
  end

  def percent_complete_display
    if (percent = percent_complete)
      '%0.1f%%' % (percent * 100.0)
    end
  end

  def to_display_s(separator = DEFAULT_SEPARATOR)
    stats.map do |k, v|
      str_value =
        case v
        when Time
          v.utc.iso8601
        when Float
          '%0.2f' % v
        else
          v.to_s
        end

      "#{k}: #{str_value}"
    end.join(separator)
  end

  def notifier
    @notifier || LOG
  end

  def notify(immediate = false,  separator = DEFAULT_SEPARATOR)
    notifier.notify to_display_s(separator), immediate
  end

  def notify?
    (cnt % notification_interval) == 0
  end

  def update(name, by = 1)
    @semaphore.synchronize do
      self[name] += by
    end
  end

  def inc(by = 1)
    @semaphore.synchronize do
      @cnt += by
    end
  end

  def inc_and_notify(by = 1, immediate = false, separator = DEFAULT_SEPARATOR)
    @semaphore.synchronize do
      @cnt += by
      notify(immediate, separator) if notify?
    end
  end
end
