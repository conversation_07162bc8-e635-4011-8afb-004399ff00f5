module AwsStsHelper
  module_function

  def aws_config
    EnvironmentLoader.instance.aws_config
  end

  def create_sts_client
    Aws::STS::Client.new(aws_config)
  end

  def sts_client
    @sts_client ||= create_sts_client
  end

  def role_arn
    EnvironmentLoader.instance.aws_role_arn
  end

  def role_session_name
    'local_session'
  end

  def assume_role
    sts_client.assume_role(
      role_arn: role_arn,
      role_session_name: role_session_name
    )
  end

  def create_temporary_credentials
    assume_role.credentials
  end
end
