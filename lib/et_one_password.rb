class ETOnePassword < OnePassword
  SUBDOMAIN = 'evertrue'
  CONFIG = YAML.load_file(BASE_DIR.join('config', 'one_password.yml'))

  def initialize
    super(SUBDOMAIN)
  end

  def rds_config(name)
    rds_settings = get_rds_settings(name)

    config = {
      'port' => rds_settings['port'] || 3306,
      'host' =>  rds_settings['host'],
      'user' =>  rds_settings['user'],
      'password' => nil
    }

    fields = get_item(rds_settings['uuid'])
    rds_fields(rds_settings).each do |config_name, one_pass_field_name|
      config[config_name] = fields.detect { |field| field['id'] == one_pass_field_name }['value']
    end

    config
  end

  def db_config(database)
    rds_settings = config['rds'][ENV['RAILS_ENV']].values.detect { |settings| settings['databases']&.include?(database) }

    config = {
      'port' => rds_settings['port'] || 3306,
      'host' =>  rds_settings['host'],
      'user' =>  rds_settings['user'],
      'password' => nil,
      'database' => database
    }

    fields = get_item(rds_settings['uuid'])
    rds_fields(rds_settings).each do |config_name, one_pass_field_name|
      config[config_name] = fields.detect { |field| field['id'] == one_pass_field_name }['value']
    end

    config
  end

  def config
    CONFIG
  end

  def get_rds_settings(name)
    name_with_underscores = name.gsub('-', '_')

    config['rds']['production'][name] ||
      config['rds']['production'][name_with_underscores] ||
      config['rds']['staging'][name] ||
      config['rds']['staging'][name_with_underscores] ||
      config['rds']['old_production'][name] ||
      config['rds']['old_production'][name_with_underscores] ||
      config['rds']['old_staging'][name] ||
      config['rds']['old_staging'][name_with_underscores] ||
      config['rds']['thankview'][name] ||
      config['rds']['thankview'][name_with_underscores]
  end

  def rds_fields(rds_settings)
    rds_settings['fields'] || default_rds_fields
  end

  def default_rds_fields
    {
      'user' => 'username',
      'password' => 'password'
    }
  end
end
