require 'shellwords'

module RedisHelpers
  module_function

  def dna_host
    REDIS_CONFIG['dna']
  end

  def get_all_dna_keys(host)
    `redis-cli -h #{host} keys #{Shellwords.escape('dna:*')}`.split("\n").map(&:strip)
  end

  def get_all_dna_gate_keys(host)
    `redis-cli -h #{host} smembers dna:gates`.split("\n").map(&:strip)
  end

  def get_all_dna_setting_keys(host)
    `redis-cli -h #{host} zrange dna:settings 0 -1`.split("\n").map(&:strip)
  end

  def delete_org_key(host, oid, key)
    `redis-cli -h #{host} del dna:setting:value:#{key}:organization:#{oid}` == "1\n"
  end
end
