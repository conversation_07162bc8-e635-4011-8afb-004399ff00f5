# frozen_string_literal: true

DynamicActiveModelGenerator.create_database_models('importer')

class ImporterDB::Presence
  def self.get_all_remote_ids(oid)
    where(oid: 237).select('remote_id').to_a.map(&:remote_id)
  end
end

# v1 ex: s3://onboarding.evertrue.com/evertrue/data/1645552711270-test.csv
# v3 ex: s3://import.evertrue.com/stage/428/job/18756/1649257443006-1649257379044-2_resetInteractions-interactions_0_delete.interactions.full.csv

class ImporterDB::Job
  include TimeHelpers

  self.inheritance_column = nil

  JOB_STATUS_QUEUED = [
    'QUEUED',
    'STAGE_TWO_QUEUED',
    'STAGE_THREE_QUEUED',
    'PRUNING_QUEUED',
    'FILECHECK_QUEUED',
    'PREPROCESS_QUEUED',
    'IMPORT_QUEUED'
  ]

  JOB_STATUS_RUNNING = [
    'RUNNING',
    'STAGE_TWO_RUNNING',
    'STAGE_THREE_RUNNING',
    'PRUNING',
    'FORKED',
    'FILECHECK_FORKED',
    'FILECHECK_RUNNING',
    'PREPROCESS_FORKED',
    'PREPROCESS_RUNNING',
    'IMPORT_FORKED',
    'IMPORT_RUNNING'
  ]

  # It is safe to remove jobs with 1 of these status.  Because no data was imported into the system.
  SAFE_TO_DELETE_JOB_STATUSES = [
    'CANCELLED',
    'FILECHECK_FAILED',
    'FILECHECK_SUCCESS',
    'NEW',
    'PREPROCESS_ERROR',
    'PREPROCESS_FAILED',
    'PREPROCESS_SUCCESS',
    'PREPROCESS_WARNING'
  ]

  TYPES = {

    'Contact' => ['CSV', 'JSON', 'CSV_BULK_UPDATE', 'CSV_APPEND_ONLY', 'ORGANIZATION_CSV', 'CONTACT', 'CONTACT_IDENTITY', 'CONTACT_IDENTITY_FULL', 'CONTACT_ADDRESS', 'CONTACT_EMAIL', 'CONTACT_PHONE', 'CONTACT_EDUCATION', 'CONTACT_EMPLOYMENT', 'CONTACT_RELATIONSHIP', 'CONTACT_SOCIAL', 'CONTACT_CUSTOM_FIELD', 'CONTACT_EXCLUSION', 'CONTACT_ANNUAL_GIFT_TOTAL', 'CONTACT_ACTIVITY', 'CONTACT_TOTAL_GIVING'],
    'Gift' => ['TRANSACTIONAL_CSV', 'TRANSACTIONAL_GIFT'],
    'UGC' => ['NOTES_CSV', 'PROPOSAL_CSV', 'INTERACTION', 'PROPOSAL'],
    'Assignment' => ['ASSIGNMENT_CSV', 'ASSIGNMENT_TEAM_CSV', 'ASSIGNMENT_VOLUNTEER', 'ASSIGNMENT_RM']
  }

  belongs_to :organization, foreign_key: :oid, class_name: 'AuthDB::Organization'

  def s3_filename_url
    "s3://#{bucket_name}/#{s3_key_escaped}"
  end

  def s3_key
    "#{base_path}/#{s3_filename}"
  end

  def s3_key_escaped
    "#{base_path}/#{CGI.escape(s3_filename)}"
  end

  def base_path
    if version == 3
      base_v3_path
    else
      base_v1_path
    end
  end

  def base_v1_path
    "#{organization.slug}/data"
  end

  def base_v3_path
    root_folder = ENV['RAILS_ENV'] == 'production' ? 'prod' : 'stage'

    "#{root_folder}/#{oid}/job/#{id}"
  end

  def bucket_name
    if version == 3
      'import.evertrue.com'
    else
      'onboarding.evertrue.com'
    end
  end

  def running?
    JOB_STATUS_RUNNING.include?(job_status)
  end

  def queued?
    JOB_STATUS_QUEUED.include?(job_status)
  end

  def import_type_name
    TYPES.each do |name, job_types|
      return name if job_types.include?(type)
    end
    nil
  end

  def waitng_for_in_words
    @waitng_for_in_words ||= time_in_words(Time.at(started_at / 1_000.0))
  end

  def elapsed_time
    (Time.now - Time.at(started_at / 1_000.0)).to_i
  end

  def current_status
    @current_status ||= job_statuses.select(&:is_current?).first
  end

  def current_status_name
    current_status.status
  end

  def has_error?
    unless defined?(@has_error)
      @has_error = job_statuses.any? &:is_error?
    end
    @has_error
  end

  def current_stats
    @current_stats ||= csv_converter_stats.select(&:is_current?).first || csv_converter_stats.last
  end

  def current_step
    current_stats.conversion_type if current_stats
  end

  def current_step_progress 
    current_stats.completion_percentage if current_stats
  end

  def total_progress
    if current_stats
      case current_stats.conversion_type
      when "FILE_CHECK"
        return current_stats.completion_percentage / 3
      when "TRANSCODE"
        return current_stats.completion_percentage / 3 + 33.0
      when "IMPORT"
        return current_stats.completion_percentage / 3 + 66.0
      end
    end
    0
  end

  def is_automatic?
    unless defined?(@is_automatic)
      @is_automatic = AuthDB::Application.where(name: 'upload')
                        .first
                        .super_user_id == create_user_id
    end
    @is_automatic
  end

  def created_at_to_time
    Time.at(created_at / 1000.0)
  end

  def created_at_in_words
    time_in_words(created_at_to_time)
  end

  def get_contact_remote_id_header_name
    if version == 1
      return nil unless mapping_hash_id

      mapping = ImporterDB::Mapping.where(hash_id: mapping_hash_id, src_type: 'CSV', binding_type: 'IDENTITY', binding_id: nil).first
      return nil unless mapping

      ImporterDB::MappingSrcCsv.find(mapping.src_id).header_value
    elsif version == 3
      return nil unless header_columns

      JSON.parse(header_columns).detect { |col| col['field_name'] == 'contact.remote_id' }['import_column']
    end
  end

  def safe_to_delete?
    SAFE_TO_DELETE_JOB_STATUSES.include?(current_status.status)
  end
end

class ImporterDB::JobStatus
  ERROR_STATUSES = [
    'IMPORT_ERROR', 
    'PREPROCESS_ERROR', 
    'FILECHECK_ERROR',
    'IMPORT_FAILED',
    'PREPROCESS_FAILED',
    'FILECHECK_FAILED',
    'ERROR',
    'FAILED'
  ]

  WARN_STATUSES = [
    'PREPROCESS_WARNING',
    'WARN'
  ]

  # retruns true if this status is the current status for the job
  def is_current?
    ended_at == nil
  end

  def is_error?
    ERROR_STATUSES.include? status
  end

  def is_warn?
    WARN_STATUSES.include? status
  end
end

class ImporterDB::ErrorEvent
end

class ImporterDB::CsvConverterStat
  def is_current?
    completed_at == nil
  end
end
