# frozen_string_literal: true

DynamicActiveModelGenerator.create_database_models('exporter')

class ExporterDB::Export
  include TimeHelpers

  def humanized_state
    case state
    when 0
      return "QUEUED"
    when 1
      return "COMPLETED"
    when 2
      return "FAILED"
    when 3
      return "SUBMITTED"
    when 4
      return "STARTING"
    when 5
      return "RUNNING"
    when 6
      return "UPLOADING"
    when 7
      return "NOTIFYING"
    end
  end

  def created_at_iso8601
    Time.at(created_at / 1000).iso8601
  end

  def created_ago
    time_in_words(Time.at(created_at / 1000))
  end

  def stalled?
    state != 1 &&
      updated_at < ((Time.now - 3600).to_i * 1000)
  end
end
