update_model do
  attr_reader :fetch_comments_error,
              :fetch_reactions_error

  def self.default_facebook_client
    SodaDB::FacebookToken.default_facebook_client
  end

  def default_facebook_client
    self.class.default_facebook_client
  end

  def fetch_facebook_posts
    facebook_client.get_comments(post_id: remote_id)
  end

  def active_facebook_token
    unless defined?(@active_facebook_token)
      @active_facebook_token = facebook_tokens.detect(&:active?)
    end
    @active_facebook_token
  end

  def has_active_facebook_token?
    !! active_facebook_token
  end

  def facebook_client
    @facebook_client ||= active_facebook_token&.create_facebook_client
  end

  def active_token_creator_id
    active_facebook_token&.user&.id
  end

  def active_token_creator_name
    active_facebook_token&.user&.name
  end

  def active_token_creator_email
    active_facebook_token&.user&.email
  end

  def last_post
    unless defined?(@last_post)
      @last_post = posts.last
    end
    @last_post
  end

  def can_fetch_comments?
    unless defined?(@can_fetch_comments)
      @can_fetch_comments =
        begin
          last_post && facebook_client&.get_comments(post_id: last_post.remote_id) && true
        rescue ClientApiBuilder::UnexpectedResponse => e
          @fetch_comments_error = e
          false
        end
    end
    @can_fetch_comments
  end

  def can_fetch_comments_with_default_client?
    unless defined?(@can_fetch_comments_with_default_client)
      @can_fetch_comments_with_default_client =
        begin
          last_post && default_facebook_client.get_comments(post_id: last_post.remote_id) && true
        rescue ClientApiBuilder::UnexpectedResponse => e
          false
        end
    end
    @can_fetch_comments_with_default_client
  end

  def fetch_comments_error_message
    return 'no token' unless active_facebook_token
    return 'no posts' unless last_post
    return unless @fetch_comments_error

    JSON.parse(@fetch_comments_error.response.body).dig('error', 'message')
  end

  def can_fetch_reactions?
    unless defined?(@can_fetch_reactions)
      @can_fetch_reactions =
        begin
          last_post && facebook_client&.get_reactions(post_id: last_post.remote_id) && true
        rescue ClientApiBuilder::UnexpectedResponse => e
          @fetch_reactions_error = e
          false
        end
    end
    @can_fetch_reactions
  end

  def can_fetch_reactions_with_default_client?
    unless defined?(@can_fetch_reactions_with_default_client)
      @can_fetch_reactions_with_default_client =
        begin
          last_post && default_facebook_client.get_reactions(post_id: last_post.remote_id) && true
        rescue ClientApiBuilder::UnexpectedResponse => e
          false
        end
    end
    @can_fetch_reactions_with_default_client
  end

  def fetch_reactions_error_message
    return 'no token' unless active_facebook_token
    return 'no posts' unless last_post
    return unless @fetch_reactions_error

    JSON.parse(@fetch_reactions_error.response.body).dig('error', 'message')
  end

  def can_fetch_posts?
    unless defined?(@can_fetch_posts)
      @can_fetch_posts =
        begin
          facebook_client.get_posts(page_id: remote_id)
          true
        rescue ClientApiBuilder::UnexpectedResponse => e
          false
        end
    end
    @can_fetch_posts
  end

  def can_fetch_posts_with_default_client?
    unless defined?(@can_fetch_posts_with_default_client)
      @can_fetch_posts_with_default_client =
        begin
          default_facebook_client.get_posts(page_id: remote_id)
          true
        rescue ClientApiBuilder::UnexpectedResponse => e
          false
        end
    end
    @can_fetch_posts_with_default_client
  end

  def fetch_facebook_posts_in_bulk
    res = facebook_client.get_posts(page_id: remote_id)
    yield res['data']

    while (next_url = res['paging']['next'])
      res = JSON.parse(HTTPClient.get(URI(next_url)).body)
      yield res['data']
    end
  end

  def each_facebook_post
    fetch_facebook_posts_in_bulk do |posts|
      posts.each do |post|
        yield post
      end
    end
  end

  def can_fetch_page?
    return false unless facebook_client

    facebook_client.get_page(page_id: remote_id)
    true
  rescue ClientApiBuilder::UnexpectedResponse => e
    false
  end

  def can_fetch_page_with_default_client?
    return false unless has_active_facebook_token?

    default_facebook_client.get_page(page_id: remote_id)
    true
  rescue ClientApiBuilder::UnexpectedResponse => e
    false
  end
end
