update_model do
  belongs_to :contact, class_name: 'ContactDB::Contact'

  def identity
    unless defined?(@identity)
      @identity = remote_user_id ? ContactDB::Identity.where(oid: organization_id, type: 1, value: remote_user_id).first : nil
    end
    @identity
  end

  def identity_by_email
    unless defined?(@identity_by_email)
      @identity_by_email = ContactDB::Identity.where(oid: organization_id, type: 0, value: user.email).first
    end
    @identity_by_email
  end

  def alumni?
    organization.alumni_role && affiliation_roles.size > 0 && affiliation_roles.all? { |ar| ar.role_id == organization.alumni_role.id }
  end

  def has_roles?
    affiliation_roles.size > 0
  end

  def no_roles?
    ! has_roles?
  end

  def deleted?
    user.nil? || user.deleted?
  end

  def super_user?
    user.super_user?
  end
end
