# frozen_string_literal: true

CONTACTS_DATABASE = DynamicActiveModelGenerator.create_database_models('contacts')

module ContactChecksum
  def self.included(base)
    base.extend ClassMethods
  end

  EXCLUDE_FIELDS = [
    'id',
    'contact_id',
    'object_guid',
    'update_source',
    'private',
    'privatized_source',
    'created_at',
    'updated_at'
  ]

  ZERO_BYTE = "\0"

  def self.generate_object_guid(attributes, parent_name, exclude_fields)
    hasher = OpenSSL::Digest::SHA1.new

    attributes
      .sort_by(&:first)
      .each do |(n, v)|
      next if exclude_fields.include?(n)
      next if v.nil?

      hasher <<
        if parent_name
          "#{parent_name}.#{n}"
        else
          n
        end

      hasher << v.to_s

      # private false
      hasher << ZERO_BYTE

      # deleted false
      hasher << ZERO_BYTE
    end

    hasher.hexdigest
  end

  module ClassMethods
    def object_guid_exclude_fields
      ContactChecksum::EXCLUDE_FIELDS
    end

    def object_guid_parent_name
      table_name
    end
  end

  def generate_object_guid
    ContactChecksum.generate_object_guid(self.attributes.to_h, self.class.object_guid_parent_name, self.class.object_guid_exclude_fields)
  end
end

CONTACTS_DATABASE.models.each do |model|
  next unless model.column_names.include?('object_guid')

  model.include ContactChecksum
end

class ContactDB::Contact
  has_one :contact_attribute

  def self.use_oid_idx
    from("#{self.table_name} USE INDEX(oid_idx)")
  end

  def remote_id
    identities.detect { |identity| identity.type == 1 }.try(:value)
  end

  def refresh(contact_client)
    contact_client.refresh_contact(oid, id)
  end

  def primary_email
    result = emails.detect(&:primary?)
    result && result.email
  end

  def name
    return nil unless contact_attribute

    contact_attribute.name_first.to_s + ' ' + contact_attribute.name_last.to_s
  end
end

class ContactDB::ContactAttribute
  def remote_id
    contact.remote_id
  end
end

class ContactDB::Email
  has_many :identities, class_name: 'ContactDB::Identity', primary_key: :contact_id, foreign_key: :contact_id
end

class ContactDB::Identity
  has_many :emails, class_name: 'ContactDB::Email', primary_key: :contact_id, foreign_key: :contact_id

  def self.by_oid(oid)
    joins(:contact).where(contact: {oid: oid})
  end

  def self.get_all_remote_ids(oid)
    by_oid(oid).where(type: 1).select('value').to_a.map(&:value)
  end
end

class ContactDB::IdentityConversion
  def self.value_new_lengths(oid)
    select('length(value_new) as value_new_len, count(*) as cnt')
      .where(oid: oid)
      .where('value_new IS NOT NULL')
      .group('value_new_len')
      .map do |identity_conversion|
      {
        value_new_len: identity_conversion.value_new_len,
        cnt: identity_conversion.cnt
      }
    end
  end
end
