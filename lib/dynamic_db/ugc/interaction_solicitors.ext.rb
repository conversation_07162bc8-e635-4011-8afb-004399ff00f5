update_model do
  belongs_to :user, class_name: 'AuthDB::User'

  def affiliation
    unless defined?(@affiliation)
      @affiliation = remote_user_id ? AuthDB::Affiliation.where(organization_id: interaction.oid, remote_user_id: remote_user_id).first : nil
    end
    @affiliation
  end

  def identity
    unless defined?(@identity)
      @identity = remote_user_id ? ContactDB::Identity.where(oid: interaction.oid, type: 1, value: remote_user_id).first : nil
    end
    @identity
  end
end
