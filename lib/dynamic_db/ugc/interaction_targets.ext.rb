update_model do
  belongs_to :contact, class_name: 'ContactDB::Contact', foreign_key: :target_id
  belongs_to :list, class_name: 'ContactDB::List', foreign_key: :target_id

  def contact?
    target_type == 0
  end

  def list?
    target_type == 1
  end

  # required for making UGC api calls
  def target_type_name
    case target_type
    when 0
      'CONTACT'
    when 1
      'LIST'
    else
      raise("unknown target_type #{target_type}")
    end
  end
end
