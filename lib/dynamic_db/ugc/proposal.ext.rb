update_model do
  belongs_to :creator_user, class_name: 'AuthDB::User'
  belongs_to :updating_user, class_name: 'AuthDB::User'

  def primary_proposal_contact
    unless defined?(@primary_proposal_contact)
      @primary_proposal_contact = proposal_contacts.detect(&:primary?) || proposal_contacts.first
    end
    @primary_proposal_contact
  end

  def primary_contact_id
    primary_proposal_contact.try(:contact_id)
  end

  def primary_solicitor
    unless defined?(@primary_solicitor)
      @primary_solicitor = proposal_solicitors.detect(&:primary?) || proposal_solicitors.first
    end
    @primary_solicitor
  end
end
