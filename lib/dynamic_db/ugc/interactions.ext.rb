update_model do
  HASH_FIELDS = [
    'primary_target_type',
    'primary_target_id',
    'primary_solicitor_name',
    'interaction_type',
    # 'date_occurred',
    'summary',
    'text'
  ]

  belongs_to :author_user, class_name: 'AuthDB::User'
  belongs_to :creator_user, class_name: 'AuthDB::User'
  belongs_to :updating_user, class_name: 'AuthDB::User'

  def author_affiliation
    unless defined?(@author_affiliation)
      @author_affiliation = author_remote_user_id ? AuthDB::Affiliation.where(organization_id: oid, remote_user_id: author_remote_user_id).first : nil
    end
    @author_affiliation
  end

  def author_identity
    unless defined?(@author_identity)
      @author_identity = author_remote_user_id ? ContactDB::Identity.where(oid: oid, type: 1, value: author_remote_user_id).first : nil
    end
    @author_identity
  end

  def primary_interaction_target
    unless defined?(@primary_interaction_target)
      @primary_interaction_target = interaction_targets.detect(&:primary?) || interaction_targets.first
    end
    @primary_interaction_target
  end

  def primary_target_type
    primary_interaction_target.try(:target_type)
  end

  def primary_target_id
    primary_interaction_target.try(:target_id)
  end

  def primary_target_type_name
    primary_interaction_target.try(:target_type_name)
  end

  def primary_contact_remote_id
    return nil unless primary_interaction_target
    return nil unless primary_interaction_target.contact?

    primary_interaction_target.contact&.remote_id
  end

  def primary_solicitor
    unless defined?(@primary_solicitor)
      @primary_solicitor = interaction_solicitors.first
    end
    @primary_solicitor
  end

  def primary_solicitor_name
    primary_solicitor&.name
  end

  def primary_solicitor_user_id
    primary_solicitor&.user_id
  end

  def to_md5
    md5 = Digest::MD5.new

    HASH_FIELDS.each do |field|
      md5.update(field)
      md5.update(public_send(field).to_s)
    end

    md5.digest.unpack('h*').first
  end
end
