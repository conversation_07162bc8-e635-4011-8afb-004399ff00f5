update_model do
  belongs_to :user, class_name: 'ContactDB::Contact'
  belongs_to :contact, class_name: 'ContactDB::Contact'

  def identity
    unless defined?(@identity)
      @identity = remote_user_id ? ContactDB::Identity.where(oid: proposal.oid, type: 1, value: remote_user_id).first : nil
    end
    @identity
  end

  def primary?
    type == 'PRIMARY'
  end

  def remote_id
    identities.detect { |identity| identity.type == 1 }.try(:value)
  end
end
