# frozen_string_literal: true

DynamicActiveModelGenerator.create_database_models('volunteers')

class VolunteerDB::Assignment
  belongs_to :prospect_contact, class_name: 'ContactDB::Contact'

  def solicitor_compound_id
    if solicitor
      "pool:#{pool_id}-solicitor:#{solicitor.contact_id}-assignment:#{id}"
    else
      nil
    end
  end

  def prospect_compound_id
    "pool:#{pool_id}-prospect:#{prospect_contact_id}-assignment:#{id}"
  end

  def fetch_solicitor_es_doc
    if solicitor
      ESContactHelpers.request_doc_by_id(:assignment, solicitor_compound_id, oid)
    else
      nil
    end
  end

  def fetch_prospect_es_doc
    ESContactHelpers.request_doc_by_id(:assignment, prospect_compound_id, oid)
  end
end

class VolunteerDB::Solicitor
  belongs_to :contact, class_name: 'ContactDB::Contact'

  def solicitor_compound_id
    "pool:#{pool_id}-solicitor:#{contact_id}-assignment:null"
  end

  def fetch_solicitor_es_doc
    ESContactHelpers.request_doc_by_id(:assignment, solicitor_compound_id, oid)
  end

  def remote_id
    contact.remote_id
  end
end

class VolunteerDB::SecondaryProspect
  belongs_to :prospect_contact, class_name: 'ContactDB::Contact'
end


class VolunteerDB::AssignmentsHistory
  belongs_to :prospect_contact, class_name: 'ContactDB::Contact'
  belongs_to :solicitors_history, foreign_key: :solicitor_id, primary_key: :solicitor_id
  belongs_to :solicitor, foreign_key: :solicitor_id

  def solicitor_valid?
    available_solicitor.nil? || !available_solicitor&.contact&.remote_id.nil?
  end

  def available_solicitor
    solicitor || solicitors_history
  end
end

class VolunteerDB::SolicitorsHistory
  belongs_to :contact, class_name: 'ContactDB::Contact'
end