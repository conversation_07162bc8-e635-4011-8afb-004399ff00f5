# frozen_string_literal: true

DynamicActiveModelGenerator.create_database_models('emma')

require 'encrypted-field'

EncryptedField::Config.configure do
  emma_secret_key_proc = Proc.new do
    secrets = EnvironmentLoader.instance.get_secret_value(EnvironmentLoader.instance.create_aws_client, 'emma')
    Base64.decode64(secrets['com.et.emma.secret_key'])
  end

  add_policy_without_iv :emma,
                        'aes-256-ecb',
                        emma_secret_key_proc,
                        prefix_with_policy_name: false
end

class EmmaDB::Account
  include EncryptedField

  encrypted_field :private_api_key_plain_text, :emma, :private_api_key, :emma
end
