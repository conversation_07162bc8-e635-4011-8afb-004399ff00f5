class EmsClient < BaseApiClient
  def get_templates
    res = request(:get, '/ems/templates')
    JSON.parse(res.body)
  end

  def get_template(slug)
    res = request(:get, "/ems/templates/slug/#{slug}")
    JSON.parse(res.body)
  end

  # update a template with the given slug, payload ex:
  # {
  #     name: string
  #     template_html: plaintext
  #     template_text: plaintext
  # }
  def put_template(slug, payload = {})
    require 'base64'

    payload[:template_html] = Base64.strict_encode64(payload[:template_html]) if payload[:template_html]
    payload[:template_text] = Base64.strict_encode64(payload[:template_text]) if payload[:template_text]

    res = request(:put, "/ems/templates/slug/#{slug}", payload)

    case res
    when Net::HTTPNoContent
      true
    else
      false
    end
  end

  def bulk_create(oid, payload)
    request(:post, '/ems/notifications/bulk_create', payload.merge(organization_id: oid))
  end
end
