class TableView
  include CommandLineHelpers

  DEFAULT_OPTIONS = {
    column_separator: ' | ',
    line_separator: "\n",
    table_separator: "\n",
    use_ansi: true,
    row_type: :hash, # or :object
    formatters: {} # format heading values. Hash of headings value => Proc formatter
  }

  attr_reader :headings,  # hash key (heading name), value (key in data to display)
              :options    # display options like column_separator (default ' | '), use_ansi default true

  def initialize(headings, options = {}, heading_formatter={})
    @headings = headings
    @options = DEFAULT_OPTIONS.merge(options)
  end

  def self.render_db_results(active_record_collection, options = {})
    return if !active_record_collection

    reject_columns = options.fetch(:reject_columns, [])

    headings = TableView.create_default_headings(
      active_record_collection
        .first
        .attributes
        .keys
        .map(&:to_sym)
        .reject{ |c| reject_columns.include?(c) } 
    )

    options[:type] = :object

    view = TableView.new(headings, options)
    view.render(active_record_collection.first.class.table_name => active_record_collection)
  end

  # Converts given hash keys or array to a new headings object with the same key as their display value
  def self.create_default_headings(headings)
    default_headings =
      if headings.is_a?(Hash) 
        Hash[headings.keys.zip(headings.keys)]
      elsif headings.is_a?(Array)
        Hash[headings.zip(headings)]
      else
        {}
      end

    default_headings
  end


  # data is {name => rows, ...}
  # ex: {'Users' => [{id: 1, name: 'Foo'}, {id: 8, name: 'Bar'}], 'Roles' => [{id: 4, name: 'Admin'}]
  def render(data)
    formatted_display_string = create_formatted_display_string(data)

    use_table_separator = false
    data.each do |table_name, rows|
      print(options[:table_separator]) if use_table_separator
      use_table_separator = true

      render_table_name(table_name)
      render_headings(formatted_display_string)

      rows.each { |row| render_row(formatted_display_string, row) }
    end
  end

  def create_formatted_display_string(data)
    max_characters_by_heading = Hash.new(0)

    headings.keys.each do |heading_name|
      max_characters_by_heading[heading_name] = heading_name.to_s.size
    end

    data.each do |_, rows|
      rows.each do |row|
        headings.each do |heading_name, hash_key|
          size = row_value(row, hash_key).to_s.size
          max_characters_by_heading[heading_name] = size if max_characters_by_heading[heading_name] < size
        end
      end
    end

    max_characters_by_heading.values.map { |size| "%-#{size}s" }.join(options[:column_separator]) + options[:line_separator]
  end

  def render_table_name(table_name)
    if options[:use_ansi]
      print highlight_string(table_name)
    else
      print table_name.to_s
    end

    print options[:line_separator]
  end

  def render_headings(formatted_display_string)
    if options[:use_ansi]
      print underline_string(sprintf(formatted_display_string, *headings.keys.map(&:to_s)))
    else
      print sprintf(formatted_display_string, *headings.keys.map(&:to_s))
    end
  end

  def render_row(formatted_display_string, row)
    values = headings.values.map { |hash_key| formatted_row_value(row, hash_key) }
    print sprintf(formatted_display_string, *values)
  end

  def formatted_row_value(row, key)
    value = row_value(row, key)
    return default_formatter(value) unless (formatter = @options[:formatters][key])
    
    formatter.call(row_value(row, key))
  end

  def row_value(row, key)
    if options[:row_type] == :object
      row.public_send(key)
    else
      row[key]
    end
  end

  def default_formatter(value)
    value.to_s
  end
end
