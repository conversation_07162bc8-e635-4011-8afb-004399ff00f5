# frozen_string_literal: true

class EmmaClient < BaseApiClient
  def get_members_by_account_id(oid, account_id, offset = 0, limit = 100)
    request(:get, "/emma/v1/member/account?oid=#{oid}&account_id=#{account_id}&offset=#{offset}&limit=#{limit}")
  end

  def get_all_members_by_account_id(oid, account_id)
    offset = 0
    limit = 100
    data = JSON.parse(get_members_by_account_id(oid, account_id, offset, limit).body)
    members = data['items']
    while offset < data['total']
      offset += limit
      data = JSON.parse(get_members_by_account_id(oid, account_id, offset, limit).body)
      members += data['items']
    end
    members
  end
end
