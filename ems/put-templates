#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
    opts.banner = <<-EOF
This script creates or updates the templates defined at the given directory. File names 
are used as the template slug and the extension defines whether the data will populate
template_html or template_text. PUT operation will be used which will create or update
the specified template. Ex.
dir/
  template_slug_1.html
  template_slug_2.txt
will result in the following API calls
PUT templates/slug/template_slug_1
{   
    "name": "Template Slug 1",
    "template_html": "<base64 encoded file content>"
}

PUT templates/slug/template_slug_2
{
    "name": "Template Slug 2",
    "template_text": "<base64 encoded file content>"
}

This can be used with the get-templates script to modify templates of your choice or on its own
to create new templates. The default directory is et_repair_shop/tmp/ems/templates

USAGE:
    ./put-templates -e stage -d ../tmp/ems/templates


EOF

    opts.on('-d', '--dir', 'Directory to read templates from') do |v|
        opts[:dir] = v
    end

end

require 'fileutils'
require File.expand_path('../config/environment', __dir__)

SCRIPT_OPTIONS[:dir] = File.join(TMP_DIR, 'ems', 'templates') unless SCRIPT_OPTIONS[:dir]

dir = SCRIPT_OPTIONS[:dir]

ems_client = EmsClient.create_app_client

templates = {}

# Read the files an parse them into a hash. text and html must be part of the same payload or 
# ems will override existing template with null
Dir.glob(File.join(dir, "*")).each do |file|
    next unless File.file? file

    ext = File.extname(file)
    template_slug = File.basename(file, ext)

    templates[template_slug] ||= {
        name: template_slug.humanize
    }

    file_data = File.read(file)
    templates[template_slug][:template_text] = file_data if ext == ".txt"
    templates[template_slug][:template_html] = file_data if ext == ".html"
end

templates.each do |template_slug, payload|
    print "updating #{template_slug} ... "
    if ems_client.put_template(template_slug, payload)
        puts "done"
    else
        puts "failed"
    end
end
