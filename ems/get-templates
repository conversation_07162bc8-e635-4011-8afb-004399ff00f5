#!/usr/bin/env ruby

# This script downloads the given ems template slugs for the given env to /tmp/templates

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
    opts.banner = <<-EOF
This scipt retrieves specific templates from EMS via their slugs. It is intended as a utility
to assist in creating/updating/viewing EMS templates.

This can be used in conjunction with put-templates to serve as essentially a remote editor for 
templates.

USAGE:
    ./get-templates -e stage -s "template_1, template_2"


EOF

    opts.on('-s', '--template-slugs TEMPLATE_SLUGS', 'Comma delimited template slug list') do |v|
        opts[:template_slugs] = v
            .split(',')
            .map{ |slug| slug.strip }
            .reject{ |slug| slug == nil || slug == "" }
        
        opts[:template_slugs] = nil if opts[:template_slugs].size == 0
    end

    opts.on('-d', '--dir', 'Directory to write templates to') do |v|
        opts[:dir] = v
    end

    opts.require_option(:template_slugs)
end

require 'fileutils'
require File.expand_path('../config/environment', __dir__)

def write_template(dir, filename, data, extension)
    path = File.join(dir, filename) + extension
    puts "writing #{filename} to #{path}"

    File.write(path, data)
end

SCRIPT_OPTIONS[:dir] = File.join(TMP_DIR, 'ems', 'templates') unless SCRIPT_OPTIONS[:dir]

dir = SCRIPT_OPTIONS[:dir]
template_slugs = SCRIPT_OPTIONS[:template_slugs]

FileUtils.mkdir_p dir

puts "searching for slugs #{template_slugs}..."

ems_client = EmsClient.create_app_client

template_slugs.each do |template_slug|
    template = ems_client.get_template(template_slug)
    if !template.dig('id')
        puts "failed to find #{template_slug}: #{template}"
        next
    end
    
    template_text = template.dig("template_text")
    template_html = template.dig("template_html")

    if !template_text && !template_html
        puts "missing template data for #{template_slug}, skipping..."
        next
    end

    write_template(dir, template_slug, template_text, ".txt") if template_text
    write_template(dir, template_slug, template_html, ".html") if template_html
end