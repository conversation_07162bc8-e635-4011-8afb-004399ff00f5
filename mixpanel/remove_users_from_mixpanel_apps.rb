#!/usr/bin/env ruby

# TODO: parameterize me and/or read from config files / secrets manager

BATCH_SIZE = 50 # limit enforced by mixpanel
user_ids = [1, 2, 3, 4, 5]
app_tokens = ['redacted1', 'redacted2']
authorization = 'redacted'

app_tokens.each do |app_token|
    user_ids.each_slice(BATCH_SIZE) do |batch|
        delete_string = batch.map { |user_id| "{ \"$token\": \"#{app_token}\", \"$distinct_id\": #{user_id}, \"$delete\": \"\" }" }.join(',')
        delete_string = "[#{delete_string}]"

        base64encoded_delete_string = Base64.encode64(delete_string).tr("\n", '')

        Faraday.post('https://api.mixpanel.com/engage') do |req|
            req.params['data'] = base64encoded_delete_string
            req.headers['Cookie'] = 'mp__origin=; mp__origin_referrer="http://mixpanel.com/"'
            req.headers['Authorization'] = 'Bearer ' + authorization
            req.headers['Content-Type'] = 'application/json'
            req.headers['Accept'] = 'application/json, text/plain'
        end
    end
end
