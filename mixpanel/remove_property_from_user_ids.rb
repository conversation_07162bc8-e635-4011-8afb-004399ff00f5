#!/usr/bin/env ruby

# TODO: parameterize me and/or read from config files / secrets manager
user_ids = [1, 2, 3 4, 5]
property_name = 'Email'
app_token = 'redacted'
authorization = 'redacted'

user_ids.each do |user_id|
    update_string = '{"$token":"' + app_token + '","$ignore_time":true,"$ip":0,"$distinct_id":"' + user_id.to_s + '","$ignore_alias":true,"$unset":["' + property_name + '"]}'
    base64encoded_update_string = Base64.encode64(email_update_string).tr("\n", '')

    Faraday.post('https://api.mixpanel.com/engage') do |req|
        req.params['data'] = base64encoded_email_update_string
        req.headers['Cookie'] = 'mp__origin=; mp__origin_referrer="http://mixpanel.com/"'
        req.headers['Authorization'] = 'Bearer ' + authorization
        req.headers['Content-Type'] = 'application/json'
        req.headers['Accept'] = 'application/json, text/plain'
    end
end
