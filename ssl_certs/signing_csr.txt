
# Prerequisites:

can edit https://github.com/evertrue/server-chef
- <NAME_EMAIL>:evertrue/server-chef.git
- DNS settigs are in data_bags/dns/zones.json

Have access to OPS vault in 1Pass

Have privileges to edit DNS entries in AWS Route 53 in the old prod account evertrue.

# Signing the CSR

A 3rd party will create a CSR request for us to sign.  This is so that they can server https traffic with 1 of our host names.

Ex: vidyard.com is hosting watch.evertrue.com.

In order for them to do this, we need to create a CNAME to their website.  And generate a signed PEM file for them to install into there web server so that they can provided https.

The CA Let's Encrypt provides free SSL certificates.

The web site below makes the process of signing CSR simple.

Goto: https://gethttpsforfree.com/

The private and public account keys can be found in 1Pass under

"gethttpsforfree.com Private & Public keys"

copy the private key to account.key locally, this is the file they expect you to have while running the openssl commands.
The public key can be copied into the form.
The email to <NAME_EMAIL>.

When validating the domain, I used options 3 DNS.

Update server-chef/data_bags/dns/zones.json with the new value given to you for _acme-challenge.watch.evertrue.com

Commit and push server-chef

Connect to prod-worker-1c

Run chef-client

You can use this command to see if DNS has updated.

dig TXT _acme-challenge.watch.evertrue.com

At the end copy the CERTIFICATE to a file and send it as an attachment back to the 3rd party.

ex: I created a file called "watch.evertrue.com.pem" for vidyard.com.

Once the file has been installed on their end, you can verify it's working by using

curl -v https://watch.evertrue.com

