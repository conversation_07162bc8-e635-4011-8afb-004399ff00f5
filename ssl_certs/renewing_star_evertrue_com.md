

## Public and Private SSL keys

It is super important to use these keys when creating a new certificate.  The iOS app has the public key baked into the app.  And if you change these keys the app's requests to api.evertrue.com will fail.

These are in 1Pass under "2019 *.evertrue.com private key" and "2019 *.evertrue.com signed public key".

## ssls.com

ssls.com is where we create our wildcard cert for "*.evertrue.com"

The username and password are in 1Pass.  You will need to have access to the OPs vault.  Search for ssls.com.

Once you have logged in.  Click on "My SSL".  You'll find the list of active CERTS.  I purchased a new one.  But I believe you can just click Renew.

If domain validation is required use email validation.  <EMAIL> is setup for this.

Once you have renewed or purchased a new certificate it is <NAME_EMAIL>.  The ZIP file will contain the CRT and bundle file (certificate chain).

## Installing the Wildcard Certificate for AWS

You can reimport the certificate using AWS ACM.

```
aws acm import-certificate --profile evertruestage --certificate fileb://STAR_evertrue_com.crt --private-key fileb://star.evertrue.com.key --certificate-chain fileb://STAR_evertrue_com.ca-bundle --certificate-arn arn:aws:acm:us-east-1:034192497236:certificate/6c731ee5-4c3c-42e9-b19d-fabf08865e69
aws acm import-certificate --profile evertrueprod --certificate fileb://STAR_evertrue_com.crt --private-key fileb://star.evertrue.com.key --certificate-chain fileb://STAR_evertrue_com.ca-bundle --certificate-arn arn:aws:acm:us-east-1:************:certificate/6d571d3a-0405-4094-bb41-1bf05834df4d
aws acm import-certificate --certificate fileb://STAR_evertrue_com.crt --private-key fileb://star.evertrue.com.key --certificate-chain fileb://STAR_evertrue_com.ca-bundle --certificate-arn arn:aws:acm:us-east-1:************:certificate/0c0c63fd-f3c9-4599-b894-b982d56278c3
```

This will take care of the AWS accounts that use it.

## WP Engine (www.evertrue.com)

Goto: https://my.wpengine.com/installs/evertrue/ssl_certificates

<NAME_EMAIL> and the password is in 1Pass.

Click on Add Certificate, scroll down to "Import using existing certificate files" and click Import

```
RSA private key: star.evertrue.com.key
Certificate (CRT): use command below, you need to set this to the crt and the bundle.
cat STAR_evertrue_com.crt STAR_evertrue_com.ca-bundle | pbcopy
paste this into the Certificate (CRT) text box
```

Now, click Next and complete the process.

