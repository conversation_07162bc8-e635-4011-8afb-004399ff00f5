#!/usr/bin/env ruby

require 'json'
require 'time'
require 'shellwords'

START_OF_STRIPE_RESOURCE = /com.et.stripe.resources.StripeResource: Full input: {/
END_OF_STRIPE_RESOURCE = /^INFO/

invoice_payments = []

Dir.glob('payments-public-*') do |log_file|
  str = nil
  File.foreach(log_file) do |line|
    if str
      if END_OF_STRIPE_RESOURCE.match(line)
        payload = JSON.parse(str)
        str = nil

        if payload['type'] == 'invoice.payment_succeeded'
          invoice_payments << payload
        end
      else
        str << line
      end
    elsif START_OF_STRIPE_RESOURCE.match(line)
      str = "{\n"
    end
  end
end

invoice_payments.sort! do |a, b|
  a['created'] <=> b['created']
end

start_time = Time.xmlschema('2018-12-28T00:00:00Z').to_i

invoice_payments.select! do |payload|
  payload['created'] >= start_time
end

charge_ids = invoice_payments.map { |payload| payload['data']['object']['charge'] }
charge_ids.uniq!

# existing_ids = File.read('existing_ids').split("\n")

# missing_ids = charge_ids - existing_ids
# puts "missing ids: #{missing_ids.size}"
# puts missing_ids.to_json

endpoint = 'http://localhost:9292/payments-public/v1/stripe/hook'

invoice_payments.each do |payload|

  cmd = "curl -H 'Content-Type: application/json' -d #{Shellwords.escape(payload.to_json)} #{endpoint}"
  puts cmd
end
