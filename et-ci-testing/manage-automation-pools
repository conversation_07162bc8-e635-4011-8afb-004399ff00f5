#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

POOL_COUNT = 21

POOL_TYPE_VOLUNTEER = 'POOL'
POOL_TYPE_TEAM = 'TEAM'
POOL_TYPES = [POOL_TYPE_VOLUNTEER, POOL_TYPE_TEAM]
@pool_types = POOL_TYPES.dup

ACTION_CREATE = 'CREATE'
ACTION_DELETE = 'DELETE'
ACTIONS = [ACTION_CREATE, ACTION_DELETE]

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on("--rm") do |v|
    opts[:rm_only] = true
    @pool_types = [POOL_TYPE_TEAM]
  end

  opts.on("--vol") do |v|
    opts[:vol_only] = true
    @pool_types = [POOL_TYPE_VOLUNTEER]
  end

  opts.on("--rm-stage-group-id STAGE_GROUP_ID") do |v|
    opts[:rm_stage_group_id] = v
  end

  opts.on("--vol-stage-group-id STAGE_GROUP_ID") do |v|
    opts[:vol_stage_group_id] = v
  end

  opts.on("--action ACTION") do |v|
    v_normalized = v.upcase
    opts.display_error_and_exit("invalid action #{v}") unless ACTIONS.include?(v_normalized)
    opts[:action] = v_normalized
  end

  opts.require_option(:action)
end

@pool_types.each do |type|
    if type == POOL_TYPE_VOLUNTEER
        SCRIPT_OPTIONS.display_error_and_exit("missing vol_stage_group_id") unless SCRIPT_OPTIONS[:vol_stage_group_id]
    else
        SCRIPT_OPTIONS.display_error_and_exit("missing rm_stage_group_id") unless SCRIPT_OPTIONS[:rm_stage_group_id]
    end
end

require File.expand_path('../config/environment', __dir__)

@assignments_client = AssignmentsClient.create_app_client

def get_stage_group_id(pool_type)
    if pool_type == POOL_TYPE_VOLUNTEER
        SCRIPT_OPTIONS[:vol_stage_group_id]
    else
        SCRIPT_OPTIONS[:rm_stage_group_id]
    end
end

def handle_action(opts={})
    if SCRIPT_OPTIONS[:action] == ACTION_CREATE
        handle_action_create(opts)
    else
        handle_action_delete(opts)
    end
end

def handle_action_create(opts={})
    print "creating #{opts[:pool_name]}, pool-id="

    resp = @assignments_client.post_pool(
        SCRIPT_OPTIONS[:oid], 
        opts[:pool_type], 
        opts[:pool_name], 
        opts[:stage_group_id]
    )

    if resp.kind_of?(Net::HTTPSuccess)
        body = JSON.parse(resp.body)
        puts body["id"]
    else 
        puts resp.body
    end
end

def handle_action_delete(opts={})
    print "deleting #{opts[:pool_name]} pool-id=#{opts[:pool_id]} ... "

    unless opts[:pool_id]
        puts "skipping #{opts[:pool_name]}, pool not found"
        return 
    end

    resp = @assignments_client.delete_pool(SCRIPT_OPTIONS[:oid], opts[:pool_id])

    if resp.kind_of?(Net::HTTPSuccess)
        puts "done"
    else 
        puts resp.body
    end
end

def get_pool_id(pool_name, pools)
    return nil unless pools
    
    pool = pools.detect{ |pool| pool["name"] == pool_name }
    return nil unless pool

    pool["id"]
end

pools = nil

if SCRIPT_OPTIONS[:action] == ACTION_DELETE
    puts "fetching all pools for oid=#{SCRIPT_OPTIONS[:oid]}"
    pools = @assignments_client.get_all_pools(SCRIPT_OPTIONS[:oid], 20, @pool_types)
end

@pool_types.each do |pool_type|
    stage_group_id = get_stage_group_id(pool_type)
    pool_base_name = "Automation #{pool_type.downcase.capitalize} "

    POOL_COUNT.times do |i|
        pool_count = i + 1
        pool_name = "#{pool_base_name}#{pool_count}"
        handle_action({
            pool_name: pool_name,
            pool_type: pool_type,
            stage_group_id: stage_group_id,
            pool_id: get_pool_id(pool_name, pools)
        })
    end
end