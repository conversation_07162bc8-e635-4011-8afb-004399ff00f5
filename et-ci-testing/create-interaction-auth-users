#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

USER_AFFILIATIONS = {
    "<EMAIL>": "automation_4",
    "<EMAIL>": "automation_27"
}

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
    opts.add_oid_option(true)
end

require File.expand_path('../config/environment', __dir__)

auth_client = AuthClient.create_client_with_app_creds

USER_AFFILIATIONS.each do |email, remote_id|
    user = JSON.parse(auth_client.lookup_by_email(email).body)
    unless user["affiliations"].any?{ |affiliation| affiliation["organization_id"] == SCRIPT_OPTIONS[:oid] && affiliation["remote_user_id"] == remote_id }
        puts "create_affiliation(#{SCRIPT_OPTIONS[:oid]}, user_id: #{user["id"]}, remote_user_id: #{remote_id})"
        auth_client.create_affiliation(SCRIPT_OPTIONS[:oid], user_id: user["id"], remote_user_id: remote_id)
    end
end