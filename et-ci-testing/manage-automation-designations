#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

ACTION_CREATE = "CREATE"
ACTION_DELETE = "DELETE"

DESIGNATION_NAMES = [
    'Automation Designation 1',
    'Automation Designation 2'
]

DATA_TYPES = [
    'PROPOSAL',
    'INTERACTION'
]

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
    opts.add_oid_option(true)

    opts.on("--create") do 
        opts[:action] = ACTION_CREATE
    end

    opts.on("--delete") do 
        opts[:action] = ACTION_DELETE
    end

    opts.require_option(:action)
end

require File.expand_path('../config/environment', __dir__)

@ugc_client = UgcClient.create_app_client
@designations = []

def create_designation(data_type, name)
    oid = SCRIPT_OPTIONS[:oid]
    print "creating oid=#{oid}  data-type=#{data_type}  designation-name=#{name} ... " 
    resp = @ugc_client.create_designation(oid, data_type, name)
    raise resp.body unless resp.kind_of?(Net::HTTPSuccess)
    puts 'done'
end

def delete_designation(data_type, name)
    oid = SCRIPT_OPTIONS[:oid]
    id = get_designation_id(data_type, name)
    
    print "deleting oid=#{oid}  data-type=#{data_type}  designation-name=#{name} designation-id=#{id}... " 
    unless id
        puts "skipping #{name}, designation not found"
        return
    end

    resp = @ugc_client.delete_designation(oid, data_type, id)
    raise resp.body unless resp.kind_of?(Net::HTTPSuccess)

    puts 'done'
end

def get_designation_id(data_type, name)
    designation = @designations.detect{ |designation| designation["name"] == name && designation["data_type"] == data_type }
    return nil unless designation
    designation["id"]
end


# If we are deleting, need to get the ids associated with names
if SCRIPT_OPTIONS[:action] == ACTION_DELETE
    DATA_TYPES.each do |data_type|
        resp = @ugc_client.get_designations(SCRIPT_OPTIONS[:oid], data_type)
        raise resp.body unless resp.kind_of?(Net::HTTPSuccess)
        
        @designations += JSON.parse(resp.body)
    end
end

DATA_TYPES.each do |data_type|
    DESIGNATION_NAMES.each do |name|
        if SCRIPT_OPTIONS[:action] == ACTION_CREATE
            create_designation(data_type, name)
        else
            delete_designation(data_type, name)
        end
    end
end

