---
_credentials: &credentials_default
  source_profile: evertruedev
_ecr: &ecr_default
  repository_name: evertrue/et-repairshop-task
_ecs: &ecs_default
  task_definition:
    name: et-repairshop-task
    container_name: web

staging:
  credentials:
    <<: *credentials_default
    assume_role_arn: arn:aws:iam::034192497236:role/PlatformEngineersAccessRole
  ecr:
    <<: *ecr_default
  ecs:
    <<: *ecs_default
    cluster:
      api: api-ecs-cluster-stage
      importer_worker: importer-worker-ecs-cluster-stage
      worker: worker-ecs-cluster-stage

production:
  credentials:
    <<: *credentials_default
    assume_role_arn: arn:aws:iam::923017004626:role/PlatformEngineersAccessRole
  ecr:
    <<: *ecr_default
  ecs:
    <<: *ecs_default
    cluster:
      api: api-ecs-cluster-prod
      importer_worker: importer-worker-ecs-cluster-prod
      worker: worker-ecs-cluster-prod
