# frozen_string_literal: true

# Set up gems listed in the Gemfile.
ENV['BUNDLE_GEMFILE'] ||= File.expand_path('../Gemfile', __dir__)
ENV['RAILS_ENV'] ||= 'development'
ENV['AWS_REGION'] ||= 'us-east-1'

require 'bundler/setup'

BASE_DIR = Pathname.new(File.expand_path('..', __dir__))
TMP_DIR = BASE_DIR.join('tmp')
Dir.chdir BASE_DIR
$LOAD_PATH << "#{BASE_DIR}/lib"

Bundler.require(:default, ENV['RAILS_ENV'])

require 'process_logger'
LOG = ProcessLogger.new(ENV['RAILS_ENV'], BASE_DIR).create_logger

require 'environment_loader'
EnvironmentLoader.load

Thread.abort_on_exception = true

real_env_name =
  if ENV['RAILS_ENV'] =~ /^old_(.*)/
    real_env_name = $1
  else
    ENV['RAILS_ENV']
  end

APP_ENV_PREFIX =
  case real_env_name
  when 'production'
    'prod'
  when 'staging'
    'stage'
  when 'development'
    'dev'
  else
    real_env_name
  end

def load_yaml_file(file)
  YAML.load_file(file, aliases: true)
end

ES_CONFIG = load_yaml_file(BASE_DIR.join('config', 'elasticsearch.yml'))[real_env_name]
ECS_CONFIG = load_yaml_file(BASE_DIR.join('config', 'ecs.yml'))[real_env_name] || {}
ECS_CONFIG['tasks'] = load_yaml_file(BASE_DIR.join('config', 'ecs-tasks.yml'))['tasks']
KS_CONFIG = load_yaml_file(BASE_DIR.join('config', 'cassandra.yml'))[real_env_name]
REDIS_CONFIG = load_yaml_file(BASE_DIR.join('config', 'redis.yml'))[real_env_name]
MEMCACHE_CONFIG = load_yaml_file(BASE_DIR.join('config', 'memcache.yml'))[real_env_name]
SPARK_CONFIG = load_yaml_file(BASE_DIR.join('config', 'spark.yml'))[real_env_name]
SQS_CONFIG = load_yaml_file(BASE_DIR.join('config', 'sqs.yml'))[real_env_name]
VAULT_CONFIG = load_yaml_file(BASE_DIR.join('config', 'vault.yml'))[real_env_name]
COGNITO_CONFIG = load_yaml_file(BASE_DIR.join('config', 'cognito.yml'))[real_env_name]
SLACK_CONFIG = load_yaml_file(BASE_DIR.join('config', 'slack.yml'))

ES_URL = ES_CONFIG ? 'http://' + ES_CONFIG['hosts'].first + ':' + ES_CONFIG['port'].to_s : nil
SINGULARITY_URL = ENV['RAILS_ENV'] == 'production' ? 'https://burrow.evertrue.com' : 'https://stage-burrow.evertrue.com'

autoload :AuthClient, 'auth_client'
autoload :AssignmentsClient, 'assignments_client'
autoload :AwsAlbHelper, 'aws_alb_helper'
autoload :AwsAppflowHelper, 'aws_appflow_helper'
autoload :AwsCodebuildHelper, 'aws_codebuild_helper'
autoload :AwsEventsHelper, 'aws_events_helper'
autoload :AwsIAMHelper, 'aws_iam_helper'
autoload :AwsSecretSearch, 'aws_secret_search'
autoload :AwsStsHelper, 'aws_sts_helper'
autoload :BaseApiClient, 'base_api_client'
autoload :BlackbaudRENXTClient, 'blackbaud_renxt_client'
autoload :ChronometerClient, 'chronometer_client'
autoload :CommandLineHelpers, 'command_line_helpers'
autoload :ContactClient, 'contact_client'
autoload :CRMClient, 'crm_client'
autoload :CSVReport, 'csv_report'
autoload :DataSourceComparer, 'data_source_comparer'
autoload :DBUtils, 'db_utils'
autoload :DNAClient, 'dna_client'
autoload :DynamicActiveModelGenerator, 'dynamic_active_model_generator'
autoload :ECSHelpers, 'ecs_helpers'
autoload :ECSTaskRunner, 'ecs_task_runner'
autoload :RepairShopECSTaskRunner, 'repair_shop_ecs_task_runner'
autoload :RepairShopScheduler, 'repair_shop_scheduler'
autoload :EmmaApiClient, 'emma_api_client'
autoload :EmmaClient, 'emma_client'
autoload :ESContactHelpers, 'es_contact_helpers'
autoload :ESHelpers, 'es_helpers'
autoload :ESSchema, 'es_schema'
autoload :ETNotify, 'et_notify'
autoload :ETOnePassword, 'et_one_password'
autoload :ETSlack, 'et_slack'
autoload :EventClient, 'event_client'
autoload :ExporterClient, 'exporter_client'
autoload :FacebookClient, 'facebook_client'
autoload :FacebookCollector, 'facebook_collector'
autoload :GithubClient, 'github_client'
autoload :GithubRepo, 'github_repo'
autoload :GraduwayClient, 'graduway_client'
autoload :GraphQLClient, 'graphql_client'
autoload :HTTPClient, 'http_client'
autoload :IDHelper, 'id_helper'
autoload :ImporterClient, 'importer_client'
autoload :ImporterDynamodbCacheHelpers, 'importer_dynamodb_cache_helpers'
autoload :ImporterContactFileComparer, 'importer_contact_file_comparer'
autoload :ImporterFileComparer, 'importer_file_comparer'
autoload :ImporterMemcacheHelpers, 'importer_memcache_helpers'
autoload :ImporterS3Client, 'importer_s3_client'
autoload :JourneyClient, 'journey_client'
autoload :JSONLIterator, 'jsonl_iterator'
autoload :MySQLHelpers, 'mysql_helpers'
autoload :MySQLMasterHelpers, 'mysql_master_helpers'
autoload :MySQLSlowLogHelpers, 'mysql_slow_log_helpers'
autoload :OnePassword, 'one_password'
autoload :Parallelizer, 'parallelizer'
autoload :PositionstackClient, 'positionstack_client'
autoload :PropertyParser, 'property_parser'
autoload :RedisHelpers, 'redis_helpers'
autoload :RepairshopFileUploader, 'repairshop_file_uploader'
autoload :S3OutputFile, 's3_output_file'
autoload :S3Utils, 's3_utils'
autoload :SearchClient, 'search_client'
autoload :SodasClient, 'sodas_client'
autoload :SparkHelpers, 'spark_helpers'
autoload :SimpleStats, 'simple_stats'
autoload :TableauApiClient, 'tableau_api_client'
autoload :TableauLocalClient, 'tableau_local_client'
autoload :TableView, 'table_view'
autoload :TagsClient, 'tags_client'
autoload :TimeHelpers, 'time_helpers'
autoload :UgcClient, 'ugc_client'
autoload :VaultClient, 'vault_client'
autoload :VaultSecretSearch, 'vault_secret_search'
autoload :Windfall, 'windfall'
autoload :WorkerThreads, 'worker_threads'
autoload :EmsClient, 'ems_client'
autoload :AiClient, 'ai_client'

# Auto Generated by DynamicActiveModelGenerator.create_dynamic_db_files
autoload :AuthDB, 'dynamic_db/auth_db'
autoload :BuoyDB, 'dynamic_db/buoy_db'
autoload :CensuDB, 'dynamic_db/census_db'
autoload :ChatDB, 'dynamic_db/chat_db'
autoload :CassandraConnection, 'cassandra_connection'
autoload :ContactDB, 'dynamic_db/contact_db'
autoload :CrmIntegrationDB, 'dynamic_db/crm_integrations_db'
autoload :EmmaDB, 'dynamic_db/emma_db'
autoload :EmDB, 'dynamic_db/em_db'
autoload :EnrichmentDB, 'dynamic_db/enrichment_db'
autoload :EventDB, 'dynamic_db/event_db'
autoload :ExporterDB, 'dynamic_db/exporter_db'
autoload :GiftDB, 'dynamic_db/gift_db'
autoload :GiveDB, 'dynamic_db/give_db'
autoload :GraduwayDB, 'dynamic_db/graduway_db'
autoload :HubDB, 'dynamic_db/hub_db'
autoload :ImporterDB, 'dynamic_db/importer_db'
autoload :IntegrationsDB, 'dynamic_db/integrations_db'
autoload :IntegrationsV2DB, 'dynamic_db/integrations_v2_db'
autoload :JourneyDB, 'dynamic_db/journeys_db'
autoload :LandingPageDB, 'dynamic_db/landing_page_db'
autoload :OutreachDB, 'dynamic_db/outreach_db'
autoload :PledgemineDB, 'dynamic_db/pledgemine_db'
autoload :SodaDB, 'dynamic_db/soda_db'
autoload :SuggestionDB, 'dynamic_db/suggestion_db'
autoload :TagsDB, 'dynamic_db/tag_db'
autoload :ThankviewDB, 'dynamic_db/thankview_db'
autoload :ThankviewCaDB, 'dynamic_db/thankview_ca_db'
autoload :TripDB, 'dynamic_db/trip_db'
autoload :UgcDB, 'dynamic_db/ugc_db'
autoload :VolunteerDB, 'dynamic_db/volunteer_db'
autoload :AiDB, 'dynamic_db/ai_db'
autoload :SearchDB, 'dynamic_db/search_db'

# Cassandra keyspace helpers
autoload :ContactsKS, 'contacts_ks'
autoload :ContactsIndexKS, 'contacts_index_ks'
autoload :PresenceKS, 'presence_ks'

# Schemas
autoload :InteractionSchema, 'schemas/ugc/interaction_schema'

module Contacts
  autoload :Contact, 'protobuf/contacts.pb'
  autoload :Identity, 'protobuf/contacts.pb'
end

module Notes
  autoload :Note, 'protobuf/notes.pb'
end

module Proposals
  autoload :Proposal, 'protobuf/proposals.pb'
end

STATS = SimpleStats.new

if Module.const_defined?(:SCRIPT_OPTIONS)
  if SCRIPT_OPTIONS[:run_remote]

    if SCRIPT_OPTIONS[:file] && !SCRIPT_OPTIONS[:file].include?('s3://')
      s3_url = RepairshopFileUploader.upload_input_file(SCRIPT_OPTIONS[:file])
      file_index = (SCRIPT_OPTIONS.program_arguments.index('-f') || SCRIPT_OPTIONS.program_arguments.index('--file')) + 1
      SCRIPT_OPTIONS.program_arguments[file_index] = s3_url
    end

    RepairShopECSTaskRunner.new.run(SCRIPT_OPTIONS)
    exit
  end

  if SCRIPT_OPTIONS[:schedule_cron_expression]
    RepairShopScheduler.new.schedule(
      SCRIPT_OPTIONS,
      SCRIPT_OPTIONS[:schedule_name] || RepairShopScheduler.get_default_schedule_name(SCRIPT_OPTIONS),
      SCRIPT_OPTIONS[:schedule_description],
      SCRIPT_OPTIONS[:schedule_cron_expression]
    )
    exit
  end

  if SCRIPT_OPTIONS.options.key?(:delete_schedule_name)
    RepairShopScheduler.new.unschedule(
      SCRIPT_OPTIONS[:delete_schedule_name] || RepairShopScheduler.get_default_schedule_name(SCRIPT_OPTIONS)
    )
    exit
  end

  ETNotify.configure do |config|
    config.slack_ids = SCRIPT_OPTIONS[:slack_notify]
    config.message_frequency = SCRIPT_OPTIONS[:slack_message_frequencyy]
  end

  ETNotify.notify_when_finished

  if SCRIPT_OPTIONS[:slack_notify_on_startup]
    LOG.notify('Started with: `' + SCRIPT_OPTIONS.program_arguments.join(' ') + '`', true)
  end

  if SCRIPT_OPTIONS[:file] && RepairshopFileUploader.is_input_file?(SCRIPT_OPTIONS[:file])
    SCRIPT_OPTIONS[:file] = RepairshopFileUploader.download_input_file(SCRIPT_OPTIONS[:file])
  end
end
