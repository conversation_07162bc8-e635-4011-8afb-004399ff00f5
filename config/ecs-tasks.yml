tasks:
  sodas-sync-fb-pages:
    task_definition: sodas-sched-task-12am
  salesloft:
    task_definition: et-datapipeline-salesloft-scheduled-task
  et_scores:
    task_definition: et-datapipeline-et-scores-sync-scheduled-task
  engage_worker:
    task_definition: engage-worker
  journey_alerts:
    task_definition: journeys-alerts-task
  households:
    task_definition: et-datapipeline-household-sync-scheduled-task
  thankview_metrics:
    task_definition: et-datapipeline-thankview-metric-sync-scheduled-task
  journeys_task_updater:
    task_definition: journeys-task-updater
  saved_search_alerter:
    task_definition: saved-search-alerter-v2-scheduled-task
  sodas_connect_facebook_pages:
    task_definition: sodas-sched-task-12am
  facebook_sync:
    task_definition: et-datapipeline-facebook-sync-scheduled-task
  giving_sync:
    task_definition: et-datapipeline-giving-sync-scheduled-task
  purge_organizations:
    task_definition: et-purger-sched-task
    container_overrides:
      name: scheduled
      command:
        - ./bin/purge_organizations
      environment:
        - name: LOG
          value: stdout
  purge_enrichment_windfall_wealth_plus:
    task_definition: enrichment-purging-task
    container_overrides:
      name: web
      command:
        - ./bin/purge
        - -p
        - purge_by_source_wealth_plus
        - -v
        - windfall_wealth_plus
        - -a
