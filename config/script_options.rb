# frozen_string_literal: true
require 'optparse'

class ScriptOptions < OptionParser
  attr_writer :options
  attr_reader :program_name
  attr_reader :program_arguments

  def self.parse!(*args, &block)
    new(*args, &block).parse!
  end

  def initialize(*args)
    super(*args)
    add_default_options
    @program_name = $0.dup
    @program_arguments = ARGV.dup
  end

  def options
    @options ||= {
      environment: ENV['RAILS_ENV'] || ENV['APP_ENV']
    }
  end

  def [](name)
    options[name]
  end

  def []=(name, value)
    options[name] = value
  end

  def required_options
    @required_options ||= []
  end

  def require_option(name)
    required_options << name
  end

  def require_options(*names)
    Array(names).each { |name| require_option name }
  end

  def add_default_options
    on('-h', '--help', 'Prints this help') do
      puts self
      exit
    end

    on('-e', '--environment ENVIRONMENT', 'Environment') do |v|
      options[:environment] = v
    end

    on('--slack-notify [USER]', 'Notify channel, user or user id') do |v|
      options[:slack_notify] = (v || ENV['USER']).split(',')
    end

    on('--slack-message-frequency FREQUENCY', Integer, 'Rate at which to send out slack notifications, default 10 minutes') do |v|
      options[:slack_message_frequencyy] = v
    end

    on('--slack-notify-on-startup', 'On startup send a slack notification with command line args') do
      options[:slack_notify_on_startup] = true
    end
  end

  def add_oid_option(is_required = true)
    on('-o', '--oid OID', Integer, 'OID') do |v|
      options[:oid] = v
    end

    require_option(:oid) if is_required
  end

  def add_oids_option(is_required = true)
    on('-o', '--oids OIDS', 'OID(s)') do |v|
      options[:oids] = v.split(',').map(&:to_i)
    end

    require_option(:oids) if is_required
  end

  def add_input_file(is_required = true)
    on('-f', '--file INPUT_FILE', 'S3 or Local file') do |v|
      options[:file] = v
    end

    require_option(:file) if is_required
  end

  def add_run_remote
    on('--run-remote', 'Run the script remotely in ECS') do |v|
      options[:run_remote] = true
    end
  end

  def add_scheduling_options
    on('--schedule CRON_EXPRESSION', 'Cron expression') do |v|
      options[:schedule_cron_expression] = v
    end

    on('--schedule-name NAME', 'Name of the cron job, must be unique') do |v|
      options[:schedule_name] = v
    end

    on('--schedule-description DESCRIPTION', 'Description of the cron job') do |v|
      options[:schedule_description] = v
    end

    on('--delete-schedule [NAME]', 'Name of the cron job, must be unique') do |v|
      options[:delete_schedule_name] = v
    end
  end

  def configure
    yield self
  end

  def display_error_and_exit(msg, exit_code = 1)
    $stderr.print "Error: #{msg}\n\n"
    $stderr.puts self
    exit exit_code
  end

  def missing_require_option(name, exit_code = 1)
    display_error_and_exit("\"#{name}\" is required", exit_code)
  end

  def validate_options_are_set_or_error(names, exit_code = 1)
    Array(names).each do |name|
      missing_require_option(name, exit_code) if options[name].nil?
    end
  end

  def parse!
    super
    validate_options_are_set_or_error(required_options)
    set_rails_env!
    self
  end

  def set_rails_env!
    ENV['RAILS_ENV'] =
      case options[:environment]
      when 'prod',
           'production'
        'production'
      when 'stage',
           'staging'
        'staging'
      when 'development',
           'thankview',
           'test'
        options[:environment]
      else
        display_error_and_exit("\"#{options[:environment]}\" is not a valid environment")
      end
  end

  def set_default_value(key, value)
    options[key] = value
  end
end
