#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-p', '--platform-id PLATFORM_ID', Integer, 'Graduway Platform ID') do |v|
    opts[:platform_id] = v
  end

  opts.on('-n', '--platform-name PLATFORM_NAME', 'Graduway Platform Name') do |v|
    opts[:platform_name] = v
  end

  opts.on('-k', '--public-key PUBLIC_KEY', 'Graduway Public Key') do |v|
    opts[:public_key] = v
  end

  opts.require_option(:platform_id)
  opts.require_option(:platform_name)
  opts.require_option(:public_key)
end

require File.expand_path('../config/environment', __dir__)

graduway_client = GraduwayClient.create_client_with_app_creds

payload = {
  platform_id: SCRIPT_OPTIONS[:platform_id],
  platform_name: SCRIPT_OPTIONS[:platform_name],
  public_key: SCRIPT_OPTIONS[:public_key]
}

res = graduway_client.create_graduway(SCRIPT_OPTIONS[:oid], payload)
if res.kind_of?(Net::HTTPSuccess)
  puts "setup Graduway"
else
  puts "failed to setup Graduway, #{res.code}/#{res.body}"
end
