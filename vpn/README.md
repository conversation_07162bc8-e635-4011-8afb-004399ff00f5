# Overview
We moved our VPN servers to the `evertrueprod` and `evertruestage` accounts (from the `evertrue` account) and as such there is a new process for granting VPN access.

## Create a VPN profile
- Logon on to evertrueprod-vpn.evertrue.com OR evertruestage-vpn.evertrue.com
- Become `root`
- Head over to `/etc/openvpn/easy-rsa/`
- Run the following:
    - `. vars`
    - `./pkitool <username>` #NOTE: username is first initial, last name. eg. `rday`
    - `cd ../keys/`
    - `cp template.conf <username>.conf`
    - `cp template.conf <username>.ovpn`
    - Edit both `<username>.conf` and `<username>.ovpn` and ensure `cert` and `key` say the right `<username>`
    - Bundle up the `<username>.*` and `ca.crt`: `tar -czvf /tmp/<username>.tar.gz <username>.* ca.crt`
    - `scp` the resulting file to your local machine

## Setup Viscosity for a new user
- Send the bundle(s) created above to the new user
- Screen share with them and help them get Viscosity installed
- Have them unpack the `<username>.tar.gz` (double click it)
- In Viscosity
    - Have them import the new connection with the "+ button --> Import Connection --> From File..." option
    - Choose the `<username>.conf` file
    - Have them edit the connection and rename it to `prod/stage` as appropriate
