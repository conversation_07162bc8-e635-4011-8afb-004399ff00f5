# frozen_string_literal: true

source 'https://rubygems.org'

gem 'activerecord-sqlserver-adapter'
gem 'addressable'
gem 'aws-sdk-appflow'
gem 'aws-sdk-codebuild'
gem 'aws-sdk-cognitoidentityprovider'
gem 'aws-sdk-dynamodb'
gem 'aws-sdk-ecs'
gem 'aws-sdk-eventbridge'
gem 'aws-sdk-iam'
gem 'aws-sdk-s3'
gem 'aws-sdk-elasticloadbalancingv2'
gem 'aws-sdk-secretsmanager'
gem 'aws-sdk-sqs'
gem 'aws-sdk-sts'
gem 'cassandra-driver'
gem 'cassandra-helpers'
gem 'client-api-builder'
gem 'csv'
gem 'csv-utils'
gem 'dalli'
gem 'dotenv'
gem 'dynamic-active-model', require: false
gem 'elasticsearch_scanner'
gem 'encrypted-field'
gem 'fiddle'
gem 'geocoder'
gem 'google-protobuf', require: false
gem 'multi-file-processor'
gem 'mysql2'
gem 'net-ssh'
gem 'nokogiri'
gem 'ostruct'
gem 'parallel'
gem 'protobuf'
gem 'rack'
gem 'rdoc'
gem 'redis'
gem 'rubyzip'
gem 's3grep'
gem 'schema-model'
gem 'schema-normalize'
gem 'slack-ruby-client'
gem 'sorted_set'
