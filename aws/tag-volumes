#!/usr/bin/env ruby

# Purpose: to attach and EBS volume to EC2 instance.

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)

def run_cmd(cmd)
  puts cmd
  JSON.parse(`#{cmd}`)
end

def list_volumes
  run_cmd("aws --profile #{EnvironmentLoader.instance.aws_profile} ec2 describe-volumes")
end

def change_volume_name(volume_id, volume_name)
  cmd = "aws --profile #{EnvironmentLoader.instance.aws_profile} ec2 create-tags --resources #{volume_id} --tags Key=Name,Value=#{volume_name}"
  puts cmd
  system(cmd)
end

def instance_names
  @instance_names ||= Hash[run_cmd("aws --profile #{EnvironmentLoader.instance.aws_profile} ec2 describe-instances --query 'Reservations[].Instances[].[InstanceId, Tags[?Key==`Name`].Value[] | [0]]' --output json")]
end

instance_volumens = {}

list_volumes['Volumes'].each do |volume|
  next if volume['Attachments'].nil? || volume['Attachments'].empty?
  next if volume['Tags'] && volume['Tags'].detect { |t| t['Key'] == 'Name' }

  instance_id = volume['Attachments'].first['InstanceId']
  device = volume['Attachments'].first['Device']

  instance_volumens[instance_id] ||= []
  instance_volumens[instance_id] << {
    volume_id: volume['VolumeId'],
    size: volume['Size'],
    volume_type: volume['VolumeType'],
    device: device
  }
end

sort_volumens_proc = proc do |a, b|
  if a[:device] == '/dev/sda1' # root volume device
    0
  else
    1
  end
end

instance_volumens.each do |instance_id, volumes|
  volumes.sort!(&sort_volumens_proc)
  volumes.each_with_index do |volume, idx|
    if idx == 0
      volume[:name] = instance_names[instance_id]
    else
      volume[:name] = instance_names[instance_id] + '-extra'
    end
  end
end

instance_volumens.each do |instance_id, volumes|
  volumes.each do |volume|
    change_volume_name(volume[:volume_id], volume[:name])
  end
end
