#!/usr/bin/env ruby
# coding: utf-8

require 'erb'
require 'json'

AWS_TOOLS_PROFILE='evertruetools'
AWS_DEV_PROFILE='evertruedev'

def get_project_details(project_name)
  JSON.parse(`aws codebuild batch-get-projects --names #{project_name} --profile=#{AWS_TOOLS_PROFILE}`)['projects'].first
end

def get_repositories(profile)
  JSON.parse(`aws ecr describe-repositories --profile=#{profile}`)['repositories']
end

def get_registry_id(project_name)
  get_repositories(AWS_TOOLS_PROFILE).each do |repository|
    return repository['registryId'] if repository['repositoryName'] =~ /evertrue\/#{project_name}/
  end

  get_repositories(AWS_DEV_PROFILE).each do |repository|
    return repository['registryId'] if repository['repositoryName'] =~ /evertrue\/#{project_name}/
  end

  # Tools account registry_id
  '************'
end

def get_environment_image(project_details)
  project_details['environment']['image']
end

project_dir = ARGV[0]

language =
  if File.exist?("#{project_dir}/pom.xml") # standard java file
    'java'
  elsif File.exist?("#{project_dir}/config/application.rb") # standard rails app file
    'ruby'
  else
    raise('unknown language')
  end

project_name = File.basename(project_dir)

project_details = get_project_details(project_name)
registry_id = get_registry_id(project_name)
environment_image = get_environment_image(project_details)

build_images =
  if language == 'ruby'
    [
      ['Dockerfile-base', 'base'],
      ['Dockerfile', 'api'],
      ['Dockerfile-api', 'api'],
      ['Dockerfile-worker', 'worker'],
      ['Dockerfile-task', 'task']
    ].map do |docker_file, image|
      File.exist?("#{project_dir}/#{docker_file}") ? image : nil
    end.compact
  elsif language == 'java'
    ['api']
  end

build_images.uniq!
deploy_images = build_images.reject { |image| image == 'base' }

template_dir = File.expand_path('../templates', __FILE__)

template = ERB.new(File.read("#{template_dir}/buildspec-#{language}.yml.erb"), nil, '-')

File.open("#{project_dir}/buildspec.yml", 'wb') do |f|
  f.write template.result(binding)
end
