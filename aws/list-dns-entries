#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)

def run_cmd(cmd)
  puts cmd
  JSON.parse(`#{cmd}`)
end

def get_hosted_zones
  run_cmd("aws --profile #{EnvironmentLoader.instance.aws_profile} route53 list-hosted-zones")
end

def priv_hosted_zone_id
  @priv_hosted_zone_id ||= get_hosted_zones['HostedZones'].detect { |zone| zone['Name'] == 'priv.evertrue.com.' }['Id']
end

def instance_private_ips
  @instance_private_ips ||= Hash[run_cmd("aws --profile default ec2 describe-instances --query 'Reservations[].Instances[].[Tags[?Key==`Name`].Value[] | [0], PrivateIpAddress]' --output json")]
end

def get_dns_entries
  run_cmd("aws --profile #{EnvironmentLoader.instance.aws_profile} route53 list-resource-record-sets --hosted-zone-id #{priv_hosted_zone_id}")
end

def get_resolv_dns
  if ENV['RAILS_ENV'] == 'production'
    Resolv::DNS.new(BASE_DIR.join('config', 'prod.resolv.conf').to_s)
  else
    Resolv::DNS.new(BASE_DIR.join('config', 'stage.resolv.conf').to_s)
  end
end

def get_resolv_hosts
  Resolv::Hosts.new('/etc/hosts')
end

def resolver
  @resolver ||= Resolv.new([get_resolv_hosts, get_resolv_dns])
end

def getaddress(host)
  resolver.getaddress(host)
rescue Resolv::ResolvError => e
  $stderr.puts e.message
  nil
end

cmd = "aws --profile #{EnvironmentLoader.instance.aws_profile} ec2 describe-instances --query 'Reservations[].Instances[].[InstanceId, Tags[?Key==`Name`].Value[] | [0], InstanceType, PrivateIpAddress]' --output json"

headings = {
  'Name' => :name,
  'Route53 Value' => :value,
  'DNS Value' => :dns_value,
  'Private IP' => :private_ip
}

rows = []
get_dns_entries['ResourceRecordSets'].each do |record|
  next unless record['Type'] == 'A'

  name = record['Name']
  value = record['ResourceRecords'].map { |r| r['Value'] }.join(', ')
  dns_value = getaddress(name)
  private_ip = instance_private_ips[name.split('.', 2).first]

  name = "* #{name}" if value != dns_value || value != private_ip

  rows << {
    name: name,
    value: value,
    dns_value: dns_value,
    private_ip: private_ip
  }
end

rows.sort! { |a, b| a[:name] <=> b[:name] }

TableView.new(headings).render('DNS' => rows)
