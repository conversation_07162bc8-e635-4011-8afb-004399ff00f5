#!/usr/bin/env ruby

require 'optparse'
require 'shellwords'
require 'json'
require 'time'

def parse_time_offset(str)
  if str =~ /min/
    str.sub(/ *min.*/, '').to_i * 60
  elsif str =~ /hour/
    str.sub(/ *hour.*/, '').to_i * 3600
  elsif str =~ /day/
    str.sub(/ *day.*/, '').to_i * 86400
  else
    nil
  end
end

def time_ago(now, str)
  if offset = parse_time_offset(str)
    time = now - offset
    time - (time.to_i % 60) # round to the start of the minute
  else
    Time.parse(str).utc
  end
end

def run_or_die(cmd)
  res = `#{cmd}`
  raise("command failed with #{$?}, #{cmd}") unless $?.success?
  res
end

def aws_get_log_events(group_name, stream_name, start_time, end_time, aws_profile, next_token)
  cmd = "aws logs get-log-events --log-group-name #{Shellwords.escape(group_name)} --log-stream-name #{Shellwords.escape(stream_name)} --start-from-head"
  # cmd += " --start-time #{start_time.to_i * 1000}" if start_time
  # cmd += " --end-time #{end_time.to_i * 1000}" if end_time
  cmd += " --profile #{aws_profile}" if aws_profile
  cmd += " --next-token #{Shellwords.escape(next_token)}" if next_token
  JSON.parse(run_or_die(cmd))
end

started_at = Time.now.utc

options = {
  start_time: nil,
  end_time: nil,
  group_name: nil,
  stream_name: nil,
  aws_profile: nil,
  log_file: $stdout,
  display_stats: false
}
OptionParser.new do |opts|
  opts.banner = "Usage: #{File.basename(__FILE__)} [options] <group name> <stream name>"

  opts.on("-s", "--start=TIME_EXP", "Start time") do |v|
    options[:start_time] = time_ago(started_at, v)
  end

  opts.on("-e", "--end=TIME_EXP", "End time") do |v|
    options[:end_time] = time_ago(started_at, v)
  end

  opts.on("-p", "--profile=PROFILE", "AWS profile") do |v|
    options[:aws_profile] = v
  end

  opts.on('-o', "--output=OUTPUT_FILE", 'File to stream matching ALB log entries to') do |v|
    f = File.open(v, 'wb')
    f.sync = true
    options[:log_file] = f
  end

  opts.on("--stats", "Display Stats") do
    options[:display_stats] = true
  end
end.parse!

options[:group_name] = ARGV.shift
options[:stream_name] = ARGV.shift

# just forgive the user and swap the values
if options[:start_time] && options[:end_time] && options[:end_time] < options[:start_time]
  $stderr.puts 'swapping start/end times'
  options[:start_time], options[:end_time] = options[:end_time], options[:start_time]
end

raise('no group name specified') unless options[:group_name]
raise('no stream name specified') unless options[:stream_name]

stats = Hash.new(0)
stats[:requests] = 0
stats[:request_time] = 0.0
stats[:entries] = 0
stats[:started_at] = started_at
display_stats_proc = Proc.new do
  stats[:elapsed_time] = Time.now.utc - stats[:started_at]
  $stderr.puts stats.inspect
end

$stop = false
trap("INT") { $stop = true }

next_token = nil
fetch_events_proc = Proc.new do
  time = Time.now

  response = aws_get_log_events(options[:group_name], options[:stream_name], options[:start_time], options[:end_time], options[:aws_profile], next_token)

  stats[:requests] += 1
  stats[:request_time] += Time.now - time
  stats[:entries] += response['events'].size
  next_token = response['nextForwardToken']
  response['events']
end

events = fetch_events_proc.call

while !$stop && events.size > 0
  display_stats_proc.call if options[:display_stats]

  events.each do |event|
    options[:log_file].puts event['message']
  end

  events = fetch_events_proc.call
end

display_stats_proc.call if options[:display_stats]
