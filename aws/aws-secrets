#!/usr/bin/env ruby

# simple/safe utility for working with the SecretString
require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('--save', 'Save secrets to a file') do
    opts[:save] = true
  end

  opts.on('--dotenv', 'Output as .env file format') do
    opts[:dotenv] = true
  end

  opts.on('--no-prompt', 'Disable patch confirmation') do
    opts[:no_prompt] = true
  end

  opts.on('--quiet', 'Silence output on get secrets failure. An exit code is still returned') do
    opts[:quiet] = true
  end
end.parse!

SCRIPT_OPTIONS[:command] = ARGV.shift
raise("invalid command #{SCRIPT_OPTIONS[:command]}") unless ['get', 'patch', 'create'].include?(SCRIPT_OPTIONS[:command])

SCRIPT_OPTIONS[:secret_id] = ARGV.shift
raise("no secret id specified") unless SCRIPT_OPTIONS[:secret_id]

if (file = ARGV.shift)
  SCRIPT_OPTIONS[:file] = file
end

if SCRIPT_OPTIONS[:command] == 'patch'
  raise('no update file specified') unless SCRIPT_OPTIONS[:file]
  raise("file not found, #{SCRIPT_OPTIONS[:file]}") unless File.exist?(SCRIPT_OPTIONS[:file])
end

require File.expand_path('../config/environment', __dir__)
include CommandLineHelpers

def secret_id_to_file_name(secret_id, backup = false)
  file = secret_id.gsub('/', '.') + '.json'
  file += '.' + Time.now.strftime('%Y-%m-%dT%H:%M:%S') + '.bak' if backup
  file
end

def save_secrets(file, secrets)
  File.open(file, 'wb') do |f|
    f.write(JSON.pretty_generate(secrets))
  end
  puts "secrets saved to #{file}"
end

def pretty_print_secrets(secrets)
  if SCRIPT_OPTIONS[:dotenv]
    print secrets.map { |k, v| "#{k}='#{v}'" }.join("\n") + "\n"
  else
    print JSON.pretty_generate(secrets) + "\n"
  end
end

def hash_changes(old_hash, new_hash)
  changes = {}
  new_hash.each do |key, new_value|
    if (old_value = old_hash[key])
      unless old_value == new_value
        changes[key] = {old_value: old_value, new_value: new_value}
      end
    else
      changes[key] = {new_value: new_value}
    end
  end
  changes
end

aws_client = EnvironmentLoader.instance.create_aws_client

if SCRIPT_OPTIONS[:command] != 'create'
  begin 
    existing_secrets = JSON.parse(aws_client.get_secret_value(secret_id: SCRIPT_OPTIONS[:secret_id]).secret_string)
  rescue Aws::SecretsManager::Errors::ResourceNotFoundException => e
    exit(1) if SCRIPT_OPTIONS[:quiet]
    raise e
  end
end

if SCRIPT_OPTIONS[:command] == 'get'
  if SCRIPT_OPTIONS[:save]
    SCRIPT_OPTIONS[:file] ||= secret_id_to_file_name(SCRIPT_OPTIONS[:secret_id])
    save_secrets(SCRIPT_OPTIONS[:file], existing_secrets)
  else
    pretty_print_secrets(existing_secrets)
  end
elsif SCRIPT_OPTIONS[:command] == 'patch'
  save_secrets(secret_id_to_file_name(SCRIPT_OPTIONS[:secret_id], true), existing_secrets)
  
  new_secrets_to_add = JSON.parse(File.read(SCRIPT_OPTIONS[:file]))
  new_secrets = existing_secrets.dup
  new_secrets_to_add.each { |key, new_value| new_secrets[key] = new_value }

  changes = hash_changes(existing_secrets, new_secrets)
  if changes.empty?
    puts "not updating, no changes"
    exit
  else
    print JSON.pretty_generate(changes) + "\n"
    exit unless SCRIPT_OPTIONS[:no_prompt] || yes_no("apply changes to '#{SCRIPT_OPTIONS[:secret_id]}'") == :yes
  end

  # update
  response = aws_client.put_secret_value(secret_id: SCRIPT_OPTIONS[:secret_id], secret_string: new_secrets.to_json)
  puts "updated '#{SCRIPT_OPTIONS[:secret_id]}'"
  print JSON.pretty_generate(response.to_h) + "\n"
  pretty_print_secrets(new_secrets)
elsif SCRIPT_OPTIONS[:command] == 'create'
  response = aws_client.create_secret({
    name: SCRIPT_OPTIONS[:secret_id],
    secret_string: File.read(SCRIPT_OPTIONS[:file])
  })
  puts "created '#{SCRIPT_OPTIONS[:secret_id]}'"
  print JSON.pretty_generate(response.to_h) + "\n"
end