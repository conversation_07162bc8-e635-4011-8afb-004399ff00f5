#!/usr/bin/env ruby

require 'optparse'

options = {
  projects: nil
}
OptionParser.new do |opts|
  opts.banner = 'Usage: ' + File.basename(__FILE__)

  opts.on('-h', '--help', 'Prints this help') do
    puts opts
    exit
  end

  opts.on('-p', '--projects PROJECTS', 'comma separated list of project names') do |v|
    options[:projects] = v.split(',')
  end
end.parse!

require File.expand_path('../config/environment', __dir__)
include CommandLineHelpers
include TimeHelpers

OLDEST_BUILD_TIME = (Time.now - 86_400)
IGNORE_CODEBUILD_PROJECTS = []

def get_project_names(aws_codebuild_client)
  (aws_codebuild_client.list_projects.projects - IGNORE_CODEBUILD_PROJECTS).sort
end

def get_project_builds(aws_codebuild_client, project_name)
  build_ids = aws_codebuild_client.list_builds_for_project(project_name: project_name).ids
  aws_codebuild_client.batch_get_builds(ids: build_ids).builds
end

def filter_build(build)
  build.start_time < OLDEST_BUILD_TIME
end

aws_codebuild_client = AwsCodebuildHelper.create_client

project_build_summaries = {}

(options[:projects] || get_project_names(aws_codebuild_client)).each do |project_name|
  get_project_builds(aws_codebuild_client, project_name).each do |build|
    next if filter_build(build)

    (project_build_summaries[project_name] ||= []) << {
      build_number: build.build_number,
      commit: build.source_version,
      status: build.build_status,
      started: time_in_words(build.start_time),
      current_phase: build.current_phase
    }
  end
end

headings = {
  BUILD_NUMBER: :build_number,
  COMMIT: :commit,
  STATUS: :status,
  STARTED: :started,
  CURRENT_PHASE: :current_phase
}

max_characters_by_heading = Hash.new(0)
headings.keys.each do |heading_name|
  max_characters_by_heading[heading_name] = heading_name.to_s.size
end

project_build_summaries.values.each do |project_summaries|
  project_summaries.each do |summary|
    headings.each do |heading_name, hash_key|
      size = summary[hash_key].to_s.size
      max_characters_by_heading[heading_name] = size if max_characters_by_heading[heading_name] < size
    end
  end
end

DISPLAY_STR_FORMAT = max_characters_by_heading.values.map { |size| "%-#{size}s" }.join(' | ') + "\n"

display_proc = Proc.new do |summary|
  str = sprintf(
    DISPLAY_STR_FORMAT,
    *headings.values.map { |hash_key| summary[hash_key] }
  )
  print str
end

new_line_separate = false
project_build_summaries.each do |project_name, project_summaries|
  print("\n") if new_line_separate
  new_line_separate = true

  print highlight_string(project_name) + "\n"
  print underline_string(sprintf(DISPLAY_STR_FORMAT, *headings.keys.map(&:to_s)))

  project_summaries.each { |summary| display_proc.call(summary) }
end

