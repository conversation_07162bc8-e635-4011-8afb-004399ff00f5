#!/usr/bin/env ruby

require 'optparse'

options = {
  project_name: nil,
  source_version: nil,
  branch: 'master'
}
optparse = OptionParser.new do |opts|
  opts.banner = 'Usage: ' + File.basename(__FILE__)

  opts.on('-h', '--help', 'Prints this help') do
    puts opts
    exit
  end

  opts.on('-p', '--project PROJECT_NAME', 'Required project name') do |v|
    options[:project_name] = v
  end

  opts.on('-b', '--branch BRANCH_NAME', 'Name of branch to build in the project') do |v|
    options[:branch] = v
  end

  opts.on('-c', '--commit FULL_COMMIT_HASH', 'Required full commit hash to build') do |v|
    options[:source_version] = v
    options[:branch] = nil
  end
end
optparse.parse!

if options[:project_name].nil? || (options[:source_version].nil? && options[:branch].nil?)
  $stderr.puts "missing required arguments"
  $stderr.puts ""
  abort(optparse.help)
end

require File.expand_path('../config/environment', __dir__)

def find_build_in_progress_with_commit(aws_codebuild_client, project_name, source_version)
  build_ids = aws_codebuild_client.list_builds_for_project(project_name: project_name).ids

  aws_codebuild_client.batch_get_builds(ids: build_ids).builds.detect do |build|
    build.build_status == 'IN_PROGRESS' && build.source_version == source_version
  end
end

unless options[:source_version]
  options[:source_version] = GithubClient.new.last_commit(options[:project_name], options[:branch])
  raise("no commit found for #{options[:project_name]}/#{options[:branch]}") unless options[:source_version]
end

client = AwsCodebuildHelper.create_client

existing_build = find_build_in_progress_with_commit(client, options[:project_name], options[:source_version])
if existing_build
  puts "build number #{existing_build.build_number} is currently building this version #{options[:source_version]}"
  exit
end

build = client.start_build(options.slice(:project_name, :source_version)).build

puts "started build number #{build.build_number} for #{options[:project_name]} using commit #{options[:source_version]}"
