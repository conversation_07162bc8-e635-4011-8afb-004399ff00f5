#!/usr/bin/env ruby

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:print_only] = false

  opts.on('-p', '--print-only', 'Only output the aws command') do
    opts[:print_only] = true
  end
end

require_relative '../config/environment'
include CommandLineHelpers

cmd = "aws --profile #{EnvironmentLoader.instance.aws_profile} ec2 describe-instances --query 'Reservations[].Instances[].[InstanceId, Tags[?Key==`Name`].Value[] | [0], InstanceType, PrivateIpAddress, State.Name]' --output json"

filter_name = ARGV.shift

rows = []
JSON.parse(`#{cmd}`).each do |result|
  next unless result[4] == 'running'
  next if filter_name && !result[1].to_s.include?(filter_name)

  rows << {
    name: result[1],
    instance_id: result[0],
    type: result[2]
  }
end
rows.sort! { |a, b| a[:name].to_s <=> b[:name].to_s }

if rows.empty?
  puts "no servers found"
  exit
end

headings = {
  'Name' => :name,
  'InstanceID' => :instance_id,
  'Type' => :type
}
TableView.new(headings).render('Instances' => rows)

ec2_instance_id = rows.first[:instance_id]
ec2_instance_id = get_input_or_default("ssh to instance (#{ec2_instance_id})", ec2_instance_id)

cmd = "aws ssm start-session --profile #{EnvironmentLoader.instance.aws_profile} --target #{ec2_instance_id}"
puts(cmd)

exit if SCRIPT_OPTIONS[:print_only]

exec(cmd)
