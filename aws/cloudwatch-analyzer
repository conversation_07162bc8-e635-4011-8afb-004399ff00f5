#!/usr/bin/env ruby

require 'optparse'
require 'time'
require 'uri'
require 'json'

ENV['TZ'] = 'UTC'

def parse_time_offset(str)
  if str =~ /min/
    str.sub(/ *min.*/, '').to_i * 60
  elsif str =~ /hour/
    str.sub(/ *hour.*/, '').to_i * 3600
  elsif str =~ /day/
    str.sub(/ *day.*/, '').to_i * 86400
  else
    nil
  end
end

def time_ago(now, str)
  if offset = parse_time_offset(str)
    time = now - offset
    time - (time.to_i % 60) # round to the start of the minute
  else
    Time.parse(str).utc
  end
end

started_at = Time.now.utc

options = {
  start_time: nil,
  end_time: nil,
  log_file: $stdout,
  display_stats: false
}
OptionParser.new do |opts|
  opts.banner = "Usage: #{File.basename(__FILE__)} [options] <group name> <stream name>"

  opts.on("-s", "--start=TIME_EXP", "Start time") do |v|
    options[:start_time] = time_ago(started_at, v)
  end

  opts.on("-e", "--end=TIME_EXP", "End time") do |v|
    options[:end_time] = time_ago(started_at, v)
  end

  opts.on('-o', "--output=OUTPUT_FILE", 'File to stream matching ALB log entries to') do |v|
    f = File.open(v, 'wb')
    f.sync = true
    options[:log_file] = f
  end

  opts.on("--stats", "Display Stats") do
    options[:display_stats] = true
  end
end.parse!

# 52.73.110.45 - - [27/Oct/2019:10:18:45 +0000] [dw-1235] "GET /contacts/v1/contacts/identities/bulk/id?app_key=ef571795d45d5be4994a3beebbf2fcb9d24466d45cbf6877250ba822420d3c64&provider=EVENTBRITE_ID&auth=ODpGZG9zTHozYmpjZ0xMc1JUUndCbg%3D%3D&oid=636&auth_provider=EvertrueAppToken&identity%5B%5D=judy%40corealign.org HTTP/1.1" 404 122 "-" "EverTrue Http Client (Java)" "Root=1-5db56f05-ce7b39507b5a97e82e7bceb0" 5
REQUEST_FIELDS =
  begin
    not_a_space = '([^ ]+)'
    in_quotes = '"(.*?)"'
    in_square_brackets = '(\[.*?\])'
      
    {
      remote_ip: not_a_space,
      unknown1: not_a_space,
      unknown2: not_a_space,
      request_time_formatted: in_square_brackets,
      thread_id: not_a_space,
      full_request_path_and_action: in_quotes,
      status_code: not_a_space,
      response_content_length: not_a_space,
      unknown3: in_quotes,
      user_agent: in_quotes,
      request_id: in_quotes,
      response_time: not_a_space
    }
  end

class RequestLine < Struct.new(:line, *REQUEST_FIELDS.keys)
  REGEX = Regexp.new(REQUEST_FIELDS.values.join(' '))
  TIME_FORMAT = '[%e/%b/%Y:%H:%M:%S %z]'

  def self.parse(line)
    if (matches = REGEX.match(line))
      new(*matches)
    else
      nil
    end
  end

  def request_method
    full_request_path_and_action.split(' ', 3)[0]
  end

  def request_path
    full_request_path_and_action.split(' ', 3)[1]
  end

  def request_protocol
    full_request_path_and_action.split(' ', 3)[2]
  end

  def request_path_key
    @request_path_key ||= (request_method + ' ' + request_path.split('?', 2).first)
  end

  def request_time
    @request_time ||= Time.strptime(request_time_formatted, TIME_FORMAT)
  end

  def request_started_at
    @request_started_at ||= (request_time - (response_time / 1_000))
  end

  def response_time
    self[:response_time].to_i
  end
end

STATUS_FIELDS =
  begin
    not_a_space = '([^ ]+)'
    in_quotes = '"(.*?)"'
    in_square_brackets = '(\[.*?\])'
      
    {
      log_level: '(WARN|ERROR)',
      request_time_formatted: in_square_brackets,
      thread_id: not_a_space,
      error_msg: '(.*)'
    }

  end

class StatusLine < Struct.new(
        :line,
        *STATUS_FIELDS.keys
      )
  REGEX = Regexp.new(STATUS_FIELDS.values.join(' '))
  TIME_FORMAT = '[%Y-%m-%d %H:%M:%S,%L]'

  def self.parse(line)
    if matches = REGEX.match(line)
      new(*matches)
    else
      nil
    end
  end

  def request_time
    @error_at ||= Time.strptime(request_time_formatted, TIME_FORMAT)
  end
end

class PathStat < Struct.new(
        :path,
        :total,
        :latency
      )

  def add(request_line)
    self.total += 1
    self.latency = request_line.response_time
  end

  def self.create(request_line)
    new(
      request_line.request_path_key,
      0,
      0
    )
  end

  def as_json(opts={})
    Hash[self.class.members.zip(values)]
  end
end

class RequestStat < Struct.new(
        :time_segment,        # granularity of time to capture stats for
        :inflight_requests,   # number of request at during the time_segment
        :completed_requests,  # number of requests that completed during this time_segment
        :total_latency,       # total amount of latency during this time_segment
        :total_errors,        # number of errors for this time segment
        :total_warnings,      # number of warnings for this time segment
        :path_stats
      )

  def add(request_line)
    self.completed_requests += 1
    self.total_latency += request_line.response_time
    (self.path_stats[request_line.request_path_key] ||= PathStat.create(request_line)).add(request_line)
  end

  def self.create(time_segment)
    new(
      time_segment,
      0,
      0,
      0,
      0,
      0,
      {}
    )
  end

  def as_json(opts={})
    hsh = Hash[self.class.members.zip(values)]
    hsh[:path_stats] = hsh[:path_stats].values.map(&:as_json)
    hsh
  end
end

request_stats = {}

def add_request(request_stats, request_line)
  (request_stats[request_line.request_time.to_i] ||= RequestStat.create(request_line.request_time.to_i)).add(request_line)

  if request_line.response_time > 1_000
    time = request_line.request_started_at.to_i
    completed_time = request_line.request_time.to_i
    while time < completed_time
      (request_stats[time] ||= RequestStat.create(time)).inflight_requests += 1
      time += 1
    end
  end
end

def add_status(request_stats, status_line)
  stat = (request_stats[status_line.request_time.to_i] ||= RequestStat.create(status_line.request_time.to_i))

  case status_line.log_level
  when 'WARN'
    stat.total_warnings += 1
  when 'ERROR'
    stat.total_errors += 1
  end
end

request_file = File.open(ARGV.shift, 'rb')

processing_stats = Hash.new(0)
processing_stats[:started_at] = Time.now
display_processsing_stats_proc = Proc.new do
  processing_stats[:took] = Time.now - processing_stats[:started_at]
  $stderr.puts processing_stats.inspect
end

time_range = options[:start_time]..options[:end_time]
stop_after = options[:end_time] + 60

while(!request_file.eof?)
  processing_stats[:lines] += 1
  line = request_file.readline

  if (request_line = RequestLine.parse(line))
    break if request_line.request_time > stop_after
    next unless time_range.cover?(request_line.request_time)
    processing_stats[:requests] += 1
    add_request(request_stats, request_line)
  elsif (status_line = StatusLine.parse(line))
    break if status_line.request_time > stop_after
    next unless time_range.cover?(status_line.request_time)
    processing_stats[:statuses] += 1
    add_status(request_stats, status_line)
  end

  display_processsing_stats_proc.call if options[:display_stats] && (processing_stats[:lines]%10_000) == 0
end

print JSON.pretty_generate(request_stats.values.sort_by(&:time_segment).map(&:as_json)) + "\n"
