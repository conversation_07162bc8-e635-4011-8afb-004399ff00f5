#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)

cmd = "aws --profile #{EnvironmentLoader.instance.aws_profile} ec2 describe-instances --query 'Reservations[].Instances[].[InstanceId, Tags[?Key==`Name`].Value[] | [0], InstanceType, PrivateIpAddress, PublicIpAddress, State.Name]' --output json"

headings = {
  'Name' => :name,
  'InstanceID' => :instance_id,
  'Type' => :type,
  'Private IP' => :private_ip,
  'Public IP' => :public_ip,
  'State' => :state
}

rows = []
JSON.parse(`#{cmd}`).each do |result|
  rows << {
    name: result[1],
    instance_id: result[0],
    type: result[2],
    private_ip: result[3],
    public_ip: result[4],
    state: result[5]
  }
end
rows.sort! { |a, b| a[:name].to_s <=> b[:name].to_s }

TableView.new(headings).render('Instances' => rows)
