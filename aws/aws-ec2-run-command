#!/usr/bin/env ruby

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse!

require_relative '../config/environment'
require 'active_support/core_ext/array'
include CommandLineHelpers

def run_command_safely(cmd)
  output = `#{cmd}`
  raise "command failed: #{cmd}" unless $?.success?
  output
end

def send_command(aws_profile, ec2_instance_ids, command)
  run_command_safely("aws ssm send-command --profile #{aws_profile} --instance-ids #{ec2_instance_ids.join(' ')} --document-name \"AWS-RunShellScript\" --comment \"Run shell script on Linux Instances\" --parameters commands=#{Shellwords.escape(command.join(' '))} --output text --query \"Command.CommandId\"").strip
end

def command_inprogress?(payload)
  payload['CommandInvocations'].all? do |invocation|
    invocation['CommandPlugins'].all? { |plugin| plugin['Status'] == 'InProgress' }
  end
end

def wait_for_command_to_finish(aws_profile, command_id)
  started_at = Time.now
  while true
    output = run_command_safely("aws ssm list-command-invocations --profile #{aws_profile} --command-id #{command_id} --details")
    payload = JSON.parse(output)

    if command_inprogress?(payload)
      elapsed_time = Time.now - started_at
      raise("waited over #{elapsed_time} for the command to finish stopping") if elapsed_time > 20
      sleep 0.5
      next
    end

    return payload
  end
end

cmd = "aws --profile #{EnvironmentLoader.instance.aws_profile} ec2 describe-instances --query 'Reservations[].Instances[].[InstanceId, Tags[?Key==`Name`].Value[] | [0], InstanceType, PrivateIpAddress, State.Name]' --output json"

filter_name = ARGV.shift || raise('must specify a server name')
command = ARGV
raise('no command specified') if command.empty?

rows = []
JSON.parse(`#{cmd}`).each do |result|
  next unless result[4] == 'running'
  next if filter_name && !result[1].to_s.include?(filter_name)

  rows << {
    name: result[1],
    instance_id: result[0]
  }
end
rows.sort! { |a, b| a[:name].to_s <=> b[:name].to_s }

if rows.empty?
  puts "no servers found"
  exit
end

headings = {
  'Name' => :name,
  'InstanceID' => :instance_id
}
TableView.new(headings).render('Instances' => rows)

exit unless yes_no("run (#{command.join(' ')}) on all instances") == :yes

aws_profile = EnvironmentLoader.instance.aws_profile

ec2_instance_ids = rows.map { |r| r[:instance_id] }

ec2_instance_ids.in_groups_of(10, false) do |group|
  command_id = send_command(aws_profile, group, command)
  sleep 1.0
  payload = wait_for_command_to_finish(aws_profile, command_id)

  payload['CommandInvocations'].each do |result|
    puts highlight_string(result['InstanceId']) + "[#{result['CommandPlugins'][0]['ResponseCode']}]:"
    print result['CommandPlugins'][0]['Output'] + "\n"
  end
end
