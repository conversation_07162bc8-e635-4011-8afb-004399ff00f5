#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:job_status] = 'RUNNING'

  opts.on('-s', '--status [JOB_STATUS]', ['RUNNING', 'FILECHECK_RUNNING', 'PREPROCESS_RUNNING', 'IMPORT_RUNNING'], 'Find log for job satus') do |v|
    opts[:job_status] = v
  end
end

require File.expand_path('../config/environment', __dir__)
require 'shellwords'

AWS_LOGS_GROUP_NAME = 'importer-worker-task'

started_at = Time.now.utc

job_id = ARGV.shift

raise("no job id specified") unless job_id

def run_or_die(cmd)
  res = `#{cmd}`
  raise("command failed with #{$?}, #{cmd}") unless $?.success?
  res
end

def aws_get_log_events(group_name, stream_name, aws_profile, next_token)
  cmd = "aws logs get-log-events --log-group-name #{Shellwords.escape(group_name)} --log-stream-name #{Shellwords.escape(stream_name)} --start-from-head"
  cmd += " --profile #{aws_profile}" if aws_profile
  cmd += " --next-token #{Shellwords.escape(next_token)}" if next_token
  JSON.parse(run_or_die(cmd))
end

def aws_filter_log_events(job_id, group_name, aws_profile, start_time, end_time)
  cmd = "aws logs filter-log-events --log-group-name #{Shellwords.escape(group_name)} --start-time #{start_time} --end-time #{end_time} --filter-pattern \"job id #{job_id}\""
  cmd += " --profile #{aws_profile}" if aws_profile
  JSON.parse(run_or_die(cmd))
end

mysql_client_importer = MySQLHelpers.create_client(:importer)

run_stat = mysql_client_importer.query("SELECT * FROM job_status WHERE job_id = #{job_id} AND status = '#{SCRIPT_OPTIONS[:job_status]}'").first
raise("job #{job_id} never ran") unless run_stat

log_event = aws_filter_log_events(job_id, AWS_LOGS_GROUP_NAME, EnvironmentLoader.instance.aws_profile, run_stat['started_at'], run_stat['started_at'] + 10_000)['events'].first
unless log_event
  puts "unable to find the log stream for #{job_id}"
  exit 1
end

puts "log stream name: #{log_event['logStreamName']}"
puts "to download:"
puts "download-cloudwatch-log -p #{EnvironmentLoader.instance.aws_profile} -o importer-worker-task-job-#{job_id}.log #{AWS_LOGS_GROUP_NAME} #{log_event['logStreamName']}"
