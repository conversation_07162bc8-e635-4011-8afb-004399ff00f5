#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)
 
SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:highlight] = true
  opts[:exact_match] = false
  opts[:ignore_case] = false
  opts[:match_keys_only] = false
  opts[:match_values_only] = false
  opts[:output_database_settings] = false

  opts.on('-i', '--ignore-case', 'Ignore case') do
    opts[:ignore_case] = true
  end

  opts.on('-s', '--search SEARCH_REGEX', 'Search criteria') do |v|
    opts[:search] = v
  end

  opts.on('-H', '--disable-highlight', 'Disable highlight') do
    opts[:highlight] = false
  end

  opts.on('--match-keys', 'Match against keys') do
    opts[:match_keys_only] = true
  end

  opts.on('--match-values', 'Match against values') do
    opts[:match_values_only] = true
  end

  opts.on('--database-settings', 'Output database settings') do
    opts[:output_database_settings] =  true
  end

  opts.on('--namespace NAMESPACE', 'Only return secrets whose secret name starts with this namespace') do |v|
    opts[:namespace] = Regexp.new("^" + Regexp.escape("#{v}/"))
  end

  opts.require_option(:search)
end

# disabling options
if SCRIPT_OPTIONS[:match_keys_only] && SCRIPT_OPTIONS[:match_values_only]
  SCRIPT_OPTIONS[:match_keys_only] = false
  SCRIPT_OPTIONS[:match_values_only] = false
end

search_regex =
  begin
    search = SCRIPT_OPTIONS[:search]
    search = Regexp.escape(search) if SCRIPT_OPTIONS[:exact_match]
    if SCRIPT_OPTIONS[:ignore_case]
      Regexp.new(search, true)
    else
      Regexp.new(search)
    end
  end

require File.expand_path('../config/environment', __dir__)
include CommandLineHelpers

def highlight_result(regex, result)
  result.gsub(regex) { |match| highlight_string(match) }
end

def escape_credential(str)
  str.gsub(/[^a-z0-9]/i) do |c|
    '%' + c.bytes.first.to_s(16).upcase.rjust(2, '0')
  end
end

def convert_database_url_to_properties(database_url)
  uri = URI(database_url)
  database_name = uri.path.sub('/', '')

  {
    secret_name: "DATABASE_URL_#{database_name.upcase}",
    database_url: database_url,
    properties: {
      "com.et.#{database_name}.db.host" => uri.host,
      "com.et.#{database_name}.db.name" => database_name,
      "com.et.#{database_name}.db.user" => uri.user,
      "com.et.#{database_name}.db.pass" => Addressable::URI.unescape(uri.password.to_s),
      "com.et.#{database_name}.db.port" => uri.port
    }
  }
end

def convert_properties_to_database_url(search_regex, secrets, adapter)
  connection_options_map = [
    [:host, /^(.*?)\.host$/],
    [:name, /^(.*?)\.name$/],
    [:user, /^(.*?)\.user$/],
    [:pass, /^(.*?)\.pass$/],
    [:port, /^(.*?)\.port$/]
  ]

  properties = {}
  connection_options = {}

  secrets.each do |key, value|
    connection_options_map.each do |name, name_match|
      next unless name_match.match(key)
      group_name = $1
      connection_options[group_name] ||= {}
      connection_options[group_name][name] = value
      properties[group_name] ||= {}
      properties[group_name][key] = value
    end
  end

  connection_options.map do |group_name, options|
    next unless options[:name]
    
    secret_name = "DATABASE_URL_#{options[:name].upcase}"
    database_url = "#{adapter}://#{escape_credential(options[:user])}:#{escape_credential(options[:pass])}@#{options[:host]}:#{options[:port] || 3306}/#{options[:name]}"
    {
      secret_name: secret_name,
      database_url: database_url,
      properties: properties[group_name]
    }
  end.compact
end

def output_database_settings(search_regex, secret_string, adapter = 'mysql2')
  secrets = JSON.parse(secret_string)

  output_proc = Proc.new do |result|
    puts "Ruby settings"
    puts "#{result[:secret_name]} #{result[:database_url]}"
    puts ""
    puts "Java settings"
    result[:properties].each do |prop_name, prop_value|
      puts "#{prop_name} #{prop_value}"
    end
    puts ""
  end

  if secret_string =~ /DATABASE_URL/
    secret_name = nil
    database_url = nil
    secrets.each do |key, value|
      next unless value.is_a?(String)
      next unless key =~ /^DATABASE_URL/
      output_proc.call(convert_database_url_to_properties(value))
    end
  else
    convert_properties_to_database_url(search_regex, secrets, adapter).each(&output_proc)
  end
end

aws_client = EnvironmentLoader.instance.create_aws_client
AwsSecretSearch.new(aws_client).search(search_regex, SCRIPT_OPTIONS[:namespace], SCRIPT_OPTIONS[:match_keys_only], SCRIPT_OPTIONS[:match_values_only]) do |entry, secret, secret_string|
  puts "Found in #{entry.name}"
  if SCRIPT_OPTIONS[:output_database_settings]
    output_database_settings(search_regex, secret_string)
  elsif SCRIPT_OPTIONS[:highlight]
    puts highlight_result(search_regex, secret_string)
  else
    puts secret_string
  end
end
