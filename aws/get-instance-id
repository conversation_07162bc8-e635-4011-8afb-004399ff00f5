#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-n', '--name SERVER_NAME', 'Server name') do |v|
    opts[:server_name] = v
  end

  opts.require_option(:server_name)
end

require File.expand_path('../config/environment', __dir__)

cmd = "aws --profile #{EnvironmentLoader.instance.aws_profile} ec2 describe-instances --query 'Reservations[*].Instances[*].{Instance:InstanceId}' --filters 'Name=tag:Name,Values=#{SCRIPT_OPTIONS[:server_name]}'"

payload = `#{cmd}`

puts JSON.parse(payload)[0][0]['Instance']
