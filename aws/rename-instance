#!/usr/bin/env ruby

# Purpose: to attach and EBS volume to EC2 instance.

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:suffix] = '-old'

  opts.on('-s', '--suffix SUFFIX', Integer, 'Suffix to append to the instance name') do |v|
    opts[:suffix] = v
  end

  opts.on('-n', '--name INSTANCE_NAME', 'Name of the instance to attach the volume') do |v|
    opts[:instance_name] = v
  end

  opts.on('--new-name NEW_NAME', 'Change instance name to this value') do |v|
    opts[:new_name] = v
  end

  opts.require_option(:instance_name)
end

require File.expand_path('../config/environment', __dir__)

def get_new_instance_name
  if SCRIPT_OPTIONS[:new_name]
    SCRIPT_OPTIONS[:new_name]
  else
    SCRIPT_OPTIONS[:instance_name] + SCRIPT_OPTIONS[:suffix]
  end
end

def run_cmd(cmd)
  puts cmd
  JSON.parse(`#{cmd}`)
end

def get_instance_id
  payload = run_cmd("aws --profile #{EnvironmentLoader.instance.aws_profile} ec2 describe-instances --query 'Reservations[*].Instances[*].{InstanceId:InstanceId}' --filters 'Name=tag:Name,Values=#{SCRIPT_OPTIONS[:instance_name]}'")
  payload.empty? ? nil : payload[0][0]['InstanceId']
end

def change_instance_name(instance_id, new_instance_name)
  cmd = "aws --profile #{EnvironmentLoader.instance.aws_profile} ec2 create-tags --resources #{instance_id} --tags Key=Name,Value=#{new_instance_name}"
  puts cmd
  exec(cmd)
end

unless (instance_id = get_instance_id)
  $stderr.puts "unable to lookup instance id for #{SCRIPT_OPTIONS[:instance_name]}"
  exit 1
end

change_instance_name(instance_id, get_new_instance_name)
