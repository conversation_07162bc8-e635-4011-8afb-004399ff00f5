#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)
include CommandLineHelpers

client = AwsAlbHelper.create_client

def rule_conditions_to_s(rule)
  path_rule = rule[:conditions].detect { |condition| condition.field == 'path-pattern' }
  method_rule = rule[:conditions].detect { |condition| condition.field == 'http-request-method' }
  host_rule = rule[:conditions].detect { |condition| condition.field == 'host-header' }

  path = path_rule ? path_rule.values.first : '<no path set>'
  methods = method_rule ? method_rule.http_request_method_config.values.join(',') : nil
  host = host_rule ? host_rule.values.first : nil

  if host
    host
  elsif methods
    "#{path} [#{methods}]"
  else
    path
  end
end

priorities_by_load_balancer = {}

AwsAlbHelper.load_balancers(client).each do |load_balancer|
  load_balancer_name = load_balancer[:load_balancer_name]
  dns_name = load_balancer[:dns_name]
  load_balancer_arn = load_balancer[:load_balancer_arn]

  priorities = (priorities_by_load_balancer["#{load_balancer_name} (#{dns_name})"] = [])

  AwsAlbHelper.listeners(client, load_balancer_arn).each do |listener|
    listener_arn = listener[:listener_arn]

    AwsAlbHelper.rules(client, listener_arn).each do |rule|
      next if rule[:priority] == 'default'

      priorities << {
        priority: rule[:priority],
        condition: rule_conditions_to_s(rule)
      }
    end
  end
end

headings = {
  PRIORITY: :priority,
  CONDITION: :condition
}

table_view = TableView.new(headings)
table_view.render(priorities_by_load_balancer)
