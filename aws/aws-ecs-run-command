#!/usr/bin/env ruby

# could use the aws gem, but avoiding that for now so that we do not have to install gems to get this to work

require 'json'
require 'shellwords'
require 'set'

AWS_ENVS = {
  'prod' => 'evertrueprod',
  'stage' => 'evertruestage'
}

CLUSTERS = [
  'api',
  'worker'
]

aws_env = ARGV.shift
product = ARGV.shift
command = ARGV

raise "invalid aws environment #{aws_env}" unless AWS_ENVS.has_key?(aws_env)
raise "no command to run" if command.empty?

def run_command_safely(cmd)
  output = `#{cmd}`
  raise "command failed: #{cmd}" unless $?.success?
  output
end

def get_tasks_for_service(aws_env, aws_profile, product)
  tasks = {}
  CLUSTERS.each do |service_type|
    begin
      cluster_name = "#{service_type}-ecs-cluster-#{aws_env}"
      service_name = get_service_name(product, service_type)
      output = run_command_safely("aws ecs list-tasks --cluster #{cluster_name} --profile #{aws_profile} --service-name #{Shellwords.escape(service_name)} 2> /dev/null")
      JSON.parse(output)['taskArns'].each do |arn|
        task_id = arn.sub(/.*task\//, '')
        tasks[task_id] = {cluster_name: cluster_name, service_name: service_name, task_id: task_id}
      end
    rescue RuntimeError => e
    end
  end
  tasks
end

def describe_task(aws_profile, cluster_name, task_id)
  output = run_command_safely("aws ecs describe-tasks --cluster #{cluster_name} --profile #{aws_profile} --tasks #{task_id}")
  JSON.parse(output)['tasks'].first
end

def describe_container_service(aws_profile, cluster_name, instance_arn)
  output = run_command_safely("aws ecs describe-container-instances --cluster #{cluster_name} --profile #{aws_profile} --container-instances #{instance_arn}")
  JSON.parse(output)['containerInstances'].first
end

def get_service_name(product, service_type)
  case product
  when 'graphql-server'
    'graphql-server-service'
  else
    "service/#{product}-#{service_type}-service"
  end
end

def send_command(aws_profile, ec2_instance_ids, command)
  run_command_safely("aws ssm send-command --profile #{aws_profile} --instance-ids #{ec2_instance_ids.join(' ')} --document-name \"AWS-RunShellScript\" --comment \"Run shell script on Linux Instances\" --parameters commands=#{Shellwords.escape(command.join(' '))} --output text --query \"Command.CommandId\"").strip
end

def command_inprogress?(payload)
  payload['CommandInvocations'].all? do |invocation|
    invocation['CommandPlugins'].all? { |plugin| plugin['Status'] == 'InProgress' }
  end
end

def wait_for_command_to_finish(aws_profile, command_id)
  started_at = Time.now
  while true
    output = run_command_safely("aws ssm list-command-invocations --profile #{aws_profile} --command-id #{command_id} --details")
    payload = JSON.parse(output)

    if command_inprogress?(payload)
      elapsed_time = Time.now - started_at
      raise("waited over #{elapsed_time} for the command to finish stopping") if elapsed_time > 20
      sleep 0.5
      next
    end

    return payload
  end
end

aws_profile = AWS_ENVS[aws_env]
ec2_instance_ids = Set.new
tasks = get_tasks_for_service(aws_env, aws_profile, product)
tasks.each do |task_id, details|
  task_desc = describe_task(aws_profile, details[:cluster_name], task_id)
  instance_arn = task_desc['containerInstanceArn']
  details[:instance_arn] = instance_arn

  ec2_instance_id = describe_container_service(aws_profile, details[:cluster_name], instance_arn)['ec2InstanceId']
  details[:ec2_instance_id] = ec2_instance_id
  ec2_instance_ids << ec2_instance_id
end

command_id = send_command(aws_profile, ec2_instance_ids.to_a, ['sudo', 'docker', 'ps'])
sleep 1.0
payload = wait_for_command_to_finish(aws_profile, command_id)

commands = {}
payload['CommandInvocations'].each do |invocation|
  ec2_instance_id = invocation['InstanceId']
  invocation['CommandPlugins'].each do |plugin|
    output = plugin['Output']
    lines = output.split("\n")
    lines.shift
    lines.each do |line|
      next unless line =~ /#{product}/
      container_id = line.split(/\s/, 2).first
      command_id = send_command(aws_profile, [ec2_instance_id], ['sudo', 'docker', 'exec', container_id] + command)
      commands[command_id] = {
        ec2_instance_id: ec2_instance_id,
        command_id: command_id,
        container_id: container_id
      }
    end
  end
end

commands.keys.each do |command_id|
  payload = wait_for_command_to_finish(aws_profile, command_id)
  commands[command_id][:output] = payload['CommandInvocations'].first['CommandPlugins'].first['Output']
end

print JSON.pretty_generate(commands) + "\n"
