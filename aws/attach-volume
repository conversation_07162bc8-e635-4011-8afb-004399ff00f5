#!/usr/bin/env ruby

# Purpose: to attach and EBS volume to EC2 instance.
# (defaults are based on adding EBS volumes to ElasticSearch data nodes)

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:size] = 900
  opts[:volume_type] = 'gp3'
  opts[:device] = '/dev/sdf'

  opts.on('-s', '--size SIZE_IN_GB', Integer, 'Size of the volume to create') do |v|
    opts[:size] = v
  end

  opts.on('-t', '--volume-type VOLUME_TYPE', 'Type of volume to create') do |v|
    opts[:volume_type] = v
  end

  opts.on('-d', '--device DEVICE', 'Specify the device') do |v|
    opts[:device] = v
  end

  opts.on('-n', '--name INSTANCE_NAME', 'Name of the instance to attach the volume') do |v|
    opts[:instance_name] = v
  end

  opts.require_option(:instance_name)
end

require File.expand_path('../config/environment', __dir__)

def run_cmd(cmd)
  puts cmd
  JSON.parse(`#{cmd}`)
end

def get_instance_id
  payload = run_cmd("aws --profile #{EnvironmentLoader.instance.aws_profile} ec2 describe-instances --query 'Reservations[*].Instances[*].{InstanceId:InstanceId}' --filters 'Name=tag:Name,Values=#{SCRIPT_OPTIONS[:instance_name]}'")
  payload.empty? ? nil : payload[0][0]['InstanceId']
end

def get_availability_zone
  payload = run_cmd("aws --profile #{EnvironmentLoader.instance.aws_profile} ec2 describe-instances --query 'Reservations[*].Instances[*].{AvailabilityZone:Placement.AvailabilityZone}' --filters 'Name=tag:Name,Values=#{SCRIPT_OPTIONS[:instance_name]}'")
  payload.empty? ? nil : payload[0][0]['AvailabilityZone']
end

def create_volume(availability_zone, volume_type, size)
  payload = run_cmd("aws --profile #{EnvironmentLoader.instance.aws_profile} ec2 create-volume --availability-zone #{availability_zone} --volume-type #{volume_type} --size #{size}")
  payload['VolumeId']
end

def wait_for_volume_to_be_available(volume_id)
  cmd = "aws --profile #{EnvironmentLoader.instance.aws_profile} ec2 describe-volumes --volume-ids #{volume_id}"
  payload = run_cmd(cmd)
  state_proc = proc do
    payload['Volumes'].first['State']
  end

  while state_proc.call != 'available'
    sleep 2
    payload = run_cmd(cmd)
  end
end

def attach_volume(instance_id, volume_id, device)
  run_cmd("aws --profile #{EnvironmentLoader.instance.aws_profile} ec2 attach-volume --volume-id #{volume_id} --instance-id #{instance_id} --device #{device}")
end

unless (instance_id = get_instance_id)
  $stderr.puts "unable to lookup instance id for #{SCRIPT_OPTIONS[:instance_name]}"
  exit 1
end

unless (availability_zone = get_availability_zone)
  $stderr.puts "unable to lookup instance availability zone for #{SCRIPT_OPTIONS[:instance_name]}"
  exit 1
end

volume_id = create_volume(availability_zone, SCRIPT_OPTIONS[:volume_type], SCRIPT_OPTIONS[:size])

wait_for_volume_to_be_available(volume_id)

attach_volume(instance_id, volume_id, SCRIPT_OPTIONS[:device])
