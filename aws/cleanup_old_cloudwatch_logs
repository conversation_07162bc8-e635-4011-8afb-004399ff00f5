#!/usr/bin/env ruby

require 'json'

AWS_PROFILE = 'evertrueprod'

service_name = ARGV[0]
max_days_old = Time.now - ((ARGV[1] || 30).to_i * 86_400)

def get_log_streams(service_name)
  res = `aws logs describe-log-streams --log-group-name #{service_name} --profile #{AWS_PROFILE}`
  raise("failed to get log streams for #{service_name}") unless $?.success?
  JSON.parse(res)
end

def delete_log_stream(service_name, stream_name)
  `aws logs delete-log-stream --log-group-name #{service_name} --log-stream-name '#{stream_name}' --profile #{AWS_PROFILE}`
  raise "failed" unless $?.success?
end

puts "run this to monitor the progress"
puts "aws logs describe-log-streams --log-group-name #{service_name} --profile #{AWS_PROFILE} | grep logStreamName | wc -l"

get_log_streams(service_name)['logStreams'].select { |stream| Time.at(stream['lastEventTimestamp']/1000) < max_days_old }.reverse.each do |stream|
  delete_log_stream(service_name, stream['logStreamName'])
end
