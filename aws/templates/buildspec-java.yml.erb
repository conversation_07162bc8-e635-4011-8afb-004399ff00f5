version: 0.2

env:
  variables:
    registry_id: "<%= registry_id %>"
    image_namespace: evertrue
    project_name: <%= project_name %>

phases:
  install:
<%- if environment_image == 'aws/codebuild/standard:2.0' -%>
    runtime-versions:
      java: openjdk8

<%- end -%>
    commands:
      - echo Installing codebuild-extras...
      - curl -fsSL https://raw.githubusercontent.com/evertrue/aws-codebuild-extras/master/install > extras.sh
      - . ./extras.sh

  pre_build:
    commands:
      - image_name=<%= deploy_images.map { |image| "$image_namespace/${project_name}-#{image}" }.first %>
      - aws s3 cp s3://codebuild.evertrue.com/.m2/settings.xml /root/.m2/settings.xml
      - $(aws ecr get-login --no-include-email --registry-ids $registry_id)

  build:
    commands:
      - ./mvn-build
      - ./docker-push $registry_id $git_commit $image_name

cache:
  paths:
    - '/root/.m2/**/*'
