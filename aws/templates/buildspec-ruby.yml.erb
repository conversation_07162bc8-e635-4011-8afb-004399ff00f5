version: 0.2

env:
  variables:
    registry_id: "<%= registry_id %>"
    project_name: <%= project_name %>
    image_namespace: evertrue
  parameter-store:
    FURY_AUTH: "/Tools/CodeBuild/gemfury-key"

phases:
  install:
    commands:
      - echo Installing codebuild-extras...
      - curl -fsSL https://raw.githubusercontent.com/evertrue/aws-codebuild-extras/master/install > extras.sh
      - . ./extras.sh

  pre_build:
    commands:
      - $(aws ecr get-login --no-include-email --registry-ids $registry_id)

  build:
    commands:
      - ./docker-build-ruby $image_namespace $project_name <%= build_images.join(' ') %>

  post_build:
    commands:
      - ./docker-push $registry_id $git_commit <%= deploy_images.map { |image| "$image_namespace/${project_name}-#{image}" }.join(' ') %>
