#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-t', '--task-arn TASK_ARN', 'AWS Task ARN') do |v|
    opts[:task_arn] = v
  end

  opts.require_option(:task_arn)
end

require File.expand_path('../config/environment', __dir__)

cmd = "./aws/download-cloudwatch-log -p #{EnvironmentLoader.instance.aws_profile} -o repair-shop-#{SCRIPT_OPTIONS[:task_arn]}.log repairshop-task-service repairshop-task-service/web/#{SCRIPT_OPTIONS[:task_arn]}"

puts cmd
exec(cmd)
