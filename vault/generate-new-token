#!/usr/bin/env ruby

=begin
Purpose is to create vault tokens for the old account's servers

Statge usage: STAGE_ROOT_VAULT_TOKEN=<copy stage root token from 1Pass> ./vault/generate-new-token -e stage
Prod usage: PROD_ROOT_VAULT_TOKEN=<copy prod root token from 1Pass> ./vault/generate-new-token -e prod

Now, edit the data bag vault tokens with this new value.

knife data bag edit vault tokens --secret=$(curl -s $(aws s3 presign s3://provisioning.evertrue.com/chef/encrypted_data_bag_secret))

Change the token value for the correct environment and save.

=end

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)

puts VaultClient.new.login!
