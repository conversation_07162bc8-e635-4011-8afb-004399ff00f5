GEM
  remote: https://rubygems.org/
  specs:
    activemodel (7.2.1)
      activesupport (= 7.2.1)
    activerecord (7.2.1)
      activemodel (= 7.2.1)
      activesupport (= 7.2.1)
      timeout (>= 0.4.0)
    activerecord-sqlserver-adapter (7.2.1)
      activerecord (~> 7.2.0)
      tiny_tds
    activesupport (7.2.1)
      base64
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    aws-eventstream (1.3.0)
    aws-partitions (1.987.0)
    aws-sdk-appflow (1.68.0)
      aws-sdk-core (~> 3, >= 3.207.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-codebuild (1.132.0)
      aws-sdk-core (~> 3, >= 3.207.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-cognitoidentityprovider (1.107.0)
      aws-sdk-core (~> 3, >= 3.207.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-core (3.209.1)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.651.0)
      aws-sigv4 (~> 1.9)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-dynamodb (1.125.0)
      aws-sdk-core (~> 3, >= 3.207.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-ecs (1.161.0)
      aws-sdk-core (~> 3, >= 3.207.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-elasticloadbalancingv2 (1.117.0)
      aws-sdk-core (~> 3, >= 3.207.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-eventbridge (1.71.0)
      aws-sdk-core (~> 3, >= 3.207.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-iam (1.111.0)
      aws-sdk-core (~> 3, >= 3.207.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-kms (1.94.0)
      aws-sdk-core (~> 3, >= 3.207.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.167.0)
      aws-sdk-core (~> 3, >= 3.207.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sdk-secretsmanager (1.108.0)
      aws-sdk-core (~> 3, >= 3.207.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-sqs (1.86.0)
      aws-sdk-core (~> 3, >= 3.207.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-sts (1.11.0)
      aws-sdk-core (~> 3, >= 3.110.0)
      aws-sigv4 (~> 1.1)
    aws-sigv4 (1.10.0)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.2.0)
    bigdecimal (3.1.8)
    cassandra-driver (3.2.5)
      ione (~> 1.2)
    cassandra-helpers (0.1.2)
      cassandra-driver
    client-api-builder (0.5.5)
      inheritance-helper (>= 0.2.5)
    concurrent-ruby (1.3.4)
    connection_pool (2.4.1)
    csv (3.3.0)
    csv-utils (0.3.24)
      csv
      inheritance-helper
    dalli (3.2.8)
    dotenv (3.1.4)
    drb (2.2.1)
    dynamic-active-model (0.5.1)
      activerecord
    elasticsearch_scanner (0.1.5)
    encrypted-field (0.1.0)
    faraday (2.12.0)
      faraday-net_http (>= 2.0, < 3.4)
      json
      logger
    faraday-mashify (0.1.1)
      faraday (~> 2.0)
      hashie
    faraday-multipart (1.0.4)
      multipart-post (~> 2)
    faraday-net_http (3.3.0)
      net-http
    fiddle (1.1.2)
    geocoder (1.8.3)
      base64 (>= 0.1.0)
      csv (>= 3.0.0)
    gli (2.21.5)
    google-protobuf (4.28.2)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.28.2-aarch64-linux)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.28.2-arm64-darwin)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.28.2-x86-linux)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.28.2-x86_64-darwin)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.28.2-x86_64-linux)
      bigdecimal
      rake (>= 13)
    hashie (5.0.0)
    i18n (1.14.6)
      concurrent-ruby (~> 1.0)
    inheritance-helper (0.2.5)
    ione (1.2.5)
    jmespath (1.6.2)
    json (2.7.2)
    logger (1.6.1)
    middleware (0.1.0)
    minitest (5.25.1)
    multi-file-processor (0.1.0)
    multipart-post (2.4.1)
    mysql2 (0.5.6)
    net-http (0.4.1)
      uri
    net-ssh (7.3.0)
    nokogiri (1.16.7-aarch64-linux)
      racc (~> 1.4)
    nokogiri (1.16.7-arm-linux)
      racc (~> 1.4)
    nokogiri (1.16.7-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.16.7-x86-linux)
      racc (~> 1.4)
    nokogiri (1.16.7-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.16.7-x86_64-linux)
      racc (~> 1.4)
    ostruct (0.6.0)
    parallel (1.26.3)
    protobuf (3.10.9)
      activesupport (>= 3.2)
      middleware
      thor
      thread_safe
    psych (5.1.2)
      stringio
    public_suffix (6.0.1)
    racc (1.8.1)
    rack (3.1.7)
    rake (13.2.1)
    rbtree (0.4.6)
    rdoc (6.7.0)
      psych (>= 4.0.0)
    redis (5.3.0)
      redis-client (>= 0.22.0)
    redis-client (0.22.2)
      connection_pool
    rubyzip (2.3.2)
    s3grep (0.1.8)
      aws-sdk-s3
    schema-model (0.6.10)
      inheritance-helper
    schema-normalize (0.1.2)
      inheritance-helper
    securerandom (0.3.1)
    set (1.1.0)
    slack-ruby-client (2.4.0)
      faraday (>= 2.0)
      faraday-mashify
      faraday-multipart
      gli
      hashie
    sorted_set (1.0.3)
      rbtree
      set (~> 1.0)
    stringio (3.1.1)
    thor (1.3.2)
    thread_safe (0.3.6)
    timeout (0.4.1)
    tiny_tds (2.1.7)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uri (0.13.1)

PLATFORMS
  aarch64-linux
  arm-linux
  arm64-darwin
  x86-linux
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  activerecord-sqlserver-adapter
  addressable
  aws-sdk-appflow
  aws-sdk-codebuild
  aws-sdk-cognitoidentityprovider
  aws-sdk-dynamodb
  aws-sdk-ecs
  aws-sdk-elasticloadbalancingv2
  aws-sdk-eventbridge
  aws-sdk-iam
  aws-sdk-s3
  aws-sdk-secretsmanager
  aws-sdk-sqs
  aws-sdk-sts
  cassandra-driver
  cassandra-helpers
  client-api-builder
  csv
  csv-utils
  dalli
  dotenv
  dynamic-active-model
  elasticsearch_scanner
  encrypted-field
  fiddle
  geocoder
  google-protobuf
  multi-file-processor
  mysql2
  net-ssh
  nokogiri
  ostruct
  parallel
  protobuf
  rack
  rdoc
  redis
  rubyzip
  s3grep
  schema-model
  schema-normalize
  slack-ruby-client
  sorted_set

BUNDLED WITH
   2.5.16
