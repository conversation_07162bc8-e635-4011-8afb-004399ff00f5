#!/bin/sh

# NOTE: at the time of writing this Ruby 3.3 still requires openssl 1.1 to build.  Hence the brew link/unlink methods below.
brew update
brew bundle

# do this to ensure RVM knows about the latest Ruby version
rvm get stable

# again, Ruby 3.3 requires openssl 1.1
brew unlink openssl@3
brew link openssl@1.1 --force

rvm install $(cat .ruby-version)

# switch back to openssl 3
brew unlink openssl@1.1
brew link openssl@3

# setup gemset
rvm use $(cat .ruby-version)
rvm gemset create $(cat .ruby-gemset)
rvm use $(cat .ruby-version)@$(cat .ruby-gemset)

# work around for install mysql2
# gem install mysql2 -- --with-cflags="-Wno-error=implicit-function-declaration" --with-ldflags=-L/opt/homebrew/opt/zstd/lib
bundle
