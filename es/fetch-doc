#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.options[:mapping] = :contact
  opts.add_oid_option

  opts.on('-m', '--mapping [MAPPING]', 'Mapping name') do |v|
    opts.options[:mapping] = v.to_sym
  end

  opts.on('-i', '--id [ID]', 'Document ID') do |v|
    opts.options[:id] = v
  end

  opts.on('-c', '--show-curl', 'Output curl command') do |v|
    opts.options[:show_curl] = true
  end

  opts.require_options(
    :id,
    :mapping
  )
end

require File.expand_path('../config/environment', __dir__)

SCRIPT_OPTIONS.display_error_and_exit("unknown mapping #{SCRIPT_OPTIONS[:mapping]}, valid mappints are #{ESContactHelpers::MAPPING_TO_OID_PROPERTY.keys.join(', ')}") unless ESContactHelpers::MAPPING_TO_OID_PROPERTY[SCRIPT_OPTIONS[:mapping]]

doc_url = ESContactHelpers.url_for_doc(SCRIPT_OPTIONS[:mapping], SCRIPT_OPTIONS[:id], SCRIPT_OPTIONS[:oid])

response = HTTPClient.get(URI(doc_url))

print JSON.pretty_generate(JSON.parse(response.body)) + "\n"

if SCRIPT_OPTIONS[:show_curl]
  print  "\ncurl '#{doc_url}'\n"
end
