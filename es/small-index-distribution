#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)

oids = MySQLHelpers.create_client(:auth).query('SELECT id FROM organizations WHERE deleted = false AND purge_at IS NULL', as: :array).map(&:first)

oid_contact_doc_counts = {}
nodes = {}
shards = {}

shard_nodes = {}
shard_contact_doc_counts = {}

es_schema = ESSchema.new(ES_URL)
small_index_name = es_schema.small_index_name
dna_client = DNAClient.create_app_client
oids.each do |oid|
  next if dna_client.is_big_customer?(oid)

  oid_contact_doc_counts[oid] = es_schema.get_doc_count({index_name: small_index_name, mapping_name: 'contact'}, 'oid:' + oid.to_s)
  data = ESHelpers.get_search_shards(small_index_name, oid)
  data['nodes'].each do |_, node_info|
    nodes[node_info['name']] ||= []
    nodes[node_info['name']] << oid
  end

  data['shards'].first.each do |shard_info|
    shards[shard_info['shard']] ||= []
    shards[shard_info['shard']] << oid
    shard_nodes[shard_info['shard']] = data['nodes'][shard_info['node']]['name']
  end
end

nodes.values.each do |list|
  list.sort!
  list.uniq!
end

shards.values.each do |list|
  list.sort!
  list.uniq!
end

shards.each do |shard_id, oids|
  shard_contact_doc_counts[shard_id] = oids.map { |oid| oid_contact_doc_counts[oid] }.sum
end

File.open(TMP_DIR.join('es-oids.json'), 'wb') do |f|
  f.write(JSON.pretty_generate(nodes: nodes, shards: shards, shard_nodes: shard_nodes, oid_contact_doc_counts: oid_contact_doc_counts, shard_contact_doc_counts: shard_contact_doc_counts))
end
