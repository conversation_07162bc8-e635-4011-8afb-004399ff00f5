#!/usr/bin/env bash

# this script retrieves query logs from production ES nodes.
# $1 - Your ssh user, set if it differs from your local user (via whoami)
# $2 - The destination to download into. Defaults to current directory.

nodes="$(curl -s http://stage-searchmaster-v2-contacts-1b:9200/_nodes | jq -r '.nodes[].name')"

sshuser=${1:-whoami}
target=${2:-.}

for node in $nodes; do
  echo $node
  scp ${sshuser}@${node}://mnt/dev0/elasticsearch/logs/contacts-search-v2.log ${target}/${node}-contacts-search-v2.log
  scp ${sshuser}@${node}://mnt/dev0/elasticsearch/logs/elasticsearch.log ${target}/${node}-elasticsearch.log
done