#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oids_option

  opts.on('--skip-counts') do 
    opts[:skip_counts] = true
  end
end

require File.expand_path('../config/environment', __dir__) 

es_schema = ESSchema.new(ES_URL)
env = SCRIPT_OPTIONS[:environment]
dna_client = DNAClient.create_app_client('dna_api')
skip_counts = SCRIPT_OPTIONS[:skip_counts]

verify_org_index = lambda do |oid|
    org_alias = "#{env}-contacts-oid-#{oid}-*"
    uri = URI("#{ES_URL}/#{org_alias}/")
    res = HTTPClient.get(uri)
    body = JSON.parse(res.body)
    
    if body.keys.size == 0
        return "❌"
    end

    "✅"
end

verify_alias = lambda do |oid|
    org_alias = "#{env}-contacts-oid-#{oid}"
    uri = URI("#{ES_URL}/#{org_alias}/_aliases")
    res = HTTPClient.get(uri)
    body = JSON.parse(res.body)
    
    if body.keys.size > 1
        return "❌ org alias points to multiple indices: [#{body.keys.join(',')}]"
    end

    index = body.keys[0]

    unless index =~ /#{env}-contacts-oid-#{oid}-\d+/
        return "❌\torg is aliased to invalid index: #{index}"
    end

    "✅"
end

verify_old_index = lambda do |oid|
    org_alias = "#{env}-contacts-oid-#{oid}"
    uri = URI("#{ES_URL}/#{org_alias}/_aliases")
    res = HTTPClient.get(uri)
    body = JSON.parse(res.body)
    
    if body.keys.detect{ |index| index =~ /big|small/ }
        return "❌ org alias still pointing to big/small index: [#{body.keys.join(',')}]"
    end

    "✅"
end

verify_dna = lambda do |oid|
    return "❌" unless dna_client.is_big_customer?(oid)
    "✅"
end

verify_index_settings = lambda do |oid|
    org_alias = "#{env}-contacts-oid-#{oid}"
    uri = URI("#{ES_URL}/#{org_alias}/_settings")
    res = HTTPClient.get(uri)
    body = JSON.parse(res.body)

    index = body.keys[0]
    refresh_interval = body[index].dig('settings', 'index', 'refresh_interval')
    if refresh_interval != '1s'
        "❌ refresh interval invalid"
    end

    blocks = body[index].dig('settings', 'index', 'blocks', 'write')
    if blocks != 'false'
    else
        "❌ writes are blocked"
    end

    return "✅"
end

verify_migration_started = lambda do |oid|
    if skip_counts
        return "skipped"
    end

    index = "#{env}-contacts-oid-#{oid}-*"
    path = "/#{index}/_count"
    if ESHelpers.get_mapping_count(path)['count'] > 0
        return "✅"
    end
    "❔ No docs on target index" # 0 docs isn't necessarily bad on its own, could just mean there are no docs in the org
end

verify_counts = lambda do |oid|
    if skip_counts
        return "skipped"
    end

    index = "#{env}-contacts-oid-#{oid}-*"

    checked_one = false
    es_schema.docs.each do |info|
        next unless info[:index_name] =~ /#{env}-contacts-oid-#{oid}-\d+/
        checked_one = true

        count_query = info[:oid_property] + ':' + oid.to_s
        current_count_thread = Thread.new{ current_count = es_schema.get_doc_count(info, count_query) }

        # use global alias since we don't know if this org was on big or small (could cause double counting issues if indexes aren't clean)
        old_index = "#{env}-contacts"
        path = "/#{old_index}/#{info[:mapping_name]}/_count?q=#{count_query}"
        old_index_count_thread = Thread.new { ESHelpers.get_mapping_count(path)['count'] }

        current_count = current_count_thread.value
        old_index_count = old_index_count_thread.value

        if current_count < old_index_count
            return "❌Invalid index counts for #{old_index}:#{info[:mapping_name]} = #{old_index_count}, migrated: #{current_count}"
        end
    end
    return "skipped" unless checked_one
    "✅"
end

status_checks = {
    'Org Index Created': verify_org_index,
    'Alias points to org index': verify_alias,
    'Alias removed from big or small index': verify_old_index,
    'Verify index settings': verify_index_settings,
    'DNA Setting is-big-customer is true': verify_dna,
    'Some docs exists in org index': verify_migration_started,
    'Docs': verify_counts
}

SCRIPT_OPTIONS[:oids].each do |oid|
    puts "> #{oid}"

    threads = {}

    status_checks.each do |k, v|
        threads[k] = Thread.new { v.call(oid) }
    end

    threads.each do |k, v|
        puts "\t#{k}: #{v.value}"
    end
end