#!/usr/bin/env ruby

$LOAD_PATH << File.expand_path('../ruby/lib', __dir__)

require 'json'
require 'net/http'
require 'elasticsearch_scanner'
require 'mysql2'
require 'yaml'
require 'fileutils'
require 'set'

def get_mappings(url)
  uri = URI(url)
  http = Net::HTTP.new(uri.host, uri.port)
  req = Net::HTTP::Get.new(uri.request_uri)
  res = http.request(req)
  JSON.parse(res.body)
end

def mysql_client_contacts
  @mysql_client_contacts ||=
    begin
      config = YAML.load_file("#{ENV['HOME']}/.evertrue/stage-dbs.yml")['contacts']
      Mysql2::Client.new(
        host: config['host'],
        username: config['username'],
        password: config['password'],
        database: config['database']
      )
    end
end

def save_bulk_request_of_docs_to_delete(dir, docs_to_delete, cnt)
  File.open("#{dir}/delete_docs_#{cnt}.request", 'wb') do |f|
    docs_to_delete.each do |doc|
      f.puts({delete: {_index: doc['_index'], _id: doc['_id'], _type: doc['_type'], _routing: doc['_routing']}}.to_json)
    end
  end
end

def get_missing_contact_ids(ids)
  existing_ids = mysql_client_contacts.query("SELECT id FROM contact WHERE id IN(#{ids.join(',')})", as: :array).map(&:first)
  ids - existing_ids
end

STAGING_ES_URL = 'http://stage-searchmaster-v2-contacts-1b.priv.evertrue.com:9200'
ES_URL = STAGING_ES_URL
mappings = get_mappings(ES_URL + '/_mapping')

CONTACT_ID_PROP_NAMES = ['contact_id', 'target_contact_id']

DEFAULT_QUERY = {'match_all' => {}}

docs_to_search = []
mappings.each do |index_name, index_info|
  index_info['mappings'].each do |mapping_name, mapping_info|
    next if mapping_name =~ /^\./
    next unless mapping_info['properties']

    if mapping_name == 'contact'
      fields_to_return = false
      docs_to_search << [index_name, mapping_name, false, DEFAULT_QUERY, Proc.new { |result| result['_id'].to_i }]
    elsif mapping_name == 'contact_note'
      fields_to_return = ['target_id']
      query = {
        "bool" => {
          "terms" => {
            "target_type" => "CONTACT"
          }
        }
      }
      docs_to_search << [index_name, mapping_name, ['target_id'], query, Proc.new { |result| result['_source']['target_id'].to_i }]
    elsif (contact_id_prop = mapping_info['properties'].keys.detect { |prop_name| CONTACT_ID_PROP_NAMES.include?(prop_name) })
      docs_to_search << [index_name, mapping_name, [contact_id_prop], DEFAULT_QUERY, Proc.new { |result| (id = result['_source'][contact_id_prop]) && id.to_i }]
    else
      # puts "no contact_id_prop found for #{mapping_name}, #{mapping_info['properties'].keys}"
      next
    end
  end
end

docs_to_search.each do |index_name, mapping_name, fields_to_return, query, id_proc|
  puts "searching #{index_name}/#{mapping_name}"

  started_at = Time.now
  url = ES_URL + '/' + index_name + '/' + mapping_name + '/_search'

  cnt = 0
  batch_cnt = 0
  total_sql_time = 0
  total_docs_to_delete = 0
  scanner = ElasticSearchScanner.new(url, query, 1_000)
  progress_proc = Proc.new do
    puts "elasticsearch times total_request_time #{scanner.total_request_time}, total_elasticsearch_time: #{scanner.total_elasticsearch_time}, total_sql_time: #{total_sql_time}, total_docs_to_delete: #{total_docs_to_delete}"
  end
  scanner.fields_to_return = fields_to_return
  results_to_check = []
  docs_to_delete = []
  dir = "bulk_requests/#{index_name}/#{mapping_name}"
  FileUtils.mkdir_p(dir)

  find_missing_conatct_ids_proc = Proc.new do
    ids = results_to_check.map(&id_proc).compact

    sql_started_at = Time.now
    missing_ids = get_missing_contact_ids(ids.to_a)
    total_sql_time += (Time.now - sql_started_at)

    if missing_ids.size > 0
      results_to_check.select do |result|
        next unless missing_ids.include?(id_proc.call(result))

        docs_to_delete << result
        total_docs_to_delete += 1
        if docs_to_delete.size >= 100
          batch_cnt += 1
          save_bulk_request_of_docs_to_delete(dir, docs_to_delete, batch_cnt)
          docs_to_delete = []
        end
      end
      results_to_check = []
    end
  end

  scanner.each_batch do |results|
    results_to_check += results

    if results_to_check.size >= 50_000
      find_missing_conatct_ids_proc.call
    end

    cnt += 1
    progress_proc.call if (cnt%20) == 0
  end

  if results_to_check.size > 0
    find_missing_conatct_ids_proc.call
  end

  progress_proc.call
  puts "took #{Time.now - started_at} to search #{index_name}/#{mapping_name}"
end
