#!/usr/bin/env ruby
# frozen_string_literal: true

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)

# get all active organization_id(s)
organization_ids = MySQLHelpers
                     .create_client(:auth)
                     .query('select id from organizations where deleted = false', as: :array)
                     .to_a
                     .flatten

# get is-big-customer settings per an organization
is_big_customer = DNAClient
                    .create_app_client
                    .get_bulk_setting_value('is-big-customer')['setting_values']
                    .each_with_object({}) do |setting, hsh|

  hsh[setting['oid']] =
    case setting['value']
    when true,
         false
      setting['value']
    else
      $stderr.puts "invalid setting for is-big-customer: #{setting.to_json}"
    end
end

# fetch all ES aliases and map to organization_id -> (big|small)
org_es_aliases = {}
ESHelpers.get_aliases.each do |index_name, index_info|
  index_info['aliases'].keys.each do |alias_name|
    next unless alias_name =~ /-oid-(\d+)/

    org_es_aliases[$1.to_i] =
      case index_name
      when /-small-/
        'small'
      when /-big-/
        'big'
      else
        raise("unknown index name #{index_name}")
      end
  end
end

organization_ids.each do |organization_id|
  unless is_big_customer.key?(organization_id)
    $stderr.puts "organization #{organization_id} is missing dna setting is-big-customer, alias is configured to point at #{org_es_aliases[organization_id]}"
    next
  end

  unless (current_index = org_es_aliases[organization_id])
    $stderr.puts "organization #{organization_id} is missing org es alias, alias is-big-customer is set to #{is_big_customer[organization_id]}"
    next
  end

  expected_index = is_big_customer[organization_id] ? 'big' : 'small'

  unless current_index == expected_index
    $stderr.puts "organization #{organization_id} is in the wrong index!!!, current_index #{current_index}, expected_index #{expected_index}, is-big-customer #{is_big_customer[organization_id]}"
  end
end

