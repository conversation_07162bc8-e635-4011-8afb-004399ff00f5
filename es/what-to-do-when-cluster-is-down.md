## The System is Down Yo!

**WARNING!!!!**  Restarting ES nodes is dangerous.  And needs to be done with caution.  Restarting more than 1 data node at a time can lead to data loss.  And in most cases doesn't get the cluster up and running as fast as possible.

Remember Elasticsearch is actually designed to prevent data loss.  But also remember we have terabytes of data in ES.  Things take time, lots of time.  So, when ES is unresponsive and things seem bleak.  There is actually light at the end of the tunnel.  So, remember to stay calm and make calculated decisions.  This mean SSH into the hosts, watch the `tail -F /mnt/dev0/elasticsearch/logs/elasticsearch.log`.

In my opinion, I believe most ES issues are caused by timeouts.  This can happen when the JVM heap fills up.  And old gen collection times take multiple seconds.  At this point data nodes stop responding to pings.  And the ES master nodes declare the data node as down.  This cause ES to start reallocating shards.

## What to do first?

Turn off anything that is updating or searching ES.

* pause storm
* terminate any EMR clusters running ES jobs
* stop exports by turning of Spark SQS Daemon and killing spark jobs
* pause imports (update importer-scheduler-service in ECS to set desired count to 0)
* stop consumers updating ES
* do what ever you can to reduce the amount of work ES needs to do

## How to find the ES data nodes in Trouble!

How to determine if the ES node is having GC issues?

In [KOPF](http://prod-searchmaster-v2-contacts-1b:9200/_plugin/kopf/#!/nodes), you will see that the HEAP is at 99% and if you watch the `tail -F /mnt/dev0/elasticsearch/logs/elasticsearch.log` and only see is GC collections (example belonw).  This means the ES server is stuck in a state where the only thing it is doing is collecting garabage.

```
INFO monitor.jvm  - [prod-search-v2-contacts-1b-1] [gc][old][45067367][32577] duration [8.1s], collections [1]/[9.1s], total [8.1s]/[56.5m], memory [14gb]->[13.6gb]/[15.4gb], all_pools {[young] [13.2mb]->[2.3mb]/[266.2mb]}{[survivor] [33.2mb]->[0b]/[33.2mb]}{[old] [13.9gb]->[13.6gb]/[15.1gb]}
```

Before even considering rebooting the node.  Try a couple things.

SSH into the ES data node having issues, and...

Clear cache

```
curl -XPOST "localhost:9200/_cache/clear"
```

Flush

```
curl -XPOST "localhost:9200/_flush"
```

Try that on any node in the cluster having issues.  Not sure if it was random luck or not.  But in the last incident, 2 of the 3 nodes having GC issues actually saw a drop in heap after running those curls.

If after running the above commands and you don't see the heap usage go down after 10 minutes or more.  Restarting ES maybe the only option left.

To restart ES you do

```
/etc/init.d/elasticsearch restart
```

Now to potentially speed things up.  You can reroute all the UNASSIGNED shards back to the node you rebooted.  In the repair shop, you can use

```
./es/reroute-unassigned-shards -e prod -s <hostname>
# ex: ./es/reroute-unassigned-shards -e prod -s prod-search-v2-contacts-1c-3
```

NOTE: The thing that worries me about this is, when the shards are reassigned to this node.  They all become replicas.  And my fear is that this will cause an imbalance in the cluster.

But this logic does significantly speed up the reboot process.  As ES won't have to move large volumes of data around.

TODO: What we should do is figure out if there is away to tell ES that the node is going down but don't do any reshuffling of data.

## Stuck UNASSIGNED Shards

In KOPF, you will see a unassigned shards.  They will get assigned.  And then almost immediately be back in the unassigned state.  And this will happen over and over again.

To fix this reroute the shard.

Use this command to find UNASSIGNED shards

```
curl -s -XGET http://localhost:9200/_cat/shards | grep UNASSIGNED
```

Then use to reroute the shard to a different node.

```
curl -XPOST 'localhost:9200/_cluster/reroute' -d '{
  "commands": [ {
    "allocate": {
      "index": "<index name ex: prod-small-contacts-0>",
      "shard": <shard ID ex: 14>,
      "node": "<pick a host not having heap issues ex: prod-search-v2-contacts-1b-4>",
      "allow_primary": true
    }
  } ]
}'
```

## Monitoring Progress

Use this command to monitor how far along the index action is

```
curl -s -XGET http://localhost:9200/_cat/recovery | grep index
```


Very last option!

If you need do need to restart node mute alarm 

https://app.datadoghq.com/monitors/146959871?view=spans
