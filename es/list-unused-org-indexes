#!/usr/bin/env ruby

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse!

require_relative '../config/environment'

aliases = ESHelpers.get_aliases()

index_prefix = ESContactHelpers.index_prefix + '-contacts-'

unused_indexes = []
aliases.each do |index_name, alias_info|
  next unless index_name.include?(index_prefix)
  next if index_name.include?('-template')
  next unless alias_info['aliases'].size == 0

  unused_indexes << index_name
end

unused_indexes.sort.each do |index_name|
  puts "#{index_name}  ---  curl -XDELETE #{ES_URL}/#{index_name}"
end
