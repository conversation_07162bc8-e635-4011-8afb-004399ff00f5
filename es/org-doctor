#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

def log(msg)
    puts "#{Time.now.strftime('%Y/%m/%d %H:%M:%S.%3N')} (#{Thread.current.name}) - #{msg}"
end

def run_cmd_proc(cmd_str, cmd_proc)
    log "Going to execute: " + cmd_str
    if SCRIPT_OPTIONS[:dry]
      response = 'no response in dry mode, command not executed'
    else
      response = cmd_proc.call
    end
    log "Response: #{response}"
    response
end

def run_cmd(cmd)
  run_cmd_proc(cmd, Proc.new{ `#{cmd}` })
end

class Action
  def description
    raise 'unimplemented'
  end

  def perform(oid)
    raise 'unimplemented'
  end
end

class FinishAction < Action
  def description
    "Finish a migration for an org who's spark data transfer completed successfully, but the steps after that didn't take place.\n\t1. Update refresh interval\n\t2. Update is-big-customer setting.\n\t3. Flip alias."
  end

  def perform(oid)
    source_index = SCRIPT_OPTIONS[:source_index] || get_source_index(oid)
    target_index = get_org_index(oid)

    log "Finishing #{oid}, source_index=#{source_index}, target_index=#{target_index}"

    # Re-enable refresh now that data transfer is complete
    payload = {
        index: {
            refresh_interval: '1s'
        }
    }.to_json

    log "Enabling refresh for target index #{target_index}"
    run_cmd("curl -XPUT '#{ES_URL}/#{target_index}/_settings' -d '#{payload}'")

    log 'Set DNA setting is-big-customer to true'
    run_cmd_proc("SET is-big-customer = true", Proc.new{ DNA_CLIENT.update_setting_value("is-big-customer", true, { oid: oid }) })

    log 'Update ES alias to point to the new index'
    alias_payload = {
        actions: [
        {remove: { index: source_index, alias: ESContactHelpers.index_name_for_oid(oid)}},
        {add: { index: target_index, alias: ESContactHelpers.index_name_for_oid(oid)}}
        ]
    }.to_json

    run_cmd("curl -XPOST '#{ES_URL}/_aliases' -d '#{alias_payload}'")
  end
end


ACTIONS = {
  'finish': FinishAction.new
}

def format_actions_help
    ACTIONS.map{ |k, v| "#{k}: #{v.description}"}.join("\n")
end

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oids_option

  opts.on('--action=ACTION', "The action to perform on selected orgs.\n\nAVAILABLE ACTIONS:\n#{format_actions_help}\n\n") do |v|
    raise "Invalid action: #{v}, use -h for available actions" unless ACTIONS.key? v.to_sym
    opts[:action] = ACTIONS[v.to_sym]
  end

  opts.on('--dry', 'run the command without actually executing any of the statements to see output') do 
    opts[:dry] = true
  end

  opts.on('--source-index=SOURCE_INDEX', 'override source index auto detection, useful when both indexes have docs') do |v|
    opts[:source_index] = v
  end

  opts.require_option(:action)
end

require File.expand_path('../config/environment', __dir__)
require_relative 'migration_helpers'

ES_SCHEMA = ESSchema.new(ES_URL)
DNA_CLIENT = DNAClient.create_app_client('dna_api')

SCRIPT_OPTIONS[:oids].each do |oid|
    puts "\n"
    SCRIPT_OPTIONS[:action].perform(oid)
end