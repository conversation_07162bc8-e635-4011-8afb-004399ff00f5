#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
end

require File.expand_path('../config/environment', __dir__)
include CommandLineHelpers

oid = SCRIPT_OPTIONS[:oid].to_s

org_mappings_in_use = []

es_schema = ESSchema.new(ES_URL)

es_schema.docs.each do |info|
  next unless info[:index_name] =~ /small/

  count_query = info[:oid_property] + ':' + oid
  next unless es_schema.get_doc_count(info, count_query) > 0

  org_mappings_in_use << info[:mapping_name]
end

# first thing to move
if org_mappings_in_use.delete('contact')
  org_mappings_in_use.unshift('contact')
end

# last thing to move
if org_mappings_in_use.delete('.percolator')
  org_mappings_in_use << '.percolator'
end

puts '# Log into spark master node'
master_node = `curl -s 'http://prod-contacts-spark-1b-1.priv.evertrue.com:8081' | grep -i 'back' | head -1 | cut -d '"' -f 2 | cut -d '/' -f 3 | cut -d ':' -f 1`
puts bold_string("ssh #{master_node}")

puts ''
puts '# 1st Command to migrate a customer to the big index'
puts bold_string("sudo -H -u spark bash -c '/usr/lib/spark/bin/spark-submit --class com.et.contacts.spark.SparkJobRunner /usr/lib/spark/lib/ext/contacts_spark-1.0-basepom-SNAPSHOT-jar-with-dependencies.jar -Djob_name=BigCustomerEsMigrationJob -Doid=#{oid} -Dsource_index=#{es_schema.small_index_name} -Dtarget_index=#{es_schema.big_index_name} -Dfilter_types=#{org_mappings_in_use.join(',')} -Dfirst_run=true'")


puts ''
puts '# 2nd Now block writes to the small index'
block_writes_payload = {
  index: {
    blocks: {
      write: true
    }
  }
}.to_json

puts bold_string("curl -XPUT '#{ES_URL}/#{es_schema.small_index_name}/_settings' -d '#{block_writes_payload}'")

puts ''
puts '# 3rd copy the remaing data'
puts bold_string("sudo -H -u spark bash -c '/usr/lib/spark/bin/spark-submit --class com.et.contacts.spark.SparkJobRunner /usr/lib/spark/lib/ext/contacts_spark-1.0-basepom-SNAPSHOT-jar-with-dependencies.jar -Djob_name=BigCustomerEsMigrationJob -Doid=#{oid} -Dsource_index=#{es_schema.small_index_name} -Dtarget_index=#{es_schema.big_index_name} -Dfilter_types=#{org_mappings_in_use.join(',')} -Dfirst_run=false'")

dna_payload = {
  "value": true
}.to_json

puts ''
puts '# 4th set DNA setting is-big-customer to true'
puts bold_string("curl -XPUT 'https://api-hb.evertrue.com/dna/setting_values/is-big-customer?app_key=ef571795d45d5be4994a3beebbf2fcb9d24466d45cbf6877250ba822420d3c64&auth_provider=EvertrueAppToken&auth=ODpGZG9zTHozYmpjZ0xMc1JUUndCbg==&oid=#{oid}' -H 'Content-Type: application/json' -d '#{dna_payload}'")

puts ''
puts '#5 update ES alias for org'
alias_payload = {
  actions: [
    {remove: { index: es_schema.small_index_name, alias: ESContactHelpers.index_name_for_oid(oid)}},
    {add: { index: es_schema.big_index_name, alias: ESContactHelpers.index_name_for_oid(oid)}},
  ]
}.to_json

puts bold_string("curl -XPOST '#{ES_URL}/_aliases' -d '#{alias_payload}'")

puts ''
puts '# 6th Now resume writes to the small index'
block_writes_payload = {
  index: {
    blocks: {
      write: false
    }
  }
}.to_json

puts bold_string("curl -XPUT '#{ES_URL}/#{es_schema.small_index_name}/_settings' -d '#{block_writes_payload}'")

puts ''
puts '# 7th Command to remove data from the small index'
puts bold_string("sudo -H -u spark bash -c '/usr/lib/spark/bin/spark-submit --class com.et.contacts.spark.SparkJobRunner /usr/lib/spark/lib/ext/contacts_spark-1.0-basepom-SNAPSHOT-jar-with-dependencies.jar -Djob_name=BigCustomerEsMigrationJob -Doid=#{oid} -Ddelete_documents=true -Dsource_index=#{es_schema.small_index_name} -Dfilter_types=#{org_mappings_in_use.join(',')}'")
