#!/usr/bin/env ruby

require File.expand_path('../config/environment', __dir__)
require 'elasticsearch_scanner'

def save_bulk_request_of_docs_to_delete(dir, docs_to_delete, cnt)
  File.open("#{dir}/delete_docs_#{cnt}.request", 'wb') do |f|
    docs_to_delete.each do |doc|
      f.puts({delete: {_index: doc['_index'], _id: doc['_id'], _type: doc['_type'], _routing: doc['_routing']}}.to_json)
    end
  end
end

mysql_client_contacts = MySQLHelpers.create_client(:auth)

valid_oids = mysql_client_contacts.query('select id from organizations where deleted = 0').map { |result| result['id'] }

es_schema = ESSchema.new(ES_URL)
es_schema.docs.each do |info|
  LOG.info "searching #{info.inspect}"
  started_at = Time.now
  url = ES_URL + '/' + info[:index_name] + '/' + info[:mapping_name] + '/_search'

  query = {
    "bool" => {
      "must_not" => {
        "terms" => {
          info[:oid_property] => valid_oids
        }
      }
    }
  }

  cnt = 0
  dir = TMP_DIR.join("bulk_requests/#{info[:index_name]}/#{info[:mapping_name]}")
  FileUtils.mkdir_p(dir)

  docs_to_delete = []
  scanner = ElasticSearchScanner.new(url, query)
  scanner.fields_to_return = false
  scanner.each do |result|
    docs_to_delete << result

    if docs_to_delete.size >= 100
      cnt += 1
      save_bulk_request_of_docs_to_delete(dir, docs_to_delete, cnt)
      docs_to_delete = []
    end
  end

  if docs_to_delete.size > 0
    cnt += 1
    save_bulk_request_of_docs_to_delete(dir, docs_to_delete, cnt)
    docs_to_delete = []
  end

  LOG.info "took #{Time.now - started_at} to search #{info.inspect}"
end
