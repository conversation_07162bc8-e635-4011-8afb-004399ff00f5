#!/usr/bin/env ruby

require File.expand_path('../config/environment', __dir__)
require 'elasticsearch_scanner'
require 'fileutils'

def save_bulk_request_of_docs_to_delete(dir, docs_to_delete, cnt)
  File.open("#{dir}/delete_docs_#{cnt}.request", 'wb') do |f|
    docs_to_delete.each do |doc|
      f.puts({delete: {_index: doc['_index'], _id: doc['_id'], _type: doc['_type'], _routing: doc['_routing']}}.to_json)
    end
  end
end

es_schema = ESSchema.new(ES_URL)
es_schema.docs.each do |info|
  next unless info[:parent_type]

  puts "searching #{info.inspect}"
  started_at = Time.now
  url = ES_URL + '/' + info[:index_name] + '/' + info[:mapping_name] + '/_search'

  query = {
    "bool" => {
      "must_not" => {
        "has_parent" => {
          "parent_type" => info[:parent_type],
          "query" => {
            "match_all" => {}
          }
        }
      }
    }
  }

  cnt = 0
  dir = "bulk_requests/#{info[:index_name]}/#{info[:mapping_name]}"
  FileUtils.mkdir_p(dir)

  docs_to_delete = []
  scanner = ElasticSearchScanner.new(url, query)
  scanner.fields_to_return = false
  scanner.each do |result|
    next unless result.has_key?('_parent')
    next if result['_parent'].empty? || result['_parent'] == 'null'

    docs_to_delete << result

    if docs_to_delete.size >= 100
      cnt += 1
      save_bulk_request_of_docs_to_delete(dir, docs_to_delete, cnt)
      docs_to_delete = []
    end
  end

  if docs_to_delete.size > 0
    cnt += 1
    save_bulk_request_of_docs_to_delete(dir, docs_to_delete, cnt)
    docs_to_delete = []
  end

  puts "took #{Time.now - started_at} to search #{info.inspect}"
end
