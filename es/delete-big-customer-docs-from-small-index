#!/usr/bin/env ruby

# Purpose: to delete any big customers documents from the small index

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option(false)
  opts.add_run_remote
end

require File.expand_path('../config/environment', __dir__)
require 'elasticsearch_scanner'
require 'fileutils'

def get_small_index_docs(mappings)
  docs = []
  mappings.each do |index_name, info|
    next if index_name =~ /^\./
    next unless index_name =~ /small/

    info['mappings'].each do |mapping_name, mapping_info|
      next if mapping_name =~ /^\./
      unless mapping_info['properties']
        # puts mapping_name + ' has no properties'
        next
      end
      unless oid_property = mapping_info['properties'].keys.detect { |name| ESHelpers::OID_PROPERTY_NAMES.include?(name) }
        # puts mapping_name + ' has no oid property'
        next
      end

      docs << {
        index_name: index_name,
        mapping_name: mapping_name,
        oid_property: oid_property
      }
    end
  end
  docs
end

def save_bulk_request_of_docs_to_delete(dir, docs_to_delete)
  time_in_ms = (Time.now.to_f * 1000).to_i
  File.open("#{dir}/delete_docs_#{time_in_ms}.request", 'wb') do |f|
    docs_to_delete.each do |doc|
      f.puts({delete: {_index: doc['_index'], _id: doc['_id'], _type: doc['_type'], _routing: doc['_routing']}}.to_json)
    end
  end
end

docs = get_small_index_docs(ESHelpers.get_mappings)

big_customer_oids = DNAClient.create_client_with_app_creds.all_big_customer_oids
oids =
  if SCRIPT_OPTIONS[:oid]
    raise("oid #{SCRIPT_OPTIONS[:oid]} is not a big customer") unless big_customer_oids.include?(SCRIPT_OPTIONS[:oid])
    [SCRIPT_OPTIONS[:oid]]
  else
    big_customer_oids
  end

AuthDB::Organization.where(id: oids).to_a.each do |org|
  log_msg_prefix = "OID #{org.id}"
  docs.each do |info|
    LOG.info "#{log_msg_prefix}: searching #{info.inspect}"
    started_at = Time.now
    url = ES_URL + '/' + info[:index_name] + '/' + info[:mapping_name] + '/_search'

    query = {
      "bool" => {
        "must" => {
          "term" => {
            info[:oid_property] => org.id
          }
        }
      }
    }

    LOG.info "#{log_msg_prefix}: ES query #{query.to_json}"
    dir = "bulk_requests/#{info[:index_name]}/#{info[:mapping_name]}/#{org.id}"

    docs_to_delete = []
    save_docs_to_delete_proc = proc do
      FileUtils.mkdir_p(dir) unless File.exist?(dir)
      save_bulk_request_of_docs_to_delete(dir, docs_to_delete)
      docs_to_delete = []
    end

    total_docs_found = 0
    scanner = ElasticSearchScanner.new(url, query)
    scanner.fields_to_return = false
    scanner.each do |result|
      total_docs_found += 1
      docs_to_delete << result
      save_docs_to_delete_proc.call if docs_to_delete.size >= 100
    end

    save_docs_to_delete_proc.call if docs_to_delete.size > 0

    LOG.info "#{log_msg_prefix}: took #{Time.now - started_at} to search #{info.inspect} found #{total_docs_found} docs to delete"
  end
end

es_bulk_uri = ESHelpers.uri_for_path('/_bulk')
Dir.glob('bulk_requests/**/**.request').each do |bulk_delete_request_file|
  res = HTTPClient.post(es_bulk_uri, File.read(bulk_delete_request_file))

  if res.kind_of?(Net::HTTPSuccess)
    LOG.info "processed #{bulk_delete_request_file}"
    FileUtils.mv(bulk_delete_request_file, bulk_delete_request_file.sub('.request', '.done'))
  else
    LOG.error "failed to process #{bulk_delete_request_file}, reason #{res.code}/#{res.body}"
  end
end
