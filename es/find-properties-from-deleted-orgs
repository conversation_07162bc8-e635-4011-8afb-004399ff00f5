#!/usr/bin/env ruby

$LOAD_PATH << File.expand_path('../ruby/lib', __dir__)

require 'json'
require 'net/http'
require 'elasticsearch_scanner'
require 'mysql2'
require 'yaml'
require 'fileutils'
require 'set'

def get_mappings(url)
  uri = URI(url)
  http = Net::HTTP.new(uri.host, uri.port)
  req = Net::HTTP::Get.new(uri.request_uri)
  res = http.request(req)
  JSON.parse(res.body)
end

def get_mapping_properties(index_name, mapping_name, mapping_info, base_prop_name = '')
  properties = {}
  return properties unless mapping_info['properties']

  mapping_info['properties'].each do |prop_name, prop_info|
    if prop_name =~ /^(\d+)_/
      oid = $1.to_i
      properties[oid] ||= []
      properties[oid] << {index_name: index_name, mapping_name: mapping_name, property: base_prop_name + prop_name, oid: oid}
    elsif prop_info['type'] == 'nested'
      get_mapping_properties(index_name, mapping_name, prop_info, base_prop_name + prop_name + '.').each do |oid, oid_properties|
        properties[oid] ||= []
        properties[oid] += oid_properties
      end
    end
  end

  properties
end

def get_properties(mappings)
  properties = {}
  mappings.each do |index_name, info|
    next if index_name =~ /^\./

    info['mappings'].each do |mapping_name, mapping_info|
      next if mapping_name =~ /^\./
      next unless mapping_info['properties']

      get_mapping_properties(index_name, mapping_name, mapping_info).each do |oid, oid_properties|
        properties[oid] ||= []
        properties[oid] += oid_properties
      end
    end
  end
  properties
end

def save_bulk_request_of_docs_to_delete(dir, docs_to_delete, cnt)
  File.open("#{dir}/delete_docs_#{cnt}.request", 'wb') do |f|
    docs_to_delete.each do |doc|
      f.puts({delete: {_index: doc['_index'], _id: doc['_id'], _type: doc['_type'], _routing: doc['_routing']}}.to_json)
    end
  end
end

def mysql_client_contacts
  @mysql_client_contacts ||=
    begin
      config = YAML.load_file("#{ENV['HOME']}/.evertrue/stage-dbs.yml")['auth']
      Mysql2::Client.new(
        host: config['host'],
        username: config['username'],
        password: config['password'],
        database: config['database']
      )
    end
end


STAGING_ES_URL = 'http://stage-searchmaster-v2-contacts-1b.priv.evertrue.com:9200'
ES_URL = STAGING_ES_URL
mappings = get_mappings(ES_URL + '/_mapping')
properties = get_properties(mappings)

valid_oids = mysql_client_contacts.query('select id from organizations where deleted = 0').map { |result| result['id'] }.to_set

properties.each do |oid, info|
  next if valid_oids.include?(oid)

end
