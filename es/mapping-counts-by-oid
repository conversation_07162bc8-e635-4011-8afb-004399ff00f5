#!/usr/bin/env ruby

require File.expand_path('../config/environment', __dir__)
include CommandLineHelpers

oid_mapping_counts = []

oids = MySQLHelpers.create_client(:auth).query('SELECT id FROM organizations WHERE deleted = false AND purge_at IS NULL', as: :array).map(&:first)

es_schema = ESSchema.new(ES_URL)
dna_client = DNAClient.create_app_client
oids.each do |oid|
  search_index_regex = dna_client.is_big_customer?(oid) ? /big/ : /small/

  es_schema.docs.each do |info|
    next unless info[:index_name] =~ search_index_regex

    count_query = info[:oid_property] + ':' + oid.to_s
    next unless (cnt = es_schema.get_doc_count(info, count_query)) > 0

    oid_mapping_counts << info.merge(oid: oid, count: cnt)
  end
end

headings = {
  OID: :oid,
  INDEX: :index_name,
  MAPPING: :mapping_name,
  COUTN: :count
}

max_characters_by_heading = Hash.new(0)
headings.keys.each do |heading_name|
  max_characters_by_heading[heading_name] = heading_name.to_s.size
end

oid_mapping_counts.each do |info|
  headings.each do |heading_name, key|
    size = info[key].to_s.size
    max_characters_by_heading[heading_name] = size if max_characters_by_heading[heading_name] < size
  end
end

DISPLAY_STR_FORMAT = max_characters_by_heading.values.map { |size| "%-#{size}s" }.join(' | ') + "\n"

display_proc = Proc.new do |info|
  printf(
    DISPLAY_STR_FORMAT,
    *headings.values.map { |key| info[key].to_s }
  )
end

print underline_string(sprintf(DISPLAY_STR_FORMAT, *headings.keys.map(&:to_s)))

oid_mapping_counts.each do |info|
  display_proc.call info
end
