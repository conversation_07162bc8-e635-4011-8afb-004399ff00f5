#!/usr/bin/env ruby

require File.expand_path('../config/environment', __dir__)
require 'elasticsearch_facet_scanner'

index_mapping_oids = {}

es_schema = ESSchema.new(ES_URL)
es_schema.docs.each do |info|
  LOG.info "searching #{info.inspect}"
  started_at = Time.now

  scanner = ElasticSearchFacetScanner.new(es_schema.get_search_url(info), info[:oid_property])
  scanner.each do |bucket|
    index_mapping_oids[info[:index_name]] ||= {}
    index_mapping_oids[info[:index_name]][info[:mapping_name]] ||= []
    index_mapping_oids[info[:index_name]][info[:mapping_name]] << bucket['key']
  end

  LOG.info "took #{Time.now - started_at} to search #{info.inspect}"
end

File.open(TMP_DIR.join('index_mapping_oids.json'), 'wb') { |f| f.write(index_mapping_oids.to_json) }
