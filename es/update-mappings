#!/usr/bin/env ruby

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_input_file()
  opts.add_run_remote()

  opts.on('-t', '--type TYPE', 'Mapping type') do |v|
    opts[:type] = v
  end

  opts.require_option(:type)
end

require_relative '../config/environment'

def update_index_mappings(index_name, type, properties)
  uri = ESHelpers.uri_for_path("/#{index_name}/_mappings/#{type}")
  HTTPClient.put(uri, properties.to_json)
end

contacts_index_prefix = "#{APP_ENV_PREFIX}-contacts-oid-"
contacts_big_index_prefix = "#{APP_ENV_PREFIX}-big-contacts-"
contacts_small_index_prefix = "#{APP_ENV_PREFIX}-small-contacts-"
contacts_template_index = "#{APP_ENV_PREFIX}-contacts-template"

properties = JSON.parse(File.read(SCRIPT_OPTIONS[:file]))
unless properties['properties']
  $stderr.puts("mapping file missing top level properties key")
  exit(1)
end

res = update_index_mappings(contacts_template_index, SCRIPT_OPTIONS[:type], properties)
if res.kind_of?(Net::HTTPSuccess)
  LOG.info("Updated ES mappings for #{SCRIPT_OPTIONS[:type]} on index #{contacts_template_index}")
else
  LOG.warn("Failed to update ES mappings for #{SCRIPT_OPTIONS[:type]} on index #{contacts_template_index} with #{res.body}")
end

ESHelpers.get_indexes().each do |index_info|
  index_name = index_info['index']
  if index_name.include?(contacts_index_prefix) ||
     index_name.include?(contacts_big_index_prefix) ||
     index_name.include?(contacts_small_index_prefix)

    res = update_index_mappings(index_name, SCRIPT_OPTIONS[:type], properties)
    if res.kind_of?(Net::HTTPSuccess)
      LOG.info("Updated ES mappings for #{SCRIPT_OPTIONS[:type]} on index #{index_name}")
    else
      LOG.warn("Failed to update ES mappings for #{SCRIPT_OPTIONS[:type]} on index #{index_name} with #{res.body}")
    end
  end
end
