#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
end

require File.expand_path('../config/environment', __dir__)

require 'elasticsearch_scanner'
require 'fileutils'

def get_mappings(url)
  uri = URI(url)
  http = Net::HTTP.new(uri.host, uri.port)
  req = Net::HTTP::Get.new(uri.request_uri)
  res = http.request(req)
  JSON.parse(res.body)
end

OID_PROPERTY_NAMES = ['oid', 'contact_oid', 'organization_id', 'content_oid']

def get_docs(mappings)
  docs = []
  mappings.each do |index_name, info|
    next if index_name =~ /^\./
    next unless index_name =~ /small/

    info['mappings'].each do |mapping_name, mapping_info|
      next if mapping_name =~ /^\./
      unless mapping_info['properties']
        # puts mapping_name + ' has no properties'
        next
      end
      unless oid_property = mapping_info['properties'].keys.detect { |name| OID_PROPERTY_NAMES.include?(name) }
        # puts mapping_name + ' has no oid property'
        next
      end

      docs << {
        index_name: index_name,
        mapping_name: mapping_name,
        oid_property: oid_property
      }
    end
  end
  docs
end

def save_bulk_request_of_docs_to_delete(dir, docs_to_delete, cnt)
  File.open("#{dir}/delete_docs_#{cnt}.request", 'wb') do |f|
    docs_to_delete.each do |doc|
      f.puts({delete: {_index: doc['_index'], _id: doc['_id'], _type: doc['_type'], _routing: doc['_routing']}}.to_json)
    end
  end
end

mappings = get_mappings(ES_URL + '/_mapping')
docs = get_docs(mappings)

docs.each do |info|
  puts "searching #{info.inspect}"
  started_at = Time.now
  url = ES_URL + '/' + info[:index_name] + '/' + info[:mapping_name] + '/_search'

  query = {
    "bool" => {
      "must" => {
        "term" => {
          info[:oid_property] => SCRIPT_OPTIONS[:oid]
        }
      }
    }
  }

  cnt = 0
  dir = "bulk_requests/#{info[:index_name]}/#{info[:mapping_name]}"
  FileUtils.mkdir_p(dir)

  docs_to_delete = []
  scanner = ElasticSearchScanner.new(url, query)
  scanner.fields_to_return = false
  scanner.each do |result|
    docs_to_delete << result

    if docs_to_delete.size >= 100
      cnt += 1
      save_bulk_request_of_docs_to_delete(dir, docs_to_delete, cnt)
      docs_to_delete = []
    end
  end

  if docs_to_delete.size > 0
    cnt += 1
    save_bulk_request_of_docs_to_delete(dir, docs_to_delete, cnt)
    docs_to_delete = []
  end

  puts "took #{Time.now - started_at} to search #{info.inspect}"
end
