#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
end

require File.expand_path('../config/environment', __dir__)

oid = SCRIPT_OPTIONS[:oid].to_s

es_schema = ESSchema.new(ES_URL)

stats = {}

es_schema.docs.each do |info|
  next unless info[:index_name] =~ /small/

  count_query = info[:oid_property] + ':' + oid
  next unless (num_docs = es_schema.get_doc_count(info, count_query)) > 0

  stats[info[:mapping_name]] = {
    small: num_docs,
    small_max_timestamp: es_schema.get_max_timestamp(info, oid),
    small_updated_at: es_schema.get_max_value_for(info, oid, 'updated_at')
  }
end

es_schema.docs.each do |info|
  next unless info[:index_name] =~ /big/

  count_query = info[:oid_property] + ':' + oid
  next unless (num_docs = es_schema.get_doc_count(info, count_query)) > 0

  stats[info[:mapping_name]] ||= {}
  stats[info[:mapping_name]][:big] = num_docs
  stats[info[:mapping_name]][:big_max_timestamp] = es_schema.get_max_timestamp(info, oid)
  stats[info[:mapping_name]][:big_updated_at] = es_schema.get_max_value_for(info, oid, 'updated_at')
  stats[info[:mapping_name]][:remaining] = stats[info[:mapping_name]][:small].to_i - num_docs
end

print JSON.pretty_generate(stats) + "\n"
