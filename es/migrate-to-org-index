#!/usr/bin/env ruby

=begin
This script will be used to do the migration from small/big index to
the org specific index for the given list of oids.
The script takes -e <environment>, -o <list of comma separated oids>
as the script options.
e.g  ./migrate-to-org-index -e stage -o 201
e.g  ./migrate-to-org-index -e stage -o 201,202
=end

require 'net/ssh'
require File.expand_path('../config/script_options', __dir__)
require File.expand_path('../lib/spark_helpers', __dir__)
require 'time'

DOCS_PER_SHARD = 500000
STAGE_CORES = 8 # Stage has max 16 cores on 2 executors for max concurrency of 2
PROD_CORES = 16 # Prod has max 256 cores, 8 workers. Choosing 16 cores per job, max concurrency of 8 to leave capacity for routine jobs.

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oids_option

  opts.on('--dry', 'run the command without actually executing any of the statements to see output') do 
    opts[:dry] = true
  end

  opts.on('--concurrency=NUM_THREADS', 'The number of threads allowed to execute concurrently, default 1') do |v|
    opts[:concurrency] = v.to_i
  end

  opts.on('--spark-cores=CORES', 'The number of cores to tell spark to assign to the job, default to max cores') do |v|
    opts[:spark_cores] = v.to_i
  end

  opts.on('--kill-at=TIME', 'The time at which the process will be killed. This is to prevent migrations outside of approved hours.') do |v|
    opts[:kill_at] = Time.iso8601(v)
  end

  opts.on('--shutdown-at=TIME' 'The time at which a graceful shutdown will be attempted.') do |v|
    opts[:shutdown_at] = Time.iso8601(v)
  end
end

require File.expand_path('../config/environment', __dir__)
include CommandLineHelpers

oids = SCRIPT_OPTIONS[:oids]
env = SCRIPT_OPTIONS[:environment]

SPARK_CORES = 
  if SCRIPT_OPTIONS[:spark_cores]
    SCRIPT_OPTIONS[:spark_cores]
  elsif env == 'stage'
    STAGE_CORES
  else
    PROD_CORES
  end

es_schema = ESSchema.new(ES_URL)
shutdown_mutex = Mutex.new
shutdown = false

start_shutdown = lambda do
  shutdown_mutex.synchronize do
    log "BEGIN GRACEFUL SHUTDOWN"
    shutdown = true
  end
end  

def log(msg)
  puts "#{Time.now.strftime('%Y/%m/%d %H:%M:%S.%3N')} (#{Thread.current.name}) - #{msg}"
end

def run_cmd_proc(cmd_str, cmd_proc)
    log bold_string("Going to execute: " + cmd_str)
    if SCRIPT_OPTIONS[:dry]
      response = 'no response in dry mode, command not executed'
    else
      response = cmd_proc.call
    end
    log "Response: #{response}"
    response
end

def run_cmd(cmd)
  run_cmd_proc(cmd, Proc.new{ `#{cmd}` })
end


@oid_index_hash = Hash.new

oids.each do |oid|
    oid_alias = ESContactHelpers.index_name_for_oid(oid)
    index_names = es_schema.get_index_for_alias(oid_alias)
    index_name = index_names[0]
    @oid_index_hash[oid] = index_name
end


index_set = Set.new(@oid_index_hash.values)
exit_status = 0
concurrency = SCRIPT_OPTIONS[:concurrency] || 1
log "Starting migrations with concurrency #{concurrency}"

do_cleanup = lambda do
  log "PERFORMING CLEANUP"
  log "Resume writes to the small and big index"
  block_writes_payload = {
    index: {
      blocks: {
        write: false
      }
    }
  }.to_json

  index_set.each do |index|
    run_cmd("curl -XPUT '#{ES_URL}/#{index}/_settings' -d '#{block_writes_payload}'")
  end

  log "DONE"
end

# Before script exits from any scenario, attempt to unblock indexes
Signal.trap("INT") do
  puts "Caught SIGINT, running cleanup..."
  do_cleanup[]
  exit
end

END {
  do_cleanup[]
}

log "Block writes to the small/big index"
block_writes_payload = {
  index: {
    blocks: {
      write: true
    }
  }
}.to_json

index_set.each do |index|
  run_cmd("curl -XPUT '#{ES_URL}/#{index}/_settings' -d '#{block_writes_payload}'")
end

class MigrationError < StandardError
end

migrate_org = lambda do |oid|
  Thread.current.name = "oid:#{oid}"

  puts "\n"
  log bold_string("Starting migration for oid: #{oid}")

  shutdown_mutex.synchronize do
    if shutdown
      log "not starting migration, shutdown initiated."
      return
    end
  end
  
  search_client = SearchClient.create_app_client('search_api')
  dna_client = DNAClient.create_app_client('dna_api')

  org_mappings_in_use = []
  es_schema = ESSchema.new(ES_URL)
  hash = Hash.new
  
  source_index_name = @oid_index_hash[oid]

  log "source index: #{source_index_name}"

  es_schema.docs.each do |info|
    next unless info[:index_name] =~ /#{source_index_name}/

    count_query = info[:oid_property] + ':' + oid.to_s
    count = es_schema.get_doc_count(info, count_query)
    next unless count > 0

    log "Count for mapping = #{info[:mapping_name]} and oid_property = #{count_query} is =  #{count}"
    hash[info[:mapping_name]] = count

    org_mappings_in_use << info[:mapping_name]
  end

  # first thing to move
  if org_mappings_in_use.delete('contact')
    org_mappings_in_use.unshift('contact')
  end

  # last thing to move
  if org_mappings_in_use.delete('.percolator')
    org_mappings_in_use << '.percolator'
  end

  total_doc_count = hash.values.sum
  desired_shards = total_doc_count.to_f/DOCS_PER_SHARD
  shards_ceil = [desired_shards.ceil, 1].max
  log bold_string("Total doc count: #{total_doc_count}")

  log bold_string("Going to create index for oid #{oid} with #{shards_ceil} shards.")
  response = run_cmd_proc("POST /search/v1/index", Proc.new{ search_client.create_index(oid, shards_ceil) })

  raise MigrationError.new("oid:#{oid} failed to create index") unless response.kind_of? Net::HTTPSuccess or SCRIPT_OPTIONS[:dry]

  # previous call created the new index, refresh local schema
  es_schema = ESSchema.new(ES_URL)
  target_index = if SCRIPT_OPTIONS[:dry]
                  "DRY_MODE_PLACEHOLDER"
                else
                  es_schema.org_index_name(oid)
                end
  
  log "target index: #{target_index}"

  # Disable refresh while migrating
  payload = {
    index: {
      refresh_interval: '-1'
    }
  }.to_json

  log bold_string("Disabling refresh for target index #{target_index}")
  run_cmd("curl -XPUT '#{ES_URL}/#{target_index}/_settings' -d '#{payload}'")


  if total_doc_count > 0
    log 'Log into spark master node'
    spark_master_host = SparkHelpers.spark_master_host
    Net::SSH.start(spark_master_host) do |ssh|
      log "Migrating data from #{source_index_name} to #{target_index}"
      cmd = "sudo -H -u spark bash -c '/usr/lib/spark/bin/spark-submit --class com.et.contacts.spark.SparkJobRunner /usr/lib/spark/lib/ext/contacts_spark-1.0-basepom-SNAPSHOT-jar-with-dependencies.jar -Djob_name=BigCustomerEsMigrationJob -Doid=#{oid} -Dsource_index=#{source_index_name} -Dtarget_index=#{target_index} -Dfilter_types=#{org_mappings_in_use.join(',')} -Dfirst_run=true -Dspark.cores.max=#{SPARK_CORES}'"

      # route output through log so its tagged with the thread context for easier searching later
      ssh_exec = Proc.new do 
        ssh.exec!(cmd) do |ch, stream, data|
          log data
        end
      end

      run_cmd_proc(cmd, ssh_exec)
    end
  else
    log 'Total doc count 0, skipping spark migration' 
  end

  # Re-enable refresh now that data transfer is complete
  payload = {
    index: {
      refresh_interval: '1s'
    }
  }.to_json

  log bold_string("Enabling refresh for target index #{target_index}")
  run_cmd("curl -XPUT '#{ES_URL}/#{target_index}/_settings' -d '#{payload}'")
  
  # Force a manual synchronus refresh so that counts are accurate
  log bold_string("Forcing refresh for target index #{target_index}")
  run_cmd("curl -XPOST '#{ES_URL}/#{target_index}/_refresh'")

  #verify counts against original schema
  es_schema.docs.each do |info|
    next unless (info[:index_name] = target_index) && hash.key?(info[:mapping_name])

    count_query = info[:oid_property] + ':' + oid.to_s
    count = es_schema.get_doc_count(info, count_query)

    log "Verifying count on #{info[:index_name]} for mapping = #{info[:mapping_name]} and oid_property = #{count_query} , count is =  #{count}"
    if hash[info[:mapping_name]] != count
      log ""
      log "!!!! ERROR !!!!"
      log "Previous count #{hash[info[:mapping_name]]} does not match for #{info[:mapping_name]} with current count #{count}"
      
      raise MigrationError.new("oid:#{oid} count validation failed")
    end
    hash.delete(info[:mapping_name])
  end unless SCRIPT_OPTIONS[:dry]
  log bold_string("SKIPPED COUNT VERIFICATION IN DRY MODE") if SCRIPT_OPTIONS[:dry]

  log 'Set DNA setting is-big-customer to true'
  run_cmd_proc("SET is-big-customer = true", Proc.new{ dna_client.update_setting_value("is-big-customer", true, { oid: oid }) })

  log 'Update ES alias to point to the new index'
  alias_payload = {
    actions: [
      {remove: { index: source_index_name, alias: ESContactHelpers.index_name_for_oid(oid)}},
      {add: { index: target_index, alias: ESContactHelpers.index_name_for_oid(oid)}}
    ]
  }.to_json

  run_cmd("curl -XPOST '#{ES_URL}/_aliases' -d '#{alias_payload}'")
end

begin
  migration_job_mutex = Mutex.new
  migration_jobs = []
  oid_queue = SizedQueue.new(concurrency)

  producer_thread = Thread.new do
    oids.each do |oid|
      if oid_queue.closed?
        log "Queue closed, closing consumer"
        Thread.current.terminate
      end

      shutdown_mutex.synchronize do
        if shutdown
          log "Shutdown producer thread"
          oid_queue.close
          Thread.current.terminate
        end
      end

      oid_queue << oid
    end

    oid_queue.close
  end

  consumer_thread = Thread.new do
    begin
      loop do
        sleep 5

        shutdown_mutex.synchronize do
          if shutdown
            log "Shutdown consumer thread"
            Thread.current.terminate
          end
        end

        migration_job_mutex.synchronize do
          # cleanup done jobs
          migration_jobs.each do |job|
            # status false means the job completed successfully
            if job.status == false || job.status == nil
              migration_jobs.delete(job)
            end
          end

          if migration_jobs.size < concurrency
            oid = oid_queue.pop

            job = Thread.new { migrate_org.call(oid) }
            migration_jobs << job
          end
        end

        if oid_queue.closed? && oid_queue.empty?
          break
        end

        # sleep before attempting to add more jobs
        sleep 1
      end
    ensure
      oid_queue.close
    end
  end

    # Graceful shutdown monitor
    if SCRIPT_OPTIONS[:shutdown_at]
      Thread.new do
        duration = SCRIPT_OPTIONS[:shutdown_at] - Time.now
        log "SHUTDOWN AT #{SCRIPT_OPTIONS[:shutdown_at]}, duration: #{duration}"
        sleep duration if duration > 0
        
        start_shutdown[]
      end
    end
  
    # Kill monitor
    if SCRIPT_OPTIONS[:kill_at]
      Thread.new do
        duration = SCRIPT_OPTIONS[:kill_at] - Time.now
        log "KILL AT #{SCRIPT_OPTIONS[:kill_at]}, duration: #{duration}"
  
        sleep duration if duration > 0
        
        log "KILL SWITCH TRIGGERED"
        exit 2
      end
    end
rescue MigrationError => e
  exit_status = 1
ensure
  producer_thread.join
  consumer_thread.join
  migration_jobs.each do |j|
    begin
      j.value
    rescue => e
      log "!!! ERROR migration for oid:#{j.name} failed: #{e}"
      exit_status = 1
    end
  end
end

if exit_status > 0
  puts '!!! ERROR: SOME MIGRATIONS FAILED, SEE OUTPUT FOR DETAILS !!!'
end

exit exit_status