#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-i', '--index INDEX', 'Specify the big or small index') do |v|
    opts[:index] = v
  end

  opts.require_option(:index)
end

unless ['big', 'small'].include?(SCRIPT_OPTIONS[:index])
  $stderr.puts "invalid index #{SCRIPT_OPTIONS[:index]} specified, valid values are big or small"
  exit 1
end

require_relative '../../config/environment'
include CommandLineHelpers

es_aliases = ESHelpers.get_aliases()

current_index_name = es_aliases.keys.detect { |index_name| index_name.include?("-#{SCRIPT_OPTIONS[:index]}-") && !es_aliases[index_name]['aliases'].empty? }

new_index_number =
  if current_index_name =~ /\-0$/
    1
  else
    0
  end

new_index_name = current_index_name.sub(/\-\d/, "-#{new_index_number}")

puts '#1 Set up the new indexes'
if es_aliases[new_index_name]
  puts bold_string("# Skipping, index #{new_index_name} already exists")
  puts '# cd ~/dev/search'
  puts "# ruby bin/index-setup.rb #{APP_ENV_PREFIX} #{new_index_number} contacts"
else
  puts bold_string('cd ~/dev/search')
  puts bold_string("ruby index-setup.rb #{APP_ENV_PREFIX} #{new_index_number} contacts")
end

puts ''
puts '#2 Turn off ES refreshing for new index'
payload = {
  index: {
    refresh_interval: "-1"
  }
}.to_json

puts bold_string("curl -XPUT '#{ES_URL}/#{new_index_name}/_settings' -d '#{payload}'")

puts ''
puts '#3 Start the redindex process'
payload = {
  conflicts: 'proceed',
  source: {
    index: current_index_name
  },
  dest: {
    index: new_index_name
  }
}.to_json

puts bold_string("curl '#{ES_URL}/_reindex?wait_for_completion=false' -d '#{payload}'")

puts ''
puts '#4 Monitor the progress of reindexing'
puts '# KOPF'
puts bold_string("open '#{ES_URL}/_plugin/kopf/#!/cluster'")

puts ''
puts '#5 Monitor reindex task in ES and keep track of the start_time_in_millis'
puts bold_string("curl '#{ES_URL}/_tasks/?pretty&detailed=true&actions=*reindex'")
puts '# or use this command'
puts bold_string("./es/reindex/reindex-task-progress -e #{APP_ENV_PREFIX}")

puts ''
puts '#6 Setup the replicas'

payload = {
  index: {
    number_of_replicas: '1'
  }
}.to_json

puts bold_string("curl -XPUT '#{ES_URL}/#{new_index_name}/_settings' -d '#{payload}'")

puts ''
puts '#7 Check the counts and if they are way off run Cleanup logic prior to locking'
puts bold_string("./es/reindex/doc-counts -e #{APP_ENV_PREFIX} -i #{SCRIPT_OPTIONS[:index]}")
puts '# cleanup logic'
puts bold_string("./es/reindex/cleanup -e #{APP_ENV_PREFIX} -i #{SCRIPT_OPTIONS[:index]}")

puts ''
puts '#8 Block writes'

payload = {
  index: {
    blocks: {
      write: true
    }
  }
}.to_json

puts bold_string("curl -XPUT '#{ES_URL}/#{current_index_name}/_settings' -d '#{payload}'")

puts ''
puts '#9 Reindex again from the 1st runs start_time_in_millis'

payload = {
  conflicts: 'proceed',
  source: {
    index: current_index_name,
    query: {
      range: {
        _timestamp: {
          gte: 0
        }
      }
    }
  },
  dest: {
    index: new_index_name,
    version_type: 'external'
  }
}.to_json.sub('"gte":0', '"gte":START_TIME_IN_MILLIS')

puts bold_string("curl '#{ES_URL}/_reindex?wait_for_completion=false' -d '#{payload}'")

puts ''
puts '#10 Reindex the percolator'

payload = {
  source: {
    index: current_index_name,
    type: '.percolator',
    size: 20_000
   },
  dest: {
    index: new_index_name
   }
}.to_json

puts bold_string("curl '#{ES_URL}/_reindex' -d '#{payload}'")

puts ''
puts '#11 Run cleanup logic'

puts bold_string("./es/reindex/cleanup -e #{APP_ENV_PREFIX} -i #{SCRIPT_OPTIONS[:index]}")

puts ''
puts '#12 Change refresh interval back to 1 second'

payload = {
  index: {
    refresh_interval: '1s'
  }
}.to_json

puts bold_string("curl -XPUT '#{ES_URL}/#{new_index_name}/_settings' -d '#{payload}'")

puts ''
puts '#13 Alias Flip'

puts bold_string('cd ~/dev/search')
puts bold_string("./bin/update-aliases -e #{APP_ENV_PREFIX} -a #{SCRIPT_OPTIONS[:index]}")

