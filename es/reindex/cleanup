#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-i', '--index INDEX', 'Specify the big or small index') do |v|
    opts[:index] = v
  end

  opts.require_option(:index)
end

unless ['big', 'small'].include?(SCRIPT_OPTIONS[:index])
  $stderr.puts "invalid index #{SCRIPT_OPTIONS[:index]} specified, valid values are big or small"
  exit 1
end

require_relative '../../config/environment'

def get_total_hits(index_name, mapping_name)
  if mapping_name
    ESHelpers.get_mapping_count("/#{index_name}/#{mapping_name}/_search?size=0").dig('hits', 'total')
  else
    ESHelpers.get_mapping_count("/#{index_name}/_search?size=0").dig('hits', 'total')
  end
end

es_aliases = ESHelpers.get_aliases()
es_mappings = ESHelpers.get_mappings()
es_docs = ESHelpers.get_docs(es_mappings)

current_index_name = es_aliases.keys.detect { |index_name| index_name.include?("-#{SCRIPT_OPTIONS[:index]}-") && !es_aliases[index_name]['aliases'].empty? }

new_index_number =
  if current_index_name =~ /\-0$/
    1
  else
    0
  end

new_index_name = current_index_name.sub(/\-\d/, "-#{new_index_number}")

current_mappings = es_docs.select { |doc| doc[:index_name] == current_index_name }.map { |doc| doc[:mapping_name] }.sort - ['.percolator']

mappings_to_cleanup = current_mappings.select do |mapping_name|
  current_total_hits = get_total_hits(current_index_name, mapping_name)
  new_total_hits = get_total_hits(new_index_name, mapping_name)
  diff = new_total_hits - current_total_hits
  diff != 0
end

puts "ssh #{SparkHelpers.spark_master_host.split('.').first}"
puts "sudo -H -u spark bash -c '/usr/lib/spark/bin/spark-submit --class com.et.contacts.spark.SparkJobRunner /usr/lib/spark/lib/ext/contacts_spark-1.0-basepom-SNAPSHOT-jar-with-dependencies.jar -Djob_name=ESReindexDeleteJob -Dsource_index=#{current_index_name} -Dtarget_index=#{new_index_name} -Dfilter_types=#{mappings_to_cleanup.join(',')} -Dexecute_deletes=true'"
