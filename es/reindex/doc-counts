#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('--old INDEX', 'Specify old index name') do |v|
    opts[:old_index] = v
  end

  opts.on('--new INDEX', 'Specify new index name') do |v|
    opts[:new_index] = v
  end

  opts.require_option(:old_index)
  opts.require_option(:new_index)
end

require_relative '../../config/environment'

def get_total_hits(index_name, mapping_name)
  if mapping_name
    ESHelpers.get_mapping_count("/#{index_name}/#{mapping_name}/_search?size=0").dig('hits', 'total')
  else
    ESHelpers.get_mapping_count("/#{index_name}/_search?size=0").dig('hits', 'total')
  end
end

es_aliases = ESHelpers.get_aliases()
es_mappings = ESHelpers.get_mappings()
es_docs = ESHelpers.get_docs(es_mappings)

current_index_name = SCRIPT_OPTIONS[:old_index]
new_index_name = SCRIPT_OPTIONS[:new_index]

current_mappings = es_docs.select { |doc| doc[:index_name] == current_index_name }.map { |doc| doc[:mapping_name] }.sort

data = current_mappings.map do |mapping_name|
  current_total_hits = get_total_hits(current_index_name, mapping_name)
  new_total_hits = get_total_hits(new_index_name, mapping_name)
  diff = new_total_hits - current_total_hits

  {
    mapping: (diff == 0 ? '' : '* ') + mapping_name,
    current_total_hits: current_total_hits,
    new_total_hits: new_total_hits,
    diff: diff
  }
end

current_total_hits = get_total_hits(current_index_name, nil)
new_total_hits = get_total_hits(new_index_name, nil)
diff = new_total_hits - current_total_hits

data << {
  mapping: 'Totals',
  current_total_hits: current_total_hits,
  new_total_hits: new_total_hits,
  diff: diff
}

headers = {
  'Mapping' => :mapping,
  'Current Index Total Records' => :current_total_hits,
  'New Index Total Records' => :new_total_hits,
  'Diff (new - current)' => :diff
}

table = TableView.new(headers)
table.render("Comparing (current) #{current_index_name} to (new) #{new_index_name}" => data)
