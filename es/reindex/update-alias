#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option()

  opts.on('--old INDEX', 'Specify old index name') do |v|
    opts[:old_index] = v
  end

  opts.on('--new INDEX', 'Specify new index name') do |v|
    opts[:new_index] = v
  end

  opts.require_option(:old_index)
  opts.require_option(:new_index)
end

require_relative '../../config/environment'

alias_name = ESContactHelpers.index_name_for_oid(SCRIPT_OPTIONS[:oid])
current_index_name = SCRIPT_OPTIONS[:old_index]
new_index_name = SCRIPT_OPTIONS[:new_index]

actions = [
  {
    remove: {
      index: SCRIPT_OPTIONS[:old_index],
      alias: alias_name
    }
  },
  {
    add: {
      index: SCRIPT_OPTIONS[:new_index],
      alias: alias_name
    }
  }
]

HTTPClient.post(URI("#{ES_URL}/_aliases"), {actions: actions}.to_json)
