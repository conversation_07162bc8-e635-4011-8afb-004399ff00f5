#!/usr/bin/env ruby

# Purpose: right after rebooting a ES data node.  Use this script to reroute UNASSIGNED shards to that server.
#  The reason being that the server already has the data for these shards

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-s', '--server SERVER', 'ES Data node') do |v|
    opts.options[:server] = v
  end

  opts.require_option(:server)
end

require File.expand_path('../config/environment', __dir__)

def get_shards()
  HTTPClient.get(ESHelpers.uri_for_path('/_cat/shards')).body.split("\n").map do |line|
    index, shard, shard_type, status, not_sure, size, ip, host = *line.split(/\s+/)
    {
      index: index,
      shard: shard.to_i,
      status: status
    }
  end
end

def get_unassigned_shards()
  get_shards().select do |shard|
    shard[:status] == 'UNASSIGNED'
  end
end

def reroute_unassigned_shard(index, shard, server)
  payload = {
    commands: [
      {
        allocate: {
          index: index,
          shard: shard,
          node: server,
          allow_primary: true
        }
      }
    ]
  }

  HTTPClient.post(ESHelpers.uri_for_path('/_cluster/reroute'), payload.to_json)
end

get_unassigned_shards().each do |shard|
  res = reroute_unassigned_shard(shard[:index], shard[:shard], SCRIPT_OPTIONS[:server])
  puts res.body
end
