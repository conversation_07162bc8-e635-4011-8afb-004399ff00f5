## How to Restore a Deleted Index

ElasicSearch has the ability to restore indexes back to snapshot.

To get a list of Snapshots you do

```
> curl -s http://stage-searchmaster-v2-contacts-1b:9200/_snapshot/07-2024/_all | jsonpp
{
  "snapshots": [
    {
      "snapshot": "07-01_0015",
      "version_id": 2040399,
      "version": "2.4.3",
      "indices": [
        "stage-contacts-oid-158-20240630",
        "stage-contacts-org-370-0",
        "stage-small-contacts-0",
        "contacts-big",
        "stage-users-1",
        "aliases",
        "backup_test_1719792902",
        "stage-contacts-template",
        "stage-sodas-1",
        "aliaswa",
        ".reindex-status",
        "list",
        "dynamic-lists",
        ".kibana",
        "lists",
        ".marvel-kibana",
        "alias",
        "stage-contacts-oid-158-20240627",
        "stage-contacts-oid-158-20240626",
        "dynamic_list",
        "awdwadsda",
        "contacts",
        "snapshot",
        "stage-big-contacts-0"
      ],
      "state": "SUCCESS",
      "start_time": "2024-07-01T00:15:06.215Z",
      "start_time_in_millis": 1719792906215,
      "end_time": "2024-07-01T01:08:33.626Z",
      "end_time_in_millis": 1719796113626,
      "duration_in_millis": 3207411,
      "failures": [],
      "shards": {
        "total": 534,
        "failed": 0,
        "successful": 534
      }
    },
```

Then to restore to the latest snapshot

```
curl -s -XPOST http://stage-searchmaster-v2-contacts-1b:9200/_snapshot/07-2024/07-01_0015/_restore -d '
{
  "indices": "stage-small-contacts-0",
  "include_global_state": false
}'
```
