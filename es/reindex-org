#!/usr/bin/env ruby

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:shards] = 1
  opts[:replicas] = 1

  opts.add_oid_option

  opts.on('-s', '--shards NUM_SHARDS', Integer, 'Number of shards for the org index') do |v|
    opts[:shards] = v
  end

  opts.on('-r', '--replicas NUM_REPLICAS', Integer, 'Number of replicas for the org index') do |v|
    opts[:replicas] = v
  end
end

require_relative '../config/environment'

GB_PER_SHARD = 20 * 1024 * 1024 * 1024

def search_client
  @search_client ||= SearchClient.create_client_with_app_creds
end

org_alias = "#{ESContactHelpers.index_prefix}-contacts-oid-#{SCRIPT_OPTIONS[:oid]}"
destination_index_name = "#{ESContactHelpers.index_prefix}-contacts-oid-#{SCRIPT_OPTIONS[:oid]}-#{Time.now.strftime('%Y%m%d')}"

aliases = ESHelpers.get_aliases()

current_index_name = aliases.detect { |_, alias_info| alias_info['aliases'].key?(org_alias) }&.first

unless current_index_name
  $stderr.puts("no existing indexo for org #{SCRIPT_OPTIONS[:oid]}")
  exit(1)
end

size_in_bytes = ESHelpers.get_index_stats(current_index_name).dig('indices', current_index_name, 'primaries', 'store', 'size_in_bytes')
min_shards = (size_in_bytes / GB_PER_SHARD) + 1
shards = [min_shards, SCRIPT_OPTIONS[:shards]].max

if aliases.key?(destination_index_name)
  $stderr.puts("index #{destination_index_name} already exists")
  exit(1)
end

puts "reindex org #{SCRIPT_OPTIONS[:oid]} with #{shards} shard(s) and #{SCRIPT_OPTIONS[:replicas]} replica(s) to index #{destination_index_name} from index #{current_index_name}"

res = search_client.reindex(
  SCRIPT_OPTIONS[:oid],
  shards,
  SCRIPT_OPTIONS[:replicas],
  destination_index_name
)

print "\nReindex Response:\n"
print JSON.pretty_generate(JSON.parse(res.body)) + "\n"
