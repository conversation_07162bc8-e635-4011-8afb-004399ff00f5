#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
end

path = "#{SCRIPT_OPTIONS[:environment]}-es-migration-report-#{ Time.now.strftime("%Y-%m-%d_%H-%M-%S") }.csv"
puts "Generating #{path}"

require File.expand_path('../config/environment', __dir__)

headers = [
  :oid,
  :org_slug,
  :status,
  :current_index,
  :note
]


@es_schema = ESSchema.new(ES_URL)

def get_source_index(oid)
  begin
    @es_schema.get_index_for_alias("#{SCRIPT_OPTIONS[:environment]}-contacts-oid-#{oid}")
  rescue RuntimeError
    nil
  end 
end

CSV.open(path, 'w') do |csv|
  csv << headers
  AuthDB::Organization.where(deleted: 0).find_each do |org|
    print '.'
    source_index = get_source_index(org.id)
    note = nil

    status = 
      if source_index == nil
        note = "Missing index"
        "IGNORE"
      elsif source_index.length != 1
        note = "Alias points to multiple indices"
        "INVALID"
      elsif source_index[0] =~ /big|small/
        "PENDING"
      elsif source_index[0] =~ /#{SCRIPT_OPTIONS[:environment]}-contacts-oid-#{org.id}-\d+/
        "DONE"
      else
        note = "Invalid source index"
        "ERROR"
      end
    
    unless ["INVALID", "IGNORE"].include? status
      source_index = source_index[0]
    end
        

    csv << [
      org.id,
      org.slug,
      status,
      source_index,
      note
    ]
    csv.flush
  end
end