Steps to create new ES nodes (MANUAL STEPS)

All nodes are on a weekly backup schedule with 2 week retention period. Backup creates AMI for each node. 
To create a new node you can search for the ami of the node and create a new instance from the AMI

Go to AWS ec2 dashboard
Select AMI from left
Search for the image you want. (Example prod-search-v2-contacts-1c-5_AMI_i-0c8f9709a3a36bae2_2024-06-09)
Select launch instance from AMI
Fill out instance creation details (you can use a current live node as guide)
Be sure to name the new node something different from a current node you can increment by 1 example from 1c-5 to 1c-6
Launch new node
Connect to new node (SSH or Sessions manager)

Stop chef - systemctl stop chef-client
Maybe worth it to search and kill chef processes - ps aux | grep chef-client and kill -9 PID

Stop elasticsearch - systemctl stop elasticsearch

Edit /etc/hosts - vim /etc/hosts
	
update hostname -  example add  or update ************ prod-search-v2-contacts-1c-6.priv.evertrue.com prod-search-v2-contacts-1c-6

This should be the new server IP and name you are trying to add 

Update hostnames - example  sudo hostnamectl set-hostname prod-search-v2-contacts-1c-6.priv.evertrue.com

Verify hostname was update by checking /etc/hostname and run hostnamectl 


Update /etc/elasticsearch/elasticsearch.yml to add the new hostname

Delete what’s in the data directory here  /mnt/dev0/elasticsearch/data/

Stop datadog - systemctl stop datadog-agent

update hostname in - vim /etc/dd-agent/datadog.conf

Start services

Systemctl start datadog-agent
Systemctl start elasticsearch
