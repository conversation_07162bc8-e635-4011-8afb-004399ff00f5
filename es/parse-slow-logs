#!/usr/bin/env ruby

# This script parses slow query logs from ES boxes into csv format. Use get-slow-logs to retrieve the logs from all boxes

require File.expand_path('../config/script_options', __dir__)
require 'time'

def parse_date(date_str)
  Time.strptime(date_str, '%Y-%m-%d %H:%M:%S,%L')
end

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option(false)

  opts.on('-t', '--target-dir=TARGET_DIR', 'The directory where slow logs are located.') do |target_dir|
    opts.options[:target_dir] = target_dir
  end

  opts.on('-r', '--range=RANGE', 'If provided, comma separated date range. Only entires within this range will be shown. Format: YYYY-MM-DD HH:MM:SS') do |range|
    opts.options[:date_min] = Time.strptime(range.split(',')[0], '%Y-%m-%d %H:%M:%S')
    opts.options[:date_max] = Time.strptime(range.split(',')[1], '%Y-%m-%d %H:%M:%S')
  end

  opts.require_option(:target_dir)
end

require File.expand_path('../config/environment', __dir__)

target_dir = SCRIPT_OPTIONS[:target_dir]

COLUMNS = [:timestamp, 
:log_level, 
:log_type, 
:node, 
:shard,
:index, 
:took, 
:took_millis, 
:search_type, 
:stats, 
:types, 
:total_shards,
:source,
:extra_source]

class LogEntry
  attr_accessor *COLUMNS

  def initialize(entry)
    # the log entries have a few unlabeled fields in the beginning, when
    # handling a field without a label, use a cursor to populate those 
    # fields.
    @unlabeled_field_ptr = 0
    @unlabeled_fields = [
      :timestamp, 
      :log_level, 
      :log_type, 
      :node, 
      :index,
      :shard
    ]
    parse(entry)
  end

  def parse(entry)
    # the log entries take basically three forms
    # the initial timestamp, log level, log type, node index, shard which are delimited within [].
    # other entries are delimited by [], but label with a string before the []
    # This parser tracks non [] characters in the buffer and will determine label or value based on when a [
    # or ] is encountered.
    # At first I attempted a simpler, split by line then regex each line method, but some of the queries in the
    # source field also had newlines unfortunately.
    buffer = ''
    label = ''
    bracket_counter = 0

    for i in 0...entry.length do
      current_char = entry[i]

      if current_char == '['
        if bracket_counter == 0
          label = buffer.gsub(/,/, '').strip
          buffer = ''
        end
        bracket_counter += 1
      elsif current_char == ']'
        bracket_counter -= 1

        if bracket_counter == 0
          handle_field(label, buffer)
          buffer = ''
          label = ''
        end
      else
        buffer += current_char
      end
    end
  end


  def handle_field(label, value)
    if label.empty?
      send("#{@unlabeled_fields[@unlabeled_field_ptr]}=", value)
      @unlabeled_field_ptr += 1
    else
      send("#{label}=", value)
    end
  end

  def to_row
    COLUMNS.map{ |a| send(a)}
  end
end

def filter_entry(entry)
  return entry unless SCRIPT_OPTIONS.options.include?(:date_min) && SCRIPT_OPTIONS.options.include?(:date_max)
  
  entry_time = parse_date(entry.timestamp)
  if entry_time >= SCRIPT_OPTIONS[:date_min] && entry_time <= SCRIPT_OPTIONS[:date_max]
    return entry
  end
  nil
end

CSV.open("slow-logs.csv", "w") do |csv|
  # write headers
  csv << COLUMNS

  Dir.foreach(target_dir).each do |file|
    next if file == '.' || file == '..'

    file_path = File.join(target_dir, file)

    File.open(file_path, 'r') do |f|
      count = 0
      f.each_line do |line|
        begin
          count += 1
          entry = LogEntry.new(line)
          entry = filter_entry(entry)

          if entry
            csv << entry.to_row
          end
        rescue => e
          puts "failed to read line: #{line}"
          raise e
        end
        
        csv.flush
      end

      puts "#{count} - #{file_path}"
    end
  end
end

puts "done #{Dir.pwd}/slow-logs.csv"