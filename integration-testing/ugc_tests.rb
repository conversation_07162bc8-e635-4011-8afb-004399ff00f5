#!/usr/bin/env ruby
# frozen_string_literal: true

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)
include CommandLineHelpers

ugc_client = UgcClient.create_app_client

interaction = UgcDB::Interaction.where('remote_id IS NOT NULL').where(deleted: false).last
interaction_type = UgcDB::InteractionType.last
proposal_custom_field = UgcDB::ProposalCustomField.last
proposal = proposal_custom_field.proposal
proposal_contact = proposal.proposal_contacts.last
proposal_custom_field_property_picklist_value = UgcDB::ProposalCustomFieldPropertyPicklistValue.last
proposal_with_remote_id = UgcDB::Proposal.where('remote_id IS NOT NULL').where(deleted: false).last
proposal_stage = UgcDB::ProposalStage.last
custom_field = UgcDB::InteractionCustomField.last
custom_field_value = UgcDB::InteractionCustomFieldValue.last
task_reminder = UgcDB::TaskReminder.last
affiliation = AuthDB::Affiliation.last

[
  [:fetch_status, [], 200],
  [:fetch_status_deps, [], 200],
  [:fetch_note_by_id, [interaction.oid, interaction.id], 200],
  [:fetch_note_by_remote_id, [interaction.oid, interaction.remote_id], 200],
  [:fetch_note_by_target, [interaction.oid, interaction.primary_target_type_name, interaction.primary_target_id, interaction.id], 200],
  [:fetch_interaction_custom_fields, [interaction.oid], 200],
  [:fetch_proposal, [proposal.oid, proposal.id], 200],
  [:fetch_proposals_in_bulk, [proposal.oid, [proposal.id]], 200],
  [:fetch_proposal_by_contact_and_id, [proposal.oid, proposal_contact.contact_id, proposal.id], 200],
  [:fetch_proposals_by_contact, [proposal.oid, proposal_contact.contact_id], 200],
  [:fetch_proposal_by_remote_id, [proposal_with_remote_id.oid, proposal_with_remote_id.remote_id], 200],
  [:fetch_proposal_id_by_remote_id, [proposal_with_remote_id.oid, proposal_with_remote_id.remote_id], 200],
  [:search_proposal, [proposal.oid, oid: proposal.oid], 200],
  [:fetch_proposal_custom_fields, [proposal.oid], 200],
  [:fetch_proposal_custom_field_picklist_by_propery_and_id, [proposal_custom_field_property_picklist_value.proposal_custom_field_property.oid, proposal_custom_field_property_picklist_value.proposal_custom_field_property_id, proposal_custom_field_property_picklist_value.id], 200],
  [:fetch_proposal_custom_field_picklists_by_propery, [proposal_custom_field_property_picklist_value.proposal_custom_field_property.oid, proposal_custom_field_property_picklist_value.proposal_custom_field_property_id], 200],
  [:fetch_proposal_stage_by_id, [proposal_stage.oid, proposal_stage.id], 200],
  [:fetch_proposal_stages, [proposal_stage.oid], 200],
  [:fetch_proposal_stages_by_group, [proposal_stage.oid, proposal_stage.stage_group_id], 200],
  [:fetch_proposal_stage_group, [proposal_stage.oid, proposal_stage.stage_group_id], 200],
  [:fetch_proposal_stage_groups, [proposal_stage.oid], 200],
  [:fetch_interactions_for_target, [interaction.oid, interaction.primary_target_type_name, interaction.primary_target_id], 200],
  [:fetch_interactions_in_bulk, [interaction.oid, [interaction.id]], 200],
  [:fetch_interaction_custom_field_values_form_values, [interaction.oid], 200],
  [:fetch_interaction_custom_field_values_form_values_by_custom_field, [custom_field.interaction.oid, custom_field.interaction_custom_field_id], 200],
  [:fetch_interaction_custom_field_values_by_custom_field, [custom_field.interaction.oid, custom_field.interaction_custom_field_id], 200],
  [:fetch_interaction_custom_field_values_by_custom_field_and_value, [custom_field_value.oid, custom_field_value.interaction_custom_field_id, custom_field_value.id], 200],
  [:fetch_interaction_types_by_id, [interaction_type.oid, interaction_type.id], 200],
  [:fetch_interaction_types, [interaction_type.oid], 200],
  [:fetch_reminder, [task_reminder.oid, task_reminder.id], 200],
  [:fetch_reminders_by_task_id, [task_reminder.oid, task_reminder.task_id], 200],
  [:refresh_remote_user_cache, [affiliation.organization_id, affiliation.user_id], 200]
].each do |client_method, method_args, expected_response_code|
  res = ugc_client.public_send(client_method, *method_args)

  if res.code.to_i == expected_response_code
    puts green_string(client_method)
  else
    puts red_string(client_method)
    puts red_string(method_args.inspect)
    puts red_string("invalid response code #{res.code}")
    puts red_string(res.body)
  end
end

# this is tricky right now, need to log into the same app

# task_ugc_client = UgcClient.create_app_client(AuthDB::Application.find(task_reminder.task.app_id).name)

# [
#   [:fetch_task, [task_reminder.oid, task_reminder.task_id], 200]
# ].each do |client_method, method_args, expected_response_code|
#   res = task_ugc_client.public_send(client_method, *method_args)

#   if res.code.to_i == expected_response_code
#     puts green_string(client_method)
#   else
#     puts red_string(client_method)
#     puts red_string(method_args.inspect)
#     puts red_string("invalid response code #{res.code}")
#     puts red_string(res.body)
#   end
# end
