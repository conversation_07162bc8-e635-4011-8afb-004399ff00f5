#!/usr/bin/env ruby
# frozen_string_literal: true

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)
include CommandLineHelpers

contact_client = ContactClient.create_client_with_app_creds

contact = ContactDB::Contact.includes(:identities).last
gift = GiftDB::GiftTransaction.last

[
  [:fetch_status, [], 200],
  [:fetch_contact_by_id, [contact.oid, contact.id], 200],
  [:get_bulk_contacts_by_ids, [contact.oid, [contact.id], false], 200],
  [:get_bulk_contacts_by_ids, [contact.oid, [contact.id], true], 200],
  [:get_bulk_contacts_by_ids_using_post, [contact.oid, [contact.id], false], 200],
  [:get_bulk_contacts_by_ids_using_post, [contact.oid, [contact.id], true], 200],
  [:get_bulk_contacts_by_identities, [contact.oid, [contact.remote_id], 'REMOTE_ID'], 200],
  [:get_bulk_contacts_by_identities_using_post, [contact.oid, [contact.remote_id], 'REMOTE_ID'], 200],
  [:get_contact_identities_match, [contact.oid, [contact.remote_id, 'INVALID-9-ID'], 'REMOTE_ID'], 200],
  [:stream_contact_remote_ids, [contact.oid, 0, 10_000], 200],
  [:fetch_contact_by_id_out_of_oid_index, [contact.oid, contact.id], 200],
  [:refresh_contact, [contact.oid, contact.id], 202],
  [:contact_exists, [contact.oid, contact.id], 200],
  [:contact_exists, [contact.oid+1, contact.id], 404],
  [:get_bulk_contact_ids_by_identities, [contact.oid, [contact.remote_id], 'REMOTE_ID'], 200],
  [:get_bulk_contact_ids_by_identities, [contact.oid, ['INVALID-9-ID'], 'REMOTE_ID'], 404],
  [:get_bulk_contact_ids_by_identities, [contact.oid, ['INVALID-9-ID', contact.remote_id], 'REMOTE_ID'], 200],
  [:get_all_properties, [contact.oid], 200],
  [:get_all_gift_properties, [contact.oid], 200],
  [:get_gift_by_id, [gift.oid, gift.id, 1], 200],
  [:get_gifts_by_contact_id, [contact.oid, contact.id], 200],
  [:get_gift_by_remote_id, [gift.oid, gift.gift_remote_id, gift.type], 200],
  [:get_recent_profile_views, [contact.oid], 200]
].each do |client_method, method_args, expected_response_code|
  res = contact_client.public_send(client_method, *method_args)

  if res.code.to_i == expected_response_code
    puts green_string(client_method)
  else
    puts red_string(client_method)
    puts red_string(method_args.inspect)
    puts red_string("invalid response code #{res.code}")
    puts red_string(res.body)
  end
end
