#!/usr/bin/env ruby
# frozen_string_literal: true

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
end

require File.expand_path('../config/environment', __dir__)
require 'securerandom'
include CommandLineHelpers

def ugc_client
  @ugc_client ||= UgcClient.create_app_client
end

def responses
  @responses ||= {}
end

primary_contact = ContactDB::Contact.where(oid: SCRIPT_OPTIONS[:oid]).first
affiliation = AuthDB::Affiliation.where(organization_id: SCRIPT_OPTIONS[:oid]).where('remote_user_id IS NOT NULL').first
interaction_type = UgcDB::UgcType.where(oid: SCRIPT_OPTIONS[:oid]).first

timestamp = (Time.now.to_f * 1000).to_i
remote_id = 'teest-' + SecureRandom.hex(8)
puts "remote_id is #{remote_id}"

new_interaction = InteractionSchema.from_hash(
  oid: SCRIPT_OPTIONS[:oid],
  remote_id: remote_id,
  target_id: primary_contact.id,
  target_type: 'CONTACT',
  creator_user_id: affiliation.user_id,
  date_occurred: ((Time.now.to_f - 86400) * 1000).to_i,
  text: 'this is a test interaction',
  application: 'EVERTRUE_APP',
  summary: 'test',
  deleted: false,
  update_source: 'EVERTRUE',
  interaction_type: interaction_type.type,
  created_at: timestamp,
  updated_at: timestamp,
  author: {
    name: affiliation.user.name,
    remote_user_id: affiliation.remote_user_id,
    user_id: affiliation.user_id
  },
  solicitor: [
    {
      name: affiliation.user.name,
      remote_user_id: affiliation.remote_user_id,
      user_id: affiliation.user_id
    }
  ],
  user_mention: [],
  contact_mention: [],
  label: [],
  custom_field: [],
  secondary_target: []
)

raise('has parsing errors') unless new_interaction.parsing_errors.empty?

def assert_request(client_method, method_args, expected_response_code)
  res = ugc_client.public_send(client_method, *method_args)

  if res.code.to_i == expected_response_code
    responses[client_method] = res
    puts green_string(client_method)
    true
  else
    puts red_string(client_method)
    puts red_string(method_args.inspect)
    puts red_string("invalid response code #{res.code}")
    puts red_string(res.body)
    false
  end
end

assert_request(:upsert_note_by_remote_id, [SCRIPT_OPTIONS[:oid], new_interaction.remote_id, new_interaction.as_json], 201)

interaction = JSON.parse(responses[:upsert_note_by_remote_id].body)
interaction['text'] = 'UPDATED ' + interaction['text']

assert_request(:upsert_note_by_remote_id, [SCRIPT_OPTIONS[:oid], interaction['remote_id'], interaction], 200)

assert_request(:delete_note_by_target, [SCRIPT_OPTIONS[:oid], interaction['target_type'], interaction['target_id'], interaction['id']], 204)

puts "existing id is #{interaction['id']}"
assert_request(:update_note_by_target, [SCRIPT_OPTIONS[:oid], interaction['target_type'], interaction['target_id'], interaction['id'], interaction], 200)

interaction = JSON.parse(responses[:update_note_by_target].body)
puts "new id is #{interaction['id']}"

new_remote_id = 'teest-' + SecureRandom.hex(8)
puts "changing remote id of #{interaction['id']}/#{interaction['remote_id']} to #{new_remote_id}"
interaction['remote_id'] = new_remote_id
assert_request(:update_note_by_target, [SCRIPT_OPTIONS[:oid], interaction['target_type'], interaction['target_id'], interaction['id'], interaction], 200)

interaction = JSON.parse(responses[:update_note_by_target].body)
puts "remote_id is #{interaction['remote_id']}"
