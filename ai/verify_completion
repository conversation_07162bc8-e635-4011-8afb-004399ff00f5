#!/usr/bin/env ruby

# This script is intended to compare the completion results of a prompt to an expected result.
# Given the random nature of the LLM, it is recommended this be used for results that are 
# expected to be deterministic, such as a list of id's. 
# Note that array ordering is not expected to matter, and array's are sorted to make comparison
# easier.

require_relative '../lib/ai_script_options'

SCRIPT_OPTIONS = AiScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_prompt_options

  opts.on('-i', '--input_path INPUT_PATH', 'a json file containing the expected result') do |input_path|
    opts[:expectation_file] = File.open(input_path, 'r')
  end

  opts.on('-n', '--num NUM_RUNS', 'the number of times to run the assertion (default: 1)') do |num|
    opts[:num_runs] = num.to_i
  end

  opts.require_option(:expectation_file)
end

require File.expand_path('../config/environment', __dir__)

SCRIPT_OPTIONS.after_env_load!

def sort_hash_arrays(element)
  if element.is_a? Hash
    element.each do |k, v|
      element[k] = sort_hash_arrays(v)
    end
  elsif element.is_a? Array
    return element.sort
  end

  return element
end

# Default 1 run
SCRIPT_OPTIONS[:num_runs] ||= 1

expected_json = JSON.parse(SCRIPT_OPTIONS[:expectation_file].read)
expected_json = sort_hash_arrays(expected_json)

failures=0
successes=0

SCRIPT_OPTIONS[:num_runs].times do 
  puts '.' # show progress


  result = ai_client.complete(SCRIPT_OPTIONS[:oid], SCRIPT_OPTIONS[:prompt])
  begin
    result_json = JSON.parse(ai_client.complete(SCRIPT_OPTIONS[:oid], SCRIPT_OPTIONS[:prompt]))
  rescue => e 
    puts e
    puts result
    failures += 1
    next
  end
  
  result_json = sort_hash_arrays(result_json)

  if expected_json != result_json
    puts "expected:#{expected_json}"
    puts "actual  :#{result_json}"
    failures += 1
    next
  end

  successes += 1
end

puts "failures: #{failures}"
puts "successes: #{successes}"