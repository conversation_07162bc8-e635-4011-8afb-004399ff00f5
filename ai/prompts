#!/usr/bin/env ruby

# This script is a utility which assists with using the ai service's prompts resource


require_relative '../lib/ai_script_options'

SCRIPT_OPTIONS = AiScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_prompt_options

  opts.on('-f', '--filter FILTER', 'Filter prompt by key prefix.') do |v|
    opts[:prompt_filter] = v
  end
end

require File.expand_path('../config/environment', __dir__)
SCRIPT_OPTIONS.after_env_load!

def get_command()
  puts ai_client.get_prompt(SCRIPT_OPTIONS[:oid], SCRIPT_OPTIONS[:prompt_key])
end

def list_command()
  puts ai_client.list_prompts(SCRIPT_OPTIONS[:oid], SCRIPT_OPTIONS[:prompt_filter])
end

def render_command()
  puts ai_client.render_prompt(SCRIPT_OPTIONS[:oid], SCRIPT_OPTIONS[:prompt])
end

def audit_command()
  # TODO: Use API endpoint when one is written
  request_id = ARGV.shift
  raise 'missing request_id' unless request_id
  audit = AiDB::PromptAudit.where(request_id: request_id).first.attributes
  audit['response'] = AiClient.parse_response(audit['response'], false)
  puts audit.to_json
end

COMMANDS = {
  'get' => -> { get_command },
  'list' => -> { list_command },
  'render' => -> { render_command },
  'audit' => -> { audit_command }
}


command = ARGV.shift

raise 'missing command' unless command

command_proc = COMMANDS[command]
raise "unknown command #{command}" unless command_proc

command_proc.call