#!/usr/bin/env ruby

# This script is a utility which allows submitting completion requests via the ai service

require_relative '../lib/ai_script_options'

SCRIPT_OPTIONS = AiScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_prompt_options
end

require File.expand_path('../config/environment', __dir__)
SCRIPT_OPTIONS.after_env_load!

puts ai_client.complete(SCRIPT_OPTIONS[:oid], SCRIPT_OPTIONS[:prompt])