#!/usr/bin/env ruby

require_relative '../config/script_options'

# This script allows you to pull a history of the chat_messages for analyzing

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
end

require File.expand_path('../config/environment', __dir__)

count = AiDB::ChatMessage.count()
limit = 100
offset = 0

file_path = "tmp/#{SCRIPT_OPTIONS[:environment]}-chat-messages-#{Time.now.getutc.to_i}.csv"

query = AiDB::ChatMessage
.joins('LEFT JOIN chats ON chats.id = chat_messages.chat_id')
.select(
  'chats.oid',
  'chats.user_id',
  'chat_messages.chat_id',
  'chat_messages.role',
  'chat_messages.message',
  'chat_messages.created_at',
  'chat_messages.id as chat_message_id',
  'chat_messages.prompt_audit_id'
)

headers = [
  :oid,
  :user_id,
  :chat_id,
  :role,
  :message,
  :created_at,
  :chat_message_id,
  :prompt_audit_id
]

puts "writing #{count} messages to #{file_path}"

CSV.open(file_path, 'w') do |csv|
  csv << headers
  csv.flush

  while offset < count
    batch = query.limit(limit).offset(offset)
    offset += limit

    batch.each do |chat_message|
      csv << chat_message.attributes.values
    end
  end
end

puts "done"