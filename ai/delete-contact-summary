#!/usr/bin/env ruby

# This script is a utility which assists with using the ai service's prompts resource

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('--email USER_EMAIL', 'Specify the email of the user') do |v|
    opts[:user_email] = v
  end

  opts.on('-c', '--contact-id CONTACT_ID', Integer, 'Specify the contact ID') do |v|
    opts[:contact_id] = v
  end

  opts.require_option(:user_email)
  opts.require_option(:contact_id)
end

require_relative '../config/environment'

user = AuthDB::User.find_by!(email: SCRIPT_OPTIONS[:user_email])

dynamodb_client = ImporterDynamodbCacheHelpers.create_dynamodb_client


request_key = "#{user.id}:#{SCRIPT_OPTIONS[:oid]}:summarization:#{SCRIPT_OPTIONS[:contact_id]}"

delete_requests = [
  {
    delete_request: {
      key: {
        request_key: request_key
      }
    }
  }
]

dynamodb_client.batch_write_item(
  request_items: {
    'ai-request-cache' => delete_requests
  }
)
