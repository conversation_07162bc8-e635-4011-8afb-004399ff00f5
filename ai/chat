#!/usr/bin/env ruby

# This is a utility script for chatting with a profile

require_relative '../lib/ai_script_options'

SCRIPT_OPTIONS = AiScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_prompt_options
end

require File.expand_path('../config/environment', __dir__)
SCRIPT_OPTIONS.after_env_load!

chat_response = ai_client.create_chat(SCRIPT_OPTIONS[:oid])
chat = chat_response["chat"]

puts "Welcome to profile chat.\n#{chat_response}\nType q! to quit.\n"
running = true
while running do
  print 'You: '
  input = gets
  if input.strip == 'q!'
    running = false
    break
  end

  response = ai_client.submit_chat_message(SCRIPT_OPTIONS[:oid], chat['id'], input, SCRIPT_OPTIONS[:prompt])
  puts "Evertrue: #{response}"
end