#!/usr/bin/env ruby
# frozen_string_literal: true

# Purpose: report on all Facebook pages permissions

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
end

require File.expand_path('../config/environment', __dir__)
require 'active_record'

ActiveRecord::Base.logger = LOG

class PageCSVRow < SodaDB::Page
  include CSVUtils::CSVRow

  csv_column :oid
  csv_column :id, header: :page_id
  csv_column :remote_id, header: :facebook_page_id
  csv_column :name
  csv_column(:linked_on) { created_at&.strftime('%Y-%m-%d') }
  csv_column(:active) { active? ? 'Y' : 'N' }
  csv_column(:active_facebook_token_id) { active_facebook_token&.id }
  csv_column(:active_token_creator_id)
  csv_column(:active_token_creator_name)
  csv_column(:active_token_creator_email)
  csv_column(:active_token_expires_on)
  csv_column(:expired) { expired? ? 'Y' : nil }
  csv_column(:fetch_page) { can_fetch_page? ? 'Y' : 'N' }
  csv_column(:permission_to_read_comments)
  csv_column(:fetch_comments_error_message, header: :read_comments_error_message)
  csv_column(:permission_to_read_reactions)
  csv_column(:fetch_reactions_error_message, header: :read_reactions_error_message)
  csv_column(:fetch_page_with_default_client) { can_fetch_page_with_default_client? ? 'Y' : 'N' }
  csv_column(:fetch_posts_with_default_client) { can_fetch_posts_with_default_client? ? 'Y' : 'N' }
  csv_column(:fetch_comments_with_default_client) { can_fetch_comments_with_default_client? ? 'Y' : 'N' }
  csv_column(:fetch_reactions_with_default_client) { can_fetch_reactions_with_default_client? ? 'Y' : 'N' }

  def self.facebook_app_client
    @facebook_app_client ||= FacebookClient.create_default_app_client
  end

  def permission_to_read_comments
    return nil unless active?
    return nil unless active_facebook_token

    can_fetch_comments? ? 'Y' : 'N'
  end

  def permission_to_read_reactions
    return nil unless active?
    return nil unless active_facebook_token

    can_fetch_reactions? ? 'Y' : 'N'
  end

  def active_token_expires_on
    return nil unless active?
    return nil unless active_facebook_token

    @active_token_expires_on ||=
      begin
        payload = self.class.facebook_app_client.debug_token(input_token: active_facebook_token.value)
        data_access_expires_at = (payload || {}).dig('data', 'data_access_expires_at')
        data_access_expires_at && Time.at(data_access_expires_at)
      end
  end

  def expired?
    active_token_expires_on && active_token_expires_on < Time.now
  end
end

path = TMP_DIR.join('reports', 'soda', "facebook-pages-permissions-report-#{Time.now.utc.strftime('%Y-%m-%d')}.csv").to_s
FileUtils.mkdir_p(File.dirname(path))

CSVUtils::CSVReport.new(path, PageCSVRow) do |report|
  pages = PageCSVRow.includes(facebook_tokens: :user).where(type: 'FacebookPage').order(:oid, :id).all.to_a

  pages.each do |page|
    report << page
  end
end

RepairshopFileUploader.upload_file_and_notify(path)
