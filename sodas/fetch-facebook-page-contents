#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-p', '--page PAGE_ID', Integer, 'Sodas page ID') do |v|
    opts[:page_id] = v
  end

  opts.on('--api-version VERSION', 'Facebook API Version') do |v|
    opts[:facebook_api_version] = v
  end

  opts.on('--use-et-credentials', 'Fetch Page/Post/Comments/Reactions using ET FB credentials') do
    opts[:use_et_client] = true
  end

  opts.require_option(:page_id)
end

require File.expand_path('../config/environment', __dir__)

def create_facebook_client(page = nil)
  client =
    if page && !SCRIPT_OPTIONS[:use_et_client]
      page.active_facebook_token.create_facebook_client
    else
      FacebookClient.create_default_app_client
    end

  client.version = SCRIPT_OPTIONS[:facebook_api_version]

  client
end

page = SodaDB::Page.where(oid: SCRIPT_OPTIONS[:oid]).find(SCRIPT_OPTIONS[:page_id])

unless page.has_active_facebook_token?
  $stderr.puts "page does not have an active facebook token"
  exit 1
end

base_page_dir = TMP_DIR.join('sodas', 'facebook', page.oid, 'pages', page.remote_id)
FileUtils.mkdir_p(base_page_dir) unless File.exist?(base_page_dir)

fetch_comments_worker = WorkerThreads.new(20) do |queue|
  facebook_collect = FacebookCollector.new(create_facebook_client(page))

  queue.each do |post_id|
    file = File.open(base_page_dir.join("comments.#{post_id}.jsonl"), 'wb')
    facebook_collect.each_comment(post_id) do |fb_comment|
      file.puts fb_comment.to_json
    end
    file.close

    File.unlink(file.path) if File.size(file.path) == 0
  end
end

fetch_reactions_worker = WorkerThreads.new(20) do |queue|
  facebook_collect = FacebookCollector.new(create_facebook_client(page))

  queue.each do |post_id|
    file = File.open(base_page_dir.join("reactions.#{post_id}.jsonl"), 'wb')
    facebook_collect.each_reaction(post_id) do |fb_reaction|
      file.puts fb_reaction.to_json
    end
    file.close

    File.unlink(file.path) if File.exist?(file.path) && File.size(file.path) == 0
  end
end

STATS.notification_interval = 100

seen_post_ids = {}
file = File.open(base_page_dir.join('posts.jsonl'), 'wb')
FacebookCollector.new(create_facebook_client(page)).each_post(page.remote_id) do |fb_post|
  STATS.inc_and_notify

  file.puts fb_post.to_json

  unless fb_post['id']
    LOG.error("no id found for post #{fb_post}")
    next
  end

  if seen_post_ids[fb_post['id']]
    LOG.info("already processed post #{fb_post['id']}")
  else
    fetch_comments_worker.enq(fb_post['id'])
    fetch_reactions_worker.enq(fb_post['id'])
  end
end
file.close

fetch_comments_worker.shutdown
fetch_reactions_worker.shutdown

puts STATS.to_display_s
