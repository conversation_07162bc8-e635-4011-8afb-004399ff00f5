#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-u', '--user USER_IDENTIFIER', 'User ID or Email') do |v|
    opts[:user_identifier] = v
  end

  opts.require_option(:user_identifier)
end

require_relative '../../config/environment'

user =
  if SCRIPT_OPTIONS[:user_identifier].include?('@')
    AuthDB::User.find_by!(email: SCRIPT_OPTIONS[:user_identifier])
  else
    AuthDB::User.find(SCRIPT_OPTIONS[:user_identifier])
  end

app = AuthDB::Application.find_by!(name: 'lids_api')
identity_schema = AuthDB::IdentitySchema.find_by!(application_id: app.id, key: 'facebook_user_access_token')
identity = AuthDB::Identity.find_by!(identity_schema_id: identity_schema.id, user_id: user.id)

facebook_client = FacebookClient.new(identity.value)

me_result = facebook_client.get_me

print JSON.pretty_generate(facebook_client.get_accounts(user_id: me_result['id'])) + "\n"
