#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
  opts.add_input_file
end

require_relative '../../config/environment'

profile_ids = File.read(SCRIPT_OPTIONS[:file]).split("\n")

profiles = SodaDB::Profile.includes(:organization, contact: [:contact_attributes, :identities]).where(remote_id: profile_ids, type: 'FacebookProfile').index_by(&:remote_id)

base_dir = TMP_DIR.join('sodas', 'facebook')
FileUtils.mkdir_p(base_dir)

deleted_profile_org_reports = {}
deleted_profile_org_report_files = {}
get_org_report_proc = proc do |org|
  deleted_profile_org_reports[org.id] ||=
    begin
      path = base_dir.join("#{org.slug}-#{org.id}-deleted-facebook-profiles-#{Time.now.utc.strftime('%Y-%m-%d')}.csv").to_s
      deleted_profile_org_report_files[org.id] = path
      csv = CSV.open(path, 'w')
      csv << [
        'PersonId',
        'EvertrueContactId',
        'FirstName',
        'LastName',
        'FacebookUserId'
      ]
      csv
    end
end

profiles.each do |remote_id, profile|
  next unless profile.contact

  csv = get_org_report_proc.call(profile.organization)

  csv << [
    profile.contact.remote_id,
    profile.contact.id,
    profile.contact&.contact_attribute&.name_first,
    profile.contact&.contact_attribute&.name_last,
    remote_id
  ]
end

deleted_profile_org_reports.values.each(&:close)

deleted_profile_org_report_files.each_value do |path|
  RepairshopFileUploader.upload_file_and_notify(path)
end
