#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option(false)
  opts.add_run_remote
end

require_relative '../../config/environment'

class UserFacebookToken < Struct.new(
        :user,
        :identity
      )

  attr_reader :facebook_user_id,
              :facebook_user_name,
              :facebook_accounts

  def token
    identity.value
  end

  def client
    @client ||= FacebookClient.new(token)
  end

  def configure
    configure_user_info
    configure_accounts
  end

  def configure_user_info
    payload = client.get_me
    @facebook_user_id = payload['id']
    @facebook_user_name = payload['name']
  rescue ClientApiBuilder::UnexpectedResponse => e
    LOG.warn("failed to fetch user data for #{user.id}/#{identity.id}")
  end

  def configure_accounts
    @facebook_accounts = []
    return unless facebook_user_id


    payload = client.get_accounts(user_id: facebook_user_id)
    @facebook_accounts += payload['data']

    while (next_url = payload.dig('paging', 'next'))
      payload = JSON.parse(HTTPClient.get(URI(next_url)).body)
      unless payload['data']
        LOG.warn("failed to fetch next page #{next_url} for #{user.id}/#{identity.id}")
        break
      end

      @facebook_accounts += payload['data']
    end
  rescue ClientApiBuilder::UnexpectedResponse => e
    LOG.warn("failed to fetch accounts for #{user.id}/#{identity.id}")
  end
end

class FacebookPage < SodaDB::Page
  include CSVUtils::CSVRow

  attr_accessor :org,
                :correct_remote_id,
                :user_facebook_token,
                :facebook_account

  csv_column(:org_name) { org.name }
  csv_column(:org_id) { org.id }
  csv_column :id, header: :page_id
  csv_column :name, header: :page_name
  csv_column :remote_id
  csv_column(:active) { active ? 'Y' : 'N' }
  csv_column(:active_facebook_token) { has_active_facebook_token? ? 'Y' : 'N' }
  csv_column(:total_posts) { posts.count }
  csv_column :status
  csv_column(:correct_remote_id) { facebook_account&.dig('id') }
  csv_column(:facebook_user_name) { user_facebook_token&.facebook_user_name }
  csv_column(:facebook_user_id) { user_facebook_token&.facebook_user_id }
  csv_column(:auth_user_name) { user_facebook_token&.user&.name }
  csv_column(:auth_user_id) { user_facebook_token&.user&.id }
  csv_column(:auth_facebook_identity_id) { user_facebook_token&.identity&.id }
  csv_column(:active_facebook_token_match_auth_user) { active_facebook_token_match_auth_user? ? 'Y' : '' }
  csv_column(:can_fetch_page) { can_fetch_page? ? 'Y' : 'N' }
  csv_column(:can_fetch_posts) { can_fetch_posts? ? 'Y' : 'N' }
  csv_column(:has_transitioned_to_new_page_experience) { has_transitioned_to_new_page_experience? ? 'Y' : 'N' }

  def self.et_fb_client
    @et_fb_client ||= FacebookClient.create_default_app_client
  end

  def status
    return 'NO_ACCESS' unless facebook_account

    if facebook_account['id'] == remote_id
      'VALID'
    else
      'INVALID_PAGE_ID'
    end
  end

  def find_facebook_account(user_facebook_token)
    find_facebook_account_by_id(user_facebook_token) ||
      find_facebook_account_by_name(user_facebook_token)
  end

  def find_facebook_account_by_id(user_facebook_token)
    user_facebook_token.facebook_accounts.detect do |facebook_account|
      facebook_account['id'] == remote_id
    end
  end

  def find_facebook_account_by_name(user_facebook_token)
    return unless name

    user_facebook_token.facebook_accounts.detect do |facebook_account|
      facebook_account['name'] == name
    end
  end

  def active_facebook_token_match_auth_user?
    user_facebook_token &&
      active_facebook_token &&
      user_facebook_token.user.id == active_facebook_token.user_id
  end

  def can_fetch_page?
    self.class.et_fb_client.get_page(page_id: remote_id)
    true
  rescue ClientApiBuilder::UnexpectedResponse => e
    false
  end

  def can_fetch_posts?
    payload = self.class.et_fb_client.get_page_posts(page_id: remote_id)
    !payload['data'].empty?
  rescue ClientApiBuilder::UnexpectedResponse => e
    false
  end

  def has_transitioned_to_new_page_experience?
    payload = self.class.et_fb_client.get_page(page_id: remote_id)
    payload['has_transitioned_to_new_page_experience']
  rescue ClientApiBuilder::UnexpectedResponse => e
    false
  end
end

def identity_schema
  @identity_schema ||=
    begin
      app = AuthDB::Application.find_by!(name: 'lids_api')
      identity_schema = AuthDB::IdentitySchema.find_by!(application_id: app.id, key: 'facebook_user_access_token')
    end
end

def get_all_user_facebook_tokens(org)
  alumni_role_id = AuthDB::Role.where(organization_id: org.id, name: 'Alumni').first&.id

  alumni_only_proc = proc do |user|
    affiliation = user.affiliations.detect { |a| a.organization_id == org.id }
    affiliation.affiliation_roles.all? { |ar| ar.role_id == alumni_role_id }
  end

  # find all users in org
  users = AuthDB::User.joins(affiliations: :affiliation_roles).includes(:identities, affiliations: :affiliation_roles).where(affiliations: {organization_id: org.id}).to_a
  # exclude EverTrue employees
  users.reject! { |u| u.email.include?('@evertrue.com') }
  # exclude Alumni only users
  users.reject! { |u| alumni_only_proc.call(u) }
  # include users with FB tokens
  users.select! { |u| u.identities.any? { |i| i.identity_schema_id == identity_schema.id } }

  user_facebook_tokens = users.map do |user|
    UserFacebookToken.new(
      user,
      user.identities.detect { |i| i.identity_schema_id == identity_schema.id }
    )
  end

  user_facebook_tokens.each(&:configure)

  user_facebook_tokens.select! do |user_facebook_token|
    if user_facebook_token.facebook_user_id
      true
    else
      LOG.info("FB token for #{user_facebook_token.user.id}/#{user_facebook_token.identity.id} is no longer valid")
      false
    end
  end

  user_facebook_tokens
end

base_dir = TMP_DIR.join('sodas', 'facebook')
FileUtils.mkdir_p(base_dir)
path = base_dir.join("facebook-pages-report-#{Time.now.utc.strftime('%Y-%m-%d')}.csv").to_s
report = CSVUtils::CSVReport.new(path, FacebookPage)

oids = SCRIPT_OPTIONS[:oid] ? [SCRIPT_OPTIONS[:oid]] : SodaDB::Page.select('DISTINCT oid').map(&:oid)

AuthDB::Organization.where(id: oids, deleted: false).each do |org|
  LOG.info("Creating FB report for #{org.name}/#{org.id}")

  STATS.update(:orgs)

  user_facebook_tokens = get_all_user_facebook_tokens(org)
  pages = FacebookPage.includes(facebook_tokens: :user).where(oid: org.id, type: 'FacebookPage').to_a

  csv_file = base_dir.join("#{org.slug}-#{org.id}-facebook-pages-account-report-#{Time.now.utc.strftime('%Y-%m-%d')}.csv").to_s

  pages.each do |page|
    STATS.inc_and_notify
    page.org = org
    found_account = false

    user_facebook_tokens.each do |user_facebook_token|
      page.facebook_account = nil
      page.user_facebook_token = nil

      if (facebook_account = page.find_facebook_account(user_facebook_token))
        found_account = true
        page.facebook_account = facebook_account
        page.user_facebook_token = user_facebook_token
        report << page
      end
    end

    report << page unless found_account
  end
end

report.close
RepairshopFileUploader.upload_file_and_notify(path)
