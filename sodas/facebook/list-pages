#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
end

require_relative '../../config/environment'

pages = SodaDB::Page.where(oid: SCRIPT_OPTIONS[:oid]).to_a

headings = {
  'id' => :id,
  'remote_id' => :remote_id,
  'name' => :name
}
table = TableView.new(headings, row_type: :object)
table.render('Pages' => pages)
