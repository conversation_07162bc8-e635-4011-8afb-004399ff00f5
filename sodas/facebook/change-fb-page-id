#!/usr/bin/env ruby

# Purpose to change the FB page remote_id

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('--old-id CURRENT_FB_PAGE_ID', 'Current FB Page ID') do |v|
    opts[:current_fb_page_id] = v
  end

  opts.on('--new-id NEW_FB_PAGE_ID', 'New FB Page ID') do |v|
    opts[:new_fb_page_id] = v
  end

  opts.require_option(:current_fb_page_id)
  opts.require_option(:new_fb_page_id)
end

require_relative '../../config/environment'

def facebook_client
  @facebook_client ||= FacebookClient.create_default_app_client
end

begin
  info = facebook_client.get_page(page_id: SCRIPT_OPTIONS[:new_fb_page_id])
rescue ClientApiBuilder::UnexpectedResponse => e
  puts e.response.body
  raise(e)
end

fb_page = SodaDB::Page.find_by!(
  oid: SCRIPT_OPTIONS[:oid],
  type: 'FacebookPage',
  remote_id: SCRIPT_OPTIONS[:current_fb_page_id]
)

if fb_page.name && fb_page.name != info['name']
  raise("current FB page name (#{fb_page.name}) doesn't match new name (#{info['name']})")
end

fb_page.remote_id = SCRIPT_OPTIONS[:new_fb_page_id]
fb_page.save!

puts "updated!"
