#!/usr/bin/env ruby
# frozen_string_literal: true

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
end

require File.expand_path('../config/environment', __dir__)
require 'active_record'

ActiveRecord::Base.logger = LOG

class PageCSVRow < SodaDB::Page
  include CSVUtils::CSVRow

  csv_column :id, header: :page_id
  csv_column :remote_id
  csv_column :name
  csv_column(:active) { active ? 'Y' : 'N' }
  csv_column(:fully_collected_on) { full_collection_ended_at&.strftime('%Y-%m-%d') }
  csv_column(:last_collected_on) { collection_ended_at&.strftime('%Y-%m-%d') }
  csv_column(:active_facebook_token_id) { facebook_tokens.select(&:active?).last&.id }
  csv_column(:num_posts) { posts.count }
  csv_column(:last_post_created_at) { posts.maximum(:created_at)&.strftime('%Y-%m-%d') }
  csv_column(:last_post_posted_at) { posts.maximum(:posted_at)&.strftime('%Y-%m-%d') }
  csv_column(:num_engagements) { engagements.count }
  csv_column(:last_engagement_created_at)
  csv_column(:last_engagement_posted_at)
  csv_column(:num_comments) { engagements.where(type: :comment).count }
  csv_column(:last_comment_created_at)
  csv_column(:last_comment_posted_at)
  csv_column(:num_reactions) { engagements.where('type != "comment"').count }
  csv_column(:last_reaction_created_at)
  csv_column(:last_reaction_posted_at)

  def last_engagement
    unless defined?(@last_engagement)
      @last_engagement = engagements.last
    end
    @last_engagement
  end

  def last_engagement_created_at
    last_engagement&.created_at&.strftime('%Y-%m-%d')
  end

  def last_engagement_posted_at
    last_engagement&.posted_at&.strftime('%Y-%m-%d')
  end

  def last_comment
    unless defined?(@last_comment)
      @last_comment = engagements.where(type: :comment).last
    end
    @last_comment
  end

  def last_comment_created_at
    last_comment&.created_at&.strftime('%Y-%m-%d')
  end

  def last_comment_posted_at
    last_comment&.posted_at&.strftime('%Y-%m-%d')
  end

  def last_reaction
    unless defined?(@last_reaction)
      @last_reaction = engagements.where('type != "comment"').last
    end
    @last_reaction
  end

  def last_reaction_created_at
    last_reaction&.created_at&.strftime('%Y-%m-%d')
  end

  def last_reaction_posted_at
    last_reaction&.posted_at&.strftime('%Y-%m-%d')
  end
end

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])
csv_file = TMP_DIR.join("#{org.slug}-#{org.id}-facebook-pages-report-#{Time.now.utc.strftime('%Y-%m-%d')}.csv").to_s

CSVUtils::CSVReport.new(csv_file, PageCSVRow) do |report|
  PageCSVRow.includes(:facebook_tokens).where(oid: SCRIPT_OPTIONS[:oid], type: 'FacebookPage').order(:id).all.each do |page|
    report << page
  end
end
