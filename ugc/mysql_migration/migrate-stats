#!/usr/bin/env ruby

require 'csv'
require 'json'

NOTES_ROOT_FOLDER = ARGV[0] || '/mnt/dev1/doug/notes'

stats = {
  totals: Hash.new(0)
}

Dir.glob("#{NOTES_ROOT_FOLDER}/*/*.csv.status").each do |csv_file|
  oid = File.basename(File.dirname(csv_file)).to_i
  stats[oid] ||= Hash.new(0)

  CSV.foreach(csv_file) do |row|
    _, _, _, status = *row
    stats[:totals][status] += 1
    stats[oid][status] += 1
  end
end

print JSON.pretty_generate(stats) + "\n"
