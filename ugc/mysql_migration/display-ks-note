#!/usr/bin/env ruby

require File.expand_path('../config/environment', __dir__)

target_type, target_id, note_id = *ARGV

raise("target_type #{target_type} is invalid") unless ['CONTACT', 'LIST'].include?(target_type)
raise("target_id is missing unless") unless target_id
raise("note_id is missing unless") unless note_id

cluster = CassandraHelpers.create_cluster(KS_CONFIG['hosts'])
keyspace = CassandraHelpers.get_keyspace(cluster, 'notes')
note_table = CassandraHelpers.get_table(keyspace, 'note')
session = CassandraHelpers.create_session(cluster, 'notes')

record = CassandraHelpers.find_record(session, note_table, 'target_type' => target_type, 'target_id' => target_id.to_i, 'id' => note_id.to_i).first
puts JSON.pretty_generate(Notes::Note.decode(record['value'])) + "\n"
