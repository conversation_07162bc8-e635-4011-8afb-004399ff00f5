#!/usr/bin/env ruby

require 'cassandra-helpers'
require 'csv'
require 'logger'

LOG = Logger.new(__FILE__ + '.log')
LOG.formatter = Logger::Formatter.new

KS_HOSTS = [
  'prod-contacts-cass-1b-1.priv.evertrue.com',
  'prod-contacts-cass-1b-2.priv.evertrue.com',
  'prod-contacts-cass-1c-1.priv.evertrue.com',
  'prod-contacts-cass-1c-2.priv.evertrue.com',
  'prod-contacts-cass-1d-1.priv.evertrue.com',
  'prod-contacts-cass-1d-2.priv.evertrue.com'
]

CassandraHelpers.logger = LOG
cluster = CassandraHelpers.create_cluster(KS_HOSTS)
keyspace = CassandraHelpers.get_keyspace(cluster, 'notes')
session = CassandraHelpers.create_session(cluster, keyspace.name)
note_table = CassandraHelpers.get_table(keyspace, 'note')

CSV.foreach(ARGV[0]) do |row|
  id, target_id, target_type, _ = *row
  record = {
    'id' => id.to_i,
    'target_id' => target_id.to_i,
    'target_type' => target_type == '0' ? 'CONTACT' : target_type
  }

  CassandraHelpers.retry do
    CassandraHelpers.delete_record(session, note_table, record)
  end
end
