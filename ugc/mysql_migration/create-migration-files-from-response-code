#!/usr/bin/env ruby

require 'csv'
require 'fileutils'

NOTES_ROOT_FOLDER = '/mnt/dev1/doug/notes'
FAILED_NOTES_ROOT_FOLDER = '/mnt/dev1/doug/failed_notes'

files = {}
add_note_to_file_proc = Proc.new do |oid, target_type, target_id, note_id, status|
  if files.dig(oid, status, :cnt).to_i >= 10_000
    files[oid][status][:file].close
    files[oid][status].delete(:file)
  end

  unless files.dig(oid, status, :file)
    dir = "#{FAILED_NOTES_ROOT_FOLDER}/#{oid}"
    FileUtils.mkdir_p(dir) unless File.exist?(dir)
    files[oid] ||= {}
    files[oid][status] ||= {num_files: 0}
    files[oid][status][:cnt] = 0
    num_files = (files[oid][status][:num_files] += 1)
    files[oid][status][:file] = CSV.open("#{dir}/notes-#{status}-#{num_files}.csv", 'wb')
  end

  files[oid][status][:file] << [target_type, target_id, note_id]
  files[oid][status][:cnt] += 1
end

Dir.glob("#{NOTES_ROOT_FOLDER}/*/*.csv.status").each do |csv_file|
  oid = File.basename(File.dirname(csv_file)).to_i

  CSV.foreach(csv_file) do |row|
    next if row.last == '200'

    add_note_to_file_proc.call(oid, *row)
  end
end

files.each do |oid, status_files|
  status_files.each do |status, file_info|
    file_info[:file].close
  end
end
