#!/usr/bin/env ruby

require File.expand_path('../config/environment', __dir__)
require 'csv'

def fetch_note(session, note_table, target_type, target_id, id)
  record = CassandraHelpers.find_record(session, note_table, 'target_type' => target_type, 'target_id' => target_id, 'id' => id).first
  record ? Notes::Note.decode(record['value']) : nil
end

def fetch_remote_id_index(session, remote_id_index_table, remote_id, oid)
  CassandraHelpers.find_record(session, remote_id_index_table, 'remote_id' => remote_id, 'oid' => oid).first || {}
end

def fetch_interactions(mysql_client, note_ids)
  mysql_client.query("select id, oid, remote_id, updated_at from interactions where id IN(#{note_ids.join(', ')})").to_a
end

def fetch_interaction_by_remote_id(mysql_client, oid, remote_id)
  mysql_client.query("select id, oid, remote_id, updated_at from interactions where oid = #{oid} and remote_id = \"#{Mysql2::Client.escape(remote_id)}\"").first || {}
end

processor = MultiFileProcessor.new(TMP_DIR.join('failed_notes', '*/*.csv'))
ugc_mysql_client = MySQLHelpers.create_client(:ugc)
cluster = CassandraHelpers.create_cluster(KS_CONFIG['hosts'])
keyspace = CassandraHelpers.get_keyspace(cluster, 'notes')
note_table = CassandraHelpers.get_table(keyspace, 'note')
remote_id_index_table = CassandraHelpers.get_table(keyspace, 'remote_id_index')
session = CassandraHelpers.create_session(cluster, 'notes')

processor.each do |csv_file|
  LOG.info "processing #{csv_file}"

  CSV.open("#{csv_file.sub(processor.inprogress_ext, 'results')}", 'wb') do |out|
    out << [
      'note.id',
      'target_type',
      'target_id',
      'note.remote_id',
      'note.oid',
      'note.interaction_type',
      'note.date_occurred',
      'note.author.remote_user_id',
      'note.updated_at',
      'remote_id_index.contact_id',
      'remote_id_index.note_id',
      'interactions.id',
      'interactions.oid',
      'interactions.remote_id',
      'interactions.updated_at',
      'correct_note.id',
      'correct_note.remote_id',
      'correct_note.oid',
      'correct_note.updated_at',
      'interaction_by_remote_id.id',
      'interaction_by_remote_id.oid',
      'interaction_by_remote_id.remote_id',
      'interaction_by_remote_id.updated_at'
    ]

    oid = File.basename(File.dirname(csv_file)).to_i

    rows = CSV.read(csv_file).map do |row|
      {
        'target_type' => row[0],
        'target_id' => row[1].to_i,
        'id' => row[2].to_i
      }
    end

    interactions = fetch_interactions(ugc_mysql_client, rows.map { |row| row['id'] })

    rows.each do |row|
      target_type = row['target_type']
      target_id = row['target_id']
      note_id = row['id']

      note = fetch_note(session, note_table, target_type, target_id, note_id)
      unless note
        LOG.error "note #{note_id} not found in #{csv_file}, skipping"
        next
      end

      remote_id_index = fetch_remote_id_index(session, remote_id_index_table, note.remote_id, oid)
      interaction = interactions[row['id']] || {}
      interaction_by_remote_id = interaction['remote_id'] == note.remote_id ? interaction : fetch_interaction_by_remote_id(ugc_mysql_client, oid, note.remote_id)

      correct_note =
        if note.id == remote_id_index['note_id']
          note
        else
          fetch_note(session, note_table, target_type, target_id, remote_id_index['note_id'])
        end

      out << [
        note.id,
        target_type,
        target_id,
        note.remote_id,
        note.oid,
        note.interaction_type,
        note.date_occurred,
        note.author&.remote_user_id,
        note.updated_at,
        remote_id_index['contact_id'],
        remote_id_index['note_id'],
        interaction['id'],
        interaction['oid'],
        interaction['remote_id'],
        interaction['updated_at'],
        correct_note&.id,
        correct_note&.remote_id,
        correct_note&.oid,
        correct_note&.updated_at,
        interaction_by_remote_id['id'],
        interaction_by_remote_id['oid'],
        interaction_by_remote_id['remote_id'],
        interaction_by_remote_id['updated_at']
      ]
    end
  end

  LOG.info "finished #{csv_file}"
end
