#!/usr/bin/env ruby

require 'logger'
require 'net/http'
require 'csv'
require 'fileutils'

BASE_API_URL = 'https://api.evertrue.com'
APP_KEY = 'ef571795d45d5be4994a3beebbf2fcb9d24466d45cbf6877250ba822420d3c64'
AUTH_TOKEN = 'ODpGZG9zTHozYmpjZ0xMc1JUUndCbg=='
AUTH_PROVIDER = 'EvertrueAppToken'

NOTES_ROOT_FOLDER = ARGV[0] || '/mnt/dev1/doug/notes'

LOG = Logger.new("#{NOTES_ROOT_FOLDER}/migration.log").tap do |logger|
  logger.formatter = Logger::Formatter.new
end

def req_headers
  {
    'Content-Type' => 'application/json',
    'Accept' => 'application/json',
    'ET-Update-Source' => 'csv_importer'
  }
end

def fetch_note(target_type, target_id, note_id, oid)
  url = "#{BASE_API_URL}/ugc/v2/note/#{target_type}/#{target_id}/#{note_id}?oid=#{oid}&app_key=#{APP_KEY}&auth=#{AUTH_TOKEN}&auth_provider=#{AUTH_PROVIDER}&includeSoftDeletes=true&script=#{Addressable::URI.escape($0)}&developer=#{Addressable::URI.escape(ENV['USER'])}"
  uri = URI(url)

  req = Net::HTTP::Get.new(uri.request_uri, req_headers)

  res = Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == 'https') do |http|
    http.request(req)
  end

  # puts url if res.code.to_i != 200
  res.code.to_i
end

def migrate_notes_file(oid, notes_csv_file)
  CSV.open("#{notes_csv_file.sub('.inprogress', '')}.status", 'wb') do |result_csv|
    CSV.foreach(notes_csv_file) do |row|
      target_type, target_id, note_id = *row
      status_code = fetch_note(target_type, target_id, note_id, oid)
      result_csv << [target_type, target_id, note_id, status_code]

      LOG.error("failed to migrate #{row.join('/')}, status_code #{status_code}") unless status_code == 200
    end
  end
end

def next_migration_file
  Dir.glob("#{NOTES_ROOT_FOLDER}/*/*.csv").shuffle.each do |csv_file|
    next unless File.exist?(csv_file)

    inprogress_csv_file = csv_file + '.inprogress'
    begin
      FileUtils.mv csv_file, inprogress_csv_file

      return inprogress_csv_file
    rescue Errno::ENOENT => e
    end
  end

  nil
end

while (csv_file = next_migration_file)
  oid = File.basename(File.dirname(csv_file)).to_i

  started_at = Time.now
  LOG.info "migrating #{csv_file}"
  migrate_notes_file(oid, csv_file)
  LOG.info "took #{Time.now - started_at} to process #{csv_file}"

  FileUtils.mv(csv_file, "#{csv_file.sub('.inprogress', '')}.done")
end
