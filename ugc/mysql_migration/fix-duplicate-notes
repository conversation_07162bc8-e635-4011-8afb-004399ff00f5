#!/usr/bin/env ruby

require File.expand_path('../config/environment', __dir__)
require 'csv'
require 'time'

ugc_mysql_client = MySQLHelpers.create_client(:ugc)
cluster = CassandraHelpers.create_cluster(KS_CONFIG['hosts'])
keyspace = CassandraHelpers.get_keyspace(cluster, 'notes')
note_table = CassandraHelpers.get_table(keyspace, 'note')
remote_id_index_table = CassandraHelpers.get_table(keyspace, 'remote_id_index')
session = CassandraHelpers.create_session(cluster, 'notes')

Dir.glob(TMP_DIR.join('failed_notes', '*/*.csv.results')).each do |results_csv_file|
  rows = CSV.read(results_csv_file)
  headers = rows.shift
  rows.map! { |row| Hash[headers.zip(row)] }

  rows.each do |row|
    unless row['interaction_by_remote_id.remote_id']
      LOG.info("skipping, no record in mysql for remote_id #{row['interaction_by_remote_id.remote_id']} for note #{row['note.id']}")
      next
    end

    if row['interaction_by_remote_id.id'] == row['correct_note.id']
      LOG.info("skipping, corrent note #{row['correct_note.id']} is already in mysql")
      next
    end

    if row['note.date_occurred'].size > 13
      LOG.info("skpping, note #{row['note.id']} has an invalid date_occurred #{row['note.date_occurred']}")
      next
    end

    if row['note.author.remote_user_id'].to_s.size > 191
      LOG.info("skpping, note #{row['note.id']} has an invalid author.remote_user_id #{row['note.author.remote_user_id']}")
      next
    end

    unless row['correct_note.updated_at']
      LOG.error("skpping, no correct_note for #{row}")
      next
    end

    interaction_updated_at = Time.iso8601(row['interaction_by_remote_id.updated_at'].sub(' ', 'T').sub(/ .*/, 'Z'))
    note_updated_at = Time.at(row['correct_note.updated_at'].to_i  / 1000).utc

    if interaction_updated_at > note_updated_at
      LOG.info("skipping, MySQL already has an updated version of this note #{row['note.id']} with remote_id #{row['interaction_by_remote_id.remote_id']} interaction_updated_at(#{interaction_updated_at}) > note_updated_at(#{note_updated_at})")
      next
    end

    LOG.info("changing note #{row['interaction_by_remote_id.id']} to #{row['correct_note.id']}, because timestamps in MySQL are interaction_updated_at(#{interaction_updated_at}) > note_updated_at(#{note_updated_at})")
    puts("changing note #{row['interaction_by_remote_id.id']} to #{row['correct_note.id']}, because timestamps in MySQL are interaction_updated_at(#{interaction_updated_at}) > note_updated_at(#{note_updated_at})")
    exit
  end
end
