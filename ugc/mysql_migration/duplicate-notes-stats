#!/usr/bin/env ruby

require File.expand_path('../config/environment', __dir__)
require 'csv'
require 'time'

stats = Hash.new(0)

Dir.glob(TMP_DIR.join('failed_notes', '*/*.csv.results')).each do |results_csv_file|
  rows = CSV.read(results_csv_file)
  status_code = File.basename(results_csv_file).split('-')[1]
  headers = rows.shift
  rows.map! { |row| Hash[headers.zip(row)] }

  rows.each do |row|
    stats[:total] += 1
    stats[status_code] += 1
    if row['interactions.id']
      puts "note is already in the db with this id #{row}"
    end

    if row['interaction_by_remote_id.remote_id']
      if row['interaction_by_remote_id.id'] == row['correct_note.id']
        stats[:has_correct_note] += 1
      else
        str = row['interaction_by_remote_id.updated_at'].sub(' ', 'T').sub(/ .*/, 'Z')
        diff = (Time.at(row['note.updated_at'].to_i  / 1000) - Time.iso8601(str))
        if diff.abs < 10
          stats[:from_migration] += 1
        elsif diff < 0
          stats[:already_updated_in_mysql] += 1
        else
          stats[:cassandra_has_newer_version] += 1
        end
      end
    else
      stats[:no_record_in_mysql_with_remote_id] += 1
    end

    if row['note.author.remote_user_id'].to_s.size > 158
      stats[:invalid_author_remote_user_id] += 1
    end

    if row['note.date_occurred'].size > 13
      stats[:invalid_date_occurred] += 1
    end
  end
end

puts stats
