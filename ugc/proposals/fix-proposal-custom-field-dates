#!/usr/bin/env ruby
# frozen_string_literal: true

require File.expand_path('../../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-p', '--proposal PROPOSAL', Integer, 'Proposal id to fix') do |v|
    opts.options[:proposal_id] = v
  end

  opts.require_option(:proposal_id)
end

require File.expand_path('../../config/environment', __dir__)

# fetch/validate proposal_id
proposal = UgcDB::Proposal.find(SCRIPT_OPTIONS[:proposal_id])

ugc_client = UgcClient.create_client_with_app_creds
response = ugc_client.fetch_proposal(proposal.oid, proposal.id)

unless response.kind_of?(Net::HTTPSuccess)
  $stderr.puts("proposal lookup failed #{e.inspect}")
  exit 1
end

orignal_payload = JSON.parse(response.body)
payload = JSON.parse(response.body)

# fix DATE custom_fields

payload['custom_fields'].reject! do |custom_field|
  custom_field['data_type'] == 'DATE' &&
    custom_field['value'] == ''
end

custom_field_changes = orignal_payload['custom_fields'] - payload['custom_fields']

if custom_field_changes.size == 0
  $stderr.puts("detected no invalid data, maybe script needs updating")
  exit 1
end

print "removing\n\n"
print JSON.pretty_generate(custom_field_changes) + "\n"

response = ugc_client.update_proposal(proposal.oid, proposal.id, payload)

unless response.kind_of?(Net::HTTPSuccess)
  $stderr.puts("proposal update failed #{e.inspect}")
  exit 1
end
