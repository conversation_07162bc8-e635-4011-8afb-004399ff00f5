#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_run_remote
end

require_relative '../../config/environment'

ActiveRecord::Base.logger = LOG

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

class ProposalCSVRow < UgcDB::Proposal
  include CSVUtils::CSVRow

  def self.add_csv_column_for_designation(index)
    col_num = index + 1
    csv_column("Designation#{col_num}Name") { proposal_designations[index]&.ugc_designation&.name }
    csv_column("Designation#{col_num}Value") { proposal_designations[index]&.value }
  end

  def self.add_custom_property(proposal_custom_field_property)
    property_id = proposal_custom_field_property.id
    csv_column(proposal_custom_field_property.display_name) { get_custom_field_value(property_id) }
  end

  csv_column(:remote_id, header: 'ProposalID')
  csv_column('EvertrueProposalID') { id }
  csv_column(:constituent_id, header: 'ConstituentID')
  csv_column('EvertrueConstituentID') { primary_proposal_contact&.id }

  csv_column('AdditionalConstituentID') { proposal_contacts[1]&.contact&.remote_id }
  csv_column('EvertrueAdditionalConstituentID') { proposal_contacts[1]&.contact&.id }

  csv_column(:title, header: 'Title')
  csv_column(:description, header: 'Description')
  csv_column(:status, header: 'Stage')
  csv_column(:status_start_date, header: 'StageStartDate') { status_start_date ? Time.at(status_start_date / 1000).iso8601 : nil }
  csv_column(:primary_proposal_unit, header: 'PrimaryProposalUnit')
  csv_column(:additional_proposal_category, header: 'AdditionalProposalCategory')

  csv_column('SolicitorName') { primary_solicitor&.name }
  csv_column('SolicitorID') { primary_solicitor&.remote_user_id }
  csv_column('SolicitorKeyProposal') { solicitor_key_proposal }
  csv_column('SolicitorTitle') { primary_solicitor&.title }

  10.times { |i| add_csv_column_for_designation(i) }

  csv_column('IsActive') { active? ? 'Y' : 'N' }
  csv_column('Reported') { reported? ? 'Y' : 'N' }
  csv_column(:proposal_type, header: 'Type')
  csv_column(:confidence_score, header: 'ConfidenceScore')
  csv_column(:created_date, header: 'CreatedDate') { created_date ? Time.at(created_date / 1000).iso8601 : nil }
  csv_column(:original_ask_amount, header: 'OriginalAskAmount')
  csv_column(:original_ask_date, header: 'OriginalAskDate') { original_ask_date ? Time.at(original_ask_date / 1000).iso8601 : nil }
  csv_column(:ask_amount, header: 'AskAmount')
  csv_column(:ask_date, header: 'AskDate') { ask_date ? Time.at(ask_date / 1000).iso8601 : nil }
  csv_column(:expected_amount, header: 'ExpectedAmount')
  csv_column(:expected_date, header: 'ExpectedDate') { expected_date ? Time.at(expected_date / 1000).iso8601 : nil }
  csv_column(:funded_amount, header: 'FundedAmount')
  csv_column(:funded_date, header: 'FundedDate') { funded_date ? Time.at(funded_date / 1000).iso8601 : nil }

  def constituent_id
    primary_proposal_contact&.contact&.remote_id
  end

  def solicitor_key_proposal
    if primary_solicitor
      primary_solicitor.key_proposal? ? 'Y' : 'N'
    else
      ''
    end
  end

  def get_custom_field_value(property_id)
    proposal_custom_fields.detect { |proposal_custom_field| proposal_custom_field.proposal_custom_field_property_id == property_id }&.value
  end
end

UgcDB::ProposalCustomFieldProperty.where(oid: org.id, deleted: false).each do |proposal_custom_field_property|
  ProposalCSVRow.add_custom_property(proposal_custom_field_property)
end

path = TMP_DIR.join('reports', 'proposals', "#{org.slug}-#{org.id}-et-only-proposals-#{Time.now.strftime('%Y-%m-%d')}.csv").to_s
FileUtils.mkdir_p(File.dirname(path))

CSVUtils::CSVReport.new(path, ProposalCSVRow) do |report|
  ProposalCSVRow
    .includes(
      :proposal_custom_fields,
      :proposal_solicitors,
      proposal_designations: [
        :ugc_designation
      ],
      proposal_contacts: [
        contact: [
          :identities
        ]
      ]
    )
    .where(
      oid: org.id
    )
    .find_each do |proposal|
    report << proposal
  end
end

RepairshopFileUploader.upload_file_and_notify(path)

puts path
