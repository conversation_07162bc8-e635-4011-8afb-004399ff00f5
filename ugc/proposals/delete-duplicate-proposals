#!/usr/bin/env ruby

# This script is designed to delete proposals with duplicate remote ids. It should choose the proposal with the 
# most recent updated_at to keep, and delete all others directly in mysql and ES. Unforunately we cannot use the
# ugc API because the API uses a inner hit on proposal_contact which is sometimes not populated for these 
# duplicates.

# This script handles this ticket specifically https://evertroops.atlassian.net/browse/ET-15259

require_relative '../../config/script_options'


SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
    opts.add_run_remote
end


require File.expand_path('../../config/environment', __dir__)
require 'active_record'

ActiveRecord::Base.logger = Logger.new(STDOUT)


def delete_proposal_mysql(proposal)
    UgcDB::Proposal.delete(proposal.id)
end

def build_delete_action(proposal)
    {
        delete: {
            _id: proposal.id,
            _type: 'proposal',
            _routing: proposal.oid,
            _index: "#{SCRIPT_OPTIONS[:environment]}-contacts-oid-#{proposal.oid}"
        }
    }
end



duplicate_proposals = UgcDB::Proposal
    .select('remote_id, count(remote_id) as cnt')
    .where('remote_id is not null and deleted = false')
    .group(:oid, :remote_id)
    .having('cnt > 1') 

STATS.total = duplicate_proposals.sum{ |result| result.attributes['cnt'] } + 1 # +1 one for the final step to submit the bulk request
STATS.notification_interval = 1

duplicate_proposals.each do |grouped_proposal|
    proposals = UgcDB::Proposal
        .select(:id, :oid)
        .where(remote_id: grouped_proposal.remote_id)
        .order('updated_at asc')

    # since we sorted asc, pop the last element which is the most recently updated proposal. 
    # Thats the one we want to keep.
    proposals = proposals.to_a
    proposals.pop

    es_bulk_actions = []
    proposals.each do |proposal|
        LOG.info "deleting proposal-id=#{proposal.id}"

        delete_proposal_mysql(proposal)

        es_bulk_actions << build_delete_action(proposal)
        STATS.inc_and_notify
    end

    LOG.info "submitting bulk request to ES"

    response = ESHelpers.bulk_request(es_bulk_actions)
    unless response.kind_of? Net::HTTPSuccess
        LOG.error "ES request failed: #{response.body}"
    end

    response_json = JSON.parse(response.body)
    if response['errors']
        error_items = response['items'].select{ |item| item['delete'].has_key?('error') }
        LOG.error "detected errors in the bulk request: #{error_items}"
    end

    STATS.inc_and_notify
end

STATS.notify(true)