#!/usr/bin/env ruby

# Purpose: to delete all the proposals for a specified org.  Be sure to clear the importer cache after running this.

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_run_remote
end

require_relative '../../config/environment'

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

ugc_client = UgcClient.create_client_with_app_creds

scope = UgcDB::Proposal.where(oid: org.id)
STATS.total = scope.count

scope.find_each do |proposal|
  res = ugc_client.v3_delete_proposal(proposal.oid, proposal.id)
  STATS.inc_and_notify

  if res.kind_of?(Net::HTTPSuccess)
    LOG.info("deleted proposal #{proposal.id}/#{proposal.oid}")
    STATS.update(:deleted_proposals)
  else
    LOG.warn("failed to delete proposal #{proposal.id}/#{proposal.oid}")
    STATS.update(:failed_proposal_deletions)
  end
end

STATS.notify(true)
