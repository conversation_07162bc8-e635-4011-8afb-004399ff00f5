#!/usr/bin/env ruby

require File.expand_path('../../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option(false)
end

require File.expand_path('../../config/environment', __dir__)
require 'csv'
require 'active_record'

ActiveRecord::Base.logger = LOG

def dna_client
  @dna_client ||= DNAClient.create_app_client
end

@keep_remote_id_leading_zeros = {}
def keep_remote_id_leading_zeros?(oid)
  unless @keep_remote_id_leading_zeros.key?(oid)
    @keep_remote_id_leading_zeros[oid] = dna_client.keep_remote_id_leading_zeros?(oid)
    LOG.info("fetched dna setting for keep_remote_id_leading_zeros for oid #{oid}, result #{@keep_remote_id_leading_zeros[oid]}")
  end
  @keep_remote_id_leading_zeros[oid]
end

def same_remote_id?(proposal, record, identities)
  return true if record.remote_user_id == identities[record.remote_user_id].value

  LOG.warn("proposal #{proposal.id} updated_at #{proposal.updated_at} has invalid remote_id for #{record.class} #{record.remote_user_id} != #{identities[record.remote_user_id].value} updated_at #{record.updated_at}")
  false
end

def same_contact_id?(proposal, record, identities)
  return true if record.contact_id == identities[record.remote_user_id].contact_id

  LOG.warn("proposal #{proposal.id} updated_at #{proposal.updated_at} has invalid contact_id for #{record.class} #{record.contact_id} != #{identities[record.remote_user_id].contact_id} updated_at #{record.updated_at}")
  false
end

def valid_solicitor?(proposal, proposal_solicitor, identities)
  return true unless proposal_solicitor.remote_user_id
  return true unless identities.key?(proposal_solicitor.remote_user_id)

  if keep_remote_id_leading_zeros?(proposal.oid)
    same_remote_id?(proposal, proposal_solicitor, identities) &&
      same_contact_id?(proposal, proposal_solicitor, identities)
  else
    same_contact_id?(proposal, proposal_solicitor, identities)
  end
end

def valid_contact?(proposal, proposal_contact, identities)
  return true unless proposal_contact.remote_user_id
  return true unless identities.key?(proposal_contact.remote_user_id)

  if keep_remote_id_leading_zeros?(proposal.oid)
    same_remote_id?(proposal, proposal_contact, identities) &&
      same_contact_id?(proposal, proposal_contact, identities)
  else
    same_contact_id?(proposal, proposal_contact, identities)
  end
end

def valid_proposal?(proposal, identities)
  proposal.proposal_solicitors.select(&:remote_user_id).all? { |proposal_solicitor| valid_solicitor?(proposal, proposal_solicitor, identities) } &&
    proposal.proposal_contacts.select(&:remote_user_id).all? { |proposal_contact| valid_contact?(proposal, proposal_contact, identities) }
end

def ugc_api_client
  @ugc_api_client ||= UgcClient.create_app_client
end

def fetch_proposal(proposal)
  res = ugc_api_client.fetch_proposal(
    proposal.oid,
    proposal.id
  )

  unless res.kind_of?(Net::HTTPSuccess)
    LOG.error("failed to fetch proposal #{proposal.id} #{res.code}/#{res.body}")
    return nil
  end

  JSON.parse(res.body)
end

def get_correct_remote_user_id(remote_user_id, identities)
  return remote_user_id unless identities.key?(remote_user_id)

  identities[remote_user_id].value
end

def get_correct_contact_id(remote_user_id, identities, current_contact_id)
  return current_contact_id unless identities.key?(remote_user_id)

  identities[remote_user_id].contact_id
end

def fix_proposal_remote_user_ids(proposal, payload, identities)
  if payload['contacts']
    payload['contacts'].each do |contact|
      next unless contact.key?('remote_user_id')

      contact['remote_user_id'] = get_correct_remote_user_id(contact['remote_user_id'], identities) if keep_remote_id_leading_zeros?(proposal.oid)
      contact['user_id'] = get_correct_contact_id(contact['remote_user_id'], identities, contact['user_id'])
      contact['contact_id'] = get_correct_contact_id(contact['remote_user_id'], identities, contact['contact_id'])
    end
  end

  if payload['solicitors']
    payload['solicitors'].each do |solicitor|
      next unless solicitor.key?('remote_user_id')

      solicitor['remote_user_id'] = get_correct_remote_user_id(solicitor['remote_user_id'], identities) if keep_remote_id_leading_zeros?(proposal.oid)
      solicitor['user_id'] = get_correct_contact_id(solicitor['remote_user_id'], identities, solicitor['user_id'])
      solicitor['contact_id'] = get_correct_contact_id(solicitor['remote_user_id'], identities, solicitor['contact_id'])
    end
  end
end

def update_proposal_contacts(proposal, identities)
  proposal.proposal_contacts.select(&:remote_user_id).each do |proposal_contact|
    next unless identities.key?(proposal_contact.remote_user_id)
    next if proposal_contact.remote_user_id == identities[proposal_contact.remote_user_id].value

    proposal_contact.remote_user_id = identities[proposal_contact.remote_user_id].value
    proposal_contact.save!
  end
end

def update_proposal(payload)
  res = ugc_api_client.update_proposal_by_payload(payload)

  return true if res.kind_of?(Net::HTTPSuccess)

  LOG.error("failed to update proposal #{payload}, res #{res.code}/#{res.body}")
  STATS[:proposal_update_failed] += 1

  false
end

oids = SCRIPT_OPTIONS[:oid] ? [SCRIPT_OPTIONS[:oid]] : AuthDB::Organization.where(id: UgcDB::Proposal.select('DISTINCT oid').to_a.map(&:oid), deleted: false).to_a.map(&:id)

oids.each do |oid|
  UgcDB::Proposal.select('id, oid, updated_at').where(oid: oid, deleted: false).includes(:proposal_contacts, :proposal_solicitors).find_in_batches do |proposals|
    contact_ids = proposals.map { |proposal| proposal.proposal_contacts.map(&:contact_id) + proposal.proposal_solicitors.map(&:contact_id) }
    contact_ids.flatten!
    contact_ids.compact!
    contact_ids.uniq!

    remote_user_ids = proposals.map { |proposal| proposal.proposal_contacts.map(&:remote_user_id) + proposal.proposal_solicitors.map(&:remote_user_id) }
    remote_user_ids.flatten!
    remote_user_ids.compact!
    remote_user_ids.uniq!

    identities = ContactDB::Identity.where(oid: oid, contact_id: contact_ids).each_with_object({}) do |identity, hsh|
      hsh[identity.value] = identity
      hsh[identity.value.sub(/^0+/, '')] = identity
    end

    remote_user_ids_without_zeros = remote_user_ids.map { |id| id =~ /^0/ ? id.sub(/^0+/, '') : nil }.compact

    identities = ContactDB::Identity.where(oid: oid, type: 1, value: remote_user_ids + remote_user_ids_without_zeros).each_with_object(identities) do |identity, hsh|
      hsh[identity.value] = identity
      hsh[identity.value.sub(/^0+/, '')] = identity
    end

    proposals.each do |proposal|
      STATS.inc_and_notify

      if valid_proposal?(proposal, identities)
        STATS[:valid_proposal] += 1
      else
        LOG.warn "proposal #{proposal.oid}/#{proposal.id} has invalid solicitors or contacts"

        STATS[:invalid_proposal] += 1

        if (payload = fetch_proposal(proposal))
          fix_proposal_remote_user_ids(proposal, payload, identities)
          update_proposal_contacts(proposal, identities) if keep_remote_id_leading_zeros?(proposal.oid)
          update_proposal(payload)
        end
      end
    end
  end
end

STATS.notify
