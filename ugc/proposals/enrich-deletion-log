#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_run_remote

  opts.on('-f', '--file DELETION_LOG_FILE', 'Deletion Log File') do |v|
    opts[:file] = v
  end
end

require_relative '../../config/environment'

csv = CSVUtils::CSVIterator.new(SCRIPT_OPTIONS[:file])

missing_headers = ['Row', 'Proposal ID', 'Pending Action', 'Evertrue Proposal ID', 'Error/Note'] - csv.first.keys
raise("missing required headers #{missing_headers}") unless missing_headers.empty?

class ProposalCSVRow < UgcDB::Proposal
  include CSVUtils::CSVRow

  csv_column(:proposal_type)
  csv_column(:status)
  csv_column(:created_date) { created_date ? Time.at(created_date / 1000).iso8601 : nil }
  csv_column(:title)
  csv_column(:description)
  csv_column(:constituent_id)

  def constituent_id
    primary_proposal_contact&.contact&.remote_id
  end
end

evertrue_proposal_id_idx = csv.first.keys.index('Evertrue Proposal ID')

csv_extender = CSVUtils::CSVExtender.new(SCRIPT_OPTIONS[:file], File.basename(SCRIPT_OPTIONS[:file]).sub('.csv', '-extended.csv'))
csv_extender.append_in_batches(ProposalCSVRow.csv_headers, 10_000) do |rows|
  proposal_ids = rows.map { |row| row[evertrue_proposal_id_idx].to_i }
  proposals = ProposalCSVRow.where(id: proposal_ids).includes(proposal_contacts: [contact: :identities]).index_by(&:id)
  proposal_ids.map { |proposal_id| proposals[proposal_id]&.csv_row }
end
