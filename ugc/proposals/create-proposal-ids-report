#!/usr/bin/env ruby

# Purpose is to investigate any/all issues with interaction IDs

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts[:remote_id_column] = 'ProposalID'

  opts.on('-r', '--remote-id INTERACTION_REMOTE_ID_COLUMN', 'InteractionID column name') do |v|
    opts[:remote_id_column] = v
  end

  opts.on('-d', '--dates DATE_FIELDS', 'Comma separated list of date fields to check') do |v|
    opts[:dates] = v.split(',')
  end

  opts.on('-f', '--file CSV_FILE', 'Last full import file') do |v|
    opts[:file] = v
  end

  opts.require_option(:remote_id_column)
end

require_relative '../../config/environment'

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

csv = CSVUtils::CSVIterator.new(SCRIPT_OPTIONS[:file], liberal_parsing: true)

headers_not_found = [SCRIPT_OPTIONS[:remote_id_column]] - csv.headers
raise("headers not found #{headers_not_found}") unless headers_not_found.empty?

# report columns
# Status: ok, invalid, archived, not_owner, not_found
# ProposalID
# OwnerOID
# CurrentEverTrueProposalID

def get_remote_id(row)
  row[SCRIPT_OPTIONS[:remote_id_column]]
end

class ReportCSVRow < Struct.new(
        :status,
        :remote_id
      )

  attr_accessor :current_proposal_id,
                :valid_dates

  include CSVUtils::CSVRow

  csv_column :status, header: 'Status'
  csv_column :remote_id, header: 'ProposalID'
  csv_column :current_proposal_id, header: 'CurrentEverTrueProposalID'
  csv_column :valid_dates, header: 'ValidDates'
end

def valid_date?(date)
  result = date > '1653' && date < '2286'
  puts "invalid date #{date}" unless result
  result
end

def valid_dates?(row)
  return true unless SCRIPT_OPTIONS[:dates]

  SCRIPT_OPTIONS[:dates].all? do |date_field|
    date = row[date_field]
    date.nil? || date.empty? || valid_date?(date)
  end
end

# rows where the EverTrueInteractionID does not match what is in our system
invalid_rows = []
not_found_rows = []

et_only_proposals = UgcDB::Proposal.select(:id, :remote_id).where(oid: SCRIPT_OPTIONS[:oid]).where('remote_id IS NOT NULL').index_by(&:remote_id)

csv.each_batch(10_000) do |batch|
  remote_ids = batch.map { |r| get_remote_id(r) }

  batch.each do |row|
    proposal = et_only_proposals.delete(get_remote_id(row))

    if proposal.nil?
      not_found_rows << row
    end

    unless valid_dates?(row)
      invalid_rows << row
    end
  end
end

puts "num invalid_rows #{invalid_rows.size}"
puts "num not_found_rows #{not_found_rows.size}"
puts "num et_only_interactions #{et_only_proposals.size}"

invalid_rows.each do |r|
  puts get_remote_id(r)
end
