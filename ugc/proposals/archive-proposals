#!/usr/bin/env ruby

=begin
Purpose: Copy all soft deleted proposal records to the archive_proposal table
if they do not already exist there. If the delete flag is set to true on our
ugc api request, the data will be deleted from the proposal table. Otherwise,
the soft deleted proposal record will remain while the archive table is updated.

Uses the UGC api endpoint ugc/v2/admin/proposal/#{proposal_id}/archive
=end

# The script runs on an oid basis, or globally if no oid is provided.
# ./ugc/proposals/archive-proposals -e <staging|production> -o <oid>
require File.expand_path('../../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option(false)
  opts.options[:delete_record] = false

  opts.on('-d', '--delete-record', 'Perform a hard delete of the proposal after archiving.') do
    opts.options[:delete_record] = true
  end
end

require File.expand_path('../../config/environment', __dir__)

def ugc_api_client
  @ugc_api_client ||= UgcClient.create_app_client
end

def archive_proposal(oid, proposal_id)
  res = ugc_api_client.archive_proposal(oid, proposal_id, SCRIPT_OPTIONS[:delete_record])

  return true if res.kind_of?(Net::HTTPSuccess)

  LOG.error("failed to archive oid #{oid}, proposal #{proposal_id}, res #{res.code}/#{res.body}")
  STATS[:proposal_archive_failed] += 1

  false
end

# only look for oids that have soft deleted proposals
oids = if SCRIPT_OPTIONS[:oid]
  [SCRIPT_OPTIONS[:oid]]
else
  LOG.measure('fetching distinct oids') do
    UgcDB::Proposal.select('DISTINCT oid').to_a.map(&:oid)
  end
end

oids.each do |oid|
  LOG.info "Archive proposals for oid #{oid}"
  UgcDB::Proposal.select('id, oid').where(oid: oid, deleted: true).find_in_batches do |proposals|
    proposals.each do |proposal|
      STATS.inc_and_notify
      if (archive_proposal(oid, proposal.id))
        STATS[:proposal_archive_success] += 1
      end
    end
  end
  STATS.notify(true)

  STATS.reset
end
