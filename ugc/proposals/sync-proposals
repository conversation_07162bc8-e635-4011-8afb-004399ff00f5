#!/usr/bin/env ruby

=begin
Purpose: to synchronize proposals between MySQL and ES

Dumps all proposal ids and update times to a file from MySQL and ES

Compares the 2 files to figure out what to delete/update/create

Deletions only occur in ES if the proposal is not found in MySQL

Uses the UGC api endpoint ugc/v2/admin/proposal/#{proposal_id}/es/sync
=end

# The script runs on an oid basis, or globally if no oid is provided.
# ./ugc/proposals/sync-proposals -e <staging|production> -o <oid>
require File.expand_path('../../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
  opts.add_oid_option(false)
end

require File.expand_path('../../config/environment', __dir__)
require 'csv'
include CommandLineHelpers

class MySQLProposalCSVRow < UgcDB::Proposal
  include CSVUtils::CSVRow

  csv_column :id
  csv_column :primary_contact_id, header: :contact_id
  csv_column :timestamp
  csv_column :oid

  def timestamp
    updated_at.to_i * 1_000
  end
end

class ESProposalCSVRow < Struct.new(:id,
                                       :contact_id,
                                       :timestamp,
                                       :index,
                                       :type,
                                       :routing
                                      )
  include CSVUtils::CSVRow

  csv_column :id
  csv_column :contact_id
  csv_column :timestamp
  csv_column :index
  csv_column :type
  csv_column :routing

  def self.create_from_doc(doc)
    new doc['_id'],
        doc['_parent'],
        doc['_source']['updated_at'],
        doc['_index'],
        doc['_type'],
        doc['_routing']
  end
end

oids = if SCRIPT_OPTIONS[:oid]
         [SCRIPT_OPTIONS[:oid]]
       else
         LOG.measure('fetching distinct oids') do
           UgcDB::Proposal.select('DISTINCT oid').to_a.map(&:oid)
         end
       end

AuthDB::Organization.where(deleted: false, id: oids).each do |org|
  LOG.info "Synchronize proposal for #{org.name}/#{org.id}"

  primary_data_file = TMP_DIR.join("ugc-mysql-proposals-oid-#{org.id}.dump").to_s
  sorted_primary_data_file = primary_data_file + '.sorted'
  secondary_data_file = TMP_DIR.join("ugc-es-proposals-oid-#{org.id}.dump").to_s
  sorted_secondary_data_file = secondary_data_file + '.sorted'

  Parallelizer.new do |parallelizer|
    parallelizer.process do
      LOG.measure('fetching proposals from mysql') do
        CSVUtils::CSVReport.new(primary_data_file, MySQLProposalCSVRow) do |report|
          MySQLProposalCSVRow.select('id, oid, updated_at').includes(:proposal_contacts).where(oid: org.id, deleted: false).find_each do |proposal|
            unless proposal.primary_proposal_contact
              LOG.error("proposal #{proposal.id} has no proposal_contacts")
              next
            end
            STATS.update(:mysql_records)
            report << proposal
          end
        end
      end

      LOG.measure('sorting proposals from mysql') do
        CSVUtils::CSVSort.new(primary_data_file, sorted_primary_data_file, 250_000).sort do |a, b|
          if a[0] == b[0]
            a[1].to_i <=> b[1].to_i
          else
            a[0].to_i <=> b[0].to_i
          end
        end
      end
    end

    parallelizer.process do
      LOG.measure('fetching proposals from es') do
        CSVUtils::CSVReport.new(secondary_data_file, ESProposalCSVRow) do |report|
          scanner = ESContactHelpers.query_mapping_by_oid(:proposal, org.id)
          scanner.fields_to_return = 'updated_at'
          scanner.each do |doc|
            STATS.update(:es_records)
            report << ESProposalCSVRow.create_from_doc(doc)
          end
        end
      end
    
      LOG.measure('sorting proposals from es') do
        CSVUtils::CSVSort.new(secondary_data_file, sorted_secondary_data_file, 250_000).sort do |a, b|
          if a[0] == b[0]
            a[1].to_i <=> b[1].to_i
          else
            a[0].to_i <=> b[0].to_i
          end
        end
        next
        FileUtils.cp(secondary_data_file, sorted_secondary_data_file)
      end      
    end
  end

  STATS.notify

  File.unlink(primary_data_file)
  primary_data_file = nil
  File.unlink(secondary_data_file)
  secondary_data_file = nil

  comparer = DataSourceComparer.new(sorted_primary_data_file) do |src_record, dest_record|
    if src_record['id'] == dest_record['id']
      src_record['contact_id'].to_i <=> dest_record['contact_id'].to_i
    else
      src_record['id'].to_i <=> dest_record['id'].to_i
    end
  end

  delete_worker = WorkerThreads.new do |queue|
    records_to_delete = []

    delete_records_proc = Proc.new do
      actions = records_to_delete.map do |record|
        {
          'delete' => {
            '_index' => record['index'],
            '_id' => record['id'],
            '_type' => record['type'],
            '_routing' => record['routing']
          }
        }
      end

      ESHelpers.bulk_request(actions)
      STATS.update(:deleted, records_to_delete.size)
      records_to_delete = []
    end

    queue.each do |record|
      records_to_delete << record

      delete_records_proc.call if records_to_delete.size == 100
    end

    delete_records_proc.call if records_to_delete.size > 0
  end

  index_worker = WorkerThreads.new(10) do |queue|
    ugc_client = UgcClient.create_client_with_app_creds

    queue.each do |record|
      res = ugc_client.sync_proposal_to_es(record['oid'], record['id'])

      if res.kind_of?(Net::HTTPSuccess)
          STATS.update(:indexed)
      else
        LOG.error "sync failed for #{record} with #{res.code}/#{res.body}"
        STATS.update(:sync_note_failed)
      end
    end
  end

  comparer.compare(sorted_secondary_data_file) do |action, record|
    STATS.inc_and_notify
    STATS[action] += 1

    case action
    when :update,
         :create
      index_worker.enq(record)
    when :delete
      delete_worker.enq(record)
    else
      raise("unknown action #{action} for record #{record}")
    end
  end

  delete_worker.shutdown
  index_worker.shutdown

  File.unlink(sorted_primary_data_file)
  File.unlink(sorted_secondary_data_file)

  STATS.notify(true)

  STATS.reset
end
