# UGC Repair Scripts
The scripts in this directory help correct inconsistencies in UGC data- primarily Interactions (Notes) and Proposals. The scripts listed here are the scripts that can be used on an ongoing basis.

## Interactions
Interaction repair scripts relate to MySQL interaction data. 

| Script Name      | Description |
| ----------- | ----------- |
| fix-interaction-solicitors   | Fix any issues with solicitor remote_user_id and contact_id. If a `remote_user_id` exists, verify that the `remote_user_id` and `contact_id` is correct based on the `remote_user_id` identity record for the solicitor. It will update the solicitor using an admin endpoint that will NOT affect the updated_at date on the interaction.         |
| fix-interactions-custom-fields   | When an org deletes custom fields in data.et, this script can be used to clean up that data on interactions. This may not be needed moving forward if UGC no longer saves deleted custom fields on interactions.        |
| fix-interactions-most-recent   | Fix the data in the `interaction_solicitors_most_recent` table. It finds all distinct solicitor/prospect combinations by oid. It will then pass this data pair to a UGC admin endpoint that will perform the upsert operation.        |
| sync-interactions   | Using MySQL as our source of truth, this script will ensure that ElasticSearch data is in sync with MySQL. It will delete any records that exist in ES but not in MySQL, and it will update all remaining records to ensure that the data matches.        |

## Proposals
| Script Name      | Description |
| ----------- | ----------- |
| archive_proposals   | This script is being used during the process of moving proposals from a soft-delete to hard-delete system. This script will go through either an org or all orgs and create an archive record, delete from ES, and if the `delete_record` flag is set, hard delete the proposal.         |
| fix-proposal-custom-field-dates   | Support script to replace bad custom date values with valid values. This should not be needed after UGC validator is updated.         |
| fix-proposal-solicitors   | Can be used during contact remote id migrations to update the solicitor records contact ids.      |
| sync-proposals   | Using MySQL as our source of truth, this script will ensure that ElasticSearch data is in sync with MySQL. It will delete any records that exist in ES but not in MySQL, and it will update all remaining records to ensure that the data matches.        |

## MYSQL Migration
Scripts in the `mysql_migration` directory are archived. They were originally used during our C* -> MySQL Interactions migration. They should no longer be needed.

## One Offs
Scripts in the `one_offs` directory are archived. These are primarily support scripts that were used when helping customer ops troubleshoot data issues.