#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)

class ArchiveInteractionCSVRow < UgcDB::ArchiveInteraction
  include CSVUtils::CSVRow

  csv_column :remote_id, header: 'InteractionImportId'
  csv_column :interaction_id, header: 'InteractionEverTrueId'
  csv_column(:Deleted) { 'Y' }
  csv_column(:CreatedAt) { created_at.utc.iso8601 }

  def json_payload
    @json_payload ||= JSON.parse(json)
  end

  def creation_source
    json_payload['creation_source']
  end

  def evertrue_created_interaction?
    creation_source == 'EVERTRUE'
  end
end

def csv_file_name(oid)
  org = AuthDB::Organization.find(oid)
  "#{org.slug}-#{org.id}-deleted-evertrue-interactions-#{Time.now.strftime('%Y-%m-%d')}.csv"
end

org_csv_files = {}

append_deleted_interaction_proc = Proc.new do |archive_interaction|
  csv = org_csv_files[archive_interaction.oid] ||=
        begin
          report = CSVUtils::CSVReport.new(csv_file_name(archive_interaction.oid))
          report.add_headers(ArchiveInteractionCSVRow)
          report
        end

  csv << archive_interaction
end

STATS.total = ArchiveInteractionCSVRow.count

valid_oids = AuthDB::Organization.where(deleted: false).map(&:id).to_set

ArchiveInteractionCSVRow.find_each(batch_size: 10_000) do |archive_interaction|
  STATS.inc_and_notify

  next unless valid_oids.include?(archive_interaction.oid)
  next unless archive_interaction.evertrue_created_interaction?

  STATS[:deleted_interactions] += 1

  append_deleted_interaction_proc.call(archive_interaction)
end

org_csv_files.values.each { |report| report.csv.close }

STATS.notify
