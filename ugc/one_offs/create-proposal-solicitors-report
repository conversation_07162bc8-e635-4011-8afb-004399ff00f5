#!/usr/bin/env ruby

require File.expand_path('../../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option(false)
end

require File.expand_path('../../config/environment', __dir__)
require 'csv'
require 'active_record'

ActiveRecord::Base.logger = LOG

 # select proposal.oid, proposal_solicitor.remote_user_id, count(*) as cnt from proposal_solicitor inner join proposal on proposal.id = proposal_solicitor.proposal_id where proposal_solicitor.remote_user_id IS NOT NULL and proposal_solicitor.contact_id IS NULL group by proposal.oid, proposal_solicitor.remote_user_id order by proposal.oid, cnt;

class ProposalSolicitorReport < UgcDB::ProposalSolicitor
  include CSVUtils::CSVRow

  attr_writer :identity

  csv_column :oid
  csv_column :remote_user_id
  csv_column :occurrences
  csv_column(:has_white_space) { remote_user_id =~ /\s/ ? 'Y' : 'N' }
  csv_column :correct_contact_id
  csv_column :correct_remote_user_id

  def normalize_remote_user_id
    remote_user_id.strip.downcase
  end

  def correct_contact_id
    @identity[:contact_id]
  end

  def correct_remote_user_id
    @identity[:remote_id]
  end
end

def lookup_contact_identities(solicitors)
  oid = solicitors.first.oid
  remote_user_ids = solicitors.map(&:normalize_remote_user_id)
  remote_user_ids += remote_user_ids.select { |remote_user_id| remote_user_id =~ /^0/ }.map { |remote_user_id| remote_user_id.sub(/^0+/, '') }
  emails = remote_user_ids.select { |remote_user_id| remote_user_id.include?('@') }
  remote_user_ids.reject! { |remote_user_id| remote_user_id.include?('@') }

  identities = {}
  ContactDB::Identity.joins(:contact).where(oid: oid, type: 1, value: remote_user_ids).to_a.each do |identity|
    identities[identity.value] = {contact_id: identity.contact_id, remote_id: identity.value}
    identities[identity.value.sub(/^0+/, '')] = {contact_id: identity.contact_id, remote_id: identity.value}
  end

  if emails.size > 0
    email_identities = ContactDB::Identity.joins(:contact).where(oid: oid, type: 0, value: emails).to_a
    if email_identities.size > 0
      remote_id_identities = ContactDB::Identity.where(oid: oid, type: 1, contact_id: email_identities.map(&:contact_id)).to_a.index_by(&:contact_id)
      email_identities.each do |identity|
        identities[identity.value] = {contact_id: identity.contact_id, remote_id: remote_id_identities[identity.contact_id].value}
      end
    end
  end

  identities
end

scope = ProposalSolicitorReport
          .select('proposal.oid, proposal_solicitor.remote_user_id, count(*) as occurrences')
          .joins(:proposal)
          .where('proposal_solicitor.remote_user_id IS NOT NULL and proposal_solicitor.contact_id IS NULL')
          .group('proposal.oid, proposal_solicitor.remote_user_id')
          .order('proposal.oid, occurrences')

solicitors = scope.to_a

identities_by_oid = {}
solicitors.group_by(&:oid).each do |oid, solicitors_by_oid|
  identities_by_oid[oid] = lookup_contact_identities(solicitors_by_oid)
end

CSVUtils::CSVReport.new('proposal-solicitors-missing-contact-ids.csv', ProposalSolicitorReport) do |report|
  solicitors.each do |solicitor|
    solicitor.identity = identities_by_oid[solicitor.oid][solicitor.normalize_remote_user_id] || {}
    report << solicitor
  end
end
