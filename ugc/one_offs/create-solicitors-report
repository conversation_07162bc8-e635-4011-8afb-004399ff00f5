#!/usr/bin/env ruby

require File.expand_path('../config/environment', __dir__)
require 'csv'
require 'active_record'

ActiveRecord::Base.logger = LOG

class UgcDB::Interaction
  belongs_to :author_user, class_name: 'AuthDB::User'
end
  
class UgcDB::InteractionSolicitor
  include CSVUtils::CSVRow

  belongs_to :user, class_name: 'AuthDB::User'

  csv_column :id, header: 'interaction_solicitor_id'
  csv_column :interaction_id
  csv_column :has_interaction
  csv_column :oid
  csv_column :user_id
  csv_column :has_user
  csv_column :remote_user_id
  csv_column :author_remote_user_id
  csv_column :author_user_id
  csv_column :has_author_user

  def oid
    interaction.try(:oid)
  end

  def has_interaction
    interaction ? 'Y' : 'N'
  end

  def author_remote_user_id
    interaction.try(:author_remote_user_id)
  end

  def author_user_id
    interaction.try(:author_user_id)
  end

  def has_user
    return '' unless user_id

    user ? 'Y' : 'N'
  end

  def has_author_user
    return '' unless author_user_id

    interaction.author_user ? 'Y' : 'N'
  end
end

csv_file = TMP_DIR.join('interaction-solicitors.csv').to_s

cnt = 0
CSVUtils::CSVReport.new(csv_file) do |report|
  report.add_headers UgcDB::InteractionSolicitor.new

  UgcDB::InteractionSolicitor.includes(:user, interaction: [:author_user]).find_each do  |solicitor|
    cnt += 1
    report << solicitor
    break if cnt >= 1_000
  end
end

puts csv_file
