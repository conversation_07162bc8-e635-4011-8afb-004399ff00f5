#!/usr/bin/env ruby

require File.expand_path('../../config/environment', __dir__)
require 'csv'
require 'active_record'

# delete duplicate proposal custom field records from the ugc database. don't need to
# delete records from ES because last write wins, there are never any duplicates in ES
#
# Instructions:
#
# 1) Run this query against the staging/production database:
# ```
# select distinct pcf1.id from proposal_custom_field pcf1
# inner join proposal_custom_field pcf2
# where pcf1.proposal_id = pcf2.proposal_id
# and pcf1.proposal_custom_field_property_id = pcf2.proposal_custom_field_property_id
# and pcf1.id < pcf2.id
# order by pcf1.id desc;
# ```
# 2) Export query results to a csv file
# 3) Move the exported file to ~/dev/et_repair_shop with the appropriate filename for the environment:
# ```
# # for staging
# mv ~/Documents/query_result.csv ~/dev/et_repair_shop/delete-proposal-custom-fields-staging.csv
#
# # for production
# mv ~/Documents/query_result.csv ~/dev/et_repair_shop/delete-proposal-custom-fields-production.csv
# ```
# 4) Remove the id header if you included headers in the export
# 5) Run the script against the appropriate environment
# ```
# # for staging
# cd ~/dev/et_repair_shop/ugc/one_offs
# RAILS_ENV=staging bundle exec ./delete-proposal-custom-fields
#
# # for production
# cd ~/dev/et_repair_shop/ugc/one_offs
# RAILS_ENV=production bundle exec ./delete-proposal-custom-fields
# ```
# 6) ???
# 7) Profit!


CSV.open("delete-proposal-custom-fields-#{ENV['RAILS_ENV'].downcase}.csv", 'rb') do |csv|
  while row = csv.shift
    id = row.first
    pcf = UgcDB::ProposalCustomField.find_by_id(id)

    if pcf
      puts "attempting to delete proposal custom field with id=#{id}, old value was:"
      puts pcf.inspect
      pcf.destroy
      puts "successfully deleted proposal custom field with id=#{id}"
    else
      puts "unable to find proposal custom field with id=#{id}"
    end
  end
end
