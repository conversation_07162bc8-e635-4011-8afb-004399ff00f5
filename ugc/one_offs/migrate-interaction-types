#!/usr/bin/env ruby

=begin
Purpose: to move all data from interaction_types to ugc_types table in UGC. We are deprecating
  interaction_types.

The UID in interaction_types is not used so we do not need to maintain it. We can therefore just
  move data over by its unique index. We will still use the UGC api to do this move because we have
  related data that needs to be created as well (categories). We will not migrate deleted types.

Updates/Creates use the UGC api
=end

# The script runs on a global basis only since there are only ~900 records in the production table.
# oid arg included for testing purposes
# ./ugc/one_offs/migrate-interaction-types -e <staging|production> -o oid

require File.expand_path('../../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option(false)
end

require File.expand_path('../../config/environment', __dir__)
require 'csv'
require 'active_record'

ActiveRecord::Base.logger = LOG

ugc_mysql_client = MySQLHelpers.create_client(:ugc)

def ugc_api_client
  @ugc_api_client ||= UgcClient.create_app_client
end

def fetch_interaction_type(oid, interaction_type_id)
  res =  ugc_api_client.fetch_interaction_types_by_id(oid, interaction_type_id)

  unless res.kind_of?(Net::HTTPSuccess)
    LOG.error("failed to fetch interaction type #{interaction_type_id} #{res.code}/#{res.body}")
    return nil
  end

  JSON.parse(res.body)
end

#this endpoint will move the record to the new ugc type table
def upsert_interaction_type(interaction_type)
  res = ugc_api_client.put_interaction_type(interaction_type)
  unless res.kind_of?(Net::HTTPSuccess)
    LOG.error("failed to update interaction type #{interaction_type} #{res.code}/#{res.body}")
    return nil
  end

  JSON.parse(res.body)
end

oids = SCRIPT_OPTIONS[:oid] ? [SCRIPT_OPTIONS[:oid]] : AuthDB::Organization.where(id: UgcDB::InteractionType.select('DISTINCT oid').pluck(:oid)).pluck(:id)

oids.each do |oid|
  STATS.inc_and_notify  
  LOG.info "Migration interaction types for oid #{oid}"
  UgcDB::InteractionType.select('id, oid').where(oid: oid, deleted: false).find_in_batches do |interaction_types|
    interaction_types.each do |interaction_type|      
      current_type = fetch_interaction_type(oid, interaction_type.id)
      success = upsert_interaction_type(current_type);
      unless success.nil?
        STATS[:migrate_success] += 1
        next
      end 
      STATS[:migrate_failed] += 1
    end
  end
end

STATS.notify