#!/usr/bin/env ruby

require File.expand_path('../../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-f', '--file FILE', 'CSV file with fixes to apply') do |v|
    opts[:file] = v
  end

  opts.require_option(:file)
end

require File.expand_path('../../config/environment', __dir__)

raise("file #{SCRIPT_OPTIONS[:file]} not found") unless SCRIPT_OPTIONS[:file]

def ugc_client
  @ugc_client ||= UgcClient.create_app_client
end

def update_interaction_targets(interaction, record, ugc_client)
  # primary contact part of the main response body
  interaction['target_id'] = record['contactId'].to_i

  ugc_client.post_targets(interaction)
end

def update_interaction_labels(interaction, record, ugc_client)
  interaction['label'].each do |label|
    label['value'] = record['volunteerContactId'] if label['name'] == 'Volunteer Contact ID'
  end

  ugc_client.post_labels(interaction)
end

# file headers: interactionId, contactId, volunteerContactId
# label name will always be Volunteer Contact ID
CSVUtils::CSVIterator.new(SCRIPT_OPTIONS[:file]).each do |record|
  record['contactId'] ||= ContactDB::Identity.where(oid: SCRIPT_OPTIONS[:oid], type: 1, value: record['contactRemoteId']).first.contact_id
  record['volunteerContactId'] ||= ContactDB::Identity.where(oid: SCRIPT_OPTIONS[:oid], type: 1, value: record['volunteerContactRemoteId']).first.contact_id

  id = record['interactionId']
  res = ugc_client.fetch_note_by_id(SCRIPT_OPTIONS[:oid], id)

  unless res.code.to_i == 200
    puts "interaction not found for id #{id}"
    next
  end

  interaction = JSON.parse(res.body)
  target_res = update_interaction_targets(interaction, record, ugc_client)
  label_res = update_interaction_labels(interaction, record, ugc_client)
  puts "interaction id #{interaction['id']}: target update: #{target_res.code.to_i} label update: #{label_res.code.to_i}"
end



