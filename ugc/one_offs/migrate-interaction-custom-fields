#!/usr/bin/env ruby

=begin
Purpose: Migrate all data in the ugc interaction_custom_field_metadata and interaction_custom_field_values 
tables into the new ugc_custom_fields and ugc_custom_field_values tables.

<PERSON><PERSON><PERSON> will find the delta of the values in the original tables but not the new tables and perform inserts for those,
maintaining the original ids. This script will migrate all valid custom fields and any custom field values associated with them. 
=end

# The script runs on an oid basis, or globally if no oid is provided.
# ./ugc/one_offs/migrate-interaction-custom-fields -e <staging|production> -o <oid>
require File.expand_path('../../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option(false)
end

require File.expand_path('../../config/environment', __dir__)
require 'csv'
include CommandLineHelpers

class MySQLInteractionCustomFieldMetadataCSVRow < UgcDB::InteractionCustomFieldMetadatum
  include CSVUtils::CSVRow

  csv_column :id
  csv_column :data_type
  csv_column :ui_control_type
  csv_column :oid

end

class MySQLUgcCustomFieldCSVRow < UgcDB::UgcCustomField
  include CSVUtils::CSVRow

  csv_column :id
  csv_column :data_type
  csv_column :ui_control_type
  csv_column :oid

end

oids = if SCRIPT_OPTIONS[:oid]
         [SCRIPT_OPTIONS[:oid]]
       else
         LOG.measure('fetching distinct oids') do
           UgcDB::InteractionCustomFieldMetadatum.select('DISTINCT oid').to_a.map(&:oid)
         end
       end

AuthDB::Organization.where(deleted: false, id: oids).each do |org|
  LOG.info "Migrate interaction custom field metadata for #{org.name}/#{org.id}"

  interaction_file = TMP_DIR.join("ugc-interaction-custom-field-metadata-#{org.id}.dump").to_s
  sorted_interaction_file = interaction_file + '.sorted'
  ugc_file = TMP_DIR.join("ugc-ugc-custom-field-#{org.id}.dump").to_s
  sorted_ugc_file = ugc_file + '.sorted'

  Parallelizer.new do |parallelizer|
    parallelizer.process do
      LOG.measure('fetching interaction custom field metadata from mysql') do
        CSVUtils::CSVReport.new(interaction_file, MySQLInteractionCustomFieldMetadataCSVRow) do |report|
          MySQLInteractionCustomFieldMetadataCSVRow.select('id, oid, data_type, ui_control_type').where(oid: org.id).find_each do |customField|
            STATS.update(:interaction_custom_fields_records)
            report << customField
          end
        end
      end

      LOG.measure('sorting interaction custom field metadata from mysql') do
        CSVUtils::CSVSort.new(interaction_file, sorted_interaction_file, 250_000).sort do |a, b|
          if a[0] == b[0]
            a[1].to_i <=> b[1].to_i
          else
            a[0].to_i <=> b[0].to_i
          end
        end
      end
    end

    parallelizer.process do
      LOG.measure('fetching ugc custom fields from mysql') do
        CSVUtils::CSVReport.new(ugc_file, MySQLUgcCustomFieldCSVRow) do |report|
          MySQLUgcCustomFieldCSVRow.select('id, oid, data_type, ui_control_type').where(oid: org.id, ugc_type: 0).find_each do |customField|
            STATS.update(:ugc_custom_fields_records)
            report << customField
          end
        end
      end

      LOG.measure('sorting ugc custom fields from mysql') do
        CSVUtils::CSVSort.new(ugc_file, sorted_ugc_file, 250_000).sort do |a, b|
          if a[0] == b[0]
            a[1].to_i <=> b[1].to_i
          else
            a[0].to_i <=> b[0].to_i
          end
        end
      end
    end
  end

  STATS.notify

  File.unlink(interaction_file)
  interaction_file = nil
  File.unlink(ugc_file)
  ugc_file = nil

  comparer = DataSourceComparer.new(sorted_interaction_file) do |src_record, dest_record|
    src_record['id'].to_i <=> dest_record['id'].to_i
  end

  migrate_worker = WorkerThreads.new(10) do |queue|
    ugc_client = UgcClient.create_client_with_app_creds

    queue.each do |record|
      res = ugc_client.migrate_interaction_custom_field(record['oid'], record['id'])

      if res.kind_of?(Net::HTTPSuccess)
          STATS.update(:custom_field_migrated)
      else
        LOG.error "migration failed for #{record} with #{res.code}/#{res.body}"
        STATS.update(:migration_failed)
      end
    end
  end

  comparer.compare(sorted_ugc_file) do |action, record|
    case action
    when :create
      migrate_worker.enq(record)
    else
      raise("unknown action #{action} for record #{record}")
    end
  end


  migrate_worker.shutdown

  File.unlink(sorted_interaction_file)
  File.unlink(sorted_ugc_file)

  STATS.notify(true)

  STATS.reset
end
