#!/usr/bin/env ruby

require 'net/http'
require 'json'
require 'csv'

PROD_CONSOLE_APP_KEY = ENV['PROD_CONSOLE_APP_KEY'] || raise("missing PROD_CONSOLE_APP_KEY environment variable")
USER_AUTH_TOKEN = File.read("#{ENV['HOME']}/.evertrue/auth-token").strip

def get_note(oid, target_id, note_id)
  uri = URI("https://api.evertrue.com/ugc/v2/note/CONTACT/#{target_id}/#{note_id}?oid=#{oid}")
  http = Net::HTTP.new(uri.host, uri.port)
  http.use_ssl = true
  req = Net::HTTP::Get.new(uri.request_uri)
  req['Content-Type'] = 'application/json'
  req['Application-Key'] = PROD_CONSOLE_APP_KEY
  req['Authorization-Provider'] = 'EvertrueAuthToken'
  req['Authorization'] = USER_AUTH_TOKEN
  res = http.request(req)
  JSON.parse(res.body)
end

def update_note(oid, target_id, note_id, payload)
  uri = URI("https://api.evertrue.com/ugc/v2/note/CONTACT/#{target_id}/#{note_id}?oid=#{oid}")
  http = Net::HTTP.new(uri.host, uri.port)
  http.use_ssl = true
  req = Net::HTTP::Put.new(uri.request_uri)
  req.body = payload.to_json
  req['Content-Type'] = 'application/json'
  req['Application-Key'] = PROD_CONSOLE_APP_KEY
  req['Authorization-Provider'] = 'EvertrueAuthToken'
  req['Authorization'] = USER_AUTH_TOKEN
  res = http.request(req)
  JSON.parse(res.body)
end

# copies the author ids into the first solicitor
def fix_note_first_solicitor_ids(note)
  author = note['author']
  solicitor = note['solicitor'].first
  return false unless author['name'] == solicitor['name']

  solicitor['user_id'] = author['user_id']

  if (remote_user_id = author['remote_user_id']) && !remote_user_id.nil? && !remote_user_id.empty?
    solicitor['remote_user_id'] = remote_user_id
  end

  true
end

stats = Hash.new(0)
stats[:started_at] = Time.now

csv = CSV.open('notes-with-invalid-solicitor-user-ids.csv', 'rb')
headers = csv.shift
while row = csv.shift
  note_id, oid, target_id, target_type, deleted, author_name, author_user_id, solicitor_name, solicitor_user_id = *row

  stats[:cnt] += 1

  if (stats[:cnt]%100) == 0
    stats[:took] = Time.now - stats[:started_at]
    puts stats.inspect
  end

  puts "looking up #{note_id} note"
  note = get_note(oid, target_id, note_id)
  unless note['id']
    stats[:missing_notes] += 1
    puts "invalid note response for #{row}: response: #{note}"
    next
  end

  unless fix_note_first_solicitor_ids(note)
    stats[:notes_with_non_matching_names] += 1
    puts "author and solicitor names do not match anymore for #{note}"
    next
  end

  update_note(oid, target_id, note_id, note)
  puts "updated #{note_id} secondary_target"
  stats[:updated_notes] += 1
end
csv.close

stats[:took] = Time.now - stats[:started_at]

puts stats.inspect
