#!/usr/bin/env ruby

$LOAD_PATH << File.expand_path('../../ruby/lib', __dir__)

require 'net/http'
require 'json'
require 'set'
require 'csv'
require 'elasticsearch_scanner'

DELETED_ORG_IDS = [132, 170, 178, 203, 21, 213, 22, 241, 262, 280, 286, 302, 306, 32, 346, 361, 382, 389, 39, 396, 398, 403, 406, 415, 420, 425, 430, 435, 438, 440, 441, 443, 444, 445, 446, 447, 448, 45, 450, 451, 454, 456, 46, 461, 462, 464, 469, 47, 470, 489, 49, 50, 503, 508, 510, 515, 516, 518, 524, 539, 541, 548, 555, 565, 617, 629, 79]

TEST_ORG_IDS = [1, 9, 41, 76, 414, 429, 597, 600, 660, 665]

ORGS_TO_IGNORE = (DELETED_ORG_IDS + TEST_ORG_IDS).to_set

def add_note_to_fix_to_csv(csv, result)
  csv << [
    result['id'],
    result['oid'],
    result['target_id'],
    result['target_type'],
    result['deleted'],
    result['author'] && result['author']['name'],
    result['author'] && result['author']['user_id'],
    result['solicitor'] && result['solicitor'].first && result['solicitor'].first['name'],
    result['solicitor'] && result['solicitor'].first && result['solicitor'].first['user_id']
  ]
end

csv = CSV.open('notes-with-invalid-solicitor-user-ids.csv', 'wb')

fields_to_return = ['id', 'oid', 'target_id', 'target_type', 'deleted', 'author.name', 'author.user_id', 'solicitor.name', 'solicitor.user_id']

csv << fields_to_return

stats = Hash.new(0)
stats[:started_at] = Time.now
result_proc = Proc.new do |result|
  result = result['_source']

  stats[:cnt] += 1

  if ORGS_TO_IGNORE.include?(result['oid'])
    stats[:skipping_org] += 1
  end

  if (stats[:cnt]%1000) == 0
    puts stats.inspect
  end

  next if ORGS_TO_IGNORE.include?(result['oid'])

  if result['deleted']
    stats[:deleted] += 1
    next
  end

  first_solicitor = result['solicitor'].first
  if first_solicitor
    stats[:no_solicitors] += 1
  end

  author = result['author']
  author_name = nil
  if author.nil?
    stats[:author_nil] += 1
  elsif author.empty?
    stats[:author_empty] += 1
  else
    author_name = author['name']

    if author_name.nil?
      stats[:author_name_nil] += 1
    elsif author_name.empty?
      stats[:author_name_empty] += 1
    end

    if author['user_id'].nil?
      stats[:author_user_id_nil] += 1
    elsif author['user_id'] == 0
      stats[:author_user_id_zero] += 1
    end
  end
  
  first_solicitor_name = nil
  if first_solicitor
    first_solicitor_name = first_solicitor['name']
    if first_solicitor_name.nil?
      stats[:first_solicitor_name_nil] += 1
    elsif first_solicitor_name.empty?
      stats[:first_solicitor_name_empty] += 1
    end

    if first_solicitor['user_id'].nil?
      stats[:first_solicitor_user_id_nil] += 1
    elsif first_solicitor['user_id'] == 0
      stats[:first_solicitor_user_id_zero] += 1
    end
  end

  if author_name && first_solicitor_name
    if author_name == first_solicitor_name
      stats[:matching_names] += 1

      if first_solicitor['user_id'].nil?
        add_note_to_fix_to_csv(csv, result)
        stats[:matching_names_first_solicitor_user_id_nil] += 1
      elsif first_solicitor['user_id'] == 0
        add_note_to_fix_to_csv(csv, result)
        stats[:matching_names_first_solicitor_user_id_zero] += 1
      elsif first_solicitor['user_id'] == author['user_id']
        stats[:matching_names_and_user_ids_match] += 1
      else
        stats[:matching_names_and_user_ids_do_not_match] += 1
      end
    else
      stats[:names_do_not_match] += 1
    end
  end
end

query = {
  "bool" => {
    "must_not" => {
      "term" => {
        "creator_user_id" => {
          "value" => 8
        }
      }
    }
  }
}

url = 'http://prod-searchmaster-v2-contacts-1b:9200/prod-small-contacts/contact_note/_search'
scanner = ElasticSearchScanner.new(url, query)
scanner.fields_to_return = fields_to_return
scanner.each(&result_proc)

url = 'http://prod-searchmaster-v2-contacts-1b:9200/prod-big-contacts/contact_note/_search'
scanner = ElasticSearchScanner.new(url, query)
scanner.fields_to_return = fields_to_return
scanner.each(&result_proc)

stats[:took] = Time.now - stats[:started_at]
stats[:total_request_time] = scanner.total_request_time
stats[:total_elasticsearch_time] = scanner.total_elasticsearch_time

csv.close

puts stats
