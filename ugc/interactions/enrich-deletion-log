#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_run_remote

  opts.on('-f', '--file DELETION_LOG_FILE', 'Deletion Log File') do |v|
    opts[:file] = v
  end
end

require_relative '../../config/environment'

csv = CSVUtils::CSVIterator.new(SCRIPT_OPTIONS[:file])

missing_headers = ['Row', 'Interaction ID', 'Pending Action', 'Evertrue Interaction ID', 'Error/Note'] - csv.first.keys
raise("missing required headers #{missing_headers}") unless missing_headers.empty?

class InteractionCSVRow < UgcDB::Interaction
  include CSVUtils::CSVRow

  csv_column(:interaction_type)
  csv_column(:category)
  csv_column(:summary)
  csv_column(:author_name)
  csv_column(:author_remote_user_id, header: 'author_user_id')
  csv_column(:date_occurred) { date_occurred ? Time.at(date_occurred / 1000).iso8601 : nil }
  csv_column(:text, header: 'body')
  csv_column(:constituent_id) { primary_contact_remote_id }
end

evertrue_interaction_id_idx = csv.first.keys.index('Evertrue Interaction ID')

csv_extender = CSVUtils::CSVExtender.new(SCRIPT_OPTIONS[:file], File.basename(SCRIPT_OPTIONS[:file]).sub('.csv', '-extended.csv'))
csv_extender.append_in_batches(InteractionCSVRow.csv_headers, 10_000) do |rows|
  interaction_ids = rows.map { |row| row[evertrue_interaction_id_idx].to_i }
  interactions = InteractionCSVRow.where(id: interaction_ids).includes(interaction_targets: [contact: :identities]).index_by(&:id)
  rows.map { |row| interactions[row[evertrue_interaction_id_idx].to_i]&.csv_row }
end
