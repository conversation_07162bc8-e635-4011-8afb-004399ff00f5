#!/usr/bin/env ruby

require File.expand_path('../../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../../config/environment', __dir__)
require 'csv'
require 'active_record'

ActiveRecord::Base.logger = LOG

def ugc_api_client
  @ugc_api_client ||= UgcClient.create_app_client
end

def fetch_note(interaction)
  res = ugc_api_client.fetch_note_by_target(
    interaction.oid,
    interaction.primary_target_type_name,
    interaction.primary_target_id,
    interaction.id
  )

  unless res.kind_of?(Net::HTTPSuccess)
    LOG.error("failed to fetch note for interaction #{interaction.id}, #{res.code}/#{res.body}")
    return nil
  end

  JSON.parse(res.body)
end

def update_note(note)
  res = ugc_api_client.update_note_by_target(
    note['oid'],
    note['target_type'],
    note['target_id'],
    note['id'],
    note
  )

  unless res.kind_of?(Net::HTTPSuccess)
    LOG.error("failed to update note for interaction #{note['id']}, #{res.code}/#{res.body}")
    return nil
  end

  res
end

def deleted_custom_field_ids
  @deleted_custom_field_ids ||= UgcDB::InteractionCustomFieldMetadatum.where(deleted: true).to_a.map(&:id).to_set
end

interaction_ids_with_deleted_custom_data = UgcDB::InteractionCustomField.select('DISTINCT interaction_id').where(interaction_custom_field_id: deleted_custom_field_ids.to_a).to_a.map(&:interaction_id)

UgcDB::Interaction.where(id: interaction_ids_with_deleted_custom_data).includes(:interaction_targets).each do |interaction|
  STATS.inc_and_notify

  unless note = fetch_note(interaction)
    STATS[:failed_fetch_note] += 1
    next
  end

  custom_fields_to_delete = note['custom_field'].select { |field| deleted_custom_field_ids.include?(field['id']) }.map { |field| field['id'] }

  raise("no custom_fields_to_delete for interaction #{interaction.id}") unless custom_fields_to_delete.size > 0

  LOG.info("interaction #{note['id']}, removing custom fields #{custom_fields_to_delete}")

  note['custom_field'].reject! { |field| custom_fields_to_delete.include?(field['id']) }

  unless update_note(note)
    STATS[:failed_update_note] += 1
    next
  end
end
