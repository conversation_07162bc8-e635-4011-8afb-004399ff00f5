#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_run_remote
end

require_relative '../../config/environment'

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

class InteractionCSVRow < UgcDB::Interaction
  include CSVUtils::CSVRow

  csv_column(:id, header: 'Evertrue Interaction ID')
  csv_column(:remote_id, header: 'Interaction ID')
  csv_column(:interaction_type)
  csv_column(:category)
  csv_column(:summary)
  csv_column(:author_name)
  csv_column(:author_remote_user_id, header: 'author_user_id')
  csv_column(:date_occurred) { date_occurred ? Time.at(date_occurred / 1000).iso8601 : nil }
  csv_column(:text, header: 'body')
  csv_column(:constituent_id) { primary_contact_remote_id }
  csv_column(:created_at) { created_at.iso8601 }
  csv_column(:updated_at) { updated_at.iso8601 }
end

path = TMP_DIR.join('reports', 'interactions', "#{org.slug}-#{org.id}-et-only-interactions-#{Time.now.strftime('%Y-%m-%d')}.csv").to_s
FileUtils.mkdir_p(File.dirname(path))

CSVUtils::CSVReport.new(path, InteractionCSVRow) do |report|
  InteractionCSVRow.includes(interaction_targets: [contact: :identities]).where(oid: org.id).where(remote_id: nil).where.not(interaction_type: 'EverTrue Comment').each do |interaction|
    report << interaction
  end
end

RepairshopFileUploader.upload_file_and_notify(path)

puts path
