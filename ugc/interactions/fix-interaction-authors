#!/usr/bin/env ruby

# Purpose: to set the interactions.author)_remote_user_id based on the auth.affiliations

require File.expand_path('../../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_run_remote
end

require File.expand_path('../../config/environment', __dir__)

def ugc_client
  @ugc_client ||= UgcClient.create_client_with_app_creds
end

def org
  @org ||= AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])
end

def affiliations
  @affiliations ||= org.affiliations.where.not(remote_user_id: nil).index_by(&:user_id)
end

interactions = UgcDB::Interaction.where(oid: org.id, author_user_id: affiliations.keys, author_remote_user_id: nil).to_a

STATS.total = interactions.size

LOG.info("interactions to fix #{interactions.size}")

interactions.each do |interaction|
  STATS.inc_and_notify

  affiliation = affiliations[interaction.author_user_id]

  LOG.info("Updating interaction #{interaction.id}/#{interaction.oid} author_remote_user_id to #{affiliation.remote_user_id}")
  
  interaction.author_remote_user_id = affiliation.remote_user_id
  interaction.save!

  LOG.info("Syncing interaction #{interaction.id}/#{interaction.oid} with ES")

  ugc_client.sync_interaction_to_es(interaction.oid, interaction.id)
end

STATS.notify(true)
