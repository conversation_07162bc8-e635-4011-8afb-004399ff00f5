#!/usr/bin/env ruby

# This script can be used to update/fix the solicitor remote_user_id and contact_id. 
# It will verify that, if a remote_user_id exists, the remote_user_id and contact_id is correct based on the 
# remote_user_id identity record for the solicitor.
# It will update the solicitor using an admin endpoint that will NOT affect the updated_at date on the interaction. This
# is important bc we do not want these changes to result in records being included in a customer export.
# 
# The script runs on an oid basis, or globally if no oid is provided.
# ./ugc/fix-interaction-solicitors -e <staging|production> -o <oid>
require File.expand_path('../../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option(false)
  opts.add_run_remote
end

require File.expand_path('../../config/environment', __dir__)
require 'csv'
require 'active_record'

ActiveRecord::Base.logger = LOG

def dna_client
  @dna_client ||= DNAClient.create_app_client
end

@keep_remote_id_leading_zeros = {}
def keep_remote_id_leading_zeros?(oid)
  unless @keep_remote_id_leading_zeros.key?(oid)
    @keep_remote_id_leading_zeros[oid] = dna_client.keep_remote_id_leading_zeros?(oid)
    LOG.info("fetched dna setting for keep_remote_id_leading_zeros for oid #{oid}, result #{@keep_remote_id_leading_zeros[oid]}")
  end
  @keep_remote_id_leading_zeros[oid]
end

def same_remote_id?(interaction, record, identities)
  return true if record.remote_user_id == identities[record.remote_user_id].value

  LOG.warn("interaction #{interaction.id} has invalid remote_id for #{record.class} #{record.remote_user_id} != #{identities[record.remote_user_id].value}")
  false
end

def same_contact_id?(interaction, record, identities)
  return true if record.contact_id == identities[record.remote_user_id].contact_id

  LOG.warn("interaction #{interaction.id} has invalid contact_id for #{record.class} #{record.contact_id} != #{identities[record.remote_user_id].contact_id}")
  false
end

def valid_solicitor?(interaction, interaction_solicitor, identities)
  # if we have no remote_user_id, we also won't be able to find a contact_id
  return true unless interaction_solicitor.remote_user_id
  return true unless identities.key?(interaction_solicitor.remote_user_id)

  if keep_remote_id_leading_zeros?(interaction.oid)
    same_remote_id?(interaction, interaction_solicitor, identities) &&
    same_contact_id?(interaction, interaction_solicitor, identities)
  else
    same_contact_id?(interaction, interaction_solicitor, identities)
  end
end

def valid_interaction?(interaction, identities)
  interaction.interaction_solicitors.select(&:remote_user_id).all? { |interaction_solicitor| valid_solicitor?(interaction, interaction_solicitor, identities) }
end

def ugc_api_client
  @ugc_api_client ||= UgcClient.create_app_client
end

def fetch_interaction(interaction)
  res = ugc_api_client.fetch_note_by_id(
    interaction.oid,
    interaction.id
  )

  unless res.kind_of?(Net::HTTPSuccess)
    LOG.error("failed to fetch interaction #{interaction.id} #{res.code}/#{res.body}")
    return nil
  end

  JSON.parse(res.body)
end

def get_correct_remote_user_id(remote_user_id, identities)
  return remote_user_id unless identities.key?(remote_user_id)

  identities[remote_user_id].value
end

def get_correct_contact_id(remote_user_id, identities, current_contact_id)
  return current_contact_id unless identities.key?(remote_user_id)

  identities[remote_user_id].contact_id
end

def fix_interaction_remote_user_ids(interaction, payload, identities)
  if payload['solicitor']
    payload['solicitor'].each do |solicitor|
      next unless solicitor.key?('remote_user_id')

      solicitor['remote_user_id'] = get_correct_remote_user_id(solicitor['remote_user_id'], identities) if keep_remote_id_leading_zeros?(interaction.oid)
      solicitor['contact_id'] = get_correct_contact_id(solicitor['remote_user_id'], identities, solicitor['contact_id'])
    end
  end
end

def update_solicitors(payload)
  res = ugc_api_client.post_solicitors(payload)

  return true if res.kind_of?(Net::HTTPSuccess)

  LOG.error("failed to update interaction #{payload}, res #{res.code}/#{res.body}")
  STATS[:interaction_update_failed] += 1

  false
end

oids = SCRIPT_OPTIONS[:oid] ? [SCRIPT_OPTIONS[:oid]] : AuthDB::Organization.where(id: UgcDB::Interaction.select('DISTINCT oid').to_a.map(&:oid)).to_a.map(&:id)

oids.each do |oid|
  UgcDB::Interaction.select('id, oid').where(oid: oid).includes(:interaction_solicitors).find_in_batches do |interactions|
    contact_ids = interactions.map { |interaction| interaction.interaction_solicitors.map(&:contact_id) }
    contact_ids.flatten!
    contact_ids.compact!
    contact_ids.uniq!

    remote_user_ids = interactions.map { |interaction| interaction.interaction_solicitors.map(&:remote_user_id) }
    remote_user_ids.flatten!
    remote_user_ids.compact!
    remote_user_ids.uniq!

    identities = ContactDB::Identity.where(oid: oid, contact_id: contact_ids).each_with_object({}) do |identity, hsh|
      hsh[identity.value] = identity
      hsh[identity.value.sub(/^0+/, '')] = identity
    end

    remote_user_ids_without_zeros = remote_user_ids.map { |id| id =~ /^0/ ? id.sub(/^0+/, '') : nil }.compact

    identities = ContactDB::Identity.where(oid: oid, type: 1, value: remote_user_ids + remote_user_ids_without_zeros).each_with_object(identities) do |identity, hsh|
      hsh[identity.value] = identity
      hsh[identity.value.sub(/^0+/, '')] = identity
    end

    interactions.each do |interaction|
      STATS.inc_and_notify

      if valid_interaction?(interaction, identities)
        STATS[:valid_interaction] += 1
      else
        LOG.warn "interaction #{interaction.oid}/#{interaction.id} has invalid solicitors"

        STATS[:invalid_interaction] += 1

        if (payload = fetch_interaction(interaction))
          fix_interaction_remote_user_ids(interaction, payload, identities)
          update_solicitors(payload)
        end
      end
    end
  end
end

STATS.notify
