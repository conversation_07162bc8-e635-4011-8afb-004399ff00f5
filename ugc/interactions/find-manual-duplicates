#!/usr/bin/env ruby

# Purpose: to find duplicates interactions that originated from the FE on the same day

require_relative '../../config/script_options'
require 'time'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
  opts.add_scheduling_options
  opts.add_oids_option(false)
  opts[:start_date] = (Time.now - (7 * 86_400))
  opts[:end_date] = Time.now

  opts.on('--start-date DATE', 'Start date YYYY-MM-DD') do |v|
    opts[:start_date] = Date.parse(v).to_time
  end

  opts.on('--end-date DATE', 'End date YYYY-MM-DD') do |v|
    opts[:end_date] = (Date.parse(v).to_time + 86_339)
  end
end

require_relative '../../config/environment'
require 'digest'

def ugc_client
  @ugc_client ||= UgcClient.create_client_with_app_creds
end

HASH_FIELDS = [
  'primary_target_type',
  'primary_target_id',
  'primary_solicitor_name',
  'interaction_type',
  'date_occurred',
  'summary',
  'text'
]

timestamp = Time.now.strftime('%Y-%m-%dT%H%M')

duplicates_dir = TMP_DIR.join('ugc', timestamp, 'interactions', 'duplicates')
FileUtils.mkdir_p(duplicates_dir)

deleted_dir = TMP_DIR.join('ugc', timestamp, 'interactions', 'deleted')
FileUtils.mkdir_p(deleted_dir)

oids = SCRIPT_OPTIONS[:oids] ? SCRIPT_OPTIONS[:oids] : UgcDB::Interaction.select('DISTINCT oid').map(&:oid)
AuthDB::Organization.where(deleted: false, id: oids).to_a.each do |org|
  LOG.info("finding duplicates interactions for oid #{org.id}")

  # find all interactions created in EverTrue (creation_source = 1)
  evertrue_interactions = {}
  UgcDB::Interaction.includes(:interaction_solicitors, interaction_targets: [contact: [:contact_attribute]]).select(:id, :oid, :remote_id, :interaction_type, :summary, :text, :created_at, :date_occurred).where(oid: org.id, creation_source: 1).where('created_at >= ?', SCRIPT_OPTIONS[:start_date]).where('created_at <= ?', SCRIPT_OPTIONS[:end_date]).order(:id).each do |interaction|
    md5 = Digest::MD5.new

    HASH_FIELDS.each do |field|
      md5.update(field)
      md5.update(interaction.public_send(field).to_s)
    end

    digest = md5.digest

    evertrue_interactions[digest] ||= []
    evertrue_interactions[digest] << interaction
  end

  has_duplicates = false
  CSV.open(duplicates_dir.join("#{org.slug}-#{org.id}-duplicate-interactions.csv"), 'wb') do |out|
    out << ['digest', 'date', 'interaction_id', 'interaction_remote_id', 'interaction_type', 'solicitor', 'prospect', 'contact_id', 'date_occurred', 'created_at', 'summary', 'text', 'contact_url', 'interaction_url', 'delete']
    evertrue_interactions.each do |digest, interactions|
      next unless interactions.size > 1

      has_duplicates = true
      interactions.each_with_index do |interaction, idx|
        date = interaction.created_at.strftime('%Y-%m-%d')
        out << [digest.unpack('h*').first, date, interaction.id, interaction.remote_id, interaction.interaction_type, interaction.primary_solicitor_name, interaction.primary_interaction_target.contact.name, interaction.primary_interaction_target.contact.id, Time.at(interaction.date_occurred / 1000).iso8601, interaction.created_at.iso8601, interaction.summary, interaction.text, "https://app.evertrue.com/contact/#{interaction.primary_target_id}", "https://app.evertrue.com/interaction/#{interaction.id}", (idx == 0 ? 'N' : 'Y')]
      end
    end
  end

  unless has_duplicates
    File.unlink(duplicates_dir.join("#{org.slug}-#{org.id}-duplicate-interactions.csv"))
    next
  end

  RepairshopFileUploader.upload_file_and_notify(duplicates_dir.join("#{org.slug}-#{org.id}-duplicate-interactions.csv").to_s)

  # need to report to the orgs which duplicate interactions we deleted.  So, that they can delete the same interactions on their end.
  has_duplicate_to_delete = false
  CSV.open(deleted_dir.join("#{org.slug}-#{org.id}-deleted-duplicate-interactions.csv"), 'wb') do |out|
    out << ['InteractionId', 'ETInteractionId', 'InteractionType', 'Solicitor', 'Prospect', 'DateOccurred', 'CreatedAt', 'Summary', 'Body']
    csv = CSVUtils::CSVIterator.new(duplicates_dir.join("#{org.slug}-#{org.id}-duplicate-interactions.csv").to_s)
    csv.each do |row|
      next if row['delete'] == 'N'
      next if row['interaction_remote_id'].nil?

      has_duplicate_to_delete = true
      out << [row['interaction_remote_id'], row['interaction_id'], row['interaction_type'], row['solicitor'], row['prospect'], row['date_occurred'], row['created_at'], row['summary'], row['body']]
    end
  end

  if has_duplicate_to_delete
    RepairshopFileUploader.upload_file_and_notify(deleted_dir.join("#{org.slug}-#{org.id}-deleted-duplicate-interactions.csv").to_s)
  else
    File.unlink(deleted_dir.join("#{org.slug}-#{org.id}-deleted-duplicate-interactions.csv"))
  end

  csv = CSVUtils::CSVIterator.new(duplicates_dir.join("#{org.slug}-#{org.id}-duplicate-interactions.csv").to_s)
  csv.each do |row|
    if row['delete'] == 'N'
      LOG.info("keeping interaction #{row['interaction_id']}/#{org.id}/#{row['digest']}")
      next
    end

    res = ugc_client.delete_note_by_target(org.id, 'CONTACT', row['contact_id'], row['interaction_id'])
    LOG.info("deleted interaction #{row['interaction_id']}/#{org.id}/#{row['digest']}: #{res}")
  end
end
