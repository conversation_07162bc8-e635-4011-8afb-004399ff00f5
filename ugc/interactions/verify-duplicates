#!/usr/bin/env ruby

# Purpose: to find duplicates interactions that originated from the FE on the same day

require_relative '../../config/script_options'
require 'time'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_input_file
end

require_relative '../../config/environment'
require 'digest'

def get_interaction_to_keep(batch, statuses, interactions)
  data = []
  batch.each do |row|
    interaction_id = row['interaction_id'].to_i
    next if statuses[interaction_id] == 'NOT_FOUND'

    data << {interaction_id: interaction_id, status: statuses[interaction_id], updated_at: interactions[interaction_id].updated_at}
  end

  data.sort do |a, b|
    if a[:status] == b[:status]
      a[:updated_at] <=> b[:updated_at]
    elsif a[:status] == 'UPDATED'
      1
    else
      -1
    end
  end

  data.empty? ? nil : data.last[:interaction_id]
end

updates_dir = TMP_DIR.join('interactions', 'updates')
FileUtils.mkdir_p(updates_dir)

updated_deletions_dir = TMP_DIR.join('interactions', 'updated-deletions')
FileUtils.mkdir_p(updated_deletions_dir)

csv = CSVUtils::CSVIterator.new(SCRIPT_OPTIONS[:file])
duplicate_interactions = csv.group_by { |r| r['digest'] }

interactions = UgcDB::Interaction.includes(:interaction_solicitors, interaction_targets: [contact: [:contact_attribute]]).where(id: csv.map { |r| r['interaction_id'] }).index_by(&:id)

interaction_statuses = {}
duplicate_interactions.each do |digest, batch|
  batch.each do |row|
    interaction_id = row['interaction_id'].to_i
      
    status =
      if interactions[interaction_id].nil?
        'NOT_FOUND'
      elsif interactions[interaction_id].to_md5 == digest
        'NO_CHANGE'
      else
        'UPDATED'
      end

    row['interaction_remote_id'] ||= interactions[interaction_id]&.remote_id

    interaction_statuses[interaction_id] = status
  end
end

update_csv_file = updates_dir.join(File.basename(SCRIPT_OPTIONS[:file])).to_s

CSV.open(update_csv_file, 'wb') do |out|
  out << csv.headers + ['status', 'last_updated_at', 'keep'] + UgcDB::Interaction::HASH_FIELDS.map { |field| "current_#{field}" }

  duplicate_interactions.each do |digest, batch|
    interaction_id_to_keep = get_interaction_to_keep(batch, interaction_statuses, interactions)
    batch.each do |row|
      interaction_id = row['interaction_id'].to_i
      keep = interaction_id_to_keep == interaction_id ? 'Y' : 'N'

      current_values = UgcDB::Interaction::HASH_FIELDS.map do |field|
        value = interactions[interaction_id]&.public_send(field)
        if field == 'date_occurred' && value
          value = Time.at(value / 1000).iso8601
        end
        value.is_a?(String) ? value.force_encoding('ASCII-8BIT') : value
      end

      out << (
        row.values +
        [interaction_statuses[interaction_id], interactions[interaction_id]&.updated_at, keep] +
        current_values
      )
    end
  end
end

deletions_csv_file = updated_deletions_dir.join(File.basename(SCRIPT_OPTIONS[:file])).to_s

CSV.open(deletions_csv_file, 'wb') do |out|
  out << ['InteractionId', 'ETInteractionId', 'InteractionType', 'Solicitor', 'Prospect', 'DateOccurred', 'CreatedAt', 'Summary', 'Body']
  
  csv = CSVUtils::CSVIterator.new(update_csv_file)
  csv.each do |row|
    next if row['keep'] == 'Y'
    next unless row['interaction_remote_id']

    out << [
      row['interaction_remote_id'],
      row['interaction_id'],
      row['interaction_type'],
      row['solicitor'],
      row['prospect'],
      row['date_occurred'],
      row['created_at'],
      row['summary'],
      row['text']
    ]
  end
end
