#!/usr/bin/env ruby

# This script can be used to update/fix the data in interaction_solicitors_most_recent. 
# It finds all distinct solicitor/prospect combinations by oid. It will then pass this
# data pair to a UGC admin endpoint that will perform the upsert operation.
# 
# The script runs on an oid basis, or globally if no oid is provided. 
# optional arg to notify via slack: --slack-notify amy
# optional arg to run remote: --run-remote
# ./ugc/interactions/fix-interactions-most-recent -e <staging|production> -o <oid>
#
# to view output when running remote: awslogs --profile evertruestage get repairshop-task-service -w | grep <task_id>
# (task id is found in run output)
require File.expand_path('../../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option(false)
  opts.add_run_remote 
end

require File.expand_path('../../config/environment', __dir__)
require 'csv'
require 'active_record'

ActiveRecord::Base.logger = LOG

ugc_mysql_client = MySQLHelpers.create_client(:ugc)

def ugc_api_client
  @ugc_api_client ||= UgcClient.create_app_client
end

def upsert_most_recent_interaction(oid, solicitor_contact_id, prospect_contact_id)
  res = ugc_api_client.upsert_most_recent_interaction(
    oid,
    solicitor_contact_id,
    prospect_contact_id
  )

  unless res.kind_of?(Net::HTTPSuccess)
    LOG.error("failed to upsert most recent interaction for  #{oid}/#{solicitor_contact_id}/#{prospect_contact_id}/ #{res.code}/#{res.body}")
    return false
  end
  true
end

oids = SCRIPT_OPTIONS[:oid] ? [SCRIPT_OPTIONS[:oid]] : AuthDB::Organization.where(id: UgcDB::Interaction.select('DISTINCT oid').to_a.map(&:oid)).to_a.map(&:id)

oids.each do |oid|
  ugc_mysql_client.query(
    "SELECT DISTINCT t.target_id AS prospect_contact_id, s.contact_id AS solicitor_contact_id " +
    "FROM interaction_solicitors s " +
    "INNER JOIN interaction_targets t ON s.interaction_id = t.interaction_id " +
    "INNER JOIN interactions i ON i.id = s.interaction_id AND i.oid = #{oid} " +
    "WHERE t.target_id IS NOT NULL AND s.contact_id IS NOT NULL")
    .each do |result|    
      STATS.inc_and_notify
      prospect_contact_id = result['prospect_contact_id']
      solicitor_contact_id = result['solicitor_contact_id']
      if upsert_most_recent_interaction(oid, solicitor_contact_id, prospect_contact_id)
        STATS[:upsert_success] += 1
      else
        STATS[:upsert_fail] += 1
      end      
    end
end

STATS.notify
