#!/usr/bin/env ruby

# Purpose is to investigate any/all issues with interaction IDs

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_run_remote
  opts.add_scheduling_options
  opts[:remote_id_column] = 'InteractionID'

  opts.on('-i', '--interaction EVERTRUE_INTERACTION_COLUMN', 'EverTrueInteractionID') do |v|
    opts[:et_interaction_id_column] = v
  end

  opts.on('-r', '--remote-id INTERACTION_REMOTE_ID_COLUMN', 'InteractionID column name') do |v|
    opts[:remote_id_column] = v
  end

  opts.on('-j', '--job JOB_ID', 'Last full import job') do |v|
    opts[:job_id] = v
  end

  opts.require_option(:remote_id_column)
end

require_relative '../../config/environment'

IMPORT_TYPES = [
  'NOTES_CSV',
  'INTERACTION'
]

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

job =
  if SCRIPT_OPTIONS[:job_id]
    ImporterDB::Job.where(oid: org.id, type: IMPORT_TYPES, id: SCRIPT_OPTIONS[:job_id]).first
  else
    ImporterDB::Job.where(oid: org.id, type: IMPORT_TYPES, prune: true).last
  end

raise("job lookup failed, possible issues, job_id was in org, job was not of type NOTES_CSV or INTERACTION, was not a full import") unless job

LOG.info("found job #{job.id}/#{job.s3_filename}, use version #{job.version} created on #{Time.at(job.created_at / 1000).iso8601}")

import_file = ImporterS3Client.new.download(job, 'interactions.csv')
at_exit do
  File.unlink(import_file) if File.exist?(import_file)
end

csv = CSVUtils::CSVIterator.new(import_file, liberal_parsing: true)

headers_not_found = [SCRIPT_OPTIONS[:et_interaction_id_column], SCRIPT_OPTIONS[:remote_id_column]].compact - csv.first.keys
raise("headers not found #{headers_not_found}") unless headers_not_found.empty?

def get_remote_id(row)
  row[SCRIPT_OPTIONS[:remote_id_column]]
end

def get_interaction_id(row)
  return nil unless SCRIPT_OPTIONS[:et_interaction_id_column]

  interaction_id = row[SCRIPT_OPTIONS[:et_interaction_id_column]]
  interaction_id.empty? ? nil : interaction_id.to_i
end

def archived?(oid, remote_id, interaction_id)
  scope = UgcDB::Interaction.where(oid: oid, remote_id: remote_id)
  scope = scope.where(interaction_id: interaction_id) if interaction_id
  scope.exists?
end

class ReportCSVRow < Struct.new(
        :status,
        :remote_id,
        :interaction_id
      )

  attr_accessor :current_interaction_oid,
                :current_remote_id,
                :current_interaction_id,
                :archived

  include CSVUtils::CSVRow

  csv_column :status, header: 'Status'
  csv_column :remote_id, header: 'InteractionID'
  csv_column :interaction_id, header: 'EverTrueInteractionID'
  csv_column :current_interaction_oid, header: 'CurrentInteractionOID'
  csv_column :current_remote_id, header: 'CurrentInteractionOID'
  csv_column :current_interaction_id, header: 'CurrentEverTrueInteractionID'
end

# rows where the EverTrueInteractionID does not match what is in our system
invalid_rows = []
not_found_rows = []

et_only_interactions = UgcDB::Interaction.select(:id, :remote_id).where(oid: SCRIPT_OPTIONS[:oid]).where('remote_id IS NOT NULL').index_by(&:remote_id)

csv.each do |row|
  import_interaction_id = get_interaction_id(row)
  interaction = et_only_interactions.delete(get_remote_id(row))

  if interaction.nil?
    not_found_rows << row
  elsif import_interaction_id && interaction.id != import_interaction_id
    invalid_rows << row
  end
end

LOG.notify("num invalid_rows #{invalid_rows.size}", true)
LOG.notify("num not_found_rows #{not_found_rows.size}", true)
LOG.notify("num et_only_interactions #{et_only_interactions.size}", true)

corrections_csv_path = TMP_DIR.join("reports/importer/orgs/#{org.id}/jobs/#{job.id}/#{org.slug}-#{org.id}-interaction-corrections-#{Time.now.strftime('%Y%m%d')}.csv").to_s
FileUtils.mkdir_p(File.dirname(corrections_csv_path))

corrections_csv = CSV.open(corrections_csv_path, 'wb')
corrections_csv << ['InteractionID', 'EverTrueInteractionID']

interactions_report_path = TMP_DIR.join("reports/importer/orgs/#{org.id}/jobs/#{job.id}/#{org.slug}-#{org.id}-interactions-report-#{Time.now.strftime('%Y%m%d')}.csv").to_s
CSVUtils::CSVReport.new(interactions_report_path, ReportCSVRow) do |report|
  [
    ['invalid', invalid_rows],
    ['not_found', not_found_rows]
  ].each do |status, batch|
    remote_ids = batch.map { |r| get_remote_id(r) }
    interaction_ids = batch.map { |r| get_interaction_id(r) }
    current_interactions = UgcDB::Interaction.where(id: interaction_ids).index_by(&:id)
    current_interactions_by_remote_id = UgcDB::Interaction.where(oid: SCRIPT_OPTIONS[:oid], remote_id: remote_ids).index_by(&:remote_id)

    batch.each do |row|
      csv_row = ReportCSVRow.new(
        status,
        get_remote_id(row),
        get_interaction_id(row)
      )

      current_interaction = current_interactions[csv_row.interaction_id]
      if current_interaction
        csv_row.current_interaction_oid = current_interaction.oid
        csv_row.current_remote_id = current_interaction.remote_id
      end

      current_interaction = current_interactions_by_remote_id[csv_row.remote_id]
      if current_interaction
        csv_row.current_interaction_id = current_interaction.id

        if current_interaction.oid == SCRIPT_OPTIONS[:oid]
          corrections_csv << [get_remote_id(row), current_interaction.id]
        end
      end

      report << csv_row
    end
  end
end

corrections_csv.close

corrections_csv_dest = RepairshopFileUploader.upload_file_and_notify(corrections_csv_path)
interactions_report_dest = RepairshopFileUploader.upload_file_and_notify(interactions_report_path)

s3_presigned_urls = [
  'Corrections Report: ' + RepairshopFileUploader.instance.presigned_url(corrections_csv_dest),
  'ID Report: ' + RepairshopFileUploader.instance.presigned_url(interactions_report_dest)
]

LOG.notify("Interactions reports for #{org.name}/#{org.id} are ready.\n#{s3_presigned_urls.join("\n")}", true)
