#!/usr/bin/env ruby

# Purpose: to delete unlinked interactions

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
  opts.add_oid_option
end

require_relative '../../config/environment'
require 'active_record'
ActiveRecord::Base.logger = LOG

def ugc_client
  @ugc_client ||= UgcClient.create_client_with_app_creds
end

def delete_interaction(interaction)
  LOG.info("oid #{interaction.oid}: deleting interaction #{interaction.id}[#{interaction.remote_id}]")

  ugc_client.v3_delete_interaction(interaction.oid, interaction.id)
end

UgcDB::Interaction.where(oid: SCRIPT_OPTIONS[:oid]).includes(interaction_targets: [contact: [:identities]]).find_each do |interaction|
  if interaction.interaction_targets.size == 0
    LOG.error("interaction #{interaction.id} has not targets???")
    next
  end

  unless interaction.primary_interaction_target.contact?
    LOG.info("skipping non contact interaction #{interaction.id}")
    next
  end

  # skip if interaction has a linked contact
  next if interaction.primary_interaction_target.contact

  delete_interaction(interaction)
end
