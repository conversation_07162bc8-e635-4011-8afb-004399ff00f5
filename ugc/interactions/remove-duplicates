#!/usr/bin/env ruby

# Purpose: to find duplicates interactions that originated from the FE on the same day

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
  opts.add_scheduling_options
  opts.add_oids_option(false)

  opts.on('--days-ago DAYS', Integer, 'Number of days ago to start checking for duplicates') do |v|
    opts[:days_ago] = v
  end

  opts.on('--debug', 'Debug mode') do
    opts[:debug] = true
  end
end

require_relative '../../config/environment'
require 'digest'

HASH_FIELDS = [
  'primary_target_type',
  'primary_target_id',
  'primary_solicitor_name',
  'interaction_type',
  'date_occurred',
  'summary',
  'text'
]

def hash_interaction(interaction)
  md5 = Digest::MD5.new

  HASH_FIELDS.each do |field|
    md5.update(field)
    md5.update(interaction.public_send(field).to_s)
  end

  md5.digest.unpack('h*').first
end

def ugc_client
  @ugc_client ||= UgcClient.create_client_with_app_creds
end

def days_ago_time
  return nil unless SCRIPT_OPTIONS[:days_ago]

  @days_ago_time ||= (Time.now - (SCRIPT_OPTIONS[:days_ago] * 86_400))
end

def delete_interaction(interaction)
  LOG.info("oid #{interaction.oid}: deleting interaction #{interaction.id}[#{interaction.remote_id}] duplicate of #{interaction.original_interaction_id}[#{interaction.original_remote_id}]")
  return if SCRIPT_OPTIONS[:debug]

  ugc_client.delete_note_by_target(
    interaction.oid,
    interaction.primary_target_type_name,
    interaction.primary_target_id,
    interaction.id
  )
end

class InteractionCSVRow < UgcDB::Interaction
  include CSVUtils::CSVRow

  attr_accessor :original_remote_id,
                :original_interaction_id,
                :original_created_at,
                :original_date_occurred

  csv_column('InteractionId') { remote_id }
  csv_column('ETInteractionId') { id }
  csv_column('InteractionType') { interaction_type }
  csv_column('Solicitor') { primary_solicitor_name }
  csv_column('Prospect') { primary_interaction_target&.contact&.name }
  csv_column('DateOccurred') { Time.at(date_occurred / 1000).iso8601 }
  csv_column('CreatedAt') { created_at.iso8601 }
  csv_column('Summary') { summary }
  csv_column('Body') { text }
  csv_column('DaysApart') { ((date_occurred - original_date_occurred).abs / 86_400_000).to_i }
  csv_column('OriginalInteractionId') { original_remote_id }
  csv_column('OriginalETInteractionId') { original_interaction_id }
  csv_column('OriginalCreatedAt') { original_created_at.iso8601 }
  csv_column('OriginalDateOccurred') { Time.at(original_date_occurred / 1000).iso8601 }
end

timestamp = Time.now.strftime('%Y-%m-%dT%H%M')

oids = SCRIPT_OPTIONS[:oids] ? SCRIPT_OPTIONS[:oids] : UgcDB::Interaction.select('DISTINCT oid').map(&:oid)
AuthDB::Organization.where(id: oids).each do |org|
  STATS.reset
  STATS[:oid] = org.id

  file = TMP_DIR.join('ugc', timestamp, 'interactions', 'duplicates', "#{org.slug}-#{org.id}-duplicate-interactions-#{timestamp}.csv").to_s
  FileUtils.mkdir_p(File.dirname(file))

  report = CSVUtils::CSVReport.new(file, InteractionCSVRow)

  total_duplicates = 0
  report_and_delete_proc = proc do |interaction, info|
    total_duplicates += 1

    interaction.original_remote_id = info[:remote_id]
    interaction.original_interaction_id = info[:id]
    interaction.original_created_at = info[:created_at]
    interaction.original_date_occurred = info[:date_occurred]

    report << interaction

    delete_interaction(interaction)
  end

  base_scope = InteractionCSVRow
                 .includes(
                   :interaction_solicitors,
                   interaction_targets: [
                     contact: [
                       :contact_attribute
                     ]
                   ]
                 ).select(
                   :id,
                   :oid,
                   :remote_id,
                   :interaction_type,
                   :summary,
                   :text,
                   :created_at,
                   :date_occurred
                 )
                 .where(
                   oid: org.id
                 )

  if days_ago_time
    base_scope = base_scope.where('created_at >= ?', days_ago_time)
  end

  # find all interactions created in EverTrue (creation_source = 1)
  evertrue_interactions = {}
  base_scope.where(creation_source: 1).find_each do |interaction|
    STATS.update(:evertrue_interactions)
    STATS.inc_and_notify

    digest = hash_interaction(interaction)
  
    if (info = evertrue_interactions[digest])
      STATS.update(:frontend_duplicates)
      report_and_delete_proc.call(interaction, info)
      next
    end

    evertrue_interactions[digest] = {
      id: interaction.id,
      remote_id: interaction.remote_id,
      created_at: interaction.created_at,
      date_occurred: interaction.date_occurred
    }
  end

  # compare EverTrue interactions to imported interactions
  base_scope.where(creation_source: 0).find_each do |interaction|
    STATS.update(:importer_interactions)
    STATS.inc_and_notify

    digest = hash_interaction(interaction)

    if (info = evertrue_interactions[digest])
      STATS.update(:imported_duplicates)
      report_and_delete_proc.call(interaction, info)
    end
  end

  report.close
  RepairshopFileUploader.upload_file_and_notify(file) if total_duplicates > 0
  File.unlink(file) unless SCRIPT_OPTIONS[:debug]
  STATS.notify(true)
end
