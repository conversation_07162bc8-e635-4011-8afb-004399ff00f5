#!/usr/bin/env ruby

# This script can be used to update/fix the data in ugc_types for interactions that don't have the contact category.
# It finds all ugc interaction types for an org. It will then check to ensure that the contact category exists
# if not it will be added to the array and passed to the UGC type endpoint that will perform the update operation.
#
# The script runs on an oid basis, or globally if no oid is provided.
# optional arg to notify via slack: --slack-notify amy
# optional arg to run remote: --run-remote
# ./ugc/interactions/fix-ugc-interaction-type-categories -e <staging|production> -o <oid>
#
# to view output when running remote: awslogs --profile evertruestage get repairshop-task-service -w | grep <task_id>
# (task id is found in run output)
require File.expand_path('../../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option(false)
  opts.add_run_remote
end

require File.expand_path('../../config/environment', __dir__)
require 'active_record'

ActiveRecord::Base.logger = LOG

def ugc_api_client
  @ugc_api_client ||= UgcClient.create_app_client
end

def fetch_interaction_types(oid, data_type)
  res =  ugc_api_client.fetch_ugc_types_by_data_type(oid, data_type)

  unless res.kind_of?(Net::HTTPSuccess)
    LOG.error("failed to fetch ugc types #{data_type} #{res.code}/#{res.body}")
    return nil
  end

  JSON.parse(res.body)
end

def update_ugc_type(data_type, ugc_type)
  res = ugc_api_client.put_ugc_type(
    data_type,
    ugc_type
  )

  unless res.kind_of?(Net::HTTPSuccess)
    LOG.error("failed to update ugc type  #{oid}/#{data_type}/#{ugc_type}/ #{res.code}/#{res.body}")
    return false
  end
  true
end

oids = SCRIPT_OPTIONS[:oid] ? [SCRIPT_OPTIONS[:oid]] : AuthDB::Organization.where(id: UgcDB::UgcType.select('DISTINCT oid').where(data_type: 0).to_a.map(&:oid)).to_a.map(&:id)

oids.each do |oid|
  STATS.inc_and_notify
  ugc_types = fetch_interaction_types(oid, "INTERACTION")

  ugc_types.each do |ugc_type|
    is_contact_category_present = ugc_type['categories'].find{|o| o['category'] === "CONTACT"}
    if !is_contact_category_present
      LOG.info("Existing ugc type: #{ugc_type}")
      ugc_type['categories'].push({category: "CONTACT"})
      LOG.info("Updated ugc type: #{ugc_type}")
      if update_ugc_type("INTERACTION", ugc_type)
        STATS[:upsert_success] += 1
      else
        STATS[:upsert_fail] += 1
      end
    end
  end
end

STATS.notify