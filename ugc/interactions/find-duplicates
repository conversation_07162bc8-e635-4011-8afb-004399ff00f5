#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
  opts.add_oids_option
end

require_relative '../../config/environment'

class InteractionCSVRow < UgcDB::Interaction
  include CSVUtils::CSVRow

  attr_accessor :original_remote_id,
                :original_interaction_id,
                :original_created_at,
                :original_date_occurred

  csv_column('InteractionId') { remote_id }
  csv_column('ETInteractionId') { id }
  csv_column('InteractionType') { interaction_type }
  csv_column('Solicitor') { primary_solicitor_name }
  csv_column('Prospect') { primary_interaction_target&.contact&.name }
  csv_column('DateOccurred') { Time.at(date_occurred / 1000).iso8601 }
  csv_column('CreatedAt') { created_at.iso8601 }
  csv_column('Summary') { summary }
  csv_column('Body') { text }
  csv_column('DaysApart') { ((date_occurred - original_date_occurred).abs / 86_400_000).to_i }
  csv_column('OriginalInteractionId') { original_remote_id }
  csv_column('OriginalETInteractionId') { original_interaction_id }
  csv_column('OriginalCreatedAt') { original_created_at.iso8601 }
  csv_column('OriginalDateOccurred') { Time.at(original_date_occurred / 1000).iso8601 }
end

AuthDB::Organization.where(id: SCRIPT_OPTIONS[:oids]).each do |org|
  STATS.reset
  STATS[:oid] = org.id

  file = TMP_DIR.join('ugc', 'interactions', 'duplicates', "#{org.slug}-#{org.id}-duplicate-interactions-#{Time.now.strftime('%Y-%m-%d')}.csv").to_s
  FileUtils.mkdir_p(File.dirname(file))

  report = CSVUtils::CSVReport.new(file, InteractionCSVRow)

  # find all interactions created in EverTrue (creation_source = 1)
  evertrue_interactions = {}
  InteractionCSVRow.includes(:interaction_solicitors, interaction_targets: [contact: [:contact_attribute]]).select(:id, :oid, :remote_id, :interaction_type, :summary, :text, :created_at, :date_occurred).where(oid: org.id, creation_source: 1).find_each do |interaction|
    STATS.update(:evertrue_interactions)
    STATS.inc_and_notify

    digest = interaction.to_md5
  
    if (info = evertrue_interactions[digest])
      STATS.update(:fe_duplicates)

      interaction.original_remote_id = info[:remote_id]
      interaction.original_interaction_id = info[:id]
      interaction.original_created_at = info[:created_at]
      interaction.original_date_occurred = info[:date_occurred]

      report << interaction
      next
    end

    evertrue_interactions[digest] = {id: interaction.id, remote_id: interaction.remote_id, created_at: interaction.created_at, date_occurred: interaction.date_occurred}
  end

  # compare EverTrue interactions to imported interactions
  InteractionCSVRow.includes(:interaction_solicitors, interaction_targets: [contact: [:contact_attribute]]).select(:id, :oid, :remote_id, :interaction_type, :summary, :text, :created_at, :date_occurred).where(oid: org.id, creation_source: 0).find_each do |interaction|
    STATS.update(:importer_interactions)
    STATS.inc_and_notify

    digest = interaction.to_md5

    if (info = evertrue_interactions[digest])
      STATS.update(:imported_duplicates)

      interaction.original_remote_id = info[:remote_id]
      interaction.original_interaction_id = info[:id]
      interaction.original_created_at = info[:created_at]
      interaction.original_date_occurred = info[:date_occurred]

      report << interaction
    end
  end

  report.close
  RepairshopFileUploader.upload_file_and_notify(file)
  STATS.notify(true)
end
