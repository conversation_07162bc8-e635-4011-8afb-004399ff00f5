#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-i', '--interaction INTERACTION_ID', Integer, 'Interaction ID to restore from archive table') do |v|
    opts[:interaction_id] = v
  end

  opts.on('-r', '--remote-id INTERACTION_REMOTE_ID', Integer, 'Interaction Remote ID to restore from archive table') do |v|
    opts[:remote_id] = v
  end

  opts.on('-f', '--file CSV_FILE', 'CSV file of archived interactions to restore.  Required Headers: interaction_id, remote_id') do |v|
    opts[:file] = v
  end
end

require_relative '../../config/environment'

def ugc_client
  @ugc_client ||= UgcClient.create_client_with_app_creds
end

csv = CSVUtils::CSVIterator.new(SCRIPT_OPTIONS[:file])

missing_headers = ['interaction_id', 'remote_id'] - csv.first.keys
raise("missing required headers #{missing_headers}") unless missing_headers.empty?

csv.each do |row|
  interaction_id = row['interaction_id']
  remote_id = row['remote_id']

  if !interaction_id || !remote_id
    LOG.warn("required fields not set in #{row}")
    next
  end

  interaction = UgcDB::Interaction.where(id: interaction_id).first

  if interaction
    if interaction.remote_id == remote_id
      LOG.info("skipping, interaction already exists for #{row}")
    else
      LOG.error("interaction exists but remote ids do not match #{row['remote_id']} != #{interaction.remote_id}")
    end

    next
  end

  archive_interaction = UgcDB::ArchiveInteraction
                          .where(
                            oid: SCRIPT_OPTIONS[:oid],
                            interaction_id: interaction_id,
                            remote_id: remote_id
                          ).last

  unless archive_interaction
    LOG.warn("no archive_interaction found for #{row}")
    next
  end

  LOG.info("found archive_interaction #{archive_interaction.id} for #{row}")

  payload = JSON.parse(archive_interaction.json)

  [
    'oid',
    'creator_user_id',
    'updating_user_id',
    'creation_source',
    'application',
    'created_at',
    'updated_at'
  ].each do |field_to_remove|
    payload.delete(field_to_remove)
  end

  [
    'id',
    'interaction_id',
    'created_at',
    'updated_at'
  ].each do |field_to_remove|
    [
      'targets',
      'custom_fields',
      'solicitors',
      'user_mentions',
      'contact_mentions',
      'labels'
    ].each do |list_field|
      payload[list_field].each do |target|
        target.delete(field_to_remove)
      end
    end

    payload['primary_target'].delete(field_to_remove)
  end

  LOG.info("restoring #{archive_interaction.interaction_id} with #{payload.to_json}")

  res = ugc_client.update_interaction(archive_interaction.oid, archive_interaction.interaction_id, payload)
  if res.kind_of?(Net::HTTPSuccess)
    LOG.info("restored #{archive_interaction.interaction_id}")
  else
    LOG.warn("failed to restore interaction #{archive_interaction.interaction_id} #{res.code}/#{res.body}")
  end
end
