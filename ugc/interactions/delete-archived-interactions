#!/usr/bin/env ruby

# Purpose: to delete an org's archived_interactions.  The issue we face is the org's archived_interactions have contact_id(s) that no longer exist.  Which cause the daily export process to fail.

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
  opts.add_oid_option
end

require_relative '../../config/environment'

class ArchiveInteractionCSVRow < UgcDB::ArchiveInteraction
  include CSVUtils::CSVRow

  csv_column :id
  csv_column :interaction_id
  csv_column :oid
  csv_column :remote_id
  csv_column :json
  csv_column :delete_source
  csv_column :created_at
end

base_dir = TMP_DIR.join('ugc')
FileUtils.mkdir_p(base_dir)
path = base_dir.join("org-#{SCRIPT_OPTIONS[:oid]}-archived-interactions-#{Time.now.utc.strftime('%Y-%m-%dT%H%M%S')}.csv").to_s

ids_to_delete = []

CSVUtils::CSVReport.new(path, ArchiveInteractionCSVRow) do |report|
  ArchiveInteractionCSVRow.where(oid: SCRIPT_OPTIONS[:oid], delete_source: 1).find_each do |archive_interaction|
    report << archive_interaction
    ids_to_delete << archive_interaction.id
  end
end

RepairshopFileUploader.upload_file_and_notify(path)

ids_to_delete.in_groups_of(1_000, false) do |batch_of_archive_interaction_ids|
  UgcDB::ArchiveInteraction.where(id: batch_of_archive_interaction_ids).delete_all
end
