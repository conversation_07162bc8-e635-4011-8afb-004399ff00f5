#!/usr/bin/env ruby

=begin
Purpose: to synchronize interactions between MySQL and ES

Dumps all interaction ids and update times to a file from MySQL and ES

Compares the 2 files to figure out what to delete/update/create

Deletions only occur in ES if the interaction is not found in MySQL

Updates/Creates use the UGC api
=end

# The script runs on an oid basis, or globally if no oid is provided.
# ./ugc/sync-interactions -e <staging|production> -o <oid>
require File.expand_path('../../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option(false)
  opts.add_run_remote
end

require File.expand_path('../../config/environment', __dir__)
require 'csv'
include CommandLineHelpers

def hash_interaction_data(updated_at:, date_occurred:, target_id:, solicitor_contact_ids:, secondary_target_target_ids:)
  digest = Digest::MD5.new
  digest << 'updated_at'
  digest << updated_at.to_s
  digest << 'date_occurred'
  digest << date_occurred.to_s
  digest << 'target_id'
  digest << target_id.to_s
  digest << 'solicitor_contact_ids'
  digest << solicitor_contact_ids.sort.join(',')
  digest << 'secondary_target_target_ids'
  digest << secondary_target_target_ids.sort.join(',')
  digest.to_s
end

class MySQLInteractionCSVRow < UgcDB::Interaction
  include CSVUtils::CSVRow

  csv_column :id
  csv_column :primary_target_id, header: :contact_id
  csv_column :timestamp
  csv_column :oid

  def solicitor_contact_ids
    ids = interaction_solicitors.map(&:contact_id)
    ids.compact!
    ids
  end

  def secondary_target_target_ids
    ids = interaction_targets.reject { |t| t.primary }.map(&:target_id)
    ids.compact!
    ids
  end

  def timestamp
    hash_interaction_data(
      updated_at: updated_at.to_i * 1000,
      date_occurred: date_occurred,
      target_id: primary_target_id,
      solicitor_contact_ids: solicitor_contact_ids,
      secondary_target_target_ids: secondary_target_target_ids
    )
  end
end

class ESInteractionCSVRow < Struct.new(:id,
                                       :contact_id,
                                       :updated_at,
                                       :date_occurred,
                                       :target_id,
                                       :solicitor_contact_ids,
                                       :secondary_target_target_ids,
                                       :index,
                                       :type,
                                       :routing
                                      )
  include CSVUtils::CSVRow

  csv_column :id
  csv_column :contact_id
  csv_column :timestamp
  csv_column :index
  csv_column :type
  csv_column :routing

  def self.create_from_doc(doc)
    solicitor_contact_ids = (doc['_source']['solicitor'] || [])
    solicitor_contact_ids.map! { |obj| obj['contact_id'] }
    solicitor_contact_ids.compact!

    secondary_target_target_ids = (doc['_source']['secondary_target'] || [])
    secondary_target_target_ids.map! { |obj| obj['target_id'] }
    secondary_target_target_ids.compact!

    new doc['_id'],
        doc['_parent'],
        doc['_source']['updated_at'],
        doc['_source']['date_occurred'],
        doc['_source']['target_id'],
        solicitor_contact_ids,
        secondary_target_target_ids,
        doc['_index'],
        doc['_type'],
        doc['_routing']
  end

  def timestamp
    hash_interaction_data(
      updated_at: updated_at,
      date_occurred: date_occurred,
      target_id: target_id,
      solicitor_contact_ids: solicitor_contact_ids,
      secondary_target_target_ids: secondary_target_target_ids
    )
  end
end

oids = if SCRIPT_OPTIONS[:oid]
         [SCRIPT_OPTIONS[:oid]]
       else
         LOG.measure('fetching distinct oids') do
           UgcDB::Interaction.select('DISTINCT oid').to_a.map(&:oid)
         end
       end

AuthDB::Organization.where(deleted: false, id: oids).each do |org|
  LOG.info "Synchronize interaction for #{org.name}/#{org.id}"

  primary_data_file = TMP_DIR.join("ugc-mysql-interactions-oid-#{org.id}.dump").to_s
  sorted_primary_data_file = primary_data_file + '.sorted'
  secondary_data_file = TMP_DIR.join("ugc-es-interactions-oid-#{org.id}.dump").to_s
  sorted_secondary_data_file = secondary_data_file + '.sorted'

  Parallelizer.new do |parallelizer|
    parallelizer.process do
      LOG.measure('fetching interactions from mysql') do
        CSVUtils::CSVReport.new(primary_data_file, MySQLInteractionCSVRow) do |report|
          MySQLInteractionCSVRow.select('id, oid, date_occurred, updated_at').includes(:interaction_targets, :interaction_solicitors).where(oid: org.id).find_each do |interaction|
            unless interaction.primary_interaction_target
              LOG.error("interaction #{interaction.id} has no interaction_targets")
              next
            end
            unless interaction.primary_interaction_target.contact?
              STATS.update(:mysql_list_records)
              next
            end
            STATS.update(:mysql_contact_records)
            report << interaction
          end
        end
      end

      LOG.measure('sorting interactions from mysql') do
        CSVUtils::CSVSort.new(primary_data_file, sorted_primary_data_file, 250_000).sort do |a, b|
          if a[0] == b[0]
            a[1].to_i <=> b[1].to_i
          else
            a[0].to_i <=> b[0].to_i
          end
        end
      end
    end

    parallelizer.process do
      LOG.measure('fetching interactions from es') do
        CSVUtils::CSVReport.new(secondary_data_file, ESInteractionCSVRow) do |report|
          scanner = ESContactHelpers.query_mapping_by_oid(:contact_note, org.id)
          scanner.fields_to_return = ['updated_at', 'date_occurred', 'target_id', 'solicitor.contact_id', 'secondary_target.target_id']
          scanner.each do |doc|
            STATS.update(:es_records)
            report << ESInteractionCSVRow.create_from_doc(doc)
          end
        end
      end
    
      LOG.measure('sorting interactions from es') do
        CSVUtils::CSVSort.new(secondary_data_file, sorted_secondary_data_file, 250_000).sort do |a, b|
          if a[0] == b[0]
            a[1].to_i <=> b[1].to_i
          else
            a[0].to_i <=> b[0].to_i
          end
        end
        next
        FileUtils.cp(secondary_data_file, sorted_secondary_data_file)
      end      
    end
  end

  STATS.notify

  File.unlink(primary_data_file)
  primary_data_file = nil
  File.unlink(secondary_data_file)
  secondary_data_file = nil

  comparer = DataSourceComparer.new(sorted_primary_data_file) do |src_record, dest_record|
    if src_record['id'] == dest_record['id']
      src_record['contact_id'].to_i <=> dest_record['contact_id'].to_i
    else
      src_record['id'].to_i <=> dest_record['id'].to_i
    end
  end

  delete_worker = WorkerThreads.new do |queue|
    records_to_delete = []

    delete_records_proc = Proc.new do
      actions = records_to_delete.map do |record|
        {
          'delete' => {
            '_index' => record['index'],
            '_id' => record['id'],
            '_type' => record['type'],
            '_routing' => record['routing']
          }
        }
      end

      ESHelpers.bulk_request(actions)
      STATS.update(:deleted, records_to_delete.size)
      records_to_delete = []
    end

    queue.each do |record|
      records_to_delete << record

      delete_records_proc.call if records_to_delete.size == 100
    end

    delete_records_proc.call if records_to_delete.size > 0
  end

  index_worker = WorkerThreads.new(10) do |queue|
    ugc_client = UgcClient.create_client_with_app_creds

    queue.each do |record|
      res = ugc_client.sync_interaction_to_es(record['oid'], record['id'])

      if res.kind_of?(Net::HTTPSuccess)
        LOG.info("syncing interaction #{record['id']}")
        STATS.update(:indexed)
      else
        LOG.error "sync failed for #{record} with #{res.code}/#{res.body}"
        STATS.update(:sync_note_failed)
      end
    end
  end

  comparer.compare(sorted_secondary_data_file) do |action, record|
    STATS.inc_and_notify
    STATS[action] += 1

    case action
    when :update,
         :create
      index_worker.enq(record)
    when :delete
      delete_worker.enq(record)
    else
      raise("unknown action #{action} for record #{record}")
    end
  end

  delete_worker.shutdown
  index_worker.shutdown

  File.unlink(sorted_primary_data_file)
  File.unlink(sorted_secondary_data_file)

  STATS.notify(true)

  STATS.reset
end
