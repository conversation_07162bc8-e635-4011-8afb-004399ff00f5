#!/usr/bin/env ruby

# This script can be used to update/fix the data in interactions.
# It finds all DXO Ask interaction types by oid. It will then pass this
# data pair to a UGC admin endpoint that will perform the upsert operation.
#
# The script runs on an oid basis, or globally if no oid is provided.
# optional arg to notify via slack: --slack-notify amy
# optional arg to run remote: --run-remote
# ./ugc/interactions/fix-interactions-dxo-date-occurred -e <staging|production> -o <oid>
#
# to view output when running remote: awslogs --profile evertruestage get repairshop-task-service -w | grep <task_id>
# (task id is found in run output)
require File.expand_path('../../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option(false)
  opts.add_run_remote
end

require File.expand_path('../../config/environment', __dir__)
require 'csv'
require 'active_record'

ActiveRecord::Base.logger = LOG

ugc_mysql_client = MySQLHelpers.create_client(:ugc)

def ugc_api_client
  @ugc_api_client ||= UgcClient.create_client_with_app_creds
end

def update_dxo_date_occurred_interactions(oid, id)
  res = ugc_api_client.update_dxo_date_occurred_interactions(
    oid,
    id
  )

  unless res.kind_of?(Net::HTTPSuccess)
    LOG.error("failed to update dxo date occurred interaction for  #{oid}/#{id} #{res.code}/#{res.body}")
    return false
  end
  true
end

oids = SCRIPT_OPTIONS[:oid] ? [SCRIPT_OPTIONS[:oid]] : AuthDB::Organization.where(id: UgcDB::Interaction.select('DISTINCT oid').to_a.map(&:oid)).to_a.map(&:id)

oids.each do |oid|
  ugc_mysql_client.query(
    "SELECT id " +
      "FROM interactions " +
      "WHERE oid = #{oid} AND interaction_type = 'DXO Ask' AND ask_date != date_occurred")
                  .each do |result|
    STATS.inc_and_notify
    id = result['id']
    if update_dxo_date_occurred_interactions(oid, id)
      STATS[:upsert_success] += 1
    else
      STATS[:upsert_fail] += 1
    end
  end
end

STATS.notify(true)

