# ET Repair Shop
![ET Repair Shop](http://astonmartin.blob.core.windows.net/magazine/issue-21/images/feature_centenary_racing_5.jpg)

Scripts for all your repair needs!

There are two ways you can use these scripts: running them with your local machine, or running them via the docker image. Both are fine approaches, but some folks (especially people on M1 or later macbook) have trouble with some of the Ruby Gems that are necessary. 

Setting up both should be easy: 

### Local Setup: 

Simply `cd` into `et_repair_shop` and then run `./setup`. This will use homebrew and rvm to install the correct version of ruby and the associated gems. 

### Docker setup: 

>Note: this assumes you already have Docker running on your machine. at the time of ths writing, <PERSON><PERSON><PERSON> uses Colima for our Docker needs. See Docker Setup docs [here.](https://www.notion.so/thankview/Docker-Setup-ee096b15c0154918a1ba92cb04b00575#3f23ad1f59d040308fb4dce2197fe468).

Once you have docker up and running on your machine, simply `cd` into `et_repair_shop` and run `./dev/build-image`. This will build a new image for et_repair_shop (without a cache). Verify it exists by running `docker images` in your CLI. You should see `et_repair_shop_cli` listed. 
>PS -- if this prints a ton of nameless useless images, run `docker image prune` to delete all the dangling ones.

>!! Sometimes, if you are attempting to run a script and are getting unexpected errors, the best place to start is to rebuild your image. 

### Locally Running Scripts:

most scripts are simple executable files that can be ran by typing the path of the file into your terminal, followed by required arguments.

For example, `./aws/aws-codebuild-summary -p journeys` will print a table with the CodeBuild status of the journeys repository. 
For details about scripts, you'll need to navigate into them and read their params and what they do. There's lots of powerful scripts in here, happy scripting!! 

### Running Scripts with Docker:

Everything is identical to running it locally, except you prefix the command with `./dev/run` so, the exampl above becomes

`./dev/run ./aws/aws-codebuild-summary -p journeys` 

This runs the script in the docker image.

### Running Scripts Remotely:

Most scripts (not all) take a `--run-remote` argument. providing this argument runs the script in our AWS bucket, instead of running it on your machine. This is nice when you are doing something that is going to take a while (like writing a bunch of records to a database). It will print out a JSON Object telling you the status of the script. Then, you can use the `download-cloudwatch-log` script to download the AWS logs for your script! execute this by running `./aws/download-cloudwatch-log repairshop-task-service $logstream -p $profile` where `logstream` is the number you see in the JSON (you can also find it in the #task_notifications Slack channel) and `profile` is the AWS profile used to run it (usually this will be `evertrueprod` or `evertruestage`).

### Quality of Life Improvements

If you find yourself using a script frequently, you can add it to your `.zshrc` file and execute it with ease.

For example, you can add the following to your `.zshrc` file:

```
function repair-shop() {
    $HOME/your/path/to/your/et_repair_shop/dev/run "$@"
}

buildstatus() {
  repo=$1
  repair-shop ./aws/aws-codebuild-summary -p $repo
}

```

Save the changes, open another terminal window, and now you can simply type `buildstatus journeys` (from anywhere!) and it does everything for you :D 

### Contribution Guide

If you want to add a new script, here are some rules:
1. generally, our scripts are organized by microservice. So if you are writing a new script for the `journeys` repository, put it in the `journeys` directory.
2. `lib/` directory is where you'll add classes/helper functions to help you do the script. things like `clients`, which are interfaces with the various APIs, belong here.
3. Please add some documentation (even just some comments in the script file) explaining what the script does, why it exists, how to interact with it, and whether or not this is a "one-time" script that can be deleted later.



Happy scripting! 