#!/usr/bin/env ruby

require File.expand_path('../config/environment', __dir__)
require 'optparse'
require 'active_support/time'

options = {
  date: 1.month.ago.at_beginning_of_month.strftime('%Y-%m-%d')
}
OptionParser.new do |opts|
  opts.banner = 'Usage: ' + File.basename(__FILE__)

  opts.on('-h', '--help', 'Prints this help') do
    puts opts
    exit
  end

  opts.on('-d', '--date YYYY-MM-DD', 'Date to build report from (default is 1 month ago)') do |v|
    options[:date] = Date.parse(v).to_time.strftime('%Y-%m-%d')
  end
end

today = Time.now.strftime('%Y-%m-%d')

seven_days_ago_date = 7.days.ago.at_beginning_of_day.strftime('%Y-%m-%d')
importer_mysql_client = MySQLHelpers.create_client(:importer)
sql = <<SQL
SELECT
  oid
FROM
  job
WHERE
  `type` = 'TRANSACTIONAL_CSV' AND
  created_at >= (UNIX_TIMESTAMP('#{seven_days_ago_date}') * 1000)
SQL
org_ids_from_last_7_days = importer_mysql_client.query(sql, as: :array).to_a.flatten

sql = <<SQL
SELECT
  oid,
  DATE(FROM_UNIXTIME(occurred_at / 1000)) AS date,
  count(*) AS num_transactions
FROM
  gift_transaction
WHERE
  occurred_at >= (UNIX_TIMESTAMP('#{options[:date]}') * 1000) AND
  oid IN(#{org_ids_from_last_7_days.join(', ')})
GROUP BY
  oid, date
ORDER BY
  oid, date;
SQL

gifts_mysql_client = MySQLHelpers.create_client(:gifts)
report = CSVReport.new("gift-transactions-from-#{options[:date]}-to-#{today}.csv")
report.from_mysql_query(gifts_mysql_client, sql)

sql = <<SQL
SELECT
  oid,
  DATE(created_at) AS date,
  count(*) AS num_charges
FROM
  charges
WHERE
  created_at >= '#{options[:date]} 00:00:00'
GROUP BY
  oid, date
ORDER BY
  oid, date;
SQL

give_mysql_client = MySQLHelpers.create_client(:give)
report = CSVReport.new("charges-from-#{options[:date]}-to-#{today}.csv")
report.from_mysql_query(give_mysql_client, sql)
