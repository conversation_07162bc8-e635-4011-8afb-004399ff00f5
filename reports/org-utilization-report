#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
  opts.add_scheduling_options
end

require File.expand_path('../config/environment', __dir__)
require 'active_record'
# uncomment to debug query/performance issues
# ActiveRecord::Base.logger = LOG

class OrganizationCSVRow < AuthDB::Organization
  include CSVUtils::CSVRow

  ACTIVE_SESSION_DAYS_AGO = 60
  ACTIVE_SESSION_SINCE = (Time.now() - (ACTIVE_SESSION_DAYS_AGO * 86_400))

  csv_column :id, header: :oid
  csv_column :name
  csv_column :num_contacts
  csv_column :num_interactions
  csv_column :num_proposals
  csv_column :num_assignments
  csv_column :num_users
  csv_column :num_active_users

  def num_contacts
    ContactDB::Contact.where(oid: id).count
  end

  def num_interactions
    UgcDB::Interaction.where(oid: id).count
  end

  def num_proposals
    UgcDB::Proposal.where(oid: id).count
  end

  def num_assignments
    VolunteerDB::Assignment.where(oid: id).count
  end

  def org_user_ids
    @org_user_ids ||=
      begin
        affiliations = AuthDB::Affiliation.includes(:user, organization: [:roles], affiliation_roles: [:role]).where(organization_id: id).to_a
        affiliations.reject!(&:alumni?)
        affiliations.reject!(&:deleted?)
        affiliations.reject!(&:super_user?)
        affiliations.map(&:user_id)
      end
  end

  def num_users
    org_user_ids.size
  end

  def num_active_users
    AuthDB::Session
      .select('DISTINCT user_id')
      .where(
        organization_id: id,
        user_id: org_user_ids
      )
      .where('created_at > ?', ACTIVE_SESSION_SINCE)
      .count
  end

  # free up memory
  def clear!
    @org_user_ids = nil
  end
end

base_dir = TMP_DIR.join('reports', 'orgs')
FileUtils.mkdir_p(base_dir)
path = base_dir.join("org-utilization-#{Time.now.utc.strftime('%Y-%m-%d')}.csv").to_s

CSVUtils::CSVReport.new(path, OrganizationCSVRow) do |report|
  OrganizationCSVRow.where(test_org: false, deleted: false).each do |org|
    report << org
    org.clear!
  end
end

RepairshopFileUploader.upload_file_and_notify(path)
