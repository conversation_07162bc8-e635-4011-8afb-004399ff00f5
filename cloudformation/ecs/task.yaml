Description: >
    Template for setting up the RepairShop task

Parameters:

    TemplateBucket:
        Description: The S3 bucket from which to fetch the templates used by this stack
        Type: String
        Default: cloudformation-proj-cross-account-artifactbucket-fc0c7ottwz7q

    TemplateVersion:
        Description: Template version
        Type: String
        Default: v0.1.3

    EnvType:
        Description: Environment type
        Type: String
        AllowedValues:
            - stage
            - prod
        ConstraintDescription: must specify staging or production.

Conditions:
    Prod: !Equals [!Ref EnvType, 'prod']

Resources:
    RepairShopLog:
        Type: AWS::CloudFormation::Stack
        Properties:
            TemplateURL: !Sub https://s3.amazonaws.com/${TemplateBucket}/aws-ecs-cloudformation/${TemplateVersion}/services/v2/cloud-watch-log-group.yaml
            Parameters:
                LogGroupName: repairshop-task-service

    RepairShopTaskRole:
        Type: AWS::IAM::Role
        Properties:
            RoleName: repairshop-task-role
            Path: /
            AssumeRolePolicyDocument:
                Statement:
                    - Effect: Allow
                      Principal:
                        Service:
                            - ecs-tasks.amazonaws.com
                      Action:
                        - sts:AssumeRole
            Policies:
                - PolicyName: ecs-worker-task-policy
                  PolicyDocument:
                    Version: "2012-10-17"
                    Statement:
                        - Effect: Allow
                          Action:
                            - secretsmanager:GetSecretValue
                            - secretsmanager:DescribeSecret
                            - ec2:DescribeInstances
                            - ecs:RunTask
                          Resource: "*"
                        - Effect: Allow
                          Action:
                            - s3:*
                          Resource:
                            - !Sub "arn:aws:s3:::et-sftp-${EnvType}"
                            - !Sub "arn:aws:s3:::et-sftp-${EnvType}/*"
                            - !Sub "arn:aws:s3:::et-enrichment-${EnvType}"
                            - !Sub "arn:aws:s3:::et-enrichment-${EnvType}/*"

    RepairShopTask:
        Type: AWS::CloudFormation::Stack
        Properties:
            TemplateURL: !Sub https://s3.amazonaws.com/${TemplateBucket}/aws-ecs-cloudformation/${TemplateVersion}/services/v2/task-definition.yaml
            Parameters:
                LogGroupName: !GetAtt RepairShopLog.Outputs.LogGroupName
                TaskRole: !Ref RepairShopTaskRole
                TaskDefinitionFamily: et-repairshop-task
                Memory: 3072
                MemoryReservation: 1024
                EnvType: !Ref EnvType
