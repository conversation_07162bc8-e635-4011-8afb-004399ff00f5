Description: >
    Template for setting up a custom AWS Event Bus

Parameters:

    EventBusName:
        Description: The name of the Event Bus
        Type: String
        Default: repairshop

    EnvType:
        Type: String
        Description: Envrionment Type
        AllowedValues:
            - stage
            - prod
        ConstraintDescription: must specify stage or prod

Resources:

    EventBusRole:
        Type: AWS::IAM::Role
        Properties:
            RoleName: eventbridge-repairshop-role
            Path: /service-role/
            AssumeRolePolicyDocument:
                Statement:
                  - Effect: Allow
                    Principal:
                        Service:
                          - events.amazonaws.com
                    Action:
                      - sts:AssumeRole
            Policies:
              - PolicyName: eventbridge-repairshop-policy
                PolicyDocument:
                    Version: "2012-10-17"
                    Statement:
                      - Action:
                          - ecs:RunTask
                        Effect: Allow
                        Resource: "*"
                      - Action: iam:PassRole
                        Condition:
                            StringLike:
                                iam:PassedToService: ecs-tasks.amazonaws.com
                        Effect: Allow
                        Resource: "*"
