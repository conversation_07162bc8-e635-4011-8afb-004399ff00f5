#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
end

require File.expand_path('../config/environment', __dir__)

existing_oids = AuthDB::Organization.where(deleted: false).select('id').map(&:id).map(&:to_s).to_set
gates = RedisHelpers.get_all_dna_gate_keys(REDIS_CONFIG['dna'])

dna_client = DNAClient.create_client_with_app_creds

class DNAGateCSVRow < Struct.new(:oid,
                                 :gate,
                                 :enabled,
                                 :platform)
  include CSVUtils::CSVRow

  csv_column :oid
  csv_column :gate
  csv_column :enabled
  csv_column :platform
end

path = TMP_DIR.join('reports', 'dna', "dna-gates-report-#{Time.now.utc.strftime('%Y-%m-%d')}.csv").to_s
FileUtils.mkdir_p(File.dirname(path))

STATS.total = gates.size
STATS.notification_interval = (gates.size / 10).to_i

CSVUtils::CSVReport.new(path, DNAGateCSVRow) do |report|
  gates.each do |gate_name|
    payload = dna_client.get_bulk_gate(gate_name)
    unless payload['organizations']
      LOG.info("gate #{gate_name} has no organizations, DNA response: #{payload}")
      next
    end

    payload['organizations'].each do |oid, org_gate_info|
      next unless existing_oids.include?(oid)

      report << DNAGateCSVRow.new(oid, gate_name, org_gate_info['enabled'], nil)

      if org_gate_info['platforms']
        org_gate_info['platforms'].each do |platform, platform_gate_info|
          report << DNAGateCSVRow.new(oid, gate_name, platform_gate_info['enabled'], platform)
        end
      end
    end
  end

  STATS.inc_and_notify
end

RepairshopFileUploader.upload_file_and_notify(path)

STATS.notify(true)
