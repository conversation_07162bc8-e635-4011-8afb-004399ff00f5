#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts[:enable] = true

  opts.on('-u', '--user USER_ID', 'User ID') do |v|
    opts.options[:user_id] = v
  end

  opts.on('-g', '--gate GATE', 'Gate name') do |v|
    opts.options[:gate] = v
  end

  opts.on('--disable', 'Disable gate') do
    opts[:enable] = false
  end

  opts.require_option(:gate)
  opts.require_option(:user_id)
end

require File.expand_path('../config/environment', __dir__)

def dna_client
  @dna_client ||= DNAClient.create_client_with_app_creds
end

# ensure the user is affiliated with the organization
affiliation = AuthDB::Affiliation.where(organization_id: SCRIPT_OPTIONS[:oid]).find_by!(user_id: SCRIPT_OPTIONS[:user_id])

res = dna_client.update_gate(
  SCRIPT_OPTIONS[:gate],
  SCRIPT_OPTIONS[:enable],
  user_id: SCRIPT_OPTIONS[:user_id],
  oid: SCRIPT_OPTIONS[:oid]
)

print JSON.pretty_generate(res) + "\n"
