#!/usr/bin/env ruby
# frozen_string_literal: true

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option(false)

  opts.on('-n', '--name SETTING_NAME', 'Setting name') do |v|
    opts.options[:name] = v
  end

  opts.require_option(:name)
end

require File.expand_path('../config/environment', __dir__)

payload =
  if SCRIPT_OPTIONS[:oid]
    DNAClient.create_app_client.get_setting_value(SCRIPT_OPTIONS[:oid], SCRIPT_OPTIONS[:name])
  else
    DNAClient.create_app_client.get_bulk_setting_value(SCRIPT_OPTIONS[:name])
  end

print JSON.pretty_generate(payload) + "\n"
