#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
end

require File.expand_path('../config/environment', __dir__)

def is_big_customer(dna_client, oid)
  dna_client.get_setting_value(oid, 'is-big-customer').dig('settings', 'is-big-customer', 'value') == true
end

puts is_big_customer(DNAClient.create_app_client, SCRIPT_OPTIONS[:oid]) ? 'yes' : 'no'
