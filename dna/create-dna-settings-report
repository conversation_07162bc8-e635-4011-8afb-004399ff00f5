#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote

  opts.on('-s', '--setting SETTING_NAME', 'Setting name') do |v|
    opts[:setting_name] = v
  end

  opts.require_option(:setting_name)
end

require File.expand_path('../config/environment', __dir__)

existing_oids = AuthDB::Organization.where(deleted: false).select('id').map(&:id).sort

def dna_client
  @dna_client ||= DNAClient.create_client_with_app_creds
end

class DNASettingCSVRow < Struct.new(:oid,
                                 :setting_name,
                                 :value)
  include CSVUtils::CSVRow

  csv_column :oid
  csv_column :setting_name
  csv_column :value
end

path = TMP_DIR.join('reports', 'dna', "dna-settings-report-#{Time.now.utc.strftime('%Y-%m-%d')}.csv").to_s
FileUtils.mkdir_p(File.dirname(path))

settings = dna_client.get_bulk_setting_value(SCRIPT_OPTIONS[:setting_name])

CSVUtils::CSVReport.new(path, DNASettingCSVRow) do |report|
  existing_oids.each do |oid|
    setting = settings['setting_values'].detect { |payload| payload['oid'] == oid }
    value = setting ? setting['value'] : 'NULL'

    report << DNASettingCSVRow.new(
      oid,
      SCRIPT_OPTIONS[:setting_name],
      value
    )
  end
end

puts path
