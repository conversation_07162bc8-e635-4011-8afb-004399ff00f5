#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('--email EMAIL', 'Evertrue email to grant super powers') do |v|
    opts.options[:email] = v
  end

  opts.require_option(:email)
end

require File.expand_path('../config/environment', __dir__)

raise('not a valid evertrue.com email') unless SCRIPT_OPTIONS[:email] =~ /@evertrue.com$/

auth_client = AuthClient.create_client_with_app_creds
dna_client = DNAClient.create_client_with_app_creds

lookup_response = auth_client.lookup_by_email(SCRIPT_OPTIONS[:email])
raise('invalid email') unless lookup_response.kind_of?(Net::HTTPSuccess)

user_id = JSON.parse(lookup_response.body)['id']

dna_client.update_gate(DNAClient::GATE_SUPER_POWERS, true, user_id: user_id)
dna_client.update_gate(DNAClient::USER_EDIT_POWERS, true, user_id: user_id)
dna_client.update_gate(DNAClient::AFFILIATION_ATTRIBUTE_POWERS, true, user_id: user_id)

puts "Granted super_powers to user #{user_id}"
