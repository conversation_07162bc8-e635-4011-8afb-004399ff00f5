#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
  opts[:setting_names] = []
  opts[:gates] = []

  opts.on('-s', '--settings SETTING_NAMES', 'Setting names') do |v|
    opts[:setting_names] = v.split(',')
  end

  opts.on('-g', '--gates GATES', 'Gate names') do |v|
    opts[:gates] = v.split(',')
  end
end

require File.expand_path('../config/environment', __dir__)

existing_oids = AuthDB::Organization.where(deleted: false).select('id').map(&:id).sort

def dna_client
  @dna_client ||= DNAClient.create_client_with_app_creds
end

class DNASettingCSVRow < Struct.new(
        :oid,
        :slug,
        :name,
        :values,
        :enables
      )
  include CSVUtils::CSVRow

  csv_column :oid
  csv_column :slug
  csv_column :name

  SCRIPT_OPTIONS[:setting_names].each_with_index do |setting_name, idx|
    csv_column(setting_name) { values[idx] }
  end

  SCRIPT_OPTIONS[:gates].each_with_index do |gate, idx|
    csv_column(gate) { enables[idx] ? 'Y' : 'N' }
  end
end

path = TMP_DIR.join('reports', 'dna', "dna-settings-report-#{Time.now.utc.strftime('%Y-%m-%d')}.csv").to_s
FileUtils.mkdir_p(File.dirname(path))

settings = {}
SCRIPT_OPTIONS[:setting_names].each do |setting_name|
  settings[setting_name] = dna_client.get_bulk_setting_value(setting_name)
end

gates = {}
SCRIPT_OPTIONS[:gates].each do |gate|
  gates[gate] = dna_client.get_bulk_gate(gate)
end

orgs = AuthDB::Organization.all.index_by(&:id)

CSVUtils::CSVReport.new(path, DNASettingCSVRow) do |report|
  existing_oids.each do |oid|
    values = SCRIPT_OPTIONS[:setting_names].map do |setting_name|
      setting = settings[setting_name]['setting_values'].detect { |payload| payload['oid'] == oid }
      value = setting ? setting['value'] : 'NULL'
    end

    enables = SCRIPT_OPTIONS[:gates].map do |gate|
      gates[gate]['organizations'][oid.to_s]['enabled']
    end

    org = orgs[oid]
    report << DNASettingCSVRow.new(
      oid,
      org.slug,
      org.name,
      values,
      enables
    )
  end
end

puts path
