#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote

  opts.on('-g', '--gate GATE', 'DNA Gate') do |v|
    opts[:gate] = v
  end

  opts.require_option(:gate)
end

require File.expand_path('../config/environment', __dir__)

class DNAGateCSVRow < Struct.new(
        :oid,
        :slug,
        :name,
        :gate,
        :enabled # at org level
      )

  # Platforms
  attr_accessor :service,
                :web,
                :ios,
                :android

  include CSVUtils::CSVRow

  csv_column :oid
  csv_column :slug
  csv_column :name
  csv_column :gate
  csv_column :enabled, header: :org_enabled
  csv_column :service, header: :platform_service
  csv_column :web, header: :platform_web
  csv_column :ios, header: :platform_ios
  csv_column :android, header: :platform_andriod
end

def dna_client
  @dna_client ||= DNAClient.create_client_with_app_creds
end

active_orgs = AuthDB::Organization.where(deleted: false).to_a

path = TMP_DIR.join('reports', 'dna', "dna-gate-#{SCRIPT_OPTIONS[:gate]}-report-#{Time.now.utc.strftime('%Y-%m-%d')}.csv").to_s
FileUtils.mkdir_p(File.dirname(path))

STATS.total = active_orgs.size
STATS.notification_interval = (active_orgs.size / 10).to_i

CSVUtils::CSVReport.new(path, DNAGateCSVRow) do |report|
  active_orgs.each do |org|
    STATS.inc_and_notify

    result = dna_client.get_gate(org.id, SCRIPT_OPTIONS[:gate])

    row = DNAGateCSVRow.new(
      org.id,
      org.slug,
      org.name,
      SCRIPT_OPTIONS[:gate],
      result['features'][SCRIPT_OPTIONS[:gate]]['enabled']
    )

    DNAClient::PLATFORMS.each do |platform|
      result = dna_client.get_gate_for_platform(org.id, SCRIPT_OPTIONS[:gate], platform)

      row.public_send("#{platform}=", result['features'][SCRIPT_OPTIONS[:gate]]['enabled'])
    end

    report << row
  end
end

RepairshopFileUploader.upload_file_and_notify(path)

STATS.notify(true)
