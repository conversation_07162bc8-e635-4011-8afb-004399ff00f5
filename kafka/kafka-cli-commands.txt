
First off, brew install kafka

Configure your environment

export ZK_PROD=prod-zookeeper-1b,prod-zookeeper-1c,prod-zookeeper-1d

# Display all the consumer names
/usr/local/Cellar/kafka/2.8.0/bin/kafka-consumer-groups --list --bootstrap-server prod-kafka-1b:9092

/usr/local/Cellar/kafka/2.8.0/bin/kafka-consumer-groups --describe --group prod-KafkaConsumer-note-elasticsearch-consumer --members --bootstrap-server prod-kafka-1b:9092


grep -v '#01' elasticsearch.log.1 | grep -v 'Caused by' | grep -v Percolate | less


$ ./kafka-consumer-groups.sh --group prod-MesosKafkaConsumer-suggestions-es-consumer --zookeeper $ZK_PROD/kafka --describe

GROUP                          TOPIC                          PARTITION  CURRENT-OFFSET  LOG-END-OFFSET  LAG             OWNER
prod-MesosKafkaConsumer-suggestions-es-consumer suggestions_wal                0          9322            9322            0               prod-MesosKafkaConsumer-suggestions-es-consumer_bb5a46e22772-1628304650126-839b2a82-0
prod-MesosKafkaConsumer-suggestions-es-consumer suggestions_wal                1          9569            9569            0               prod-MesosKafkaConsumer-suggestions-es-consumer_bb5a46e22772-1628304650126-839b2a82-1
prod-MesosKafkaConsumer-suggestions-es-consumer suggestions_wal                2          9609            9609            0               prod-MesosKafkaConsumer-suggestions-es-consumer_bb5a46e22772-1628304650126-839b2a82-2



17:50:56 (0) root@prod-kafka-1c: /usr/local/kafka/kafka_2.11-********/bin
$ ./kafka-run-class.sh kafka.tools.GetOffsetShell --topic contacts_write_ahead_log --broker-list localhost:9092
contacts_write_ahead_log:2:1015034172
contacts_write_ahead_log:1:1021752007
contacts_write_ahead_log:0:1014776685

20:23:28 (1) root@prod-kafka-1b: /usr/local/kafka/kafka_2.11-********
$ export ZK_PROD=prod-zookeeper-1b,prod-zookeeper-1c,prod-zookeeper-1d

20:23:38 (0) root@prod-kafka-1b: /usr/local/kafka/kafka_2.11-********
$ ./bin/kafka-topics.sh  --zookeeper $ZK_PROD/kafka --list
__consumer_offsets
analytics_reporting_wal

20:55:00 (1) root@prod-kafka-1b: /usr/local/kafka/kafka_2.11-********
$ ./bin/kafka-topics.sh  --zookeeper $ZK_PROD/kafka --topic journey_timeout --delete
Topic journey_timeout is marked for deletion.
Note: This will have no impact if delete.topic.enable is not set to true.




15:55:52 (0) root@stage-kafka-3zq: /usr/local/kafka/kafka_2.11-********
$ ./bin/kafka-topics.sh --zookeeper stage-zookeeper-1b,stage-zookeeper-1c,stage-zookeeper-1d/kafka --list
__consumer_offsets
assignments-elasticsearch-sync

$ ./bin/kafka-topics.sh --zookeeper stage-zookeeper-1b,stage-zookeeper-1c,stage-zookeeper-1d/kafka --describe --topic interaction_import
Topic:interaction_import	PartitionCount:10	ReplicationFactor:1	Configs:
	Topic: interaction_import	Partition: 0	Leader: 3	Replicas: 3	Isr: 3
	Topic: interaction_import	Partition: 1	Leader: 4	Replicas: 4	Isr: 4


