#!/bin/bash

# Host/Port will change if the burrow process is restarted in singularity.
# to update these commands goto https://singularity.evertrue.com/
# click on Tasks > Filter tasks: burrow
# click on the running task use the Hostname and Ports values at the bottom of the page for the curl commands below.

# list consumers

curl 'https://burrow.evertrue.com/v2/kafka/prod/consumer' | jsonpp

# to delete consumer monitoring

# curl -XDELETE 'https://burrow.evertrue.com/v2/kafka/prod/consumer/prod-MesosKafkaConsumer-remote-user-id-mapper'
# successful response: {"error":false,"message":"consumer group removed","result":{}}
