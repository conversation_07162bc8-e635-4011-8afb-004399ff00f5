#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-c', '--consumer CONSUMER', 'Consumer name') do |v|
    opts[:consumer] = v
  end

  opts.require_option(:consumer)
end

require File.expand_path('../config/environment', __dir__)

def cluster_name
  case ENV['RAILS_ENV']
  when 'production'
    'prod'
  when 'staging'
    'stage'
  else
    'local'
  end
end

def get_topic_name(consumer)
  res = Net::HTTP.get_response(URI(SINGULARITY_URL + "/v2/kafka/#{cluster_name}/consumer/#{consumer}/topic"))
  JSON.parse(res.body)['topics'].first
end

topic_name = get_topic_name(SCRIPT_OPTIONS[:consumer])

res = Net::HTTP.get_response(URI(SINGULARITY_URL + "/v2/kafka/#{cluster_name}/consumer/#{SCRIPT_OPTIONS[:consumer]}/lag"))
consumer_status = JSON.parse(res.body)

res = Net::HTTP.get_response(URI(SINGULARITY_URL + "/v2/kafka/#{cluster_name}/topic/#{topic_name}"))
topic_status = JSON.parse(res.body)

partitions = consumer_status['status']['partitions'].map do |partition|
  {
    partition: partition['partition'],
    status: partition['status'],
    position: partition['start']['offset'],
    remaining: partition['end']['offset'] - partition['start']['offset']
  }
end

headings = {
  'Partition' => :partition,
  'Status' => :status,
  'Current Offset' => :position,
  'Remaining Messages' => :remaining
}

table = TableView.new(headings)
table.render("Consumer #{SCRIPT_OPTIONS[:consumer]}" => partitions)
