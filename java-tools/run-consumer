#!/usr/bin/env ruby

consumer_args = case ARGV[0]
                when 'contacts-remote-user-id-mapper'
                  'contacts-remote-user-id-mapper auth_user_change_write_ahead_log com.et.kafka_consumer.consumer.ContactsRemoteUserIdMapper myPortfolio'
                else
                  raise("invalid or no consumer specified")
                end

java_environment = ARGV[1] || 'stage'

unless File.exist?('pom.xml')
  $stderr.puts 'no pom.xml file found, be sure to be in the consumer folder'
  exit 1
end

cmd = "mvn exec:java -Dexec.mainClass=\"#{main_class}\" -Dexec.args=\"server #{Dir.pwd}/#{server_yaml}\"  -Dcom.et.log.type=console -Dcom.et.env=#{java_environment}  -Dexec.workingdir=#{Dir.pwd}"

puts cmd
exec(cmd)
