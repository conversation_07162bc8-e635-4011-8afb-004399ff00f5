#!/usr/bin/env ruby

java_environment = ARGV[0] || 'stage'

unless File.exist?('pom.xml')
  $stderr.puts 'no pom.xml file found, be sure to be in the base or api folder'
  exit 1
end

main_classess =  Dir.glob('**/**.java').select do |file|
  File.read(file).include?('static void main')
end

unless main_classess.size > 0
  $stderr.puts 'no classes in this project have a main'
  exit 1
end

# if there is only 1 class use it
api_class = main_classess.first if main_classess.size == 1

# find the first class with a main that has api in the name or path
api_class ||= main_classess.detect do |file|
  file.to_s.downcase.include?('api')
end

# find a class with dw (dropwizard) in the name
api_class ||= main_classess.detect do |file|
  file.to_s.downcase.include?('dw')
end

unless api_class
  $stderr.puts 'no class in this project as the name api'
  exit 1
end

dirs = api_class.split('/')

unless dirs.first == 'src'
  Dir.chdir dirs.shift
end

main_class = dirs.join('.')
main_class.sub!(/\.java$/, '')
main_class.sub!(/^.*com/, 'com')

server_yaml = Dir.glob('*.yml').detect do |yaml_file|
  File.read(yaml_file).include?('server:')
end

unless server_yaml
  $stderr.puts 'no server yaml found'
  exit 1
end

cmd = "mvn exec:java -Dexec.mainClass=\"#{main_class}\" -Dexec.args=\"server #{Dir.pwd}/#{server_yaml}\"  -Dcom.et.log.type=console -Dcom.et.env=#{java_environment}  -Dexec.workingdir=#{Dir.pwd}"

puts cmd
exec(cmd)
