#!/usr/bin/env ruby

require 'optparse'

ENV['RAILS_ENV'] = 'staging'

OptionParser.new do |opts|
  opts.banner = 'Usage: ' + File.basename(__FILE__) + ' <job-id>'

  opts.on('-h', '--help', 'Prints this help') do
    puts opts
    exit
  end

  opts.on('-e', '--environment ENVIRONMENT', 'production or staging') do |v|
    ENV['RAILS_ENV'] = v
  end
end.parse!

job_id = ARGV.shift
bypass_cache = '1'

importer_dir = ENV['IMPORTER_DIR'] || "#{ENV['HOME']}/dev/importer"
importer_worker_dir = importer_dir + '/ImporterWorker'
unless File.exist?(importer_worker_dir)
  $stderr.puts "ImporterWorker folder not found at #{importer_worker_dir}, you can set IMPORTER_DIR environment variable to fix non standard setup"
  exit 1
end

require File.expand_path('../config/environment', __dir__)

importer_client = MySQLHelpers.create_client(:importer)

job = importer_client.query("SELECT * FROM job WHERE id = #{job_id}").first
unless job
  $stderr.puts "no job found with id #{job_id}"
  exit 1
end
oid = job['oid']

job_statuses = importer_client.query("SELECT * FROM job_status WHERE job_id = #{job_id} ORDER BY id DESC").to_a

preprocess_complete_status = job_statuses.detect { |job_status| job_status['status'] == 'FILECHECK_COMPLETE' || job_status['status'] == 'FILECHECK_SUCCESS' }

unless preprocess_complete_status
  $stderr.puts "job FILECHECK_COMPLETE status not found in #{job_statuses.map { |js| js['status'] }.join(' ,')}"
  exit 1
end

BEFORE_PROCESSING_STATUSES = ['FILECHECK_QUEUED', 'FILECHECK_FORKED', 'FILECHECK_RUNNING', 'FILECHECK_COMPLETE', 'NEW', 'FILECHECK_SUCCESS']
job_statuses_to_delete = job_statuses.reject { |job_status| BEFORE_PROCESSING_STATUSES.include?(job_status['status']) }

if job_statuses_to_delete.size > 0
  importer_client.query("DELETE FROM job_status WHERE id IN(#{job_statuses_to_delete.map { |js| js['id'] }.join(',')})")
end

timestamp = (Time.now.to_f * 1000).to_i
importer_client.query("UPDATE job_status SET ended_at = #{timestamp} WHERE id = #{preprocess_complete_status['id']}")
importer_client.query("INSERT into job_status (job_id, status, started_at, ended_at) values (#{job_id}, 'PREPROCESS_QUEUED', #{timestamp}, #{timestamp}), (#{job_id}, 'PREPROCESS_FORKED', #{timestamp}, NULL)")

java_environment =
  case ENV['RAILS_ENV']
  when 'production'
    'prod'
  when 'staging'
    'stage'
  else
    'local'
  end

main_class = 'com.et.importer.worker.WorkerDispatcher'

Dir.chdir(importer_worker_dir)
cmd = "mvn exec:java -Dexec.mainClass=\"#{main_class}\" -Dexec.args=\"#{oid} #{job_id} #{bypass_cache}\"  -Dcom.et.log.type=console -Dcom.et.env=#{java_environment}  -Dexec.workingdir=#{Dir.pwd}"

puts cmd
exec(cmd)
