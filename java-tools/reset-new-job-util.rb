BEFORE_PROCESSING_STATUSES = ['F<PERSON><PERSON><PERSON><PERSON><PERSON>_QUEUED', 'FILECHECK_FORKED', 'FILECHECK_RUNNING', 'FILECHECK_SUCCESS', 'NEW', 'PREPROCESS_QUEUED', 'PREPROCESS_FORKED', 'PREPROCESS_RUNNING', 'READY', 'PREPROCESS_SUCCESS', 'PREPROCESS_WARNING']

def reset_job(job_id)
    require File.expand_path('../config/environment', __dir__)

    importer_client = MySQLHelpers.create_client(:importer)
    
    job = importer_client.query("SELECT * FROM job WHERE id = #{job_id}").first
    unless job
      $stderr.puts "no job found with id #{job_id}"
      exit 1
    end
    oid = job['oid']
    
    job_statuses = importer_client.query("SELECT * FROM job_status WHERE job_id = #{job_id} ORDER BY id DESC").to_a
    
    preprocess_success_status = job_statuses.detect { |job_status| job_status['status'] == 'PREPROCESS_SUCCESS' || job_status['status'] == 'PREPROCESS_WARNING' }
    
    unless preprocess_success_status
      $stderr.puts "job PREPROCESS_SUCCESS status not found in #{job_statuses.map { |js| js['status'] }.join(' ,')}"
      exit 1
    end
    
    job_statuses_to_delete = job_statuses.reject { |job_status| BEFORE_PROCESSING_STATUSES.include?(job_status['status']) }
    
    if job_statuses_to_delete.size > 0
      importer_client.query("DELETE FROM job_status WHERE id IN(#{job_statuses_to_delete.map { |js| js['id'] }.join(',')})")
    end
    
    timestamp = (Time.now.to_f * 1000).to_i
    importer_client.query("UPDATE job_status SET ended_at = #{timestamp} WHERE id = #{preprocess_success_status['id']}")
    importer_client.query("INSERT into job_status (job_id, status, started_at, ended_at) values (#{job_id}, 'IMPORT_QUEUED', #{timestamp}, #{timestamp}), (#{job_id}, 'IMPORT_FORKED', #{timestamp}, NULL)")    
    
    return oid
end
