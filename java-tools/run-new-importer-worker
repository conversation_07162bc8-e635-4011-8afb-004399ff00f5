#!/usr/bin/env ruby

require 'optparse'
require_relative 'reset-new-job-util'

ENV['RAILS_ENV'] = 'staging'

OptionParser.new do |opts|
  opts.banner = 'Usage: ' + File.basename(__FILE__) + ' <job-id>'

  opts.on('-h', '--help', 'Prints this help') do
    puts opts
    exit
  end

  opts.on('-e', '--environment ENVIRONMENT', 'production or staging') do |v|
    ENV['RAILS_ENV'] = v
  end
end.parse!

job_id = ARGV.shift
bypass_cache = '1'

importer_dir = ENV['IMPORTER_DIR'] || "#{ENV['HOME']}/dev/importer"
importer_worker_dir = importer_dir + '/ImporterWorker'
unless File.exist?(importer_worker_dir)
  $stderr.puts "ImporterWorker folder not found at #{importer_worker_dir}, you can set IMPORTER_DIR environment variable to fix non standard setup"
  exit 1
end

oid = reset_job(job_id)

java_environment =
  case ENV['RAILS_ENV']
  when 'production'
    'prod'
  when 'staging'
    'stage'
  else
    'local'
  end

main_class = 'com.et.importer.worker.WorkerDispatcher'

Dir.chdir(importer_worker_dir)
cmd = "mvn exec:java -Dexec.mainClass=\"#{main_class}\" -Dexec.args=\"#{oid} #{job_id} #{bypass_cache}\"  -Dcom.et.log.type=console -Dcom.et.env=#{java_environment}  -Dexec.workingdir=#{Dir.pwd}"

puts cmd
exec(cmd)
