#!/usr/bin/env ruby

require 'optparse'

ENV['RAILS_ENV'] = 'staging'

OptionParser.new do |opts|
  opts.banner = 'Usage: ' + File.basename(__FILE__) + ' <job-id>'

  opts.on('-h', '--help', 'Prints this help') do
    puts opts
    exit
  end

  opts.on('-e', '--environment ENVIRONMENT', 'production or staging') do |v|
    ENV['RAILS_ENV'] = v
  end
end.parse!

job_id = ARGV.shift
bypass_cache = '1'

importer_dir = ENV['IMPORTER_DIR'] || "#{ENV['HOME']}/dev/importer"
importer_worker_dir = importer_dir + '/ImporterWorker'
unless File.exist?(importer_worker_dir)
  $stderr.puts "ImporterWorker folder not found at #{importer_worker_dir}, you can set IMPORTER_DIR environment variable to fix non standard setup"
  exit 1
end

require File.expand_path('../config/environment', __dir__)

importer_client = MySQLHelpers.create_client(:importer)

job = importer_client.query("SELECT * FROM job WHERE id = #{job_id}").first
unless job
  $stderr.puts "no job found with id #{job_id}"
  exit 1
end
oid = job['oid']

job_statuses = importer_client.query("SELECT * FROM job_status WHERE job_id = #{job_id}").to_a

forked_status = job_statuses.detect { |job_status| job_status['status'] == 'FORKED' }

unless forked_status
  $stderr.puts "job FORKED status not found in #{job_statuses.map { |js| js['status'] }.join(' ,')}"
  exit 1
end

BEFORE_PROCESSING_STATUSES = ['NEW', 'QUEUED', 'FORKED']
job_statuses_to_delete = job_statuses.reject { |job_status| BEFORE_PROCESSING_STATUSES.include?(job_status['status']) }

if job_statuses_to_delete.size > 0
  importer_client.query("DELETE FROM job_status WHERE id IN(#{job_statuses_to_delete.map { |js| js['id'] }.join(',')})")
end

importer_client.query("UPDATE job_status SET ended_at = NULL WHERE id = #{forked_status['id']}")

java_environment =
  case ENV['RAILS_ENV']
  when 'production'
    'prod'
  when 'staging'
    'stage'
  else
    'local'
  end

main_class = 'com.et.importer.worker.WorkerDispatcher'

Dir.chdir(importer_worker_dir)
cmd = "mvn exec:java -Dexec.mainClass=\"#{main_class}\" -Dexec.args=\"#{oid} #{job_id} #{bypass_cache}\"  -Dcom.et.log.type=console -Dcom.et.env=#{java_environment}  -Dexec.workingdir=#{Dir.pwd}"

puts cmd
exec(cmd)
