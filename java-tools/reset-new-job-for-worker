#!/usr/bin/env ruby

require 'optparse'
require_relative 'reset-new-job-util'

ENV['RAILS_ENV'] = 'staging'

OptionParser.new do |opts|
  opts.banner = 'Usage: ' + File.basename(__FILE__) + ' <job-id>'

  opts.on('-h', '--help', 'Prints this help') do
    puts opts
    exit
  end

  opts.on('-e', '--environment ENVIRONMENT', 'production or staging') do |v|
    ENV['RAILS_ENV'] = v
  end
end.parse!

job_id = ARGV.shift

reset_job(job_id)

