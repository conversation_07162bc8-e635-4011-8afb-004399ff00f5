#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
end

require File.expand_path('../config/environment', __dir__)

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

class GiveDesignationCSVRow < GiveDB::Designation
  include CSVUtils::CSVRow

  csv_column('CampaignID') { campaign.org_campaign_id }
  csv_column('CampaignName') { campaign.name }
  csv_column('CampaignSlug') { campaign.slug }
  csv_column('CampaignShortName') { campaign.short_name }
  csv_column :guid, header: 'DesignationGUID'
  csv_column :parent_guid, header: 'DesignationParentGUID'
  csv_column :label, header: 'DesignationLabel'
  csv_column :value, header: 'DesignationValue'
end

CSVUtils::CSVReport.new("#{org.slug}-give-designations-#{Time.now.strftime('%Y-%m-%d')}.csv", GiveDesignationCSVRow) do |report|
  GiveDesignationCSVRow.where(oid: SCRIPT_OPTIONS[:oid]).includes(:campaign).order(:campaign_id, :sequence).find_each do |designation|
    report << designation
  end
end
