# rcov generated
coverage
coverage.data

# rdoc generated
rdoc

# yard generated
doc
.yardoc

# bundler
.bundle

# jeweler generated
pkg

# Have editor/IDE/OS specific files you need to ignore? Consider using a global gitignore: 
#
# * Create a file at ~/.gitignore
# * Include files you want ignored
# * Run: git config --global core.excludesfile ~/.gitignore
#
# After doing this, these files will be ignored in all your git projects,
# saving you from having to 'pollute' every project you touch with them
#
# Not sure what to needs to be ignored for particular editors/OSes? Here's some ideas to get you started. (Remember, remove the leading # of the line)
#
# For MacOS:
#
.DS_Store

# For TextMate
#*.tmproj
#tmtags

# For emacs:
*~
\#*
.\#*

# For vim:
*.swp

# For redcar:
#.redcar

# For rubinius:
#*.rbc
*.gem
*.db
*.csv
*.log

tmp
.env
.idea

Brewfile.lock.json
