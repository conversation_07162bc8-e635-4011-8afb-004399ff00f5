version: 0.2

env:
  variables:
    registry_id: "478370645242"
    project_name: et-repairshop
    image_namespace: evertrue
    region: us-east-1
  parameter-store:
    FURY_AUTH: "/Tools/CodeBuild/gemfury-key"

phases:
  install:
    runtime-versions:
      ruby: 2.6
    commands:
      - echo Installing codebuild-extras...
      - curl -fsSL https://raw.githubusercontent.com/evertrue/aws-codebuild-extras/master/install > extras.sh
      - . ./extras.sh

  pre_build:
    commands:
      - aws ecr get-login-password --region $region | docker login --username AWS --password-stdin $registry_id.dkr.ecr.$region.amazonaws.com

  build:
    commands:
      - ./docker-build-ruby $image_namespace $project_name task

  post_build:
    commands:
      - ./docker-push $registry_id $git_commit $image_namespace/${project_name}-task
