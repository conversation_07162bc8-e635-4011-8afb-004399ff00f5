#!/usr/bin/env ruby

require 'json'
require 'net/http'
require 'logger'
require 'mysql2'
require 'yaml'
require 'thread'

class ContactClient
  DEFAULT_BASE_URL = 'https://api.evertrue.com'

  def initialize(app_key, auth_token, base_url=DEFAULT_BASE_URL, provider='EvertrueAppToken')
    @app_key = app_key
    @auth_token = auth_token
    @base_url = base_url
    @provider = provider
  end

  def get_contact(oid, contact_id)
    path = "/contacts/v1/contacts/#{contact_id}?oid=#{oid}&script=#{__FILE__}"
    url = @base_url + path

    uri = URI(url)
    req = Net::HTTP::Get.new(uri.request_uri, req_headers)

    Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == 'https') do |http|
      http.request(req)
    end
  end

  def update_contact(oid, contact_id, data)
    path = "/contacts/v1/contacts/#{contact_id}?oid=#{oid}&script=#{__FILE__}"
    url = @base_url + path

    uri = URI(url)
    req = Net::HTTP::Put.new(uri.request_uri, req_headers)
    req.body = JSON.dump(data)

    Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == 'https') do |http|
      http.request(req)
    end
  end

  private

  def req_headers
    {
      'Content-Type' => 'application/json',
      'Accept' => 'application/json',
      'Application-Key' => @app_key,
      'Authorization-Provider' => @provider,
      'Authorization' => @auth_token,
      'ET-Update-Source' => 'hadoop'
    }
  end
end

def mark_hadoop_giving_annual_donation_as_deleted(giving_annual_donation)
  giving_annual_donation['properties'].each do |prop|
    prop['deleted'] = true
  end
end

def mark_haddop_giving_annual_donations_deleted(data)
  data['giving_annual_donations'].each do |giving_annual_donation|
    next unless giving_annual_donation['properties'].first['update_source'] == 'hadoop'
    mark_hadoop_giving_annual_donation_as_deleted(giving_annual_donation)
  end
end

def mysql_client_prod
  @mysql_client ||=
    begin
      config = YAML.load_file("#{ENV['HOME']}/.evertrue/prod-dbs.yml")['contacts']
      Mysql2::Client.new(
        host: config['host'],
        username: config['username'],
        password: config['password'],
        database: config['database']
      )
    end
end

alias mysql_client mysql_client_prod

LOG = Logger.new(__FILE__ + '.log')
LOG.formatter = Logger::Formatter.new

contact_client = ContactClient.new('5087f1d0493aa43c0bbcadd674400b5381f54a5aa852176e5993e1c14622aabc', 'MTY6MXhKcTlrU0J2S1pqaFhDRFBzeW4=')

oid = 603

def time_elapsed(stats)
  Time.now - stats[:started_at]
end

stats = Hash.new(0)
stats[:started_at] = Time.now

contacts = mysql_client.query("SELECT DISTINCT contact_id FROM giving_annual_donation INNER JOIN contact ON contact.id = contact_id WHERE oid = #{oid} AND update_source = 11").to_a
stats[:elapsed_time] = time_elapsed(stats)
stats[:total] = contacts.size

semaphore = Mutex.new

def inc_stat(semaphore, stats, field)
  semaphore.synchronize {
    stats[field] += 1
  }
end

queue = contacts.inject(Queue.new, :push)

NUM_THREADS = 20
Thread.abort_on_exception = true

stats[:cnt] = 0
stats[:starting_removing_at] = Time.now

# Delete old identity value
LOG.info("removing hadoop giving_annual_donation")
threads = Array.new(NUM_THREADS) do
  Thread.new do
    until queue.empty?
      # This will remove the first object from @queue
      contact_id = queue.shift['contact_id']
      data = JSON.parse(contact_client.get_contact(oid, contact_id).body)
      mark_haddop_giving_annual_donations_deleted(data)
      res = contact_client.update_contact(oid, contact_id, data)

      if (inc_stat(semaphore, stats, :cnt)%1_000) == 0
        stats[:elapsed_time] = time_elapsed(stats)
        LOG.info(stats.inspect)
      end

      if res.kind_of?(Net::HTTPOK)
        inc_stat(semaphore, stats, :deleted_giving_annual_donations)
        LOG.info("removed giving_annual_donation for contact #{contact_id}")
      else
        inc_stat(semaphore, stats, :unhandled_delete_response)
        LOG.error("unhandled response #{res} for contact #{contact_id}")
      end
    end
  end
end

threads.each(&:join)

stats[:elapsed_time] = time_elapsed(stats)
stats[:finished_at] = Time.now
LOG.info(stats.inspect)
