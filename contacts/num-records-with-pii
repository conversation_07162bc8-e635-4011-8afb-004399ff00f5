#!/usr/bin/env ruby

# Purpose: to calculate the number of contact records that contain PII
# PII is being defined in this script as a first and last name.  Along with an address or phone or email record

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
end

require File.expand_path('../config/environment', __dir__)

active_org_ids = AuthDB::Organization.where(test_org: false, deleted: false).to_a.map(&:id).to_set

class ContactPII < ContactDB::Contact
  has_one :contact_attribute, class_name: 'ContactDB::ContactAttribute', foreign_key: :contact_id

  def has_pii?
    has_name? &&
      (
        has_address? ||
        has_email? ||
        has_phone?
      )
  end

  def has_name?
    contact_attribute&.name_first && contact_attribute&.name_last
  end

  def has_address?
    addresses.size > 0
  end

  def has_email?
    emails.size > 0
  end

  def has_phone?
    phones.size > 0
  end
end

ContactPII.includes(:contact_attribute, :addresses, :emails, :phones).find_each(batch_size: 10_000) do |contact|
  STATS.inc_and_notify

  next unless active_org_ids.include?(contact.oid)
  next unless contact.has_pii?

  STATS.update(:contacts_with_pii)
end

STATS.notify(true)
