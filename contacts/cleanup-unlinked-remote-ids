#!/usr/bin/env ruby

# Purpose is to delete identity records that do not have a corresponding contact record

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_run_remote
end

require File.expand_path('../config/environment', __dir__)
require 'active_support/core_ext/array'
require 'active_record'

ActiveRecord::Base.logger = LOG

identities_to_delete = []
ContactDB::Identity.includes(:contact).where(oid: SCRIPT_OPTIONS[:oid], type: 1).find_each do |identity|
  next if identity.contact

  LOG.info("deleting identity #{identity.id} from org #{identity.oid} linked to conatct #{identity.contact_id} with value #{identity.value}")
  identities_to_delete << identity
end

identities_to_delete.in_groups_of(10_000, false).each do |batch|
  ContactDB::Identity.where(id: batch.map(&:id)).delete_all
end
