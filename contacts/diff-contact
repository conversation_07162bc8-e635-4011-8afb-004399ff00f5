#!/usr/bin/env ruby

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-c', '--contact CONTACT_ID', 'Contact ID') do |v|
    opts.options[:contact_id] = v
  end

  opts.require_option(:contact_id)
end

require_relative '../config/environment'

# Verifies that contacts in MySQL and in Cassandra contacts_index.oid_index are the same.
# Removes records from the oid_index that are not in MySQL
# Refreshes contacts where the updated_at times do not match or if the id is in MySQL but not in the oid_index
# Starts by dumping all contacts from MySQL to a file of (contact_id,updated_at)
# dumps all oid_index contact records to a file of, (contact_id,updated_at,shard_id)

mysql_client_contacts = MySQLHelpers.create_client(:contacts)

def get_cassandra_contact_record(session, oid, contact_id)
  shards = 20.times.map { |i| Cassandra::Util.encode_string("#{oid}_shard_#{i}") }
  cql = "SELECT * FROM oid_index WHERE id IN(#{shards.join(',')}) AND oid = #{oid} AND contact_id = #{contact_id}"
  LOG.info(cql)
  result = session.execute(cql)
  Contacts::Contact.decode(result.first['contact'])
end

oid = SCRIPT_OPTIONS[:oid]
contact_id = SCRIPT_OPTIONS[:contact_id]

contact_client = ContactClient.create_app_client

class Property < Struct.new(:id,
                            :oid,
                            :name,
                            :data_type,
                            :parent_id,
                            :default_prop
                           )
  attr_accessor :parent

  def children
    @children ||= {}
  end

  def es_name
    if default_prop == 1
      name
    else
      "#{oid}_#{id}_#{data_type.upcase}_#{name}"
    end
  end

  def nested_name
    if parent
      parent.nested_name + '.' + name
    else
      name
    end
  end
end

properties_by_id = mysql_client_contacts.query("select id, oid, name, data_type, parent_id, default_prop from property where oid = #{oid}").map { |result| Property.new(*result.values) }.inject({}) { |hsh, property| hsh[property.id] = property; hsh }
properties_by_parent = properties_by_id.values.group_by(&:parent_id)

property_map = properties_by_parent[nil].inject({}) { |hsh, property| hsh[property.id] = property; hsh }
parents = property_map.values
while parent = parents.shift
  next unless properties_by_parent[parent.id]

  properties_by_parent[parent.id].each do |property|
    property.parent = parent
    parent.children[property.id] = property
    parents << property
  end
end

def sort_property_values(property_values)
  property_values.sort
end

class PropertyValue < Struct.new(:property,
                                 :value
                                )
  include Comparable

  def self.new_with_type_conversion(property, value)
    if property.data_type == 'boolean'
      value = value.is_a?(String) ? value == 'true' : value
    elsif property.data_type == 'number'
      value = value.to_i
    elsif property.data_type == 'currency'
      value = value.to_f
    elsif property.data_type == 'date_string'
      if value.include?('/')
        value = value.sub(/(\d+)\/(\d+)\/(\d+)/) do |_|
          sprintf('%d-%02d-%02d', $3.to_i, $1.to_i, $2.to_i)
        end
      end
    end
    new(property, value)
  end

  def as_json(opts = {})
    {
      id:  property.id,
      name: property.nested_name,
      value: value
    }
  end

  def <=>(other)
    if property.nested_name != other.property.nested_name
      property.nested_name <=> other.property.nested_name
    elsif property.data_type == 'boolean'
      if value == other.value
        0
      elsif value
        1
      else
        -1
      end
    else
      value <=> other.value
    end
  end
end

def display_differences(source_values, destination_values)
  diff = destination_values - source_values
  return if diff.empty?

  puts JSON.pretty_generate(diff.map(&:as_json))
end

def updated_at_difference(source_data, destination_data)
  source_data['updated_at'] - destination_data['updated_at']
end

def convert_es_contact_to_property_values(property_map, es_contact)
  property_values = []
  property_map.each do |parent_id, property|
    next unless es_contact.has_key?(property.es_name)

    value = es_contact[property.es_name]

    if property.data_type == 'list'
      unless property.children.first
        next
      end
      property.children.values.first.children.each do |child_id, nested_property|
        value.each do |nested_object|

          if nested_property.name == 'lat' || nested_property.name == 'lng'
            nested_value =
              if (location = nested_object['location'])
                location.split(',', 2)[nested_property.name == 'lat' ? 0 : 1]
              end

            property_values << PropertyValue.new_with_type_conversion(nested_property, nested_value) if nested_value
            next
          end

          next unless nested_object.has_key?(nested_property.es_name)

          nested_value = nested_object[nested_property.es_name]
          property_values << PropertyValue.new_with_type_conversion(nested_property, nested_value)
        end
      end
    elsif property.data_type == 'object'
      property.children.each do |child_id, nested_property|
        next unless value.has_key?(nested_property.es_name)

        nested_value = value[nested_property.es_name]
        property_values << PropertyValue.new_with_type_conversion(nested_property, nested_value)
      end
    else
      property_values << PropertyValue.new_with_type_conversion(property, value)
    end
  end

  sort_property_values(property_values)
end

def convert_ks_contact_to_property_values(properties_by_id, ks_contact)
  property_values = []

  add_property_value_proc = Proc.new do |nvp|
    property_values << PropertyValue.new_with_type_conversion(properties_by_id[nvp['property']['id']], nvp['value'])
  end

  add_nvo_proc = Proc.new do |nvo|
    nvo['name_value_pair'].each { |nvp| add_property_value_proc.call(nvp) }
  end

  ks_contact['name_value_pair'].each do |nvp|
    add_property_value_proc.call nvp
  end

  if ks_contact['name_value_object']
    ks_contact['name_value_object'].each do |nvo|
      add_nvo_proc.call(nvo)
    end
  end

  ks_contact['name_value_object_list'].each do |nvol|
    next unless nvol['name_value_object']

    nvol['name_value_object'].each do |nvo|
      add_nvo_proc.call(nvo)
    end
  end

  sort_property_values(property_values)
end

def convert_api_contact_to_property_values(property_map, api_contact)
  property_values = []

  find_property_proc = Proc.new do |properties, nvp|
    properties.detect { |property| property.name == nvp['name'] } || raise("no property found for #{nvp['name']}, in #{properties.map(&:name).join(', ')}")
  end

  api_contact.each do |section_name, section_data|
    case section_data
    when Hash
      properties = property_map.values.detect { |property| property.name == section_name }.children.values
      section_data['properties'].each do |nvp|
        property = find_property_proc.call properties, nvp
        property_values << PropertyValue.new_with_type_conversion(property, nvp['value'])
      end
    when Array
      case section_name
      when 'properties'
        section_data.each do |nvp|
          property = find_property_proc.call property_map.values, nvp
          property_values << PropertyValue.new_with_type_conversion(property, nvp['value'])
        end
        next
      when 'roles',
           'identities',
           'roles_override'
        next
      end

      properties = property_map.values.detect { |property| property.name == section_name }.children.values.first.children.values

      section_data.each do |nvo|
        nvo['properties'].each do |nvp|
          property = find_property_proc.call properties, nvp
          property_values << PropertyValue.new_with_type_conversion(property, nvp['value'])
        end
      end
    end
  end

  sort_property_values(property_values)
end

url = ES_URL + "/prod-contacts/contact/#{contact_id}?routing=#{oid}"
es_res = Net::HTTP.get_response(URI(url))

es_contact = JSON.parse(es_res.body)['_source']

ks_contact = get_cassandra_contact_record(CassandraHelpers.create_session(CassandraHelpers.create_cluster(KS_CONFIG['hosts']), 'contacts_index'), oid, contact_id).as_json

api_contact = contact_client.get_contact(oid, contact_id)

File.open(TMP_DIR.join("contact.#{contact_id}.api.json"), 'wb') do |f|
  f.puts JSON.pretty_generate(api_contact)
end

File.open(TMP_DIR.join("contact.#{contact_id}.ks.json"), 'wb') do |f|
  f.puts JSON.pretty_generate(ks_contact)
end

File.open(TMP_DIR.join("contact.#{contact_id}.es.json"), 'wb') do |f|
  f.puts JSON.pretty_generate(es_contact)
end

es_property_values = convert_es_contact_to_property_values(property_map, es_contact)
ks_property_values = convert_ks_contact_to_property_values(properties_by_id, ks_contact)
api_property_values = convert_api_contact_to_property_values(property_map, api_contact)

File.open(TMP_DIR.join("contact.#{contact_id}.api.property_values.json"), 'wb') do |f|
  f.puts JSON.pretty_generate(api_property_values.map(&:as_json))
end

File.open(TMP_DIR.join("contact.#{contact_id}.es.property_values.json"), 'wb') do |f|
  f.puts JSON.pretty_generate(es_property_values.map(&:as_json))
end

File.open(TMP_DIR.join("contact.#{contact_id}.ks.property_values.json"), 'wb') do |f|
  f.puts JSON.pretty_generate(ks_property_values.map(&:as_json))
end

puts <<STR
Differences in updated_at
API #{api_contact['updated_at']}
ES  #{es_contact['updated_at']} (#{api_contact['updated_at'] - es_contact['updated_at']})
KS  #{ks_contact['updated_at']} (#{api_contact['updated_at'] - ks_contact['updated_at']})
STR

puts "Contact data in ES and not in API"
display_differences(api_property_values, es_property_values)

puts "Contact data in API and not in ES"
display_differences(es_property_values, api_property_values)

puts "Contact data in KS and not in API"
display_differences(api_property_values, ks_property_values)

puts "Contact data in API and not in KS"
display_differences(ks_property_values, api_property_values)

puts "Contact data in ES and not in KS"
display_differences(es_property_values, ks_property_values)

puts "Contact data in KS and not in ES"
display_differences(ks_property_values, es_property_values)

