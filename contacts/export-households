#!/usr/bin/env ruby

require 'logger'
require 'mysql2'
require 'yaml'
require 'set'
require 'csv'
require 'json'
require 'digest/md5'

def mysql_client_contacts
  @mysql_client_contacts ||=
    begin
      config = YAML.load_file("#{ENV['HOME']}/.evertrue/prod-dbs.yml")['contacts']
      Mysql2::Client.new(
        host: config['host'],
        username: config['username'],
        password: config['password'],
        database: config['database']
      )
    end
end

LOG = Logger.new(__FILE__ + '.log')
LOG.formatter = Logger::Formatter.new

oid = ARGV[0] || raise('no oid specified')

class Identity < Struct.new(:contact_id, :remote_id, :name_first, :name_middle, :name_last)
  include Comparable

  def <=>(other)
    remote_id <=> other.remote_id
  end

  def name_middle_abbrv
    name_middle ? name_middle[0] + '.' : nil
  end

  def full_name_downcase
    names = [name_first, name_middle, name_last]
    names.compact!
    names.map!(&:strip)
    names.join(' ').downcase
  end

  def full_name_with_abbrv_downcase
    names = [name_first, name_middle_abbrv, name_last]
    names.compact!
    names.map!(&:strip)
    names.join(' ').downcase
  end

  def full_name_without_middle_downcase
    names = [name_first, name_last]
    names.compact!
    names.map!(&:strip)
    names.join(' ').downcase
  end
end

class Household
  attr_reader :identities

  def initialize
    @identities = Set.new
  end

  def <<(identity)
    @identities << identity
  end

  def primary_remote_id
    @identities.to_a.sort.first.remote_id
  end
end

def cache_query(base_file_name, query)
  cache_file_name = base_file_name + '-' + Digest::MD5.hexdigest(query) + '.json'
  if File.exist?(cache_file_name)
    JSON.parse(File.read(cache_file_name))
  else
    results = mysql_client_contacts.query(query).to_a
    File.open(cache_file_name, 'wb') { |f| f.write(results.to_json) }
    results
  end
end

stats = Hash.new(0)

# find all contacts in the org
identities = {}
cache_query("#{oid}_identities", "SELECT identity.contact_id, identity.value as remote_id, name_first, name_middle, name_last FROM identity INNER JOIN contact_attributes ON identity.contact_id = contact_attributes.contact_id WHERE identity.oid = #{oid} AND identity.`type` = 1").each do |result|
  identity = Identity.new(result['contact_id'], result['remote_id'], result['name_first'], result['name_middle'], result['name_last'])
  identities[identity.remote_id] = identity
  # identities[identity.full_name_downcase] ||= identity
  # identities[identity.full_name_with_abbrv_downcase] ||= identity
  # identities[identity.full_name_without_middle_downcase] ||= identity
  stats[:total_identities] += 1
end

# find all the relationships and create households for each
households = {}
cache_query("#{oid}_relationships", "SELECT identity.value as remote_id, relationship.remote_id as relationship_remote_id, relationship.name as relationship_name FROM identity INNER JOIN relationship ON identity.contact_id = relationship.contact_id WHERE identity.oid = #{oid} AND identity.`type` = 1 AND relationship.`type` = 'spouse' AND relationship.remote_id IS NOT NULL").each do |result|
  remote_id = result['remote_id']
  relationship_remote_id = result['relationship_remote_id'].sub(/^0+/, '')
  relationship_name = result['relationship_name']
  relationship_name.downcase! if relationship_name

  stats[:total_relationships] += 1

  # find an existing household or start a new one
  household = households[remote_id] || households[relationship_remote_id] || Household.new

  # add contact identities to the household
  household << identities[remote_id] || raise("no identity for #{remote_id}")
  relationship_identity = identities[relationship_remote_id] || identities[relationship_name]
  if relationship_identity
    # puts "found identity for relationship_remote_id #{relationship_remote_id}"
    household << relationship_identity
  else
    LOG.warn "no relationship identity for #{relationship_name} / #{relationship_remote_id}"
    stats[:missing_identities] += 1
  end

  # save the house holds related to the the contacts
  households[remote_id] ||= household
  households[relationship_remote_id] ||= household
end

CSV.open('households.csv', 'wb') do |csv|
  csv << ['household_id', 'contact_id', 'remote_id', 'name_first', 'name_middle', 'name_last']

  export_households = []
  identities.values.uniq.each do |identity|
    household = households[identity.remote_id]
    household_id = household ? household.primary_remote_id : identity.remote_id

    export_households << [household_id, identity.contact_id, identity.remote_id, identity.name_first, identity.name_middle, identity.name_last]
  end

  export_households.sort_by!(&:first)

  export_households.each do |row|
    csv << row
  end
end

puts stats.to_json
