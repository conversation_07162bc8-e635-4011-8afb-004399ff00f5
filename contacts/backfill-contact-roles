#!/usr/bin/env ruby

# Purpose: This populates a contact's role with the default role for that contact's org.
# Background: V3 Importer had a bug where it didn't create contacts with role id's, so this script is needed
# to populate contact created in v3 importer. 
# Solution: Accept a date as input. Then, find all contacts with a created_at on or after that date and set 
# the contact role_id to its org's default.

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
  
  opts.on('-s', '--start_date=DATE', String, 'The minimum created at date for contacts') do |v|
    opts[:start_date] = v
  end

  opts.require_option(:start_date)
end

require File.expand_path('../config/environment', __dir__)
require 'active_record'

ActiveRecord::Base.logger = LOG

# Value from
# https://github.com/evertrue/contacts/blob/c12bbb666355179001214918f9f502083e2781d6/ContactsData/src/main/java/com/et/contacts/v1/data/contacts.proto#L127
UPDATE_SOURCE_CSV_IMPORTER = 4

@auth_client = AuthClient.create_client_with_app_creds

def get_default_roles(oid)
  # cache api responses
  @default_org_roles ||= {}

  return @default_org_roles[oid] if @default_org_roles.key?(oid)

  @default_org_roles[oid] = @auth_client.get_org_default_roles(oid)
end


contacts = ContactDB::Contact
  .left_joins(:contact_attributes)
  .where(contact_attributes: {update_source: UPDATE_SOURCE_CSV_IMPORTER})
  .where("contact.created_at > UNIX_TIMESTAMP(#{SCRIPT_OPTIONS[:start_date]})")
  .where(contact: {roles: [nil, ""]})

STATS.total = contacts.count
STATS.notification_interval = 1000

puts "processing #{STATS.total} contacts"

contacts.find_each(batch_size: 10_000) do |contact|
  STATS.inc_and_notify

  next unless (default_roles = get_default_roles(contact.oid))
  
  contact.roles = default_roles.join(',')
  contact.save!
end

STATS.notify(true)