#!/usr/bin/env ruby

require 'optparse'

options = {
  oid: nil,
  timestamp: nil
}
OptionParser.new do |opts|
  opts.banner = 'Usage: ' + File.basename(__FILE__)

  opts.on('-h', '--help', 'Prints this help') do
    puts opts
    exit
  end

  opts.on('-o', '--oid OID', 'Required organization id') do |v|
    options[:oid] = v.to_i
  end

  opts.on('-t', '--timestamp TIMESTAMP', 'Required timestamp of the first duplicate record') do |v|
    options[:timestamp] = v.to_i
  end
end.parse!

raise('no oid specified') unless options[:oid]
raise('no timestamp specified') unless options[:timestamp]

require File.expand_path('../config/environment', __dir__)
require 'csv'
require 'set'

class ContactAttribute
  FIELDS_TO_COMPARE = [
    'name_first',
    'name_last',
    'name_middle',
    'name_prefix',
    'year'
  ]

  attr_reader :attributes

  def initialize(attributes)
    @attributes = attributes
  end

  def key
    @key ||= FIELDS_TO_COMPARE.map { |field| @attributes[field].to_s.downcase }.join('|')
  end

  def contact_id
    @attributes['contact_id']
  end
end

mysql_contacts_client = MySQLHelpers.create_client(:contacts)

contacts = MySQLHelpers.cache_query_results(
  mysql_contacts_client,
  "SELECT * FROM contact WHERE oid = #{options[:oid]}",
  "oid-#{options[:oid]}-contacts.json"
)

contact_attributes = MySQLHelpers.cache_query_results(
  mysql_contacts_client,
  "SELECT contact_attributes.* FROM contact_attributes INNER JOIN contact ON contact.id = contact_attributes.contact_id WHERE contact.oid = #{options[:oid]}",
  "oid-#{options[:oid]}-contact_attributes.json"
).each_with_object({}) { |result, hsh| hsh[result['contact_id']] = ContactAttribute.new(result) }

identities = MySQLHelpers.cache_query_results(
  mysql_contacts_client,
  "SELECT * FROM identity WHERE oid = #{options[:oid]}",
  "oid-#{options[:oid]}-identities.json"
)

old_contacts = contacts.select { |contact| contact['created_at'] < options[:timestamp] }
new_contacts = contacts.select { |contact| contact['created_at'] >= options[:timestamp] }

old_ids = old_contacts.map { |contact| contact['id'] }.to_set

collisions = []
old_contact_attributes = contact_attributes.values.select { |contact_attribute| old_ids.include?(contact_attribute.contact_id) }.each_with_object({}) do |contact_attribute, hsh|
  if (previous = hsh[contact_attribute.key])
    collisions << previous.key
  end
  hsh[contact_attribute.key] = contact_attribute
end
collisions.each { |key| old_contact_attributes.delete(key) }
new_contact_attributes = contact_attributes.values.reject { |contact_attribute| old_ids.include?(contact_attribute.contact_id) }.each_with_object({}) { |contact_attribute, hsh| hsh[contact_attribute.contact_id] = contact_attribute }

File.open('collisions.json', 'wb') { |f| f.write collisions.to_json }

puts "total attributes #{contact_attributes.size}, total identities #{identities.size}"
puts "number of old contacts is #{old_contacts.size}, attributes #{old_contact_attributes.size}"
puts "number of new contacts is #{new_contacts.size}, attributes #{new_contact_attributes.size}"

new_to_old_contact_ids = {}
new_contacts_without_a_match = []

new_contacts.each do |new_contact|
  new_contact_attribute = new_contact_attributes[new_contact['id']]
  matching_old_contact_attribute = old_contact_attributes[new_contact_attribute.key]

  if matching_old_contact_attribute
    new_to_old_contact_ids[new_contact_attribute.contact_id] = matching_old_contact_attribute.contact_id
  else
    new_contacts_without_a_match << new_contact_attribute.contact_id
  end
end

old_contact_ids_cnt = Hash.new(0)
new_to_old_contact_ids.values.each { |id| old_contact_ids_cnt[id] += 1 }
duplicate_old_contact_ids = old_contact_ids_cnt.select { |key, value| value > 1 }.keys
duplicate_new_contact_ids = new_to_old_contact_ids.select { |new_id, old_id| duplicate_old_contact_ids.include?(old_id) }

new_to_old_contact_ids.reject! { |new_id, old_id| duplicate_old_contact_ids.include?(old_id) }

puts "matched #{new_to_old_contact_ids.size}, uniq #{new_to_old_contact_ids.values.uniq.size}, unmatched #{new_contacts_without_a_match.size}"

old_matched_contact_ids = new_to_old_contact_ids.values.to_set

old_contacts_to_delete = old_contacts.reject { |contact| old_matched_contact_ids.include?(contact['id']) }

puts "number of old contacts to delete is #{old_contacts_to_delete.size}"

contact_remote_ids = identities.select { |identity| identity['type'] == 1 }.each_with_object({}) { |identity, hsh| hsh[identity['contact_id']] = identity['value'] }

puts "number of remote_ids is #{contact_remote_ids.size}"

CSV.open("org-#{options[:oid]}-potential-duplicates.csv", 'wb') do |csv|
  csv << ['PersonId', 'EvertrueId'] + ContactAttribute::FIELDS_TO_COMPARE
  duplicate_new_contact_ids.to_a.sort_by(&:last).each do |new_id, old_id|
    contact_attribute = contact_attributes[new_id]
    csv << [contact_remote_ids[new_id], new_id] + ContactAttribute::FIELDS_TO_COMPARE.map { |f| contact_attribute.attributes[f] }
  end
end

CSV.open("org-#{options[:oid]}-conversions.csv", 'wb') do |csv|
  csv << ['OldPersonId', 'NewPersonId', 'OldEvertrueId', 'NewEvertrueId'] + ContactAttribute::FIELDS_TO_COMPARE
  new_to_old_contact_ids.each do |new_id, old_id|
    contact_attribute = contact_attributes[new_id]
    csv << [contact_remote_ids[old_id], contact_remote_ids[new_id], old_id, new_id] + ContactAttribute::FIELDS_TO_COMPARE.map { |f| contact_attribute.attributes[f] }
  end
end

CSV.open("org-#{options[:oid]}-new-records-to-delete.csv", 'wb') do |csv|
  csv << ['PersonId', 'EvertrueId', 'PersonIsDeleted'] + ContactAttribute::FIELDS_TO_COMPARE
  new_to_old_contact_ids.each do |new_id, old_id|
    contact_attribute = contact_attributes[new_id]
    csv << [contact_remote_ids[new_id], new_id, 'Y'] + ContactAttribute::FIELDS_TO_COMPARE.map { |f| contact_attribute.attributes[f] }
  end
end

CSV.open("org-#{options[:oid]}-old-records-to-delete.csv", 'wb') do |csv|
  csv << ['PersonId', 'EvertrueId', 'PersonIsDeleted'] + ContactAttribute::FIELDS_TO_COMPARE
  old_contacts_to_delete.each do |contact|
    contact_attribute = contact_attributes[contact['id']]
    csv << [contact_remote_ids[contact['id']], contact['id'], 'Y'] + ContactAttribute::FIELDS_TO_COMPARE.map { |f| contact_attribute.attributes[f] }
  end
end
