#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)

class IdentityCSVRow < ContactDB::Identity
  include CSVUtils::CSVRow

  csv_column :id, header: :identity_id
  csv_column :oid
  csv_column :contact_id
  csv_column :value, header: :email
  csv_column :has_contact
  csv_column :created_date
  csv_column :updated_date

  def has_contact
    contact ? 'Y' : 'N'
  end

  def in_sync?
    emails.any? { |email| email.email&.downcase == value.downcase }
  end

  def created_date
    Time.at(created_at / 1000).strftime('%Y-%m-%d')
  end

  def updated_date
    Time.at(updated_at / 1000).strftime('%Y-%m-%d')
  end
end

stats = SimpleStats.new
stats_display_proc = Proc.new do
  LOG.notify stats.to_display_s
end

contacts_client = ContactClient.create_app_client

CSVUtils::CSVReport.new("#{ENV['RAILS_ENV']}-deleted-email-identities-not-in-sync-with-emails-#{Time.now.strftime('%Y-%m-%dT%H%M')}.csv", IdentityCSVRow) do |report|
  IdentityCSVRow.where(type: 0).includes(:contact, :emails).find_each do |identity|
    stats[:cnt] += 1
    stats_display_proc.call if (stats[:cnt] % 10_000) == 0

    unless identity.contact
      LOG.warn("identity #{identity.id} is missing contact #{identity.contact_id}")
      next
    end
    next if identity.in_sync?

    stats[:missing_email_record] += 1

    result = contacts_client.delete_identity(
      identity.oid,
      identity.contact_id,
      'EMAIL',
      identity.value
    )

    if result
      stats[:deleted_identity] += 1
      LOG.info("deleted identity #{identity.id} for contact #{identity.contact_id}")
      report << identity
    else
      stats[:delete_identity_failures] += 1
    end
  end
end

stats_display_proc.call
