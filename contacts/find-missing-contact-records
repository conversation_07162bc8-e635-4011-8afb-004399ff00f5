#!/usr/bin/env ruby

require File.expand_path('../config/environment', __dir__)

# Verifies that contacts in MySQL and in Cassandra contacts_index.oid_index are the same.
# Removes records from the oid_index that are not in MySQL
# Refreshes contacts where the updated_at times do not match or if the id is in MySQL but not in the oid_index
# Starts by dumping all contacts from MySQL to a file of (contact_id,updated_at)
# dumps all oid_index contact records to a file of, (contact_id,updated_at,shard_id)

def mysql_client_contacts
  @mysql_client ||= MySQLHelpers.create_client(:contacts)
end

def cassandra_cluster
  @cassandra_cluster ||= Cassandra.cluster(hosts: KS_CONFIG['hosts'], port: KS_CONFIG['port'])
end

def create_cassandra_session(keyspace)
  cassandra_cluster.connect(keyspace)
end

def each_cassandra_record(session, cql, page_size = 100)
  LOG.info("Starting: #{cql}")

  result = session.execute(cql, page_size: page_size)
  loop do
    result.each { |row| yield row }
    break if result.last_page?

    result = result.next_page
  end

  LOG.info("Finished: #{cql}")
end

def run_command(cmd)
  LOG.info "running: #{cmd}"
  started_at = Time.now
  result = `#{cmd}`
  LOG.info "took #{Time.now - started_at} to run #{cmd}"
  raise("command failed #{cmd}") unless $?.success?
  result
end

stats = Hash.new(0)
stats[:started_at] = Time.now

display_stats_proc = Proc.new do
  stats[:elapsed_time] = Time.now - stats[:started_at]
  LOG.info stats.inspect
end

oid = ARGV[0] || raise('no oid specified')

base_dir = "contacts-cleanup/#{oid}"
FileUtils.mkdir_p(base_dir)
Dir.chdir(base_dir)

File.open("#{oid}_ks_oid_index.txt", 'wb') do |f|
  shards = 20.times.map { |i| Cassandra::Util.encode_string("#{oid}_shard_#{i}") }
  stats[:started_ks_at] = Time.now
  semaphore = Mutex.new
  shard_threads = shards.map do |shard_id|
    Thread.new do
      cql = "SELECT * FROM oid_index WHERE id = #{shard_id} AND oid = #{oid}"
      Thread.current[:ks_records] = 0
      each_cassandra_record(create_cassandra_session('contacts_index'), cql) do |contact_data|
        Thread.current[:ks_records] += 1
        contact = Contacts::Contact.decode(contact_data['contact'])
        semaphore.synchronize do
          f.write "#{contact_data['contact_id']},#{contact.updated_at}\n"
        end
      end
    end
  end
  shard_threads.each(&:join)
  stats[:ks_records] = shard_threads.map { |t| t[:ks_records] }.sum
  display_stats_proc.call
end

stats[:ks_took] = Time.now - stats[:started_ks_at]
display_stats_proc.call

run_command "sort -n -k 1,1 -t',' #{oid}_ks_oid_index.txt > #{oid}_ks_oid_index.sorted.txt"
File.unlink "#{oid}_ks_oid_index.txt"

File.open("#{oid}_es_contacts.txt", 'wb') do |f|
  url = ES_URL + "/prod-contacts/contact/_search"
  query = {
    'term' => {
      "oid" => {
        "value" => oid
      }
    }
  }
  scanner = ElasticSearchScanner.new(url, query)
  scanner.fields_to_return = ['updated_at']
  stats[:started_es_at] = Time.now
  scanner.each do |result|
    stats[:es_records] += 1
    f.puts "#{result['_id']},#{result['_source']['updated_at']}"
    display_stats_proc.call if (stats[:es_records]%50_000) == 0
  end
end

stats[:es_took] = Time.now - stats[:started_es_at]
display_stats_proc.call

run_command "sort -n -k 1,1 -t',' #{oid}_es_contacts.txt > #{oid}_es_contacts.sorted.txt"
File.unlink "#{oid}_es_contacts.txt"


File.open("#{oid}_mysql_contacts.txt", 'wb') do |f|
  stats[:started_mysql_at] = Time.now
  mysql_client_contacts.query("SELECT id, updated_at FROM contact WHERE oid = #{oid} ORDER BY id").each do |result|
    stats[:mysql_records] += 1
    f.puts "#{result['id']},#{result['updated_at']}"
    display_stats_proc.call if (stats[:mysql_records]%100_000) == 0
  end
end

stats[:mysql_took] = Time.now - stats[:started_mysql_at]
display_stats_proc.call

class ContactResult < Struct.new(:contact_id,
                                 :updated_at
                                )

  def self.next_from_file(file)
    if file.eof?
      nil
    else
      new *file.readline.strip.split(',').map(&:to_i)
    end
  end
end

def compare_files(source_file, destination_file, results_file_name)
  stats = Hash.new(0)

  read_next_source_contact = true
  read_next_destination_contact = true

  File.open(results_file_name, 'wb') do |f|
    while(!source_file.eof? || !destination_file.eof?)
      source_contact_result = ContactResult.next_from_file(source_file) if read_next_source_contact
      destination_contact_result = ContactResult.next_from_file(destination_file) if read_next_destination_contact

      if ! source_contact_result
        stats[:delete] += 1
        LOG.info("contact #{destination_contact_result.contact_id} is not in source, no more destination contacts to read")
        f.puts "#{destination_contact_result.contact_id},delete"
        read_next_destination_contact  = true
      elsif ! destination_contact_result
        stats[:refresh] += 1
        LOG.info("contact #{source_contact_result.contact_id} is not in destination, no more source contacts to read")
        f.puts "#{source_contact_result.contact_id},refresh"
        read_next_source_contact =  true
      elsif source_contact_result.contact_id == destination_contact_result.contact_id
        if source_contact_result.updated_at != destination_contact_result.updated_at
          stats[:refresh] += 1
          newer_in = source_contact_result.updated_at > destination_contact_result.updated_at ? 'source' : 'destination'
          LOG.info("contact #{source_contact_result.contact_id} is newer in #{newer_in} by #{(source_contact_result.updated_at - destination_contact_result.updated_at).abs} milliseconds")
          f.puts "#{source_contact_result.contact_id},refresh"
        end
        read_next_source_contact = true
        read_next_destination_contact = true
      elsif source_contact_result.contact_id > destination_contact_result.contact_id
        stats[:delete] += 1
        LOG.info("contact #{destination_contact_result.contact_id} is not in source")
        f.puts "#{destination_contact_result.contact_id},delete"
        read_next_source_contact = false
      else
        stats[:refresh] += 1
        LOG.info("contact #{source_contact_result.contact_id} is not in destination")
        f.puts "#{source_contact_result.contact_id},refresh"
        read_next_destination_contact = false
      end
    end
  end

  stats
end

source_file = File.open("#{oid}_mysql_contacts.txt", 'rb')
destination_file = File.open("#{oid}_ks_oid_index.sorted.txt", 'rb')
puts compare_files(source_file, destination_file, "#{oid}_cassandra_cleanup.txt").inspect
source_file.close
destination_file.close

source_file = File.open("#{oid}_mysql_contacts.txt", 'rb')
destination_file = File.open("#{oid}_es_contacts.sorted.txt", 'rb')
puts compare_files(source_file, destination_file, "#{oid}_es_cleanup.txt").inspect
source_file.close
destination_file.close

run_command("cat #{oid}_cassandra_cleanup.txt #{oid}_es_cleanup.txt | sort -n -k 1,1 -t',' | uniq > #{oid}_cleanup.txt")

File.unlink "#{oid}_mysql_contacts.txt"
File.unlink "#{oid}_es_contacts.sorted.txt"
File.unlink "#{oid}_ks_oid_index.sorted.txt"
File.unlink "#{oid}_cassandra_cleanup.txt"
File.unlink "#{oid}_es_cleanup.txt"

File.unlink("#{oid}_cleanup.txt") if File.new("#{oid}_cleanup.txt").size == 0
