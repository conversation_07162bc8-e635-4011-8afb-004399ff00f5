#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
end

require File.expand_path('../config/environment', __dir__)
require 'csv'
require 'active_record'

# ActiveRecord::Base.logger = LOG

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

class ContactCSVRow < ContactDB::Contact
  include CSVUtils::CSVRow

  csv_column :id, header: 'EvertrueContactId'
  csv_column :remote_id, header: 'RemoteId'
  [
    :name_prefix,
    :name_first,
    :name_middle,
    :name_last,
    :name_suffix,
    :year
  ].each do |contact_attribute_field|
    csv_column(contact_attribute_field, header: contact_attribute_field.to_s.classify) { contact_attribute.try(contact_attribute_field) }
  end

  5.times do |i|
    field_name = "Email#{i + 1}Email".to_sym
    csv_column(field_name) { emails[i].try(:email) }
  end
end

STATS.total = ContactCSVRow.where(oid: SCRIPT_OPTIONS[:oid]).count

CSVUtils::CSVReport.new("#{org.slug}-#{org.id}-cleanup-remote-id-scientific-notation.csv", ContactCSVRow) do |report|
  ContactCSVRow.use_oid_idx.where(oid: SCRIPT_OPTIONS[:oid]).includes(:identities, :contact_attribute, :emails).find_each(batch_size: 10_000) do |contact|
    STATS.inc_and_notify
    next unless contact.remote_id =~ /E\+/

    STATS[:contact_with_scientific_notation] += 1
    report << contact
  end
end
