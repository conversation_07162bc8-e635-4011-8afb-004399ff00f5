#!/usr/bin/env ruby

require 'json'
require 'net/http'
require 'logger'
require 'mysql2'
require 'yaml'
require 'thread'

class ContactClient
  DEFAULT_BASE_URL = 'https://api.evertrue.com'

  def initialize(app_key, auth_token, base_url=DEFAULT_BASE_URL, provider='EvertrueAppToken')
    @app_key = app_key
    @auth_token = auth_token
    @base_url = base_url
    @provider = provider
  end

  def get_contact(oid, contact_id)
    path = "/contacts/v1/contacts/#{contact_id}?oid=#{oid}&script=#{__FILE__}&developer=#{ENV['USER']}"
    url = @base_url + path

    uri = URI(url)
    req = Net::HTTP::Get.new(uri.request_uri, req_headers)

    Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == 'https') do |http|
      http.request(req)
    end
  end

  def update_contact(oid, contact_id, data)
    path = "/contacts/v1/contacts/#{contact_id}?oid=#{oid}&script=#{__FILE__}&developer=#{ENV['USER']}"
    url = @base_url + path

    uri = URI(url)
    req = Net::HTTP::Put.new(uri.request_uri, req_headers)
    req.body = JSON.dump(data)

    Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == 'https') do |http|
      http.request(req)
    end
  end

  def delete_identity(oid, contact_id, type, value)
    path = "/contacts/v1/identities/contact/#{contact_id}?oid=#{oid}&script=#{__FILE__}&developer=#{ENV['USER']}"
    url = @base_url + path

    uri = URI(url)
    req = Net::HTTP::Delete.new(uri.request_uri, req_headers)
    req.body = JSON.dump(value: value, type: type)

    Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == 'https') do |http|
      http.request(req)
    end
  end

  def add_identity(oid, contact_id, type, value)
    path = "/contacts/v1/identities/contact/#{contact_id}?oid=#{oid}&script=#{__FILE__}&developer=#{ENV['USER']}"
    url = @base_url + path

    uri = URI(url)
    req = Net::HTTP::Post.new(uri.request_uri, req_headers)
    req.body = JSON.dump(value: value, type: type)

    Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == 'https') do |http|
      http.request(req)
    end
  end

  private

  def req_headers
    {
      'Content-Type' => 'application/json',
      'Accept' => 'application/json',
      'Application-Key' => @app_key,
      'Authorization-Provider' => @provider,
      'Authorization' => @auth_token,
      'ET-Update-Source' => 'hadoop'
    }
  end
end

def mysql_client_contacts
  @mysql_client_contacts ||=
    begin
      config = YAML.load_file("#{ENV['HOME']}/.evertrue/prod-dbs.yml")['contacts']
      Mysql2::Client.new(
        host: config['host'],
        username: config['username'],
        password: config['password'],
        database: config['database']
      )
    end
end

def mysql_client_auth
  @mysql_client_auth ||=
    begin
      config = YAML.load_file("#{ENV['HOME']}/.evertrue/prod-dbs.yml")['auth']
      Mysql2::Client.new(
        host: config['host'],
        username: config['username'],
        password: config['password'],
        database: config['database']
      )
    end
end

USER_IDENTITY_TYPE = 4

LOG = Logger.new(__FILE__ + '.log')
LOG.formatter = Logger::Formatter.new

contact_client = ContactClient.new('5087f1d0493aa43c0bbcadd674400b5381f54a5aa852176e5993e1c14622aabc', 'MTY6MXhKcTlrU0J2S1pqaFhDRFBzeW4=')

user_id = ARGV[0]
user = mysql_client_auth.query("select * from users where id = #{user_id}").first.to_h
raise("unknown user for id #{user_id}") if user.empty?

affiliations = mysql_client_auth.query("select * from affiliations where user_id = #{user_id} and contact_id IS NOT NULL").map(&:to_h)

identities = mysql_client_contacts.query("select * from identity where `type` = #{USER_IDENTITY_TYPE} and contact_id IN(#{affiliations.map { |a| a['contact_id'] }.join(', ')})").map(&:to_h)

# identities to add
affiliations.each do |affiliation|
  contact_id = affiliation['contact_id']
  oid = affiliation['organization_id']
  existing_identity = identities.detect { |identity| identity['contact_id'] == contact_id && identity['oid'] == oid }
  if existing_identity && existing_identity['value'] == user_id
    LOG.info "user #{user_id} identity #{existing_identity['id']} is already mapped for contact_id #{existing_identity['contact_id']} and oid #{existing_identity['oid']}"
    next
  end

  LOG.info "user #{user_id} identity missing for affiliation contact_id #{contact_id} and oid #{oid}"
  res = contact_client.add_identity(oid, contact_id, 'USER_ID', user_id)
  unless res.is_a?(Net::HTTPNoContent)
    LOG.error "failed to update identity #{res}"
    LOG.error res.body
  end
end

# identities to remove
identities.each do |identity|
  contact_id = identity['contact_id']
  oid = identity['oid']
  existing_affiliation = affiliations.detect { |affiliation| affiliation['contact_id'] == contact_id && affiliation['organization_id'] == oid }
  if existing_affiliation && existing_affiliation['user_id'] == user_id.to_i
    # LOG.info "user #{user_id} affiliation #{existing_affiliation['id']} is already mapped for contact_id #{existing_affiliation['contact_id']} and oid #{existing_affiliation['organization_id']}"
    next
  end

  LOG.info "user #{user_id} affiliation missing for identity contact_id #{contact_id} and oid #{oid}"
  res = contact_client.delete(oid, contact_id, 'USER_ID', user_id)
  unless res.is_a?(Net::HTTPNoContent)
    LOG.error "failed to delete identity #{res}"
    LOG.error res.body
  end
end

