#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

# Purpose: to ensure the data in the contact assignee file is the only data in the Contacts DB

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-j', '--job JOB_ID', Integer, 'Job ID') do |v|
    opts[:job_id] = v
  end
end

require File.expand_path('../config/environment', __dir__)
require 'active_record'
ActiveRecord::Base.logger = LOG

ContactDB

class ContactDB::Assignee
  include CSVUtils::CSVRow

  columns.each do |column|
    csv_column column.name
  end
end

unless (job = ImporterDB::Job.where(oid: SCRIPT_OPTIONS[:oid], id: SCRIPT_OPTIONS[:job_id]).first)
  raise 'no job found'
end

importer_s3_client = ImporterS3Client.new

s3_object = importer_s3_client.get_object(job.base_path + '/' + job.s3_filename)

csv = CSVUtils::CSVIterator.new(CSV.new(s3_object.body))
valid_remote_ids = csv.map { |row| row['RemoteID'] }

assignees = ContactDB::Assignee.includes(contact: :identities).joins(:contact).where(contact: {oid: SCRIPT_OPTIONS[:oid]}).to_a

invalid_assignees = assignees.reject { |assignee| valid_remote_ids.include?(assignee.contact.remote_id) }

puts "total assignees #{assignees.size}"
puts "invalid assignees #{invalid_assignees.size}"

CSVUtils::CSVReport.new("oid-#{SCRIPT_OPTIONS[:oid]}-deleted-assignees-#{Time.now.strftime('%Y%M%d%H%m%S')}.csv") do |report|
  invalid_assignees.each { |assignee| report << assignee }
end

contact_client = ContactClient.create_client_with_app_creds

STATS.total = invalid_assignees.size
STATS.notification_interval = 100

invalid_assignees.each do |assignee|
  STATS.inc_and_notify
  LOG.info("deleted assignee #{assignee.id} for #{assignee.contact.oid}/#{assignee.contact.id}")
  assignee.destroy
  contact_client.refresh_contact(assignee.contact.oid, assignee.contact.id)
end

STATS.notify(true)
