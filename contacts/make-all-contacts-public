#!/usr/bin/env ruby

# Purpose is to unset the privatized_source

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
  opts.add_oid_option
end

require File.expand_path('../config/environment', __dir__)
require 'active_record'
ActiveRecord::Base.logger = LOG

def contact_client
  @contact_client ||= ContactClient.create_client_with_app_creds
end

NESTED_MODELS = {
  addresses: ContactDB::Address,
  assignees: ContactDB::Assignee,
  avatars: ContactDB::Avatar,
  blacklist_settings: ContactDB::BlacklistSetting,
  constituencies: ContactDB::Constituency,
  contact_attributes: ContactDB::ContactAttribute,
  custom_property_values: ContactDB::CustomPropertyValue,
  educations: ContactDB::Education,
  emails: ContactDB::Email,
  employments: ContactDB::Employment,
  extracurricular_activities: ContactDB::ExtracurricularActivity,
  facebooks: ContactDB::Facebook,
  givings: ContactDB::Giving,
  giving_annual_donations: ContactDB::GivingAnnualDonation,
  giving_categories: ContactDB::GivingCategory,
  ignored_uids: ContactDB::IgnoredUid,
  linkedin_positions: ContactDB::LinkedinPosition,
  phones: ContactDB::Phone,
  prospect_statuses: ContactDB::ProspectStatus,
  relationships: ContactDB::Relationship,
  scores: ContactDB::Score,
  solicitation_codes: ContactDB::SolicitationCode,
  sports: ContactDB::Sport
}

def has_private_data?(contact)
  contact_client.fetch_contact_by_id(contact.oid, contact.id).body.include?('privatized_source')
end

ContactDB::Contact.includes(*NESTED_MODELS.keys).where(oid: SCRIPT_OPTIONS[:oid], private: true).each do |contact|
  LOG.info("contact #{contact.id}/#{contact.oid} has private data") if has_private_data?(contact)

  NESTED_MODELS.each do |relationship, nested_model|
    if contact.send(relationship).any? { |record| record.private? || record.privatized_source }
      ids = contact.send(relationship).map(&:id)

      if relationship == :addresses
        ContactDB::AddressCensusDatum.where(address_id: ids).update_all(private: false, privatized_source: nil)
        ContactDB::AddressGeoCoordinate.where(address_id: ids).update_all(private: false, privatized_source: nil)
      elsif relationship == :educations
        ContactDB::EducationMajor.where(education_id: ids).update_all(private: false, privatized_source: nil)
      end

      nested_model.where(id: ids).update_all(private: false, privatized_source: nil)
    end
  end

  ContactDB::Contact.where(id: contact.id).update_all(private: false, privatized_source: nil)

  contact_client.refresh_contact(contact.oid, contact.id)

  if has_private_data?(contact)
    LOG.warn("contact #{contact.id}/#{contact.oid} still has private data")
  else
    LOG.info("contact #{contact.id}/#{contact.oid} is public")
  end
end
