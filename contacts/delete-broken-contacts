#!/usr/bin/env ruby

# we have some contact records that are crossing orgs due to the migration from KS to MySQL where the auto increment ids collided

# This script will query for these records where the oid in contact and identity do not match

# retain existing contact data

# the use the delete contact API to remove them

# keep track of all oids affected

# updated the mapping hash updated_at for all orgs affected so that the data is re-importered


require File.expand_path('../config/environment', __dir__)
require 'set'

mysql_client_contacts = MySQLHelpers.create_client(:contacts)
mysql_client_importer = MySQLHelpers.create_client(:importer)
contact_client = ContactClient.create_app_client

results = MySQLHelpers.cache_query_results(mysql_client_contacts, 'SELECT contact.id AS c_id, contact.oid AS c_oid, identity.oid AS i_oid FROM contact INNER JOIN identity ON contact.id = identity.contact_id WHERE contact.oid != identity.oid', 'contact_identity_mismatches.json')

LOG.info results.inspect

contacts_data_dir = TMP_DIR.join('contacts').join(ENV['RAILS_ENV'])

FileUtils.mkdir_p(contacts_data_dir) unless File.exist?(contacts_data_dir)

oids = Set.new
results.each do |result|
  oids << result['c_oid']
  oids << result['i_oid']

  contact_file = contacts_data_dir.join("contact_#{result['c_id']}.json")

  unless File.exist?(contact_file)
    File.open(contact_file, 'wb') { |f| f.write contact_client.get_contact(result['c_oid'], result['c_id']) }
  end
end

results.each do |result|
  contact_client.delete_contact(result['c_oid'], result['c_id'])
end

new_mapping_hash_updated_at = (Time.now.to_f * 1_000).to_i

LOG.info "new mapping hash updated at is #{new_mapping_hash_updated_at}"

mysql_client_importer.query("UPDATE mapping_hash SET updated_at = #{new_mapping_hash_updated_at} WHERE oid IN(#{oids.to_a.join(', ')})")
