#!/usr/bin/env ruby

#!/usr/bin/env ruby


require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
end

require File.expand_path('../config/environment', __dir__)
require 'csv'
require 'active_record'

# ActiveRecord::Base.logger = LOG

class ContactCSVRow < ContactDB::Contact
  include CSVUtils::CSVRow

  csv_column :oid
  csv_column :remote_id, header: 'ClientID'
  csv_column :id, header: :contact_id
  csv_column :constituency
  csv_column(:is_deceased) { contact_attribute.try(:deceased) ? 'Y' : 'N' }
  csv_column :degree_key
  csv_column(:year) { educations.first.try(:year) }
  csv_column(:email) { emails.first.try(:email) }
  csv_column(:last_gift_date) { giving_category.try(:last_gift_date) }
  csv_column(:last_gift_amount) { giving_category.try(:last_gift_amount) || 0 }
  csv_column(:lifetime_amount) { giving_category.try(:lifetime_amount) || 0 }
  csv_column(:largest_gift_amount) { giving_category.try(:largest_gift_amount) || 0 }
  csv_column(:fy_0_amount, header: 'GiftTotalFY21') { giving_category.try(:fy_0_amount) || 0 }
  csv_column(:fy_1_amount, header: 'GiftTotalFY20') { giving_category.try(:fy_1_amount) || 0 }
  csv_column(:fy_2_amount, header: 'GiftTotalFY19') { giving_category.try(:fy_2_amount) || 0 }
  csv_column(:fy_3_amount, header: 'GiftTotalFY18') { giving_category.try(:fy_3_amount) || 0 }
  csv_column(:fy_4_amount, header: 'GiftTotalFY17') { giving_category.try(:fy_4_amount) || 0 }
  csv_column(:fy_5_amount, header: 'GiftTotalFY16') { giving_category.try(:fy_5_amount) || 0 }
  csv_column(:donor_score) { giving.try(:donor_score) }
  csv_column(:capacity_score) { giving.try(:capacity_score) }
  csv_column(:engagement_score) { giving.try(:engagement_score) }
  csv_column :restriction_codes
  csv_column :giving_lables

  def giving
    givings.first
  end

  def giving_category
    giving_categories.first
  end

  def constituency
    constituencies.map(&:status).join(';')
  end

  def degree_key
    educations.map(&:degree_key).join(';')
  end

  def restriction_codes
    solicitation_codes.map(&:code).join(';')
  end

  def giving_lables
    giving_categories.map(&:label).join(';')
  end
end

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

STATS.total = ContactCSVRow.where(oid: SCRIPT_OPTIONS[:oid]).count

CSVUtils::CSVReport.new("#{org.slug}-#{org.id}-constituency-report.csv", ContactCSVRow) do |report|
  ContactCSVRow.use_oid_idx.where(oid: SCRIPT_OPTIONS[:oid]).includes(:identities, :educations, :contact_attribute, :givings, :giving_categories, :emails, :solicitation_codes, :constituencies).find_each(batch_size: 10_000) do |contact|
    STATS.inc_and_notify
    report << contact
  end
end
