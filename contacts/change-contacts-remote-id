#!/usr/bin/env ruby

# Purpose is to change the contact IDs remote ID.  In this use case the new remote ID maybe used by another contact (the duplicate).  And this script is designed to actually delete the identity record of the duplicate contact.

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-f', '--file CSV_FILE', 'CSV contain contact remtoe ID changes') do |v|
    opts.options[:file] = v
  end

  opts.on('-d', '--dry-run', 'Dry run do nothing but log') do
    opts.options[:dry_run] = true
  end

  opts.require_option(:file)
end

require File.expand_path('../config/environment', __dir__)

def contact_client
  @contact_client ||= ContactClient.create_client_with_app_creds
end

def with_retries(max_attempts = 5, sleep_for = 1)
  max_attempts.times do
    return true if yield
    LOG.info("sleeping for #{sleep_for} seconds")
    STATS.update(:retries)
    sleep(sleep_for)
  end

  false
end

def add_remote_id(contact, remote_id)
  if SCRIPT_OPTIONS[:dry_run]
    LOG.info("added remote_id #{remote_id} to contact #{contact.id}")
    STATS.update(:added_remote_ids)
    return true
  end

  with_retries do
    res = contact_client.add_remote_id_identity(contact.oid, contact.id, remote_id)

    if res.kind_of?(Net::HTTPNoContent)
      LOG.info("added remote_id #{remote_id} to contact #{contact.id}")
      STATS.update(:added_remote_ids)
      true
    elsif res.kind_of?(Net::HTTPNotFound)
      LOG.info("contact #{contact.id} already has remote_id #{remote_id}")
      STATS.update(:already_added_remote_ids)
      true
    else
      LOG.error("unhandled add remote_id response #{res} for remote_id #{remote_id} and contact #{contact.id}")
      STATS.update(:unhandled_add_identity_response)
      false
    end
  end
end

def delete_remote_id(contact, remote_id)
  if SCRIPT_OPTIONS[:dry_run]
    LOG.info("deleteed remote_id #{remote_id} to contact #{contact.id}")
    STATS.update(:deleteed_remote_ids)
    return true
  end

  with_retries do
    res = contact_client.delete_remote_id_identity(contact.oid, contact.id, remote_id)

    if res.kind_of?(Net::HTTPNoContent)
      LOG.info("deleteed remote_id #{remote_id} to contact #{contact.id}")
      STATS.update(:deleteed_remote_ids)
      true
    elsif res.kind_of?(Net::HTTPNotFound)
      LOG.info("contact #{contact.id} already has remote_id #{remote_id}")
      STATS.update(:already_deleteed_remote_ids)
      true
    else
      LOG.error("unhandled delete remote_id response #{res} for remote_id #{remote_id} and contact #{contact.id}")
      STATS.update(:unhandled_delete_identity_response)
      false
    end
  end
end

def delete_contact(contact)
  if SCRIPT_OPTIONS[:dry_run]
    STATS.update(:deleted_contact)
    return true
  end

  with_retries do
    if contact_client.delete_contact(contact.oid, contact.id)
      STATS.update(:deleted_contact)
      true
    else
      STATS.update(:delete_contact_failures)
      false
    end
  end
end

def delete_identity(identity)
  if SCRIPT_OPTIONS[:dry_run]
    LOG.info("deleted identity #{identity.id} for contact_id #{identity.contact_id} with remote_id #{identity.value}")
    STATS.update(:deleted_identity)
    return true
  end

  begin
    identity.destroy!
    LOG.info("deleted identity #{identity.id} for contact_id #{identity.contact_id} with remote_id #{identity.value}")
    STATS.update(:deleted_identity)
    true
  rescue Exception => e
    LOG.info("failed to deleted identity #{identity.id} for contact_id #{identity.contact_id} with remote_id #{identity.value}, exception #{e.inspect}")
    STATS.update(:failed_to_delete_identity)
    false
  end
end

REQUIRED_CSV_COLUMNS = ['contact_id', 'current_remote_id', 'new_remote_id']

csv = CSVUtils::CSVIterator.new(SCRIPT_OPTIONS[:file])

missing_headers = REQUIRED_CSV_COLUMNS - csv.first.keys
raise("CSV file is missing these required headers #{missing_headers.join(', ')}") unless missing_headers.size == 0

contact_ids = csv.map { |r| r['contact_id'].to_i }
contacts = ContactDB::Contact.includes(:identities).where(oid: SCRIPT_OPTIONS[:oid], id: contact_ids).all.index_by(&:id)

missing_contact_ids = contact_ids - contacts.keys
LOG.warn("CSV file contains unknown contact ids #{missing_contact_ids.join(', ')}") unless missing_contact_ids.size == 0

duplicate_contacts_by_identity = ContactDB::Identity.includes(:contact).where(oid: SCRIPT_OPTIONS[:oid], type: 1, value: csv.map { |r| r['new_remote_id'] }).all.index_by(&:value)

csv.each do |row|
  STATS.inc_and_notify

  contact_id = row['contact_id'].to_i
  if missing_contact_ids.include?(contact_id)
    STATS.update(:unknown_contact_ids)
    next
  end

  contact = contacts[row['contact_id'].to_i]

  if contact.remote_id == row['new_remote_id']
    LOG.info("contact #{contact.id} is using the new_remote_id #{contact.remote_id}")
    STATS.update(:already_changed_remote_id)
    next
  end

  unless contact.remote_id == row['current_remote_id']
    LOG.warn("contact #{contact.id} remote id is currently #{contact.remote_id} and does not match #{row['current_remote_id']}")
    STATS.update(:existing_remote_id_mismatch)
    next
  end

  duplicate_contact_by_identity = duplicate_contacts_by_identity[row['new_remote_id']]
  if duplicate_contact_by_identity
    if duplicate_contact_by_identity.contact
      delete_contact(duplicate_contact_by_identity.contact)
    end

    delete_identity(duplicate_contact_by_identity)
  else
    LOG.info("duplicate contact identity record for #{row['new_remote_id']} does not exists")
    STATS.update(:new_remote_id_not_used)
  end

  delete_remote_id(contact, contact.remote_id)
  add_remote_id(contact, row['new_remote_id'])
end

STATS.notify(true)
