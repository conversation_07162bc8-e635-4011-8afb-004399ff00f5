#!/usr/bin/env ruby

=begin
Purpose: to ensure emails in the email table are in the identity table
=end

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)

class ContactDB::Contact
  def identity_emails
    @identity_emails ||=
      begin
        list = identities.select { |identity| identity.type == 0 }
        list.map!(&:value)
        list.each(&:downcase!)
        list
      end
  end

  def email_emails
    @email_emails ||=
      begin
        list = emails.select { |email| email.email.present? }
        list.map!(&:email)
        list.each(&:downcase!)
        list.uniq!
        list
      end
  end

  def valid_email_encodings?
    identity_emails.all?(&:valid_encoding?) &&
      extra_emails.all?(&:valid_encoding?)
  end

  def extra_emails
    @extra_emails ||=
      begin
        emails.select do |email|
          email.email.present? &&
            !identity_emails.include?(email.email.downcase)
        end
      end
  end

  def in_sync?
    extra_emails.size == 0
  end
end

class ContactDB::Email
  include CSVUtils::CSVRow

  attr_accessor :oid,
                :status

  csv_column :oid
  csv_column :contact_id
  csv_column :status
  csv_column :email
  csv_column :type
  csv_column :primary
  csv_column(:valid_encoding) { valid_encoding? ? 'Y' : 'N' }
  csv_column(:current_identity_id) { identity&.id }
  csv_column(:current_identity_contact_id) { identity&.contact_id }

  def identity
    unless defined?(@identity)
      @identity = ContactDB::Identity.where(oid: oid, type: 0, value: email).first
    end
    @identity
  end

  def valid_encoding?
    email.dup.force_encoding('ASCII').valid_encoding?
  end
end

class ContactDB::Identity
  def valid_encoding?
    email.dup.force_encoding('ASCII').valid_encoding?
  end

  def self.create_for_email(contact, email)
    timestamp = (Time.now.to_f * 1000).to_i

    create!(
      oid: contact.oid,
      contact_id: contact.id,
      type: 0,
      value: email.email,
      created_at: timestamp,
      updated_at: timestamp
    )
  end
end

def contact_client
  @contact_client ||= ContactClient.create_client_with_app_creds
end

CSVUtils::CSVReport.new("deleted-emails.csv", ContactDB::Email) do |report|
  ContactDB::Contact.includes(:identities, :emails).find_each(batch_size: 10_000) do |contact|
    STATS.inc_and_notify

    next if contact.in_sync?

    unless contact.valid_email_encodings?
      STATS[:contact_with_invalid_email_encodings] += 1
    end

    STATS[:contact_with_extra_email_records] += 1
    LOG.warn "contact #{contact.id} has extra emails #{contact.extra_emails.map(&:email).join(', ')}, identities(#{contact.identity_emails.join(', ')}), emails(#{contact.email_emails.join(', ')})"

    contact.extra_emails.each do |extra_email|
      extra_email.oid = contact.oid

      if extra_email.identity
        STATS[:existing_identity_record] += 1
        extra_email.destroy
        extra_email.status = 'deleted'
      else
        STATS[:create_identity_record] += 1
        ContactDB::Identity.create_for_email(contact, extra_email)
        extra_email.status = 'created identity'
      end

      report << extra_email
    end

    contact.refresh(contact_client)
  end
end

STATS.notify(true)
