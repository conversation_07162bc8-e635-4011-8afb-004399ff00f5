#!/usr/bin/env ruby

require 'optparse'

options = {
  oid: nil,
  file: nil
}
OptionParser.new do |opts|
  opts.banner = 'Usage: ' + File.basename(__FILE__)

  opts.on('-h', '--help', 'Prints this help') do
    puts opts
    exit
  end

  opts.on('-o', '--oid OID', 'Required organization id') do |v|
    options[:oid] = v.to_i
  end

  opts.on('-f', '--file CSV_FILE', 'Remote ids to delete') do |v|
    options[:file] = v
  end
end.parse!

raise('no oid specified') unless options[:oid]
raise('no file specified') unless options[:file]

require File.expand_path('../config/environment', __dir__)
require 'csv'

mysql_client_contacts = MySQLHelpers.create_client(:contacts)
contact_client = ContactClient.create_app_client

ids = CSV.read(options[:file]).map(&:first)

escaped_ids = ids.map { |id| '"' + Mysql2::Client.escape(id) + '"' }.join(',')

cnt = 0
deleted_cnt = 0
mysql_client_contacts.query("SELECT * FROM identity WHERE oid = #{options[:oid]} AND type = 1 AND value IN(#{escaped_ids})").each do |identity|
  LOG.info "deleting contact #{identity['contact_id']} from #{identity['oid']}"
  cnt += 1
  deleted_cnt += 1 if contact_client.delete_contact(identity['oid'], identity['contact_id'])
end

puts "records to delete #{cnt}, deleted #{deleted_cnt}, original count #{ids.size}"
