#!/usr/bin/env ruby

# Purpose: to call the contacts API refresh endpoint

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
  opts.add_input_file
end

require_relative '../config/environment'

def contacts_client
  @contacts_client ||= ContactClient.create_client_with_app_creds
end

csv = CSVUtils::CSVIterator.new(SCRIPT_OPTIONS[:file])

missing_headers = ['oid', 'contact_id'] - csv.headers
unless missing_headers.empty?
  LOG.error("missing required headers #{missing_headers.join(', ')}")
  exit 1
end

STATS.total = csv.size

csv.each do |row|
  res = contacts_client.refresh_contact(row['oid'], row['contact_id'])
  STATS.inc_and_notify
  LOG.info("refreshed #{row['contact_id']}, endpoint status code #{res.code}")
end

STATS.notify(true)
