#!/usr/bin/env ruby


require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)
require 'csv'
require 'active_record'

# ActiveRecord::Base.logger = LOG

class EmailCSVRow < ContactDB::Email
  include CSVUtils::CSVRow

  csv_column :id, header: :email_id
  csv_column :oid
  csv_column :contact_id
  csv_column :email
  csv_column :has_contact
  csv_column :created_date
  csv_column :updated_date

  def oid
    contact&.oid
  end

  def has_contact
    contact ? 'Y' : 'N'
  end

  def in_sync?
    identities.any? { |identity| identity.type == 0 && identity.value == email.downcase }
  end

  def created_date
    Time.at(created_at / 1000).strftime('%Y-%m-%d')
  end

  def updated_date
    Time.at(updated_at / 1000).strftime('%Y-%m-%d')
  end
end

stats = Hash.new(0)
stats[:started_at] = Time.now
stats_display_proc = Proc.new do
  stats[:elapsed_time] = (Time.now - stats[:started_at]).to_i
  LOG.info stats.inspect
end

CSVUtils::CSVReport.new("#{ENV['RAILS_ENV']}-emails-not-in-sync-with-identities.csv", EmailCSVRow) do |report|
  EmailCSVRow.includes(:contact, :identities).find_each do |email|
    stats[:cnt] += 1
    stats_display_proc.call if (stats[:cnt] % 10_000) == 0

    unless email.email
      stats[:missing_email] += 1
      report << email
      next
    end

    next if email.in_sync?

    stats[:missing_identity_record] += 1
    report << email
  end
end

stats_display_proc.call
