#!/usr/bin/env ruby

require File.expand_path('../config/environment', __dir__)
require 'csv'

contact_ids = [61574553, 61574813, 61574951, 61575100, 61575363, 61575385, 61575394, 61575412, 61575413, 61575525, 61576230, 61576258, 61576407, 61576414, 61576417, 61576421, 61577443, 61580375, 61580402, 61580403, 61580404, 61580405, 61580406, 61580407, 61580408, 61580409, 61580410, 61580420, 61580428, 61580430, 61580431, 61580437, 61580441, 61580451, 61580452, 61580453, 61580454, 61580491, 61580492, 61580493, 61580494, 61580501, 61582940, 61582941, 61582942, 61582943, 61582944, 61582945, 61582952, 61582953, 61582954, 61582955, 61582956, 61582957, 61582958, 61582959, 61582964, 61582965, 61582966, 61582967, 61582968, 61582969, 61582971, 61584018, 61584060, 61588671, 61588700, 61588705, 61588708, 61588743, 61588745, 61588748, 61589193, 61590065, 61590116, 61590117, 61590118, 61590119, 61590120, 61590121, 61590139, 61590151, 61590186, 61590187, 61590188, 61590189, 61840597, 61840618, 61840621, 61840626, 61840638, 61840666, 61840668, 61840671, 61840674, 61840678, 61840703, 61841121, 61841123, 61841124, 61842291, 61842292, 61842296, 61842310, 61842334, 61842335, 61842358, 61842359, 61842361, 61842363, 61842364, 61842365, 61842366, 61842367, 61842369, 61842370, 61842372, 61842413, 61842414, 61842427, 61842428, 61842433, 61842436, 61843252, 61843269, 61843270, 61843271, 61843272, 61843273, 61843274, 61849163, 61849165, 61849179, 61849180, 61849181, 61849195, 61849201, 61849209, 61849210, 61849211, 61849212, 61849285, 61849308, 61849309, 61849310, 61849311, 61849312, 61849313, 61849321, 61849364, 61849365, 61849366, 61849368, 61849370, 61849378, 61849380, 61850395, 61855008, 61861311, 61861312, 61861341, 61861342, 61861354, 61861356, 61861357, 61865765, 61865766, 61865808, 61865814, 61865834, 61865836, 61994041, 61994042, 61994043, 61994044, 61994045, 61994047, 61994048, 61994049, 61994050, 61994051, 61994054, 61994066, 61994069, 61994072, 61994073, 61994076, 61994078].to_set

CassandraHelpers.logger = LOG
cluster = CassandraHelpers.create_cluster(KS_CONFIG['hosts'])
session = CassandraHelpers.create_session(cluster, 'contacts')

out = CSV.open('contact_identities.csv', 'wb')

out << [ 'value', 'oid', 'type', 'contact_id', 'created_at']

LOG.info "searching for contacts"

CassandraHelpers.each_record(session, 'SELECT * from identity') do |record|
  next unless contact_ids.include?(record['contact_id'])

  LOG.info "found #{record.inspect}"
  out << record.values
end

out.close

LOG.info "done searching for contacts"
