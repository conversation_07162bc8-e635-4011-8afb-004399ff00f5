#!/usr/bin/env ruby

# Purpose: to refresh address census data

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:concurrency] = 10

  opts.add_run_remote
  opts.add_oids_option(false)

  opts.on('--concurrency=NUM_THREADS', Integer, 'The number of threads allowed to execute concurrently, default 10') do |v|
    opts[:concurrently] = v
  end

  opts.on('--start CONTACT_ID', Integer, 'Starting from contact ID') do |v|
    opts[:start_contact_id] = v
  end
end

require_relative '../config/environment'

# This code was developed on Oct 4, 2024
# if rerunning this code in the future.
# this date needs to be updated.
# The idea behind this time is to skip over records that already have new census data.
# So, set this data to a time after you have updated the production census DB
CENSUS_REFRESH_TIME = 1737144000000

STATE_CODE_TO_MODEL = {
  'AL' => CensuDB::STATEAl,
  'AK' => CensuDB::STATEAk,
  'AZ' => CensuDB::STATEAz,
  'AR' => CensuDB::STATEAr,
  'CA' => CensuDB::STATECa,
  'CO' => CensuDB::STATECo,
  'CT' => CensuDB::STATECt,
  'DE' => CensuDB::STATEDe,
  'DC' => CensuDB::STATEDc,
  'FL' => CensuDB::STATEFl,
  'GA' => CensuDB::STATEGa,
  'HI' => CensuDB::STATEHi,
  'ID' => CensuDB::STATEId,
  'IL' => CensuDB::STATEIl,
  'IN' => CensuDB::STATEIn,
  'IA' => CensuDB::STATEIum,
  'KS' => CensuDB::STATEK,
  'KY' => CensuDB::STATEKy,
  'LA' => CensuDB::STATELa,
  'ME' => CensuDB::STATEMe,
  'MD' => CensuDB::STATEMd,
  'MA' => CensuDB::STATEMa,
  'MI' => CensuDB::STATEMi,
  'MN' => CensuDB::STATEMn,
  'MS' => CensuDB::STATEM,
  'MO' => CensuDB::STATEMo,
  'MT' => CensuDB::STATEMt,
  'NE' => CensuDB::STATENe,
  'NV' => CensuDB::STATENv,
  'NH' => CensuDB::STATENh,
  'NJ' => CensuDB::STATENj,
  'NM' => CensuDB::STATENm,
  'NY' => CensuDB::STATENy,
  'NC' => CensuDB::STATENc,
  'ND' => CensuDB::STATENd,
  'OH' => CensuDB::STATEOh,
  'OK' => CensuDB::STATEOk,
  'OR' => CensuDB::STATEOr,
  'PA' => CensuDB::STATEPa,
  'RI' => CensuDB::STATERi,
  'SC' => CensuDB::STATESc,
  'SD' => CensuDB::STATESd,
  'TN' => CensuDB::STATETn,
  'TX' => CensuDB::STATETx,
  'UT' => CensuDB::STATEUt,
  'VT' => CensuDB::STATEVt,
  'VA' => CensuDB::STATEVa,
  'WA' => CensuDB::STATEWa,
  'WV' => CensuDB::STATEWv,
  'WI' => CensuDB::STATEWi,
  'WY' => CensuDB::STATEWy
}

STATE_NAME_TO_CODE = {
  'ALABAMA' => 'AL',
  'ALASKA' => 'AK',
  'ARIZONA' => 'AZ',
  'ARKANSAS' => 'AR',
  'CALIFORNIA' => 'CA',
  'COLORADO' => 'CO',
  'CONNECTICUT' => 'CT',
  'WASHINGTON DC' => 'DC',
  'DELAWARE' => 'DE',
  'FLORIDA' => 'FL',
  'GEORGIA' => 'GA',
  'HAWAII' => 'HI',
  'IDAHO' => 'ID',
  'ILLINOIS' => 'IL',
  'INDIANA' => 'IN',
  'IOWA' => 'IA',
  'KANSAS' => 'KS',
  'KENTUCKY' => 'KY',
  'LOUISIANA' => 'LA',
  'MAINE' => 'ME',
  'MARYLAND' => 'MD',
  'MASSACHUSETTS' => 'MA',
  'MICHIGAN' => 'MI',
  'MINNESOTA' => 'MN',
  'MISSISSIPPI' => 'MS',
  'MISSOURI' => 'MO',
  'MONTANA' => 'MT',
  'NEBRASKA' => 'NE',
  'NEVADA' => 'NV',
  'NEW HAMPSHIRE' => 'NH',
  'NEW JERSEY' => 'NJ',
  'NEW MEXICO' => 'NM',
  'NEW YORK' => 'NY',
  'NORTH CAROLINA' => 'NC',
  'NORTH DAKOTA' => 'ND',
  'OHIO' => 'OH',
  'OKLAHOMA' => 'OK',
  'OREGON' => 'OR',
  'PENNSYLVANIA' => 'PA',
  'RHODE ISLAND' => 'RI',
  'SOUTH CAROLINA' => 'SC',
  'SOUTH DAKOTA' => 'SD',
  'TENNESSEE' => 'TN',
  'TEXAS' => 'TX',
  'UTAH' => 'UT',
  'VERMONT' => 'VT',
  'VIRGINIA' => 'VA',
  'WASHINGTON' => 'WA',
  'WEST VIRGINIA' => 'WV',
  'WISCONSIN' => 'WI',
  'WYOMING' => 'WY'
}

def valid_address_state?(address)
  state = address.state.upcase
  STATE_CODE_TO_MODEL.key?(state) || STATE_NAME_TO_CODE.key?(state)
end

def get_state_census_model(address)
  state = address.state.upcase
  state = STATE_NAME_TO_CODE[state] if STATE_NAME_TO_CODE.key?(state)
  STATE_CODE_TO_MODEL[state]
end

def address_can_be_updated?(address)
  return false unless address.address_geo_coordinates.first
  return false unless address.state

  # skip records that have already been updated
  address_census_datum = address.address_census_data.first
  return false if address_census_datum && address_census_datum.updated_at > CENSUS_REFRESH_TIME

  valid_address_state?(address)
end

def contact_has_addresses_to_update?(contact)
  contact.addresses.any? { |address| address_can_be_updated?(address) }
end

def invalid_census_data?(census)
  census.house_value_lq < 0 &&
    census.house_value_median < 0 &&
    census.house_value_uq < 0 &&
    census.income_median < 0
end

def fetch_census_data(address)
  state_census_model = get_state_census_model(address)
  address_geo_coordinate = address.address_geo_coordinates.first
  lat = address_geo_coordinate.lat
  lng = address_geo_coordinate.lng
  state_census_model
    .select('B25076_001E as house_value_lq, B25077_001E as house_value_median, B25078_001E as house_value_uq, B19013_001E income_median')
    .where("ST_Contains(geom, ST_GeomFromText('POINT(#{lng} #{lat})'))")
    .first
end

def delete_address_census(address)
  address_census_datum = address.address_census_data.first
  return false unless address_census_datum

  address_census_datum.destroy
  STATS.update(:deleted_address_census_data)
  true
end

def update_address_census(address)
  address_geo_coordinate = address.address_geo_coordinates.first
  if address_geo_coordinate.lat.nil? || address_geo_coordinate.lng.nil?
    puts "address #{address.id} has invalid coordinates"
    return false
  end

  unless (census = fetch_census_data(address))
    STATS.update(:no_census_data_for_address)
    return delete_address_census(address)
  end

  if invalid_census_data?(census)
    STATS.update(:invalid_census_data_for_address)
    return delete_address_census(address)
  end

  address_census_datum = address.address_census_data.first || address.address_census_data.new(update_source: 11, private: 0, created_at: (Time.now.to_f * 1000).to_i)
  address_census_datum.house_value_lq = census.house_value_lq > 0 ? census.house_value_lq : nil
  address_census_datum.house_value_median = census.house_value_median > 0 ? census.house_value_median : nil
  address_census_datum.house_value_uq = census.house_value_uq > 0 ? census.house_value_uq : nil
  address_census_datum.income_median = census.income_median > 0 ? census.income_median : nil

  unless address_census_datum.changed?
    STATS.update(:skipping_update_no_change_to_census_data)
    return false
  end

  address_census_datum.updated_at = (Time.now.to_f * 1000).to_i

  address_census_datum.save!
  true
end

update_census_data_worker = WorkerThreads.new(SCRIPT_OPTIONS[:concurrency]) do |queue|
  contacts_client = ContactClient.create_client_with_app_creds

  queue.each do |contact|
    refresh = false
    contact.addresses.each do |address|
      next unless address_can_be_updated?(address)

      if update_address_census(address)
        STATS.update(:updated_addresses)
        refresh = true
      end
    end

    if refresh
      LOG.info("updated contact #{contact.id}/#{contact.oid} address census data")
      attempts = 0
      begin
        contacts_client.refresh_contact(contact.oid, contact.id)
      rescue OpenSSL::SSL::SSLError, Net::OpenTimeout => e
        LOG.error("an exception occurred trying to refresh contact #{contact.id}/#{contact.oid}, #{e}")
        attempts += 1
        raise(e) if attempts >= 10
        sleep(0.1)
        retry
      end
      STATS.update(:refreshed_contacts)
    end
  end
end

update_census_data_worker.max_size = 100_000

contacts = if SCRIPT_OPTIONS[:oids]
             ContactDB::Contact.where(oid: SCRIPT_OPTIONS[:oids])
           else
             ContactDB::Contact
           end

STATS.total = contacts.count

contacts.includes(addresses: [:address_geo_coordinates, :address_census_data]).find_each(start: SCRIPT_OPTIONS[:start_contact_id]) do |contact|
  STATS.inc_and_notify

  next unless contact_has_addresses_to_update?(contact)

  update_census_data_worker.enq(contact)
end

update_census_data_worker.shutdown

STATS.notify(true)
