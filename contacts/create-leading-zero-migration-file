#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_run_remote

  opts.on('-l', '--length ID_LENGTH', Integer, 'Static length of remote ids') do |v|
    opts.options[:id_length] = v
  end

  opts.require_option(:id_length)
end

require File.expand_path('../config/environment', __dir__)
require 'csv'

organization = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

class IdentityMigration < ContactDB::Identity
  include CSVUtils::CSVRow

  csv_column :value, header: :old_id
  csv_column :value_with_zeros, header: :new_id

  def value_with_zeros
    IDHelper.pad_with_zeros(value, SCRIPT_OPTIONS[:id_length])
  end
end

base_dir = TMP_DIR.join('contacts', 'orgs')
FileUtils.mkdir_p(base_dir)
csv_file = base_dir.join("#{organization.slug}-#{organization.id}-remote-id-leading-zero-migration-#{Time.now.strftime('%Y-%m-%d')}.csv").to_s

CSVUtils::CSVReport.new(csv_file) do |report|
  report.add_headers IdentityMigration

  IdentityMigration.where(oid: SCRIPT_OPTIONS[:oid], type: 1).find_each do |identity|
    report << identity
  end
end

RepairshopFileUploader.upload_file_and_notify(csv_file)
