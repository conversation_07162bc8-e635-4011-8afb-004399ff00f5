#!/usr/bin/env ruby


require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)
require 'csv'
require 'active_record'

# ActiveRecord::Base.logger = LOG

class IdentityCSVRow < ContactDB::Identity
  include CSVUtils::CSVRow

  csv_column :id, header: :identity_id
  csv_column :oid
  csv_column :contact_id
  csv_column :value, header: :email
  csv_column :has_contact
  csv_column :created_date
  csv_column :updated_date

  def has_contact
    contact ? 'Y' : 'N'
  end

  def in_sync?
    emails.any? { |email| email.email&.downcase == value.downcase }
  end

  def created_date
    Time.at(created_at / 1000).strftime('%Y-%m-%d')
  end

  def updated_date
    Time.at(updated_at / 1000).strftime('%Y-%m-%d')
  end
end

stats = Hash.new(0)
stats[:started_at] = Time.now
stats_display_proc = Proc.new do
  stats[:elapsed_time] = (Time.now - stats[:started_at]).to_i
  LOG.info stats.inspect
end

CSVUtils::CSVReport.new("#{ENV['RAILS_ENV']}-identities-not-in-sync-with-emails.csv", IdentityCSVRow) do |report|
  IdentityCSVRow.where(type: 0).includes(:contact, :emails).find_each do |identity|
    stats[:cnt] += 1
    stats_display_proc.call if (stats[:cnt] % 10_000) == 0

    next if identity.in_sync?

    stats[:missing_email_record] += 1
    report << identity
  end
end

stats_display_proc.call
