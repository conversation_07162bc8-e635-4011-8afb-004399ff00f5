#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
end

require File.expand_path('../config/environment', __dir__)
require 'csv'

auth_client = MySQLHelpers.create_client(:auth)

organization = auth_client.query("SELECT * FROM organizations WHERE id = #{SCRIPT_OPTIONS[:oid]}").first

raise("no organization found with id #{SCRIPT_OPTIONS[:oid]}") unless organization

contact_client = MySQLHelpers.create_client(:contacts)

export_file = "#{organization['slug']}-#{SCRIPT_OPTIONS[:oid]}-remote-ids-#{Time.now.strftime('%Y-%m-%d')}.csv"

CSV.open(export_file, 'wb') do |out|
  out << ['remote_id']

  contact_client.query("SELECT * FROM identity WHERE oid = #{SCRIPT_OPTIONS[:oid]} AND type = 1").each do |result|
    out << [result['value']]
  end
end
