
How an organization remote ids are migrated.


# Obtain the remote id migration file

The process starts by having a CSV file with the old_id and new_id.

If the organization is asking us to create the CSV file and their remote ids have a static length.

Use:

./contacts/create-leading-zero-migration-file -e prod -o <oid> -l <remote id length>

ex: ./contacts/create-leading-zero-migration-file -e prod -o 616 -l 10 --slack-notify

The above command scans the identity table creating a CSV file with old_ids (identity.value) and new_ids (identity.value with leading zeros).

The file will be in the base directory and look like

<slug>-<oid>-remote-id-leading-zero-migration-<date>.csv

ex: umassmed-616-remote-id-leading-zero-migration-2020-09-04.csv


# Import the remote id migration file

Now that we have the rmeote id migration file.  We need to load it into the identity_conversion table.

Use:

./contacts/create-remote-id-migration  -e prod -o <oid> <remote id migration file>

ex: ./contacts/create-remote-id-migration  -e prod -o 616 umassmed-616-remote-id-leading-zero-migration-2020-09-04.csv --slack-notify


# Swap remote ids

Time to change the remote ids.  The process works by first removing the old remote_id and then adding the new one.

Use:

./contacts/swap-remote-ids -e prod -o 616 -m 1599177600000

The -m is the migration timestamp.  It is just the timestamp at the start of the day.
It is there incase we need to run additional migrations in the future for the org.

To get the migration timestamp I use

./bin/mysql-connect prod contacts -e 'select * from identity_conversion where oid = 616 order by id desc limit 1'

## On Failure(s)

Swapping remote ids can lead to API failures.  It's possible their is an import that starts while ids are being swapped.

If this occurred, you can rerun the command with -f to fix any issues.

Will cleanup any failed delete remote id requests
Will ensure the remote id is assigned to the correct conatct (delete the new contact, assigne remote id to the old)

./contacts/swap-remote-ids -e prod -o 616 -m 1599177600000 -f --slack-notify

# Update DNA Settings

before or after you start the migration process, update the DNA settings

./bin/console -e prod

client = DNAClient.create_client_with_app_creds
# tells the system that this org has leadign 0s in their contact remote ids
client.update_setting_value(DNAClient::KEEP_LEADING_ZEROS, true, oid: 616)
# tells the system that this org's remote ids are static length, in this case 10
client.update_setting_value(DNAClient::LEADING_ZERO_PADDING_LENGTH, 10, oid: 616)

# Fix importer.presence

The importer use a table called presence for pruning logic.  It's important to remove all the existing records and sync new remote ids.

./bin/mysql-connect prod importer -e 'delete from presence where oid = 616'

Use:

./importer/sync-presence -e prod -o 616


# Update importer mapping hash(es)

This forces a reimport of any data on their end

Generate a timestamp, I use irb with

(Time.now.to_f * 1000).to_i

./bin/mysql-connect prod importer -e 'update mapping_hash set updated_at = <value> where oid = 616'


# Fix affiliations

The remote_user_id(s) affiliated to the org need to be the same value as the contact's remote_id.
Otherwise things like LaunchPad will not work correctly.

This is a manual process at the moment.  I use this SQL to find all remote_user_id(s).

select id, remote_user_id from affiliations where organization_id = 616 and remote_user_id is not null;

If it is a leading zero migration, make sure the remote_user_id are the correct lenght, do not contain spaces or any extra characters.

If not, attempt to find the remote_user_id in the contacts.identity, by search for it with leading zeros.

If found update the remote_user_id using the affiliation API.  DO NOT EDIT IN MYSQL DIRECTLY.

I use the repair shop console

./bin/console -e prod
auth_client = AuthClient.create_client_with_app_creds
auth_client.update_affiliation(oid, affiliation.id, {remote_user_id: "<identity.value>", skip_roles_overwrite: true})

# Fix hub solicitor goals

Find all the goals for the org.

select solicitor_remote_id, solicitor_contact_id from solicitor_goals where oid = 616;

And make sure the solicitor_remote_id matches the value in contacts.identity.

On any mismatch, you can just update the record in mysql.


# Fix interaction solicitors

Currently we ue the affiliations remote_user_id with interaction solicitors.

This script will go through all interactions, fixing any mismatching values.

./ugc/fix-interaction-solicitors -e prod -o 616 --slack-notify


# Fix proposal solicitors

This script fixes remote_user_id and the user_id value if needed.

./ugc/fix-proposal-solicitors -e prod -o 616 --slack-notify


