#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)
require 'csv'
require 'active_record'

# ActiveRecord::Base.logger = LOG

class EmailCSVRow < ContactDB::Email
  include CSVUtils::CSVRow

  csv_column :id, header: :email_id
  csv_column :oid
  csv_column :contact_id
  csv_column :email
  csv_column :has_contact
  csv_column :created_date
  csv_column :updated_date

  def oid
    contact&.oid
  end

  def has_contact
    contact ? 'Y' : 'N'
  end

  def in_sync?
    identities.any? { |identity| identity.type == 0 && identity.value == email.downcase }
  end

  def created_date
    Time.at(created_at / 1000).strftime('%Y-%m-%d')
  end

  def updated_date
    Time.at(updated_at / 1000).strftime('%Y-%m-%d')
  end
end

contacts_client = ContactClient.create_client_with_app_creds

STATS.total = EmailCSVRow.count

CSVUtils::CSVReport.new("#{ENV['RAILS_ENV']}-added-emails-not-in-sync-with-identities-#{Time.now.strftime('%Y-%m-%dT%H%M')}.csv", EmailCSVRow) do |report|
  EmailCSVRow.includes(:contact, :identities).find_each do |email|
    STATS.inc_and_notify

    unless email.email
      STATS[:missing_email] += 1
      next
    end

    next if email.in_sync?

    unless email.contact
      STATS[:missing_contact] += 1
      next
    end

    unless email.email.include?('@')
      STATS[:email_missing_at_symbol] += 1
      next
    end

    STATS[:missing_identity_record] += 1

    if contacts_client.add_email_identity(email.oid, email.contact_id, email.email.downcase)
      STATS[:added_identity_record] += 1
      LOG.info("added email #{email.id} to contact #{email.contact_id}")
      report << email
    else
      STATS[:added_identity_record_failures] += 1
    end
  end
end

STATS.notify
