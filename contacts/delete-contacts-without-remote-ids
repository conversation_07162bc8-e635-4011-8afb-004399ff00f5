#!/usr/bin/env ruby

# Purpose is to delete identity records that do not have a corresponding contact record

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_run_remote
end

require File.expand_path('../config/environment', __dir__)
require 'active_record'

ActiveRecord::Base.logger = LOG

def contact_client
  @contact_client ||= ContactClient.create_client_with_app_creds
end

contacts = ContactDB::Contact.joins('LEFT JOIN identity ON identity.contact_id = contact.id AND identity.type = 1').where(oid: SCRIPT_OPTIONS[:oid]).where('identity.id IS NULL').to_a

affiliations = AuthDB::Affiliation.where(contact_id: contacts.map(&:id)).to_a.index_by(&:contact_id)

contacts.each do |contact|
  if (affiliation = affiliations[contact.id])
    LOG.info("skipping, contact #{contact.id}/#{contact.oid} is in use by affiliation #{affiliation.id}")
    next
  end

  res = contact_client.fetch_contact_by_id(contact.oid, contact.id)
  LOG.info("deleting contact #{contact.id}/#{contact.oid}: #{res.body}")

  contact_client.delete_contact(contact.oid, contact.id)
end
