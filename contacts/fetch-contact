#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-c', '--contact [CONTACT_ID]', Integer, 'Contact ID') do |v|
    opts[:contact_id] = v
  end

  opts.require_option(:contact_id)
end

require File.expand_path('../config/environment', __dir__)

contact = ContactDB::Contact.find(SCRIPT_OPTIONS[:contact_id])

contact_client = ContactClient.create_client_with_app_creds

response = contact_client.fetch_contact_by_id(contact.oid, contact.id)

print JSON.pretty_generate(JSON.parse(response.body)) + "\n"
