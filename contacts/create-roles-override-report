#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)

ROLES = AuthDB::Role.all.each_with_object({}) { |role, hsh| hsh[role.id.to_s] = role.name }

class ContactCSVRow < ContactDB::Contact
  include CSVUtils::CSVRow

  csv_column(:id, header: :contact_id)
  csv_column(:oid)
  csv_column(:roles_override)
  csv_column(:roles_override_details)

  def roles_override_details
    roles_override.split(',').map { |role_id| ROLES[role_id] }.join(', ')
  end
end

CSVUtils::CSVReport.new('roles_override.csv', ContactCSVRow) do |report|
  ContactCSVRow.where('roles_override IS NOT NULL && roles_override != ""').each do |contact|
    report << contact
  end
end
