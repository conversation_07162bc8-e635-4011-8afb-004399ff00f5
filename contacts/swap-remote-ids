#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.options[:fix_remote_ids] = false
  opts.options[:dry_run] = false
  opts.add_oid_option
  opts.add_run_remote

  opts.on('-m', '--migration-timestamp [MIGRATION_TIMESTAMP]', 'Migration timestamp from the identity_conversion table') do |v|
    opts.options[:migration_timestamp] = v
  end

  opts.on('-f', '--fix-remote-ids', 'Repair remote ids that failed on the first pass (will reassign the remote_id back to the original contact)') do
    opts.options[:fix_remote_ids] = true
  end

  opts.on('-d', '--dry-run', 'Dry run do nothing but log') do
    opts.options[:dry_run] = true
  end

  opts.require_option(:migration_timestamp)
end

require File.expand_path('../config/environment', __dir__)

def contact_client
  @contact_client ||= ContactClient.create_client_with_app_creds
end

def with_retries(max_attempts = 5, sleep_for = 1)
  max_attempts.times do
    return true if yield
    LOG.info("sleeping for #{sleep_for} seconds")
    STATS.update(:retries)
    sleep(sleep_for)
  end

  false
end

def add_remote_id(contact, remote_id)
  if SCRIPT_OPTIONS[:dry_run]
    LOG.info("added remote_id #{remote_id} to contact #{contact.id}")
    STATS.update(:added_remote_ids)
    return true
  end

  with_retries do
    res = contact_client.add_remote_id_identity(contact.oid, contact.id, remote_id)

    if res.kind_of?(Net::HTTPNoContent)
      LOG.info("added remote_id #{remote_id} to contact #{contact.id}")
      STATS.update(:added_remote_ids)
      true
    elsif res.kind_of?(Net::HTTPNotFound)
      LOG.info("contact #{contact.id} already has remote_id #{remote_id}")
      STATS.update(:already_added_remote_ids)
      true
    else
      LOG.error("unhandled add remote_id response #{res} for remote_id #{remote_id} and contact #{contact.id}")
      STATS.update(:unhandled_add_identity_response)
      false
    end
  end
end

def delete_remote_id(contact, remote_id)
  if SCRIPT_OPTIONS[:dry_run]
    LOG.info("deleteed remote_id #{remote_id} to contact #{contact.id}")
    STATS.update(:deleteed_remote_ids)
    return true
  end

  with_retries do
    res = contact_client.delete_remote_id_identity(contact.oid, contact.id, remote_id)

    if res.kind_of?(Net::HTTPNoContent)
      LOG.info("deleteed remote_id #{remote_id} to contact #{contact.id}")
      STATS.update(:deleteed_remote_ids)
      true
    elsif res.kind_of?(Net::HTTPNotFound)
      LOG.info("contact #{contact.id} already has remote_id #{remote_id}")
      STATS.update(:already_deleteed_remote_ids)
      true
    else
      LOG.error("unhandled delete remote_id response #{res} for remote_id #{remote_id} and contact #{contact.id}")
      STATS.update(:unhandled_delete_identity_response)
      false
    end
  end
end

def delete_contact(contact)
  if SCRIPT_OPTIONS[:dry_run]
    STATS.update(:deleted_contact)
    return true
  end

  unless SCRIPT_OPTIONS[:fix_remote_ids]
    LOG.info("skipping, delete contact #{contact.id}")
    STATS.update(:skipping_delete_contact)
    return true
  end

  with_retries do
    if contact_client.delete_contact(contact.oid, contact.id)
      STATS.update(:deleted_contact)
      true
    else
      STATS.update(:delete_contact_failures)
      false
    end
  end
end

def refresh_contact(contact)
  if SCRIPT_OPTIONS[:dry_run]
    LOG.info("refreshed contact #{contact.id}")
    STATS.update(:refreshed_contact)
    return true
  end

  with_retries do
    res = contact_client.refresh_contact(contact.oid, contact.id)

    if res.kind_of?(Net::HTTPAccepted)
      LOG.info("refreshed contact #{contact.id}")
      STATS.update(:refreshed_contact)
      true
    else
      LOG.error("unhandled refresh contact response #{res} for contact #{contact.id}")
      STATS.update(:unhandled_delete_identity_response)
      false
    end
  end
end

def update_identity_with_remote_id(identity, remote_id)
  old_value = identity.value
  identity.value = remote_id

  if SCRIPT_OPTIONS[:dry_run]
    LOG.info("changed identity #{identity.id} remote_id from #{old_value} to #{identity.value}")
    STATS.update(:updated_identity_with_remote_id)
    return true
  end

  begin
    identity.save!
    LOG.info("changed identity #{identity.id} remote_id from #{old_value} to #{identity.value}")
    STATS.update(:updated_identity_with_remote_id)
    true
  rescue Exception => e
    LOG.error("failed to changed identity #{identity.id} remote_id from #{old_value} to #{identity.value}, exception #{e.inspect}")
    STATS.update(:failed_to_update_identity_with_remote_id)
    false
  end
end

def change_identity_contact(identity, contact)
  old_contact_id = identity.contact_id
  identity.contact_id = contact.id

  if SCRIPT_OPTIONS[:dry_run]
    LOG.info("changed identity #{identity.id} contact_id from #{old_contact_id} to #{identity.contact_id}")
    STATS.update(:changed_identity_contact)
    return true
  end

  unless SCRIPT_OPTIONS[:fix_remote_ids]
    LOG.info("skipping, change identity #{identity.id} contact_id from #{old_contact_id} to #{identity.contact_id}")
    STATS.update(:skipping_change_identity_contact)
    return true
  end

  begin
    identity.save!
    LOG.info("changed identity #{identity.id} contact_id from #{old_contact_id} to #{identity.contact_id}")
    STATS.update(:changed_identity_contact)
    refresh_contact(contact)
    true
  rescue Exception => e
    LOG.error("failed to changed identity #{identity.id} contact_id from #{old_contact_id} to #{identity.contact_id}, exception #{e.inspect}")
    STATS.update(:failed_to_change_identity_contact)
    false
  end
end

def delete_identity(identity)
  if SCRIPT_OPTIONS[:dry_run]
    LOG.info("deleted identity #{identity.id} for contact_id #{identity.contact_id} with remote_id #{identity.value}")
    STATS.update(:deleted_identity)
    return true
  end

  unless SCRIPT_OPTIONS[:fix_remote_ids]
    LOG.info("skipping, deleted identity #{identity.id} for contact_id #{identity.contact_id} with remote_id #{identity.value}")
    STATS.update(:skipping_delete_identity)
    return true
  end

  begin
    identity.destroy!
    LOG.info("deleted identity #{identity.id} for contact_id #{identity.contact_id} with remote_id #{identity.value}")
    STATS.update(:deleted_identity)
    true
  rescue Exception => e
    LOG.info("failed to deleted identity #{identity.id} for contact_id #{identity.contact_id} with remote_id #{identity.value}, exception #{e.inspect}")
    STATS.update(:failed_to_delete_identity)
    false
  end
end

def reassign_remote_id(from_contact, to_contact, remote_id)
  unless SCRIPT_OPTIONS[:fix_remote_ids]
    LOG.info("skipping, reassign remote_id #{remote_id} from_contact #{from_contact.id} to_contact #{to_contact.id}")
    STATS.update(:skipping_reassign_remote_id)
    return true
  end

  LOG.info("reassign remote_id #{remote_id} from_contact #{from_contact.id} to_contact #{to_contact.id}")

  unless delete_remote_id(from_contact, remote_id)
    STATS.update(:failed_to_reassign_remote_id_action_delete_remote_id)
    return false
  end

  unless add_remote_id(to_contact, remote_id)
    STATS.update(:failed_to_reassign_remote_id_action_add_remote_id)
    return false
  end

  if delete_contact(from_contact)
    STATS.update(:reassigned_remote_ids)
    true
  else
    STATS.update(:failed_to_reassign_remote_id_action_delete_contact)
    false
  end
end

identity_conversion_scope = ContactDB::IdentityConversion.where(
  oid: SCRIPT_OPTIONS[:oid],
  migration_timestamp: SCRIPT_OPTIONS[:migration_timestamp],
  marked_for_deletion: 0
).where('value_old != value_new')

STATS.total = identity_conversion_scope.count

identity_conversion_scope.find_in_batches(batch_size: 10_000) do |batch|
  contacts = ContactDB::Contact.where(id: batch.map(&:contact_id)).index_by(&:id)
  old_identities = ContactDB::Identity.where(oid: SCRIPT_OPTIONS[:oid], type: 1, value: batch.map(&:value_old)).to_a
  new_identities = ContactDB::Identity.where(oid: SCRIPT_OPTIONS[:oid], type: 1, value: batch.map(&:value_new)).to_a
  old_identities_by_contact = old_identities.index_by(&:contact_id)
  new_identities_by_contact = new_identities.index_by(&:contact_id)
  old_identities_by_remote_id = old_identities.index_by(&:value)
  new_identities_by_remote_id = new_identities.index_by(&:value)

  Parallel.each(batch, in_threads: 20) do |identity_conversion|
    STATS.inc_and_notify

    if (contact = contacts[identity_conversion.contact_id])
      # contact exists

      if new_identities_by_contact.key?(identity_conversion.contact_id) &&
         !old_identities_by_contact.key?(identity_conversion.contact_id)
        # identity conversion already occurred
        STATS.update(:already_updated_remote_id)
        next
      end

      if !new_identities_by_contact.key?(identity_conversion.contact_id)
        # contact is missing new remote_id

        if (new_identity = new_identities_by_remote_id[identity_conversion.value_new])
          # another contact is using this remote_id
          if new_identity.contact
            reassign_remote_id(new_identity.contact, contact, identity_conversion.value_new)
          else
            change_identity_contact(new_identity, contact)
          end
        else
          add_remote_id(contact, identity_conversion.value_new)
        end
      end

      if old_identities_by_contact.key?(identity_conversion.contact_id)
        # old remote_id is still associated to contact
        delete_remote_id(contact, identity_conversion.value_old)
      end
    else
      # contact doesn't exists

      if (old_identity = old_identities_by_contact[identity_conversion.contact_id])
        # old_identity exists
        if new_identities_by_remote_id.key?(identity_conversion.value_new)
          # been assigned to another contact, delete this one
          delete_identity(old_identity)
        else
          # change the remote_id in the database
          update_identity_with_remote_id(old_identity, identity_conversion.value_new)
        end
      else
        STATS.update(:already_updated_identity)
      end
    end
  end
end

STATS.notify
