#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oids_option(false)
  opts.add_run_remote
end

require File.expand_path('../config/environment', __dir__)

def contact_client
  @contact_client ||= ContactClient.create_client_with_app_creds
end

oids = SCRIPT_OPTIONS[:oids] || AuthDB::Organization.select(:id).where(deleted: false).to_a.map(&:id)

oids.each do |oid|
  res = contact_client.update_properties_schema(oid)
  if res.kind_of?(Net::HTTPSuccess)
    puts "Updated schema for oid #{oid}"
  else
    puts "Failed to update schema for oid #{oid} #{res.body}"
  end
end
