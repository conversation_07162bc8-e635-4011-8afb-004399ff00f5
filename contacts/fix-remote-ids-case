#!/usr/bin/env ruby

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_run_remote
  opts.add_input_file
end

require_relative '../config/environment'

def contact_client
  @contact_client ||= ContactClient.create_client_with_app_creds
end

csv = CSVUtils::CSVIterator.new(SCRIPT_OPTIONS[:file])
raise('missing remote_id column') unless csv.headers.include?('remote_id')

# Ensuring the org exists
org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

remote_ids = []
fix_remote_ids_proc = proc do
  identities = ContactDB::Identity.includes(:contact).where(oid: org.id, type: 1, value: remote_ids).index_by { |i| i.value.downcase }

  remote_ids.each do |remote_id|
    identity = identities[remote_id.downcase]

    if identity.nil?
      LOG.info("No identity found for #{remote_id} in oid #{org.id}")
      STATS.update(:missing_identities)
      next
    end

    if identity.value == remote_id
      STATS.update(:same_remote_ids)
      next
    end

    if identity.contact.nil?
      LOG.warn("Contact record #{identity.contact_id}/#{identity.oid} is missing, updating DB directly.  Changing remote ID from #{identity.value} to #{remote_id}")
      STATS.update(:missing_contacts)
      identity.value = remote_id
      identity.save!
      next
    end

    LOG.info("Changing the contact #{identity.contact_id}/#{identity.oid} remote ID from #{identity.value} to #{remote_id}")
    contact_client.delete_remote_id_identity(identity.oid, identity.contact_id, identity.value)
    contact_client.add_remote_id_identity(identity.oid, identity.contact_id, remote_id)
    STATS.update(:updated_remote_ids)
  end

  remote_ids = []
end

STATS.total = csv.size

csv.each do |row|
  STATS.inc_and_notify

  remote_ids << row['remote_id']

  fix_remote_ids_proc.call() if remote_ids.size >= 100
end

fix_remote_ids_proc.call() if remote_ids.size > 0

STATS.notify(true)
