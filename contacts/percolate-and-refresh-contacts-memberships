#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_run_remote
  opts.add_scheduling_options
end

require File.expand_path('../config/environment', __dir__)

def contact_client
  @contact_client ||= ContactClient.create_client_with_app_creds
end

STATS.total = ContactDB::Contact.where(oid: SCRIPT_OPTIONS[:oid]).count

ContactDB::Contact.where(oid: SCRIPT_OPTIONS[:oid]).find_in_batches(batch_size: 10_000) do |batch|
  batch.in_groups_of(100, false) do |contacts|
    STATS.inc_and_notify(contacts.size)
    contact_client.percolate(SCRIPT_OPTIONS[:oid], contacts.map(&:id))
  end
end

STATS.notify(true)
