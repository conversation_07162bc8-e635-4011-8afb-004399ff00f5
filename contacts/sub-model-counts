#!/usr/bin/env ruby

# Purpose: displays number of records per a contact sub model, ex: emails, phones, ...

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
end

require_relative '../config/environment'

sub_models = [
  :addresses,
  :constituencies,
  :contact_attributes,
  :educations,
  :extracurricular_activities,
  :emails,
  :employments,
  :givings,
  :households,
  :phones,
  :relationships,
  :solicitation_codes,
  :sports
]

contact_scope = ContactDB::Contact.where(oid: SCRIPT_OPTIONS[:oid])
counts = {contacts: contact_scope.count}
sub_models.each do |sub_model|
  counts[sub_model] = contact_scope.joins(sub_model).count
end

print JSON.pretty_generate(counts) + "\n"
