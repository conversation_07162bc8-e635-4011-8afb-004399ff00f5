#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_run_remote
end

require File.expand_path('../config/environment', __dir__)

def contact_client
  @contact_client ||= ContactClient.create_client_with_app_creds
end

contact_assignees = ContactDB::Assignee.joins(:contact).where(contact: {oid: SCRIPT_OPTIONS[:oid]}).to_a.group_by(&:contact_id)

contact_assignees.each do |contact_id, assignees|
  contact = JSON.parse(contact_client.fetch_contact_by_id(SCRIPT_OPTIONS[:oid], contact_id).body)

  contact_without_assignees = contact.slice('id', 'private', 'updated_at', 'assignees')
  contact_without_assignees['assignees'].each do |assignee|
    assignee['properties'].each do |property|
      property['deleted'] = true
    end
  end

  LOG.info("deleting assignees for #{contact_id}")
  contact_client.update_contact(SCRIPT_OPTIONS[:oid], contact_id, contact_without_assignees)
end
