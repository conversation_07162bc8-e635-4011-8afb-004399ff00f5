#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-l', '--length ID_LENGTH', Integer, 'Static length of remote ids') do |v|
    opts.options[:id_length] = v
  end

  opts.require_option(:id_length)
end

require File.expand_path('../config/environment', __dir__)
require 'active_record'
require 'csv'

ActiveRecord::Base.logger = LOG

organization = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

max_relationships = ContactDB::Relationship
                      .joins(:contact)
                      .where('contact.oid = ?', organization.id)
                      .select('contact_id, count(*) as cnt')
                      .group('contact_id')
                      .order('cnt desc')
                      .first
                      .cnt

csv_file = "#{organization.slug}-relationship-remote-id-updates-by-evertrue-#{Time.now.strftime('%Y-%m-%d')}.csv"

out = CSV.open(csv_file, 'wb')

write_contact_relationships_proc = Proc.new do |relationships|
  row = [relationships.first.contact_remote_id] + relationships.map { |relationship| [relationship.type, relationship.name, relationship.remote_id ? IDHelper.pad_with_zeros(relationship.remote_id, SCRIPT_OPTIONS[:id_length]) : nil] }.flatten + ([nil, nil, nil] * (max_relationships - relationships.size)).flatten
  out << row
end

headers = ['PersonID'] + max_relationships.times.map { |idx| ["Rel#{idx +1}Type", "Rel#{idx +1}Name", "Rel#{idx +1}ID"] }.flatten

out << headers


ContactDB::Relationship.joins(contact: :identities).where('contact.oid = ?', organization.id).where('identity.type = 1').select('relationship.*, identity.value as contact_remote_id').group_by(&:contact_id).each do |contact_id, relationships|
  write_contact_relationships_proc.call relationships
end

out.close
