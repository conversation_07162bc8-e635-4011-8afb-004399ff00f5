#!/usr/bin/env ruby

# Purpose is to create a V3 relationships import files

require_relative '../../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-b', '--backup-host MYSQL_BACKUP_HOST', 'Specify a MySQL backup host') do |v|
    opts[:backup_host] = v
  end
end

require_relative '../../../config/environment'

def contact_db_backup
  @contact_db_backup ||= DynamicActiveModelGenerator.create_database_models_for_backup('contacts', SCRIPT_OPTIONS[:backup_host])
end

def base_contact_class
  @base_contact_class ||=
    begin
      if SCRIPT_OPTIONS[:backup_host]
        contact_db_backup()
        ContactsBackupDB::Contact
      else
        ContactDB::Contact
      end
    end
end

def get_contact_class(table_name)
  table_name = table_name.to_s

  models =
    if SCRIPT_OPTIONS[:backup_host]
      contact_db_backup.models
    else
      ContactDB
      CONTACTS_DATABASE.models
    end

  models.detect { |m| m.table_name == table_name }
end

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

LOG.info("creating import relationships file for #{org.name}/#{org.id}")

class RelationshipCSVRow < get_contact_class(:relationship)
  include CSVUtils::CSVRow

  attr_accessor :contact_remote_id

  csv_column('REMOTE_ID') { contact_remote_id }
  csv_column('REL_RELATIONSHIP_TYPE') { type }
  csv_column('REL_NAME_FULL') { name }
  csv_column('REL_ID') { remote_id }
end

class Contact < base_contact_class
  has_many :relationships, class_name: 'RelationshipCSVRow'

  def remote_id
    identities.detect { |identity| identity.type == 1 }.try(:value)
  end
end

path = "#{org.slug}-contact-relationships-#{Time.now.strftime('%Y-%m-%d')}.csv"

CSVUtils::CSVReport.new(path, RelationshipCSVRow) do |report|
  Contact.includes(:identities, :relationships).where(oid: org.id).find_each do |contact|
    contact.relationships.each do |relationship|
      relationship.contact_remote_id = contact.remote_id
      STATS.inc_and_notify
      report << relationship
    end
  end
end

STATS.notify(true)

puts "created: #{path}"
