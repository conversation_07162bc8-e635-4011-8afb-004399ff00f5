#!/usr/bin/env ruby

# Purpose is to create a V3 identity import files

require_relative '../../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-b', '--backup-host MYSQL_BACKUP_HOST', 'Specify a MySQL backup host') do |v|
    opts[:backup_host] = v
  end
end

require_relative '../../../config/environment'

def contact_db_backup
  @contact_db_backup ||= DynamicActiveModelGenerator.create_database_models_for_backup('contacts', SCRIPT_OPTIONS[:backup_host])
end

def base_contact_class
  @base_contact_class ||=
    begin
      if SCRIPT_OPTIONS[:backup_host]
        contact_db_backup()
        ContactsBackupDB::Contact
      else
        ContactDB::Contact
      end
    end
end

def get_contact_class(table_name)
  table_name = table_name.to_s

  models =
    if SCRIPT_OPTIONS[:backup_host]
      contact_db_backup.models
    else
      ContactDB
      CONTACTS_DATABASE.models
    end

  models.detect { |m| m.table_name == table_name }
end

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

LOG.info("creating import sports file for #{org.name}/#{org.id}")

class SportCSVRow < get_contact_class(:sport)
  include CSVUtils::CSVRow

  attr_accessor :remote_id

  csv_column('REMOTE_ID') { remote_id }
  csv_column('SPORT') { name }
end

class Contact < base_contact_class
  has_many :sports, class_name: 'SportCSVRow'

  def remote_id
    identities.detect { |identity| identity.type == 1 }.try(:value)
  end
end

path = "#{org.slug}-contact-sports-#{Time.now.strftime('%Y-%m-%d')}.csv"

CSVUtils::CSVReport.new(path, SportCSVRow) do |report|
  Contact.includes(:identities, :sports).where(oid: org.id).find_each do |contact|
    contact.sports.each do |sport|
      STATS.inc_and_notify
      sport.remote_id = contact.remote_id
      report << sport
    end
  end
end

STATS.notify

puts "created: #{path}"
