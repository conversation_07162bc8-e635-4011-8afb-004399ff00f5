#!/usr/bin/env ruby

# Purpose is to create a V3 addresses import files

require_relative '../../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-b', '--backup-host MYSQL_BACKUP_HOST', 'Specify a MySQL backup host') do |v|
    opts[:backup_host] = v
  end
end

require_relative '../../../config/environment'

def contact_db_backup
  @contact_db_backup ||= DynamicActiveModelGenerator.create_database_models_for_backup('contacts', SCRIPT_OPTIONS[:backup_host])
end

def base_contact_class
  @base_contact_class ||=
    begin
      if SCRIPT_OPTIONS[:backup_host]
        contact_db_backup()
        ContactsBackupDB::Contact
      else
        ContactDB::Contact
      end
    end
end

def get_contact_class(table_name)
  table_name = table_name.to_s

  models =
    if SCRIPT_OPTIONS[:backup_host]
      contact_db_backup.models
    else
      ContactDB
      CONTACTS_DATABASE.models
    end

  models.detect { |m| m.table_name == table_name }
end

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

LOG.info("creating import addresses file for #{org.name}/#{org.id}")

class AddressCSVRow < get_contact_class(:address)
  include CSVUtils::CSVRow

  attr_accessor :remote_id

  csv_column('REMOTE_ID') { remote_id }
  csv_column('ADDRESS_TYPE') { type }
  csv_column('ADDRESS_LINE1') { address_1 }
  csv_column('ADDRESS_LINE2') { address_2 }
  csv_column('ADDRESS_LINE3') { address_3 }
  csv_column('ADDRESS_CITY') { city }
  csv_column('ADDRESS_STATE') { state }
  csv_column('ADDRESS_ZIP') { zip_code }
  csv_column('ADDRESS_COUNTRY') { country }
  csv_column('ADDRESS_IS_PRIMARY') { primary? ? 'Y' : 'N' }
end

class Contact < base_contact_class
  has_many :addresses, class_name: 'AddressCSVRow'

  def remote_id
    identities.detect { |identity| identity.type == 1 }.try(:value)
  end
end

path = "#{org.slug}-contact-addresses-#{Time.now.strftime('%Y-%m-%d')}.csv"

CSVUtils::CSVReport.new(path, AddressCSVRow) do |report|
  Contact.includes(:identities, :addresses).where(oid: org.id).find_each do |contact|
    contact.addresses.each do |address|
      STATS.inc_and_notify
      address.remote_id = contact.remote_id
      report << address
    end
  end
end

STATS.notify

puts "created: #{path}"
