#!/usr/bin/env ruby

# Purpose is to create a V3 custom properties import files

require_relative '../../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-b', '--backup-host MYSQL_BACKUP_HOST', 'Specify a MySQL backup host') do |v|
    opts[:backup_host] = v
  end
end

require_relative '../../../config/environment'

def contact_db_backup
  @contact_db_backup ||= DynamicActiveModelGenerator.create_database_models_for_backup('contacts', SCRIPT_OPTIONS[:backup_host])
end

def base_contact_class
  @base_contact_class ||=
    begin
      if SCRIPT_OPTIONS[:backup_host]
        contact_db_backup()
        ContactsBackupDB::Contact
      else
        ContactDB::Contact
      end
    end
end

def get_contact_class(table_name)
  table_name = table_name.to_s

  models =
    if SCRIPT_OPTIONS[:backup_host]
      contact_db_backup.models
    else
      ContactDB
      CONTACTS_DATABASE.models
    end

  models.detect { |m| m.table_name == table_name }
end

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

LOG.info("creating import custom properties file for #{org.name}/#{org.id}")

class Contact < base_contact_class
  def remote_id
    identities.detect { |identity| identity.type == 1 }.try(:value)
  end
end

class Property < get_contact_class(:property)
end

# CustomField: purpose is to map a job_type_column_mapping to contact property
# job_type_column_mapping knows the csv header
# property tells us which custom_property_value to use
class CustomField < Struct.new(
        :job_type_column_mapping
      )

  attr_accessor :property

  def csv_header
    job_type_column_mapping.import_column
  end

  def property_id
    property.id
  end

  def to_s
    "#{csv_header}: #{property_id}"
  end
end

# find all custom fields the org has imported
custom_fields = ImporterDB::JobTypeColumnMapping.where(oid: org.id, job_type: 'CONTACT_CUSTOM_FIELD', mapping_object: 'CUSTOM_FIELD').map do |job_type_column_mapping|
  custom_field = CustomField.new(job_type_column_mapping)
end

# find the matching coantct propery and add it to the custom_field object
custom_fields.each do |custom_field|
  property_parent_name, property_name = *custom_field.job_type_column_mapping.column_name.split('.', 2)

  next unless property_parent = Property.where(oid: org.id, name: property_parent_name, data_type: 'object', deleted: false).first
  next unless property = Property.where(oid: org.id, name: property_name, parent_id: property_parent.id, deleted: false).first

  custom_field.property = property
end

# removed custom_fields that were not mapped above
custom_fields.select!(&:property)

custom_fields.each { |cf| puts cf }

class CustomFieldCSVRow < Struct.new(
        :contact
      )
  include CSVUtils::CSVRow

  csv_column('REMOTE_ID') { contact.remote_id }

  def get_custom_property_value(property_id)
    custom_property_value = contact.custom_property_values.detect { |v| v.property_id == property_id }
    custom_property_value&.value
  end
end

custom_fields.each do |custom_field|
  CustomFieldCSVRow.csv_column(custom_field.csv_header) { get_custom_property_value(custom_field.property_id) }
end

path = "#{org.slug}-contact-custom-properties-#{Time.now.strftime('%Y-%m-%d')}.csv"

CSVUtils::CSVReport.new(path, CustomFieldCSVRow) do |report|
  Contact.includes(:identities, :custom_property_values).where(oid: org.id).find_each do |contact|
    report << CustomFieldCSVRow.new(contact)
  end
end

STATS.notify

puts "created: #{path}"
