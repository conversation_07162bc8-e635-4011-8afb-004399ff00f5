#!/usr/bin/env ruby

# Purpose is to create a V3 annual gifts import files

require_relative '../../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-b', '--backup-host MYSQL_BACKUP_HOST', 'Specify a MySQL backup host') do |v|
    opts[:backup_host] = v
  end
end

require_relative '../../../config/environment'

def contact_db_backup
  @contact_db_backup ||= DynamicActiveModelGenerator.create_database_models_for_backup('contacts', SCRIPT_OPTIONS[:backup_host])
end

def base_contact_class
  @base_contact_class ||=
    begin
      if SCRIPT_OPTIONS[:backup_host]
        contact_db_backup()
        ContactsBackupDB::Contact
      else
        ContactDB::Contact
      end
    end
end

def get_contact_class(table_name)
  table_name = table_name.to_s

  models =
    if SCRIPT_OPTIONS[:backup_host]
      contact_db_backup.models
    else
      ContactDB
      CONTACTS_DATABASE.models
    end

  models.detect { |m| m.table_name == table_name }
end

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

LOG.info("creating import annual gifts file for #{org.name}/#{org.id}")

class GivingAnnualDonationCSVRow < get_contact_class(:giving_annual_donation)
  include CSVUtils::CSVRow

  attr_accessor :contact_remote_id

  csv_column('REMOTE_ID') { contact_remote_id }
  csv_column('FISCAL_YEAR') { fiscal_year }
  csv_column('GIVING_AMOUNT') { amount }
end

class Contact < base_contact_class
  has_many :giving_annual_donations, class_name: 'GivingAnnualDonationCSVRow'

  def remote_id
    identities.detect { |identity| identity.type == 1 }.try(:value)
  end
end

path = "#{org.slug}-contact-annual-gifts-#{Time.now.strftime('%Y-%m-%d')}.csv"

CSVUtils::CSVReport.new(path,GivingAnnualDonationCSVRow) do |report|
  Contact.includes(:identities, :giving_annual_donations).where(oid: org.id).find_each do |contact|
    contact.giving_annual_donations.each do |giving_annual_donation|
      giving_annual_donation.contact_remote_id = contact.remote_id
      STATS.inc_and_notify
      report << giving_annual_donation
    end
  end
end

STATS.notify(true)

puts "created: #{path}"
