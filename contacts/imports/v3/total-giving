#!/usr/bin/env ruby

# Purpose is to create a V3 total giving import files

require_relative '../../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-b', '--backup-host MYSQL_BACKUP_HOST', 'Specify a MySQL backup host') do |v|
    opts[:backup_host] = v
  end
end

require_relative '../../../config/environment'

def contact_db_backup
  @contact_db_backup ||= DynamicActiveModelGenerator.create_database_models_for_backup('contacts', SCRIPT_OPTIONS[:backup_host])
end

def base_contact_class
  @base_contact_class ||=
    begin
      if SCRIPT_OPTIONS[:backup_host]
        contact_db_backup()
        ContactsBackupDB::Contact
      else
        ContactDB::Contact
      end
    end
end

def get_contact_class(table_name)
  table_name = table_name.to_s

  models =
    if SCRIPT_OPTIONS[:backup_host]
      contact_db_backup.models
    else
      ContactDB
      CONTACTS_DATABASE.models
    end

  models.detect { |m| m.table_name == table_name }
end

def format_date(date)
  return unless date

  date.strftime('%d-%^b-%y')
end

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

LOG.info("creating import total giving file for #{org.name}/#{org.id}")

class GivingCSVRow < get_contact_class(:giving)
  include CSVUtils::CSVRow

  attr_accessor :contact_remote_id

  csv_column('REMOTE_ID') { contact_remote_id }
  csv_column('DONOR_SCORE') { donor_score }
  csv_column('CAPACITY_SCORE') { capacity_score }
  csv_column('ENGAGEMENT_SCORE') { engagement_score }
  csv_column('LTG_AMOUNT') { lifetime_amount }
  csv_column('LGST_AMOUNT') { largest_gift_amount }
  csv_column('LGST_GIFT_DATE') { format_date(largest_gift_date) }
  csv_column('LGST_GIFT_LABEL') { largest_gift_label }
  csv_column('LGST_GIFT_SCHOOL') { nil }
  csv_column('LAST_AMOUNT') { last_gift_amount }
  csv_column('LAST_GIFT_DATE') { format_date(last_gift_date) }
  csv_column('LAST_GIFT_LABEL') { last_gift_label }
  csv_column('LAST_GIFT_SCHOOL') { nil }
  csv_column('FIRST_GIFT_SCHOOL') { nil }
  csv_column('PLEDGE_BALANCE') { total_pledge_balance }
  csv_column('ORIGINAL_PLEDGE_AMOUNT') { nil }
  csv_column('REMAINING_PLEDGE_PAYMENT_COUNT') { nil }
  csv_column('TOTAL_PLEDGE_PAYMENT_COUNT') { nil }
  csv_column('RECURRING_PAYMENTS') { recurring_payments }
  csv_column('PLANNED_GIFT') { nil }

  def recurring_payments
    case has_recurring_payments
    when true
      'Y'
    when false
      'N'
    else
      nil
    end
  end
end

class Contact < base_contact_class
  has_many :givings, class_name: 'GivingCSVRow'

  def remote_id
    identities.detect { |identity| identity.type == 1 }.try(:value)
  end
end

path = "#{org.slug}-contact-total-giving-#{Time.now.strftime('%Y-%m-%d')}.csv"

CSVUtils::CSVReport.new(path,GivingCSVRow) do |report|
  Contact.includes(:identities, :givings).where(oid: org.id).find_each do |contact|
    contact.givings.each do |giving|
      giving.contact_remote_id = contact.remote_id
      STATS.inc_and_notify
      report << giving
    end
  end
end

STATS.notify(true)

puts "created: #{path}"
