#!/usr/bin/env ruby

# Purpose is to create a V3 identity import files

require_relative '../../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-b', '--backup-host MYSQL_BACKUP_HOST', 'Specify a MySQL backup host') do |v|
    opts[:backup_host] = v
  end
end

require_relative '../../../config/environment'

def contact_db_backup
  @contact_db_backup ||= DynamicActiveModelGenerator.create_database_models_for_backup('contacts', SCRIPT_OPTIONS[:backup_host])
end

def base_contact_class
  @base_contact_class ||=
    begin
      if SCRIPT_OPTIONS[:backup_host]
        contact_db_backup()
        ContactsBackupDB::Contact
      else
        ContactDB::Contact
      end
    end
end

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

LOG.info("creating import identity file for #{org.name}/#{org.id}")

class ContactCSVRow < base_contact_class
  include CSVUtils::CSVRow

  csv_column('REMOTE_ID') { remote_id }
  [
    :name_prefix,
    :name_first,
    :name_nick,
    :name_middle,
    :name_last,
    :name_suffix,
    :ethnicity,
    :gender
  ].each do |field|
    csv_column(field.to_s.upcase) { get_contact_attribute(field) }
  end

  csv_column('NAME_PREVIOUS') { get_contact_attribute(:name_maiden) }
  csv_column('GRADUATION_YEAR') { get_contact_attribute(:year) }
  csv_column('IS_DECEASED') { contact_attribute&.deceased? ? 'Y' : 'N' }
  csv_column('IS_DELETED') { 'N' }

  def contact_attribute
    unless defined?(@contact_attribute)
      @contact_attribute = contact_attributes.first
    end
    @contact_attribute
  end

  def get_contact_attribute(field)
    return '' unless contact_attribute

    contact_attribute.public_send(field) || ''
  end

  def remote_id
    identities.detect { |identity| identity.type == 1 }.try(:value)
  end
end

path = "#{org.slug}-contact-identities-#{Time.now.strftime('%Y-%m-%d')}.csv"

CSVUtils::CSVReport.new(path, ContactCSVRow) do |report|
  ContactCSVRow.includes(:identities, :contact_attributes).where(oid: org.id).find_each do |contact|
    STATS.inc_and_notify

    report << contact
  end
end

STATS.notify

puts "created: #{path}"
