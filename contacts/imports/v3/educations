#!/usr/bin/env ruby

# Purpose is to create a V3 educations import files

require_relative '../../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-b', '--backup-host MYSQL_BACKUP_HOST', 'Specify a MySQL backup host') do |v|
    opts[:backup_host] = v
  end
end

require_relative '../../../config/environment'

def contact_db_backup
  @contact_db_backup ||= DynamicActiveModelGenerator.create_database_models_for_backup('contacts', SCRIPT_OPTIONS[:backup_host])
end

def base_contact_class
  @base_contact_class ||=
    begin
      if SCRIPT_OPTIONS[:backup_host]
        contact_db_backup()
        ContactsBackupDB::Contact
      else
        ContactDB::Contact
      end
    end
end

def get_contact_class(table_name)
  table_name = table_name.to_s

  models =
    if SCRIPT_OPTIONS[:backup_host]
      contact_db_backup.models
    else
      ContactDB
      CONTACTS_DATABASE.models
    end

  models.detect { |m| m.table_name == table_name }
end

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

LOG.info("creating import educations file for #{org.name}/#{org.id}")

class EducationCSVRow < Struct.new(
        :contact,
        :education,
        :education_major
      )

  include CSVUtils::CSVRow

  csv_column('REMOTE_ID') { contact.remote_id }
  csv_column('EDU_SCHOOL_NAME') { education.school_name }
  csv_column('EDU_DEGREE_KEY') { education.degree_key }
  csv_column('EDU_MAJOR') { education_major&.major || '' }
  csv_column('EDU_GRAD_YEAR') { education.year }
end

class Contact < base_contact_class
  has_many :constituencies, class_name: 'ConstituencyCSVRow'

  def remote_id
    identities.detect { |identity| identity.type == 1 }.try(:value)
  end
end

path = "#{org.slug}-contact-educations-#{Time.now.strftime('%Y-%m-%d')}.csv"

CSVUtils::CSVReport.new(path, EducationCSVRow) do |report|
  Contact.includes(:identities, educations: :education_majors).where(oid: org.id).find_each do |contact|
    contact.educations.each do |education|
      has_majors = false
      education.education_majors.each do |education_major|
        has_majors = true

        report << EducationCSVRow.new(contact, education, education_major)
      end

      report << EducationCSVRow.new(contact, education, nil) unless has_majors
    end
  end
end

STATS.notify

puts "created: #{path}"
