#!/usr/bin/env ruby

# Purpose is to create a V3 identity import files

require_relative '../../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-b', '--backup-host MYSQL_BACKUP_HOST', 'Specify a MySQL backup host') do |v|
    opts[:backup_host] = v
  end
end

require_relative '../../../config/environment'

def contact_db_backup
  @contact_db_backup ||= DynamicActiveModelGenerator.create_database_models_for_backup('contacts', SCRIPT_OPTIONS[:backup_host])
end

def base_contact_class
  @base_contact_class ||=
    begin
      if SCRIPT_OPTIONS[:backup_host]
        contact_db_backup()
        ContactsBackupDB::Contact
      else
        ContactDB::Contact
      end
    end
end

def get_contact_class(table_name)
  table_name = table_name.to_s

  models =
    if SCRIPT_OPTIONS[:backup_host]
      contact_db_backup.models
    else
      ContactDB
      CONTACTS_DATABASE.models
    end

  models.detect { |m| m.table_name == table_name }
end

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

LOG.info("creating import activities file for #{org.name}/#{org.id}")

class ExtracurricularActivityCSVRow < get_contact_class(:extracurricular_activity)
  include CSVUtils::CSVRow

  attr_accessor :remote_id

  csv_column('REMOTE_ID') { remote_id }
  csv_column('ACTIVITY') { name }
end

class Contact < base_contact_class
  has_many :extracurricular_activities, class_name: 'ExtracurricularActivityCSVRow'

  def remote_id
    identities.detect { |identity| identity.type == 1 }.try(:value)
  end
end

path = "#{org.slug}-contact-activities-#{Time.now.strftime('%Y-%m-%d')}.csv"

CSVUtils::CSVReport.new(path, ExtracurricularActivityCSVRow) do |report|
  Contact.includes(:identities, :extracurricular_activities).where(oid: org.id).find_each do |contact|
    contact.extracurricular_activities.each do |extracurricular_activity|
      STATS.inc_and_notify
      extracurricular_activity.remote_id = contact.remote_id
      report << extracurricular_activity
    end
  end
end

STATS.notify

puts "created: #{path}"
