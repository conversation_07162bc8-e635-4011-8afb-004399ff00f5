#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
end

require_relative '../../config/environment'

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

class AddressCSVRow < ContactDB::Address
  include CSVUtils::CSVRow

  has_one :address_geo_coordinate, class_name: 'ContactDB::AddressGeoCoordinate', foreign_key: :address_id

  attr_accessor :positionstack_latitude,
                :positionstack_longitude
  
  csv_column :id, header: :address_id
  csv_column :type
  csv_column :address_1
  csv_column :city
  csv_column :state
  csv_column :zip_code
  csv_column :country
  csv_column(:google_latitude) { google_latitude.round(6) }
  csv_column(:google_longitude) { google_longitude.round(6) }
  csv_column(:positionstack_latitude) { positionstack_latitude.round(6) }
  csv_column(:positionstack_longitude) { positionstack_longitude.round(6) }
  csv_column(:distance)

  def google_latitude
    address_geo_coordinate.lat
  end

  def google_longitude
    address_geo_coordinate.lng
  end

  def distance
    Geocoder::Calculations.distance_between(
      [google_latitude, google_longitude],
      [positionstack_latitude, positionstack_longitude]
    )
  end

  def address_search_query
    [
      address_1,
      city,
      state,
      zip_code&.split('-', 2)&.first,
      country
    ].compact.join(' ')
  end 
end

class ContactDB::Contact
  has_many :addresses, class_name: 'AddressCSVRow'

  def primary_address
    addresses.detect { |a| a.primary? }
  end
end

def positionstack_client
  @positionstack_client ||= PositionstackClient.new('')
end

CSVUtils::CSVReport.new('positionstack-report.csv', AddressCSVRow) do |report|
  ContactDB::Contact.includes(addresses: :address_geo_coordinate).where(oid: org.id).find_each do |contact|
    STATS.inc_and_notify

    next unless (primary_address = contact.primary_address)
    next unless primary_address.address_geo_coordinate

    coordinates =
      begin
        STATS.update(:address_lookups)
        positionstack_client.get_coordinates(primary_address.address_search_query)
      rescue ClientApiBuilder::UnexpectedResponse => e
        LOG.info("stopping, positionstack_client returned #{e.inspect}")
        break
      end

    unless coordinates
      LOG.warn("no coordinates returned for address query #{primary_address.address_search_query}")
      next
    end

    primary_address.positionstack_latitude = coordinates['latitude']
    primary_address.positionstack_longitude = coordinates['longitude']

    STATS.update(:compared_addresses)
    report << primary_address
  end
end

STATS.notify(true)
