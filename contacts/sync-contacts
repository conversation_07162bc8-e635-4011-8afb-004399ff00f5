#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:starting_oid] = 0

  opts.add_oid_option(false)
  opts.add_run_remote

  opts.on('--starting-oid [OID]', Integer, 'Starting oid') do |v|
    opts[:starting_oid] = v
  end
end

require File.expand_path('../config/environment', __dir__)
require 'active_record'
ActiveRecord::Base.logger = LOG

class MySQLContactCSVRow < ContactDB::Contact
  include CSVUtils::CSVRow

  csv_column :id, header: :contact_id
  csv_column :updated_at, header: :timestamp
end

class ESContactCSVRow < Struct.new(:contact_id,
                                   :timestamp,
                                   :index,
                                   :type,
                                   :routing
                                  )
  include CSVUtils::CSVRow

  csv_column :contact_id
  csv_column :timestamp
  csv_column :index
  csv_column :type
  csv_column :routing

  def self.create_from_doc(doc)
    new doc['_id'],
        doc['_source']['updated_at'],
        doc['_index'],
        doc['_type'],
        doc['_routing']
  end
end

class KSContactCSVRow < Struct.new(:contact_id,
                                   :updated_at,
                                   :oid,
                                   :shard_id
                                  )
  include CSVUtils::CSVRow

  csv_column :contact_id
  csv_column :updated_at, header: :timestamp
  csv_column :oid
  csv_column :shard_id

  def self.get_updated_at(contact_data)
    Protobuf::Decoder.decode_each_field(StringIO.new(contact_data['contact'])) do |tag, bytes|
      return bytes if tag == 4
    end

    raise("no updated_at for #{contact_data}")
  end

  def self.create_from_contact(contact_data)
    new contact_data['contact_id'],
        get_updated_at(contact_data),
        contact_data['oid'],
        contact_data['id']
  end
end

def cassandra_cluster
  @cassandra_cluster ||= Cassandra.cluster(
    hosts: KS_CONFIG['hosts'],
    port: KS_CONFIG['port'],
    reconnection_policy: Cassandra::Reconnection::Policies::Constant.new(5),
    consistency: :one
  )
end

def create_cassandra_session(keyspace)
  cassandra_cluster.connect(keyspace)
end

@contacts_index_cassandra_session = create_cassandra_session('contacts_index')
def contacts_index_cassandra_session
  @contacts_index_cassandra_session
end

def each_cassandra_record(session, cql, page_size = 100)
  LOG.info("Starting: #{cql}")

  result = session.execute(cql, page_size: page_size)
  loop do
    result.each { |row| yield row }
    break if result.last_page?

    result = result.next_page
  end

  LOG.info("Finished: #{cql}")
end

oids = if SCRIPT_OPTIONS[:oid]
         [SCRIPT_OPTIONS[:oid]]
       else
         LOG.measure('fetching distinct oids') do
           ContactDB::Contact.select('DISTINCT oid').to_a.map(&:oid)
         end
       end

AuthDB::Organization.where(deleted: false, id: oids).where("id >= #{SCRIPT_OPTIONS[:starting_oid]}").sort_by(&:id).each do |org|
  LOG.info "Synchronize contacts for #{org.name}/#{org.id}"

  STATS[:oid] = org.id

  mysql_data_file = TMP_DIR.join("contacts-mysql-oid-#{org.id}.dump").to_s
  sorted_mysql_data_file = mysql_data_file + '.sorted'
  es_data_file = TMP_DIR.join("contacts-es-oid-#{org.id}.dump").to_s
  sorted_es_data_file = es_data_file + '.sorted'
  ks_data_file = TMP_DIR.join("contacts-ks-oid-#{org.id}.dump").to_s
  sorted_ks_data_file = ks_data_file + '.sorted'

  LOG.measure('fetching contacts from mysql') do
    CSVUtils::CSVReport.new(mysql_data_file, MySQLContactCSVRow) do |report|
      MySQLContactCSVRow.use_oid_idx.select('id, updated_at').where(oid: org.id).find_each do |contact|
        STATS[:mysql_records] += 1
        report << contact
      end
    end
  end

  LOG.measure('sorting contacts from mysql') do
    CSVUtils::CSVSort.new(mysql_data_file, sorted_mysql_data_file, 250_000).sort do |a, b|
      a[0].to_i <=> b[0].to_i
    end
  end

  LOG.measure('fetching contacts from ks') do
    CSVUtils::CSVReport.new(ks_data_file, KSContactCSVRow) do |report|
      semaphore = Mutex.new
      append_row_proc = Proc.new do |contact_data|
        STATS[:ks_records] += 1
        semaphore.synchronize { report << KSContactCSVRow.create_from_contact(contact_data) }
      end

      shard_threads = 20.times.map do |i|
        shard_id = Cassandra::Util.encode_string("#{org.id}_shard_#{i}")
        Thread.new do
          cql = "SELECT * FROM oid_index WHERE id = #{shard_id} AND oid = #{org.id}"
          each_cassandra_record(contacts_index_cassandra_session, cql) do |contact_data|
            append_row_proc.call contact_data
          end
        end
      end
      shard_threads.each(&:join)
    end
  end

  LOG.measure('sorting contacts from ks') do
    CSVUtils::CSVSort.new(ks_data_file, sorted_ks_data_file, 250_000).sort do |a, b|
      a[0].to_i <=> b[0].to_i
    end
  end

  LOG.measure('fetching contacts from es') do
    CSVUtils::CSVReport.new(es_data_file, ESContactCSVRow) do |report|
      scanner = ESContactHelpers.query_mapping_by_oid(:contact, org.id)
      scanner.fields_to_return = 'updated_at'
      scanner.each do |doc|
        STATS[:es_records] += 1
        report << ESContactCSVRow.create_from_doc(doc)
      end
    end
  end

  LOG.measure('sorting contacts from es') do
    CSVUtils::CSVSort.new(es_data_file, sorted_es_data_file, 250_000).sort do |a, b|
      a[0].to_i <=> b[0].to_i
    end
  end

  STATS.notify

  File.unlink(mysql_data_file)
  File.unlink(es_data_file)
  File.unlink(ks_data_file)

  delete_es_worker = WorkerThreads.new do |queue|
    records_to_delete = []

    delete_records_proc = Proc.new do
      actions = records_to_delete.map do |record|
        {
          'delete' => {
            '_index' => record['index'],
            '_id' => record['contact_id'],
            '_type' => record['type'],
            '_routing' => record['routing']
          }
        }
      end

      ESHelpers.bulk_request(actions)
      STATS.update(:es_deletes, records_to_delete.size)
      records_to_delete = []
    end

    queue.each do |record|
      records_to_delete << record

      delete_records_proc.call if records_to_delete.size == 100
    end

    delete_records_proc.call if records_to_delete.size > 0
  end

  delete_ks_worker = WorkerThreads.new do |queue|
    ks_session = contacts_index_cassandra_session

    queue.each do |record|
      cql = "DELETE FROM oid_index WHERE id = #{Cassandra::Util.encode_string(record['shard_id'])} AND oid = #{record['oid']} AND contact_id = #{record['contact_id']}"
      ks_session.execute(cql, consistency: :all)
      STATS.update(:ks_deletes)
    end
  end

  contact_refresh_worker = WorkerThreads.new(10) do |queue|
    contact_client = ContactClient.create_client_with_app_creds

    queue.each do |record|
      LOG.info "refreshing contact #{record}"

      res = contact_client.refresh_contact(org.id, record['contact_id'])

      if res.kind_of?(Net::HTTPSuccess)
        STATS.update(:refreshes)
      else
        LOG.error "refreshing failed for #{record} with #{res.code}/#{res.body}"
        STATS.update(:refresh_contact_failed)
      end
    end
  end

  comparer = DataSourceComparer.new(sorted_mysql_data_file) do |src_record, dest_record|
    src_record['contact_id'].to_i <=> dest_record['contact_id'].to_i
  end

  comparer.compare(sorted_es_data_file) do |action, record|
    STATS.inc_and_notify
    LOG.info "ES #{action}: #{record}"

    STATS[action] += 1

    case action
    when :update,
         :create
      contact_refresh_worker.enq(record)
    when :delete
      delete_es_worker.enq(record)
    else
      raise("unknown action #{action} for ES record #{record}")
    end
  end

  comparer.compare(sorted_ks_data_file) do |action, record|
    STATS.inc_and_notify
    LOG.info "KS #{action}: #{record}"

    STATS[action] += 1

    case action
    when :update,
         :create
      contact_refresh_worker.enq(record)
    when :delete
      delete_ks_worker.enq(record)
    else
      raise("unknown action #{action} for KS record #{record}")
    end
  end

  File.unlink(sorted_mysql_data_file)
  File.unlink(sorted_es_data_file)
  File.unlink(sorted_ks_data_file)

  delete_es_worker.shutdown
  delete_ks_worker.shutdown
  contact_refresh_worker.shutdown

  STATS.notify(true)

  STATS.reset
end
