#!/usr/bin/env ruby

=begin
Purpose is to create a migration of remote ids.  And to maintain the mapping.

All other migration scripts should use the migration_timestamp and oid to figure out what users to migrate to new remote ids.

How it works:

We go through the specified CSV.  Look up the contact_id by old remote id.  And track contact_id, old/new remote id in identity_conversion.

Then at the end we determine which users to mark for deletion because they are in our system and not in the CSV.

Post anaylsis should be done to determine that the number of contacts to delete is low.  And the old remote ids of the users to delete should be sent to the customer for verification.

The actual deletion of the contacts can occur the next time the customer does a full import.
=end

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  # defaults
  opts.options[:identity_type] = 1
  opts.options[:old_id_column_name] = 'old_id'
  opts.options[:new_id_column_name] = 'new_id'

  opts.add_oid_option
  opts.add_input_file
  opts.add_run_remote

  opts.on("-t", "--type [TYPE]", Integer, "type of remote ids") do |v|
    opts[:identity_type] = v
  end

  opts.on("-t", "--type [TYPE]", Integer, "type of remote ids") do |v|
    opts[:identity_type] = v
  end

  opts.on('--old-id [OLD COLUMN NAME]', 'Old id column name') do |v|
    opts[:old_id_column_name] = v
  end

  opts.on('--new-id [NEW COLUMN NAME]', 'New id column name') do |v|
    opts[:new_id_column_name] = v
  end
end

require File.expand_path('../config/environment', __dir__)
require 'csv'

# set the MIGRATION_TIMESTAMP to the beginning of the day
now = Time.now.utc.to_i
MIGRATION_TIMESTAMP = (now - (now % 86_400)) * 1_000

def mysql_client
  @mysql_client ||= MySQLHelpers.create_client(:contacts)
end

def insert_mapped_identities(oid, type, mapped_identities)
  created_at = (Time.now.to_f * 1000).to_i
  updated_at = created_at
  sql = 'INSERT identity_conversion (oid, contact_id, value_old, value_new, `type`, migration_timestamp, marked_for_deletion, created_at, updated_at) VALUES'

  add_value_proc = Proc.new do |mapped_identity|
    sql << sprintf("(%d, %d, \"%s\", \"%s\", %d, %d, %d, %d, %d)",
                   oid,
                   mapped_identity[:contact_id],
                   mysql_client.escape(mapped_identity[:value_old]),
                   mysql_client.escape(mapped_identity[:value_new]),
                   type,
                   MIGRATION_TIMESTAMP,
                   0,
                   created_at,
                   updated_at)
  end

  add_value_proc.call mapped_identities.shift

  mapped_identities.each do |mapped_identity|
    sql << ','
    add_value_proc.call mapped_identity
  end

  sql << ';'

  mysql_client.query(sql);
end

def lookup_contact_ids(oid, type, remote_ids)
  sql = sprintf(
    'SELECT contact_id, value FROM identity WHERE oid = %d AND `type` = %d AND value IN(%s)',
    oid,
    type,
    remote_ids.map { |id| '"' + mysql_client.escape(id) + '"' }.join(',')
  )

  mysql_client.query(sql, as: :array)
end

# find all identity records that where not part of the conversion and mark them for deletion
def insert_missing_identities(oid, type)
  created_at = (Time.now.to_f * 1000.0).to_i
  updated_at = created_at
  sql = <<SQL
INSERT identity_conversion (oid, contact_id, value_old, value_new, `type`, migration_timestamp, marked_for_deletion, created_at, updated_at)
  SELECT oid, contact_id, value as value_old, NULL as value_new, `type`, #{MIGRATION_TIMESTAMP} as migration_timestamp, 1 as marked_for_deletion, #{created_at} as created_at, #{updated_at} as updated_at
  FROM identity
  WHERE
    oid = #{oid} AND
    `type` = #{type} AND
    value NOT IN(SELECT value_old FROM identity_conversion WHERE oid = #{oid} AND `type` = #{type} AND migration_timestamp = #{MIGRATION_TIMESTAMP})
SQL

  mysql_client.query(sql)
end

CSV.open(SCRIPT_OPTIONS[:file], 'rb') do |csv|
  headers = csv.shift

  raise("no old_id in csv headers #{headers}") unless headers.include?(SCRIPT_OPTIONS[:old_id_column_name])
  raise("no new_id in csv headers #{headers}") unless headers.include?(SCRIPT_OPTIONS[:new_id_column_name])

  old_id_idx = headers.index(SCRIPT_OPTIONS[:old_id_column_name])
  new_id_idx = headers.index(SCRIPT_OPTIONS[:new_id_column_name])

  insert_mapped_identities_proc = Proc.new do |old_to_new_ids|
    mapped_identities = lookup_contact_ids(SCRIPT_OPTIONS[:oid], SCRIPT_OPTIONS[:identity_type], old_to_new_ids.keys).map do |contact_id, remote_id|
      {
        contact_id: contact_id,
        value_old: remote_id,
        value_new: old_to_new_ids.delete(remote_id)
      }
    end

    if old_to_new_ids.size > 0
      LOG.error "remote_id(s) have no contact_ids, #{old_to_new_ids.keys}"
    end

    insert_mapped_identities(SCRIPT_OPTIONS[:oid], SCRIPT_OPTIONS[:identity_type], mapped_identities)

    old_to_new_ids = {}
  end

  old_to_new_ids = {}
  csv.each do |row|
    old_to_new_ids[row[old_id_idx]] = row[new_id_idx]

    if old_to_new_ids.size >= 10_000
      insert_mapped_identities_proc.call old_to_new_ids
      old_to_new_ids = {}
    end
  end

  if old_to_new_ids.size > 0
    insert_mapped_identities_proc.call old_to_new_ids
    old_to_new_ids = {}
  end
end

insert_missing_identities(SCRIPT_OPTIONS[:oid], SCRIPT_OPTIONS[:identity_type])

LOG.notify("migration_timestamp: #{MIGRATION_TIMESTAMP}", true)
