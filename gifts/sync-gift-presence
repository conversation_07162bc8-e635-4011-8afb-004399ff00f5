#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:starting_oid] = 0
  opts[:dry_run] = false

  opts.add_oid_option(false)

  opts.on('--starting-oid [OID]', Integer, 'Starting oid') do |v|
    opts[:starting_oid] = v
  end

  opts.on('--dry-run', 'Dry run') do
    opts[:dry_run] = true
  end
end

require File.expand_path('../config/environment', __dir__)
require 'active_record'
ActiveRecord::Base.logger = LOG

class PresenceKSCSVRow < Struct.new(:data)
  include CSVUtils::CSVRow

  csv_column(:gift_remote_id) { data['remote_id'] }
  csv_column(:gift_type) { data['sub_type'] }
  csv_column(:oid) { data['oid'] }
  csv_column(:type) { data['type'] }
end

class GiftTransactionCSVRow < GiftDB::GiftTransaction
  include CSVUtils::CSVRow

  csv_column :gift_remote_id
  csv_column :type, header: :gift_type
  csv_column :id
  csv_column :updated_at
end

oids = if SCRIPT_OPTIONS[:oid]
         [SCRIPT_OPTIONS[:oid]]
       else
         LOG.measure('fetching distinct oids') do
           GiftTransactionCSVRow.select('DISTINCT oid').to_a.map(&:oid)
         end
       end

PRESENCE_TYPE = 'GIFT'

AuthDB::Organization.where(deleted: false, id: oids).where("id >= #{SCRIPT_OPTIONS[:starting_oid]}").sort_by(&:id).each do |org|
  LOG.info "Synchronize gift presence for #{org.name}/#{org.id}"

  STATS[:oid] = org.id

  mysql_data_file = TMP_DIR.join("gift-transactions-mysql-oid-#{org.id}.dump").to_s
  sorted_mysql_data_file = mysql_data_file + '.sorted'

  ks_data_file = TMP_DIR.join("gift-presence-ks-oid-#{org.id}.dump").to_s
  sorted_ks_data_file = ks_data_file + '.sorted'

  presence_ks = PresenceKS.new(org.id)

  LOG.measure('fetching gift presence from ks') do
    CSVUtils::CSVReport.new(ks_data_file, PresenceKSCSVRow) do |report|
      presence_ks.each_presence_records(PRESENCE_TYPE) do |presence|
        report << PresenceKSCSVRow.new(presence)
        STATS[:ks_records] += 1
      end
    end
  end

  LOG.measure('sorting gift presence from ks') do
    if STATS[:ks_records] == 0
      FileUtils.cp(ks_data_file, sorted_ks_data_file)
    else
      CSVUtils::CSVSort.new(ks_data_file, sorted_ks_data_file, 250_000).sort do |a, b|
        result = a[0].to_s <=> b[0].to_s
        result == 0 ? a[1].to_s <=> b[1].to_s : result
      end
    end
  end

  LOG.measure('fetching gift transactions from mysql') do
    CSVUtils::CSVReport.new(mysql_data_file, GiftTransactionCSVRow) do |report|
      GiftTransactionCSVRow.select(:id, :oid, :gift_remote_id, :type, :updated_at).where(oid: org.id).find_each(batch_size: 50_000) do |transaction|
        report << transaction
        STATS[:mysql_records] += 1
      end
    end
  end

  LOG.measure('sorting gift transactions from mysql') do
    CSVUtils::CSVSort.new(mysql_data_file, sorted_mysql_data_file, 250_000).sort do |a, b|
      result = a[0].to_s <=> b[0].to_s
      result == 0 ? a[1].to_s <=> b[1].to_s : result
    end
  end

  STATS.notify

  File.unlink(mysql_data_file)
  File.unlink(ks_data_file)

  comparer = DataSourceComparer.new(sorted_mysql_data_file) do |src_record, dest_record|
    result = src_record['gift_remote_id'].to_s <=> dest_record['gift_remote_id'].to_s
    result == 0 ? src_record['gift_type'].to_s <=> dest_record['gift_type'].to_s : result
  end

  comparer.compare(sorted_ks_data_file) do |action, record|
    STATS.inc_and_notify
    LOG.info "KS #{action}: #{record}"

    STATS[action] += 1

    next if SCRIPT_OPTIONS[:dry_run]

    case action
    when :update,
         :create
      presence_ks.create_presence(PRESENCE_TYPE, record['gift_type'].to_s, record['gift_remote_id'].to_s, 1, record['updated_at'].to_i)
    when :delete
      presence_ks.delete_presence(PRESENCE_TYPE, record['gift_type'].to_s, record['gift_remote_id'].to_s)
    else
      raise("unknown action #{action} for KS record #{record}")
    end
  end

  File.unlink(sorted_mysql_data_file)
  File.unlink(sorted_ks_data_file)

  STATS.notify(true)

  STATS.reset
end
