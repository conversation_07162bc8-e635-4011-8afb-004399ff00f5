#!/usr/bin/env ruby

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:starting_oid] = 0
  opts[:dry_run] = false

  opts.add_oid_option(false)
  opts.add_run_remote

  opts.on('--starting-oid [OID]', Integer, 'Starting oid') do |v|
    opts[:starting_oid] = v
  end

  opts.on('--dry-run', 'Dry run') do
    opts[:dry_run] = true
  end
end

require_relative'../config/environment'
require 'active_record'
ActiveRecord::Base.logger = LOG

class GiftTransactionCSVRow < GiftDB::GiftTransaction
  include CSVUtils::CSVRow

  csv_column :id
  csv_column :updated_at, header: :timestamp
end

class ESGiftCSVRow < Struct.new(
        :id,
        :contact_id,
        :timestamp,
        :index,
        :type,
        :routing
      )
  include CSVUtils::CSVRow

  csv_column :id
  csv_column :contact_id
  csv_column :timestamp
  csv_column :index
  csv_column :type
  csv_column :routing

  def self.create_from_doc(doc)
    new doc['_id'],
        doc['_parent'],
        doc['_timestamp'],
        doc['_index'],
        doc['_type'],
        doc['_routing']
  end
end

class GiftComparer < CSVUtils::CSVCompare
  def update_row?(src_record, dest_record)
    src_record['timestamp'] > dest_record['timestamp']
  end
end

oids = if SCRIPT_OPTIONS[:oid]
         [SCRIPT_OPTIONS[:oid]]
       else
         LOG.measure('fetching distinct oids') do
           GiftTransactionCSVRow.select('DISTINCT oid').to_a.map(&:oid)
         end
       end

PRESENCE_TYPE = 'GIFT'

AuthDB::Organization.where(deleted: false, id: oids).where("id >= #{SCRIPT_OPTIONS[:starting_oid]}").sort_by(&:id).each do |org|
  LOG.info "Synchronize gift in ES for #{org.name}/#{org.id}"

  STATS[:oid] = org.id

  primary_data_file = TMP_DIR.join("gifts-oid-#{org.id}-primary.dump").to_s
  sorted_primary_data_file = primary_data_file + '.sorted'
  secondary_data_file = TMP_DIR.join("gifts-oid-#{org.id}-secondary.dump").to_s
  sorted_secondary_data_file = secondary_data_file + '.sorted'

  Parallelizer.new do |parallelizer|
    parallelizer.process do
      LOG.measure('fetching gifts from mysql') do
        CSVUtils::CSVReport.new(primary_data_file, GiftTransactionCSVRow) do |report|
          GiftTransactionCSVRow.select(*GiftTransactionCSVRow.csv_columns.keys).where(oid: org.id).find_each do |gift|
            STATS.update(:mysql_gift_records)
            report << gift
          end
        end
      end

      LOG.measure('sorting gift transactions from mysql') do
        CSVUtils::CSVSort.new(primary_data_file, sorted_primary_data_file, 250_000).sort do |a, b|
          a[0] <=> b[0]
        end
      end
    end

    parallelizer.process do
      LOG.measure('fetching gifts from es') do
        CSVUtils::CSVReport.new(secondary_data_file, ESGiftCSVRow) do |report|
          scanner = ESContactHelpers.query_mapping_by_oid(:gift, org.id)
          scanner.fields_to_return = false
          scanner.each do |doc|
            STATS.update(:es_records)
            report << ESGiftCSVRow.create_from_doc(doc)
          end
        end
      end

      LOG.measure('sorting gift transactions from es') do
        CSVUtils::CSVSort.new(secondary_data_file, sorted_secondary_data_file, 250_000).sort do |a, b|
          a[0] <=> b[0]
        end
      end
    end
  end

  delete_es_worker = WorkerThreads.new(10) do |queue|
    records_to_delete = []

    delete_records_proc = Proc.new do
      actions = records_to_delete.map do |record|
        {
          'delete' => {
            '_index' => record['index'],
            '_id' => record['id'],
            '_type' => record['type'],
            '_routing' => record['routing']
          }
        }
      end

      ESHelpers.bulk_request(actions)
      STATS.update(:es_deletes, records_to_delete.size)
      records_to_delete = []
    end

    queue.each do |record|
      records_to_delete << record

      delete_records_proc.call if records_to_delete.size == 100
    end

    delete_records_proc.call if records_to_delete.size > 0
  end

  index_worker = WorkerThreads.new(10) do |queue|
    contact_client = ContactClient.create_client_with_app_creds

    queue.each do |record|
      res = contact_client.sync_gift_to_es(org.id, record['id'])

      if res.kind_of?(Net::HTTPSuccess)
        LOG.info "syncing gift #{record['id']}"
        STATS.update(:indexed)
      else
        LOG.error "sync failed for #{record} with #{res.code}/#{res.body}"
        STATS.update(:sync_gift_failed)
      end
    end
  end

  STATS.notify

  File.unlink(primary_data_file)
  File.unlink(secondary_data_file)

  comparer = GiftComparer.new(sorted_primary_data_file) do |src_record, dest_record|
    src_record['id'] <=> dest_record['id']
  end

  comparer.compare(sorted_secondary_data_file) do |action, record|
    STATS.inc_and_notify
    LOG.info "ES #{action}: #{record}"

    STATS[action] += 1

    next if SCRIPT_OPTIONS[:dry_run]

    case action
    when :create,
         :update
      index_worker.enq(record)
    when :delete
      delete_es_worker.enq(record)
    else
      raise("unknown action #{action} for KS record #{record}")
    end
  end

  delete_es_worker.shutdown
  index_worker.shutdown

  File.unlink(sorted_primary_data_file)
  File.unlink(sorted_secondary_data_file)

  STATS.notify(true)

  STATS.reset
end
