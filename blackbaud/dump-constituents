#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
end

require File.expand_path('../config/environment', __dir__)

ENV['BLACKBAUD_SUBSCRIPTION_KEY'] = 'fde69c1acd6548059d184599f996cd86'

auth_client = AuthClient.create_client_with_app_creds

blackbaud_client = BlackbaudRENXTClient.new(BlackbaudRENXTClient.get_access_token(auth_client, SCRIPT_OPTIONS[:oid]))

out = File.open('blackbaud_constituents.jsonl', 'wb')

offset = 0
limit = 1000
attempts = 0
begin
  while (constituents = blackbaud_client.get_constituents(offset: offset, limit: limit)).size > 0
    constituents['value'].each do |constituent|
      out.puts(constituent.to_json)
    end

    break unless constituents['next_link'] =~ /offset=(\d+)/
    offset = $1.to_i

    break unless constituents['next_link'] =~ /limit=(\d+)/
    limit = $1.to_i
  end
rescue ClientApiBuilder::UnexpectedResponse => e
  raise e if attempts > 0

  LOG.info("fetching a new access token");
  attempts += 1
  blackbaud_client = BlackbaudRENXTClient.new(BlackbaudRENXTClient.get_access_token(auth_client, SCRIPT_OPTIONS[:oid]))
  retry
end

out.close
