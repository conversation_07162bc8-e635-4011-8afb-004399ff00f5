#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:offset] = 0
  opts[:limit] = 50

  opts.add_oid_option

  opts.on('-l', '--limit LIMIT', Integer, 'Number of constituents to display') do |v|
    opts[:limit] = v
  end

  opts.on('--offset OFFSET', Integer, 'Offset to start displaying constituents from') do |v|
    opts[:limit] = v
  end
end

require File.expand_path('../config/environment', __dir__)

class BlackbaudConstituentSchema
  include Schema::All

  attribute :id, :string

  has_one :address do
    attribute :id, :string
    attribute :constituent_id, :string
    attribute :do_not_mail, :boolean
    attribute :formatted_address, :string
    attribute :inactive, :boolean
    attribute :preferred, :boolean
    attribute :type, :string
  end

  attribute :age, :integer

  has_one :birthdate do
    attribute :d, :integer
    attribute :m, :integer
    attribute :y, :integer
  end

  attribute :date_added, :time
  attribute :date_modified, :time
  attribute :deceased, :boolean

  has_one :email do
    attribute :id, :string
    attribute :address, :string
    attribute :constituent_id, :string
    attribute :do_not_email, :boolean
    attribute :inactive, :boolean
    attribute :primary, :boolean
    attribute :type, :string
  end

  attribute :first, :string
  attribute :fundraiser_status, :string
  attribute :gender, :string
  attribute :gives_anonymously, :boolean
  attribute :inactive, :boolean
  attribute :last, :string
  attribute :lookup_id, :string
  attribute :marital_status, :string
  attribute :middle, :string
  attribute :name, :string
  attribute :preferred_name, :string

  has_one :spouse do
    attribute :is_head_of_household, :boolean
  end

  attribute :title, :string
  attribute :type, :string

  def to_csv_row
    [
      id,
      type,
      title,
      first,
      last,
      preferred_name,
      middle,
      gender,
      marital_status,
      age,
      email&.address
    ]
  end

  def self.to_csv_headers
    [
      'ConstituentID',
      'ConstituentType',
      'Title',
      'FirstName',
      'LastName',
      'PreferredName',
      'MiddleName',
      'Gender',
      'MaritalStatus',
      'Age',
      'EMail'
    ]
  end
end

auth_client = AuthClient.create_client_with_app_creds

blackbaud_client = BlackbaudRENXTClient.new(auth_client.get_blackbaud_access_token(SCRIPT_OPTIONS[:oid]))

constituents = blackbaud_client.get_constituents(offset: SCRIPT_OPTIONS[:offset], limit: SCRIPT_OPTIONS[:limit])['value']

# print(CSV.generate do |csv|
#   csv << BlackbaudConstituentSchema.to_csv_headers

#   constituents.each do |payload|
#     constituent = BlackbaudConstituentSchema.from_hash(payload)
#     csv << constituent.to_csv_row
#   end
# end)

html = <<STR
<html>
  <title>Blackbaud Constituents</title>
  <style>
    table { border-collapse: collapse; width: 100% }
    table thead { border-bottom: 1px solid #999; }
    table tr:nth-child(even) { background-color: #ddd; }
    table tr td { padding-left: 5px; border-left: 1px solid #999; border-right: 1px solid #999; }
  </style>
  <body>
STR

html << '<table>'
html << '<thead><tr>'
BlackbaudConstituentSchema.to_csv_headers.each do |heading|
  html << "<th>#{heading}</th>"
end
html << '</tr></thead>'

html << '<tbody>'
constituents.each do |payload|
  constituent = BlackbaudConstituentSchema.from_hash(payload)
  html << '<tr>'
  constituent.to_csv_row.each do |value|
    html << "<td>#{value}</td>"
  end
  html << '</tr>'
end
html << '</tbody>'

html << '</table>'

html << '</body></html>'

print html + "\n"
