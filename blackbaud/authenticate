#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-c', '--authorization-code CODE', 'Authorization code from callback') do |v|
    opts[:authorization_code] = v
  end

  opts.require_option(:authorization_code)
end

require File.expand_path('../config/environment', __dir__)

auth_client = AuthClient.create_client_with_app_creds

puts JSON.pretty_generate(auth_client.authorize_blackbaud(SCRIPT_OPTIONS[:oid], SCRIPT_OPTIONS[:authorization_code]))
