#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
end

require File.expand_path('../config/environment', __dir__)

ENV['BLACKBAUD_SUBSCRIPTION_KEY'] = 'fde69c1acd6548059d184599f996cd86'

auth_client = AuthClient.create_client_with_app_creds

blackbaud_client = BlackbaudRENXTClient.new(BlackbaudRENXTClient.get_access_token(auth_client, SCRIPT_OPTIONS[:oid]))

out = File.open('blackbaud_gifts.jsonl', 'wb')

offset = 0
limit = 5000
attempts = 0
begin
  while (gifts = blackbaud_client.get_gifts(offset: offset, limit: limit)).size > 0
    gifts['value'].each do |gift|
      out.puts(gift.to_json)
    end

    break unless gifts['next_link'] =~ /offset=(\d+)/
    offset = $1.to_i

    break unless gifts['next_link'] =~ /limit=(\d+)/
    limit = $1.to_i
  end
rescue ClientApiBuilder::UnexpectedResponse => e
  raise e if attempts > 0

  LOG.info("fetching a new access token");
  attempts += 1
  blackbaud_client = BlackbaudRENXTClient.new(BlackbaudRENXTClient.get_access_token(auth_client, SCRIPT_OPTIONS[:oid]))
  retry
end

out.close
