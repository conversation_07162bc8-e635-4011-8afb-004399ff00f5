#!/usr/bin/env ruby
# frozen_string_literal: true

require_relative '../config/script_options'


SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-d', '--dependency=DEPENDENCY') do |v|
    opts[:dependency] = v
  end
end

require File.expand_path('../config/environment', __dir__)
require 'active_support/all'

dependency = SCRIPT_OPTIONS[:dependency]

csv = CSV.open('basepom-report.csv', 'wb')
csv << ['repository', 'last_modified_on', 'basepom_version', dependency + '_versions' ]

GithubRepo.all_repos
  .select(&:pom?)
  .sort { |a, b| a.base_pom_version == b.base_pom_version ? a.repo_name <=> b.repo_name : b.base_pom_version.to_s <=> a.base_pom_version.to_s }
  .each do |repo|
    target_dep_versions = repo.dependency_versions('com.evertrue', dependency).join(',')
    row = [repo.repo_name, repo.git_date, repo.base_pom_version, target_dep_versions]
    csv << row unless target_dep_versions.blank?
  end

csv.close
