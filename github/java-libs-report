#!/usr/bin/env ruby
# frozen_string_literal: true

require File.expand_path('../config/environment', __dir__)

libs = [
  ['com.evertrue', 'kafka-consumer-core'],
  ['org.slf4j', 'log4j-over-slf4j'],
  ['log4j', 'log4j']
]

csv = CSV.open('java-libs-version-report.csv', 'wb')
csv << ['repository', 'last_modified_on'] + libs.map { |group, lib| group + ':' + lib }

GithubRepo.all_repos
  .select(&:pom?)
  .each do |repo|
  csv << [repo.repo_name, repo.git_date] + libs.map { |group, lib| repo.dependency_versions(group, lib).join(',') }
end

csv.close
