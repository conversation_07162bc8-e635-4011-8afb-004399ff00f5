#!/usr/bin/env ruby

# Add color helper methods
def red(text); "\e[31m#{text}\e[0m"; end
def green(text); "\e[32m#{text}\e[0m"; end
def yellow(text); "\e[33m#{text}\e[0m"; end
def blue(text); "\e[34m#{text}\e[0m"; end

PROTECTED_BRANCHES = ['main', 'master', 'release', 'feature', 'dev']
VERSION_PATTERN = /^v?\d+\.\d+\.\d+$/

puts "Fetching latest branches..."
system("git fetch --prune")

branches = `git branch --list`.split("\n").map(&:strip).reject { |b| b.start_with?('*') }
deletable_branches = []

branches.each do |branch|
  if PROTECTED_BRANCHES.include?(branch) || branch.match?(VERSION_PATTERN)
    puts "Skipping protected branch: #{branch}"
    next
  end

  deletable_branches << branch
end

puts blue("\nBranches to be deleted:")
deletable_branches.each { |branch| puts "  - #{yellow(branch)}" }
print "\nOptions:\n"
print "  [#{green('a')}] Delete all branches\n"
print "  [#{green('s')}] Step through each branch\n"
print "  [#{red('n')}] Cancel\n"
print "Choose an option [a/s/n]: "

response = gets.chomp.downcase
case response
when 'a'
  deletable_branches.each do |branch|
    if system("git branch -d #{branch}")
      puts green("Deleted #{branch}")
    else
      puts red("#{branch} could not be deleted.")
    end
  end
  puts green("\nBulk deletion complete.")
when 's'
  deletable_branches.each do |branch|
    print "Delete '#{yellow(branch)}'? [#{green('y')}/n]: "
    if gets.chomp.downcase == 'y'
      if system("git branch -d #{branch}")
        puts green("Deleted #{branch}")
      else
        puts yellow("#{branch} could not be deleted")
        print "Force delete? #{red("WARNING:")} This operation cannot be undone. ? [#{green('y')}/n]: "
        if gets.chomp.downcase == 'y'
          if system("git branch -D #{branch}")
            puts green("Deleted branch #{branch}")  
          end
        end
      end
    else
      puts yellow("Skipped #{branch}")
    end
  end
  puts green("\nStep-through deletion complete.")
else
  puts yellow("\nOperation cancelled. No branches were deleted.")
end

puts blue("Branch cleanup complete.")
