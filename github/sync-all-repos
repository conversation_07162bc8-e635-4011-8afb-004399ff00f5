#!/usr/bin/env ruby

require File.expand_path('../config/environment', __dir__)

repo_dir = "#{ENV['HOME']}/github/evertrue"

FileUtils.mkdir_p(GithubRepo::ORG_DIR)
Dir.chdir(GithubRepo::ORG_DIR)

GithubClient.new.repositories.each do |repo|
  unless File.exist?(repo['name'])
    puts "cloning #{repo['name']} to #{repo_dir}"
    `git clone #{repo['ssh_url']}`
  end

  GithubRepo.new(repo['name']).update!
end
