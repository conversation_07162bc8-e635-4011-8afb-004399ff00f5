#!/usr/bin/env ruby
# frozen_string_literal: true

require File.expand_path('../config/environment', __dir__)
require 'active_support/all'

evertrue_libs = [
  'searchutils',
  'webcommon',
  'authclient',
  'dna_client',
  'AssignmentsClient',
  'contacts_client',
  'ems_client',
  'ugc_client'
]

csv = CSV.open('basepom-report.csv', 'wb')
csv << ['repository', 'last_modified_on', 'basepom_version'] + evertrue_libs.map { |lib| lib + '_versions' }

GithubRepo.all_repos
  .select(&:pom?)
  .sort { |a, b| a.base_pom_version == b.base_pom_version ? a.repo_name <=> b.repo_name : b.base_pom_version.to_s <=> a.base_pom_version.to_s }
  .each do |repo|
  csv << [repo.repo_name, repo.git_date, repo.base_pom_version] + evertrue_libs.map { |lib| repo.dependency_versions('com.evertrue', lib).join(',') }
end

csv.close
