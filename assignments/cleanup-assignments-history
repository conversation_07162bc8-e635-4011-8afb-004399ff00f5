#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)
require 'active_support/core_ext/array'
require 'csv'

volunteer_mysql_client = MySQLHelpers.create_client(:volunteers)

csv = CSV.open('deleted-assignments-history-records.csv', 'wb')
csv << ['id', 'type', 'record']

assignment_history_ids_without_a_solicitor = volunteer_mysql_client.query(
  'SELECT assignments_history.id 
  FROM assignments_history LEFT JOIN solicitors ON solicitors.id = assignments_history.solicitor_id 
  WHERE assignments_history.solicitor_id IS NOT NULL 
  AND solicitors.id IS NULL', as: :array).to_a.flatten

assignment_history_ids_without_a_solicitor.in_groups_of(1_000, false) do |batch_of_assignment_history_ids|
  assignments_history = VolunteerDB::AssignmentsHistory.where(id: batch_of_assignment_history_ids).to_a
  assignments_history.each do |assignment_history|
    csv << [assignment_history.id, 'assignment_history', assignment_history.attributes.to_h.to_json]
  end

  VolunteerDB::AssignmentsHistory.where(id: batch_of_assignment_history_ids).delete_all
end

csv.close
