#!/usr/bin/env ruby

# Purpose is to create a V3 assignment import files
# Note if you are using this outside of IR6, you should review as there are some
# things specific to that incident in here

require_relative '../../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oids_option

  opts.on('-b', '--backup-host MYSQL_BACKUP_HOST', 'Specify a MySQL backup host') do |v|
    opts[:backup_host] = v
  end

  opts.on('--skip-duplicates', 'Skip assignments which are found in the current database') do |v|
    opts[:skip_duplicates] = true
  end

  opts.on('--db-restored-at', 'Time when database was restored') do |v|
    opts[:db_restored_at] = v
  end

  opts.on('--user-delete-cutover', 'Time when user deletes became valid') do |v|
    opts[:user_delete_cutover] = v
  end

  opts.on('--dir OUTPUT_DIR', 'Directory to put generated files in') do |v|
    opts[:output_dir] = v
  end
end

require_relative '../../../config/environment'
require 'active_record'
ActiveRecord::Base.logger = LOG

CHRONOMETER_USER = '187265'

def volunteer_db_backup
  @volunteer_db_backup ||= DynamicActiveModelGenerator.create_database_models_for_backup('volunteers', SCRIPT_OPTIONS[:backup_host])
end

def base_assignment_class
  @base_assignment_class ||=
    begin
      if SCRIPT_OPTIONS[:backup_host]
        volunteer_db_backup()
        VolunteersBackupDB::Assignment
      else
        VolunteerDB::Assignment
      end
    end
end

def base_solicitor_class
  @base_solicitor_class ||=
  begin
    if SCRIPT_OPTIONS[:backup_host]
      volunteer_db_backup()
      VolunteersBackupDB::Solicitor
    else
      VolunteerDB::Solicitor
    end
  end
end

def base_secondary_prospect_class
  @base_secondary_prospect_class ||=
  begin
    if SCRIPT_OPTIONS[:backup_host]
      volunteer_db_backup()
      VolunteersBackupDB::SecondaryProspect
    else
      VolunteerDB::SecondaryProspect
    end
  end
end

if SCRIPT_OPTIONS[:backup_host]
  base_solicitor_class.class_eval do 
    belongs_to :contact, class_name: 'ContactDB::Contact'
  end
end

if SCRIPT_OPTIONS[:backup_host]
  base_secondary_prospect_class.class_eval do 
    belongs_to :prospect_contact, class_name: 'ContactDB::Contact'
  end
end

class AssignmentCSVRow < base_assignment_class
  belongs_to :prospect_contact, class_name: 'ContactDB::Contact'

  include CSVUtils::CSVRow


  csv_column("assignment_remote_id") { remote_id }
  csv_column("prospect_remote_id") { prospect_contact.remote_id }
  csv_column("stage") { stage } 
  csv_column("key_prospect") { key_prospect }
  csv_column("prospect_assignment_date") { prospect_assignment_date }
  csv_column("primary_unit") { primary_unit }

  def get_errors(existing_assignments)
    errors = []
    errors << 'missing pool' unless pool
    errors << 'invalid pool type' unless pool.type == 1 || pool.type == 2
    errors << 'missing prospect contact' unless prospect_contact 
    errors << 'missing prospect remote id' if prospect_contact && !prospect_contact.remote_id 
    errors << 'missing solicitor remote id' unless solicitor_valid?
    
    exists_in = existing_assignments.dig(pool_id, prospect_contact_id, solicitor&.contact_id)
    if exists_in
      errors << "assignment already exists in #{exists_in}"
    end

    errors
  end

  def solicitor_valid?
    solicitor.nil? || !solicitor&.contact&.remote_id.nil?
  end
end

class RMAssignmentCSVRow < AssignmentCSVRow
  csv_column("team_name") { pool.name }
  csv_column("solicitor_title") { solicitor&.solicitor_title }
  csv_column("stage_start_date") { stage_start_date }
  csv_column("assignment_title") { title }
  csv_column("solicitor_remote_id") { solicitor&.contact&.remote_id }
end

class VolunteerAssignmentCSVRow < AssignmentCSVRow
  csv_column("pool_name") { pool.name } 
  csv_column("volunteer_remote_id") { solicitor&.contact&.remote_id }
  csv_column("linked_prospect_remote_id"){ secondary_prospects&.first&.prospect_contact&.remote_id }
  csv_column("solicitor_role"){ solicitor&.role }
  csv_column("ask_amount"){ ask_amount_in_cents }
  csv_column("committed_amount"){ committed_amount_in_dollars }
end

class SolicitorCSVRow < base_solicitor_class
  include CSVUtils::CSVRow

  belongs_to :contact, class_name: 'ContactDB::Contact'

  # must have same columns in same order as VolunteerAssingmentCsvRow
  csv_column("assignment_remote_id") { '' }
  csv_column("prospect_remote_id") { '' }
  csv_column("stage") { '' } 
  csv_column("key_prospect") { '' }
  csv_column("prospect_assignment_date") { '' }
  csv_column("primary_unit") { '' }

  def get_errors(existing_solicitors)
    errors = []
    errors << 'missing pool' unless pool
    errors << 'invalid pool type' unless pool.type == 1 || pool.type == 2
    errors << 'missing contact' unless contact
    
    exists_in = existing_solicitors.dig(pool_id, contact_id)
    if exists_in
      errors << "solicitor already exists in #{exists_in}"
    end

    errors
  end
end

class RMSolicitorCsvRow < SolicitorCSVRow
  include CSVUtils::CSVRow

  csv_column("team_name"){ pool.name }
  csv_column("solicitor_title"){ solicitor_title }
  csv_column("stage_start_date") { '' }
  csv_column("assignment_title") { '' }
  csv_column("solicitor_remote_id"){ contact.remote_id }
end


class VolSolicitorCsvRow < SolicitorCSVRow
  include CSVUtils::CSVRow

  csv_column("pool_name"){ pool.name }
  csv_column("volunteer_remote_id"){ contact.remote_id }
  csv_column("linked_prospect_remote_id"){ '' }
  csv_column("solicitor_role"){ role }
  csv_column("ask_amount"){ '' }
  csv_column("committed_amount"){ '' }
end

if SCRIPT_OPTIONS[:skip_duplicates]
  raise 'you probably want to set this, if not feel free to comment this.' unless SCRIPT_OPTIONS[:user_delete_cutover]
  raise 'you probably want to set this, if not feel free to comment this.' unless SCRIPT_OPTIONS[:db_restored_at]
end


output_dir = SCRIPT_OPTIONS[:output_dir] || '.'
AuthDB::Organization.where(id: SCRIPT_OPTIONS[:oids]).find_each do |org|
  vol_path = "#{output_dir}/#{org.slug}-#{org.id}-vol-assignments-#{Time.now.strftime('%Y-%m-%d')}.csv"
  rm_path = "#{output_dir}/#{org.slug}-#{org.id}-rm-assignments-#{Time.now.strftime('%Y-%m-%d')}.csv"
  errors_log_path = "#{output_dir}/#{org.slug}-#{org.id}-assignments-errors.log"

  existing_assignments = {}
  if SCRIPT_OPTIONS[:skip_duplicates]
    LOG.info("caching current assignment data")
    VolunteerDB::Assignment.where(oid: org.id).includes(:pool, solicitor: [contact: [:identities]], prospect_contact: [:identities]).find_each do |a|
      existing_assignments[a.pool_id] ||= {}
      existing_assignments[a.pool_id][a.prospect_contact_id] ||= {}
      existing_assignments[a.pool_id][a.prospect_contact_id][a.solicitor&.contact_id] = 'assignments'
    end

    VolunteerDB::AssignmentsHistory.where(oid: org.id).where("created_at > ?", SCRIPT_OPTIONS[:db_restored_at]).where('update_source != ?', CHRONOMETER_USER).where(record_action_taken: 3).find_each do |a|
      existing_assignments[a.pool_id] ||= {}
      existing_assignments[a.pool_id][a.prospect_contact_id] ||= {}
      existing_assignments[a.pool_id][a.prospect_contact_id][a.solicitor&.contact_id] ||= 'assignments_history'
    end
  end

  existing_solicitors = {}
  if SCRIPT_OPTIONS[:skip_duplicates]
    LOG.info("caching current solicitor data")
    VolunteerDB::Solicitor.where(oid: org.id).includes(:pool, contact: [:identities]).find_each do |s|
      existing_solicitors[s.pool_id] ||= {}
      existing_solicitors[s.pool_id][s.contact_id] = 'solicitors'
    end

    VolunteerDB::SolicitorsHistory.where(oid: org.id).where("created_at > ?", SCRIPT_OPTIONS[:user_delete_cutover]).where(record_action_taken: 3).find_each do |s|
      existing_solicitors[s.pool_id] ||= {}
      existing_solicitors[s.pool_id][s.contact_id] = 'solicitors_history'
    end
  end
  LOG.info "Writing errors to #{errors_log_path}"

  def handle_record(report, row, existing, errors_log)
    STATS.inc_and_notify
          
    errors = row.get_errors(existing)
    if errors.empty?
      report << row 
    else
      a = row.attributes.to_json
      msg = "#{errors.join('. ')}: #{a}\n"
      LOG.error msg
      errors_log.write(msg)
    end
  end

  File.open(errors_log_path, 'w') do |errors_log|
    LOG.info("creating import assignments file for #{org.name}/#{org.id}")
    CSVUtils::CSVReport.new(vol_path, VolunteerAssignmentCSVRow) do |report|
      VolunteerDB::Pool.where(oid: org.id, type: 1).find_each do |pool|
        VolunteerAssignmentCSVRow.includes(:pool, solicitor: [contact: [:identities]], secondary_prospects: [prospect_contact: [:identities]], prospect_contact: [:identities]).where(pool_id: pool.id, oid: org.id).find_each do |assignment|
          handle_record(report, assignment, existing_assignments, errors_log)
        end
        
        # some of these will be duplicative but counting on importer to merge them
        VolSolicitorCsvRow.includes(:pool, contact: [:identities]).where(pool_id: pool.id, oid: org.id).find_each do |solicitor|
          handle_record(report, solicitor, existing_solicitors, errors_log)
        end
      end
    end
    puts "created: #{vol_path}"

    CSVUtils::CSVReport.new(rm_path, RMAssignmentCSVRow) do |report|
      VolunteerDB::Pool.where(oid: org.id, type: 2).find_each do |pool|
        RMAssignmentCSVRow.includes(:pool, solicitor: [contact: [:identities]], prospect_contact: [:identities]).where(pool_id: pool.id, oid: org.id).find_each do |assignment|
          handle_record(report, assignment, existing_assignments, errors_log)
        end
        
        # some of these will be duplicative but counting on importer to merge them
        RMSolicitorCsvRow.includes(:pool, contact: [:identities]).where(pool_id: pool.id, oid: org.id).find_each do |solicitor|
          handle_record(report, solicitor, existing_solicitors, errors_log)
        end
      end
    end
    puts "created: #{rm_path}"
    puts "errors: #{errors_log_path}"

    # NOTE: Currently type=3 is unused, DXO pools are treated as type 2
  end
end

STATS.notify