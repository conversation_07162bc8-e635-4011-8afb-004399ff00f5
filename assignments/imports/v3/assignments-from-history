#!/usr/bin/env ruby

# WARNING: Solicitors cannot be restored from history using update-source as they do not store update source
#     !! THIS WILL CREATE AN INCOMPLETE DATA SET, MISSING SOLICITORS WHO AREN'T ASSIGNED TO A PROSPECT !!
#     When originally creating this script it did not consider solicitors without assignments, so those would
#     need to be added into this functionality, assuming you don't need to use update-source and are just 
#     filtering by time-range.


# Purpose is to create a V3 assignment import files from assignments history

require_relative '../../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oids_option

  opts.on('--from FROM_TIMESTAMP', 'When to start searching from') do |v|
    opts[:from] = v
  end

  opts.on('--update-source UPDATE_SOURCE', 'Update source to use, other sources will be ignored') do |v|
    opts[:update_source] = v
  end

  opts.on('--include-original-timestamps', 'Include the assignment_updated_at/created_at timestamps') do 
    opts[:include_original_timestamps] = true
  end

  opts.on('--include-history-id', 'Include the assignment history id') do 
    opts[:include_history_id] = true
  end

  opts.on('--output-path PATH', 'Path to output to') do |v|
    opts[:output_path] = v
  end

  opts.require_option(:update_source)
  opts.require_option(:from)
end

require_relative '../../../config/environment'
require 'active_record'
ActiveRecord::Base.logger = LOG

class AssignmentCSVRow < Struct.new(:id,
                                    :assignment_id,
                                    :oid,
                                    :pool_id,
                                    :solicitor_id,
                                    :prospect_contact_id,
                                    :title,
                                    :stage_start_date,
                                    :ask_amount_in_cents,
                                    :committed_amount_in_dollars,
                                    :create_source,
                                    :update_source,
                                    :remote_id,
                                    :assignment_updated_at,
                                    :assignment_created_at,
                                    :record_action_taken,
                                    :created_at,
                                    :pool,
                                    :solicitor,
                                    :prospect_contact, 
                                    :stage, 
                                    :key_prospect, 
                                    :prospect_assignment_date, 
                                    :primary_unit)
  include CSVUtils::CSVRow

  csv_column("assignment_remote_id") { remote_id }
  csv_column("prospect_remote_id") { prospect_contact&.remote_id }
  csv_column("stage") { stage } 
  csv_column("key_prospect") { key_prospect }
  csv_column("prospect_assignment_date") { prospect_assignment_date }
  csv_column("primary_unit") { primary_unit }
end

if SCRIPT_OPTIONS[:include_history_id]
  AssignmentCSVRow.class_eval do
    csv_column("evertrue_history_id") { id }
  end
end

if SCRIPT_OPTIONS[:include_original_timestamps]
  AssignmentCSVRow.class_eval do
    csv_column('original_created_at') { assignment_created_at }
    csv_column('original_updated_at') { assignment_updated_at }
  end
end

class RMAssignmentCSVRow < AssignmentCSVRow
  csv_column("team_name") { pool.name }
  csv_column("solicitor_title") { solicitor&.solicitor_title }
  csv_column("stage_start_date") { stage_start_date.present? && Time.at(stage_start_date/1000).strftime("%Y-%m-%d %H:%M:%S") }
  csv_column("assignment_title") { title }
  csv_column("solicitor_remote_id") { solicitor&.contact&.remote_id }
end

class VolunteerAssignmentCSVRow < AssignmentCSVRow
  csv_column("pool_name") { pool.name } 
  csv_column("volunteer_remote_id") { solicitor&.contact&.remote_id }
  csv_column("solicitor_role"){ solicitor&.role }
  csv_column("ask_amount"){ ask_amount_in_cents }
  csv_column("committed_amount"){ committed_amount_in_dollars }
end

def get_assignment_errors(assignment, pool_assignments, processed_assignemnts)
  errors = []
  errors << 'already added assignment' if processed_assignemnts.dig(assignment.pool_id, assignment.prospect_contact_id, assignment.solicitor&.contact_id)
  errors << 'missing pool' unless assignment.pool
  errors << 'invalid pool type' unless assignment.pool.type == 1 || assignment.pool.type == 2
  errors << 'missing prospect contact' unless assignment.prospect_contact 
  errors << 'missing prospect remote id' if assignment.prospect_contact && !assignment.prospect_contact.remote_id 
  errors << 'missing solicitor remote id' unless assignment.solicitor_valid?
  errors << "assignment was recreated already" if pool_assignments.dig(assignment.pool_id, assignment.prospect_contact_id, assignment.solicitor&.contact_id)
  errors
end

output_path = SCRIPT_OPTIONS[:output_path] || '.'
AuthDB::Organization.where(id: SCRIPT_OPTIONS[:oids]).find_each do |org|
  vol_path = "#{output_path}/#{org.slug}-#{org.id}-vol-assignments-#{Time.now.strftime('%Y-%m-%d')}.assignments.csv"
  rm_path = "#{output_path}/#{org.slug}-#{org.id}-rm-assignments-#{Time.now.strftime('%Y-%m-%d')}.solicitors.csv"
  errors_log_path = "#{output_path}/#{org.slug}-#{org.id}-assignments-errors.log"

  LOG.info("creating import assignments file for #{org.name}/#{org.id}")

  LOG.info("caching assignment data")
  pool_assignments = {}
  VolunteerDB::Assignment.where(oid: org.id).includes(:pool, solicitor: [contact: [:identities]], prospect_contact: [:identities]).find_each do |a|
    pool_assignments[a.pool_id] ||= {}
    pool_assignments[a.pool_id][a.prospect_contact_id] ||= {}
    pool_assignments[a.pool_id][a.prospect_contact_id][a.solicitor&.contact_id] = true
  end

  # LOG.info("caching solicitor data")
  # pool_assignments = {}
  # VolunteerDB::Solicitor.where(oid: org.id).includes(:pool, contact: [:identities]).find_each do |s|
  #   existing_solicitors[s.pool_id] ||= {}
  #   existing_solicitors[s.pool_id][s.contact_id] = true
  # end

  processed_assignemnts = {}

  File.open(errors_log_path, 'w') do |errors_log|
    CSVUtils::CSVReport.new(vol_path, VolunteerAssignmentCSVRow) do |vol_report|
      CSVUtils::CSVReport.new(rm_path, RMAssignmentCSVRow) do |rm_report|
        VolunteerDB::AssignmentsHistory.includes(:pool, solicitor: [contact: [:identities]], solicitors_history: [contact: [:identities]], prospect_contact: [:identities]).where(oid: org.id, update_source: SCRIPT_OPTIONS[:update_source], record_action_taken: 3).where('created_at > ?', SCRIPT_OPTIONS[:from]).find_each(order: :desc) do |assignment|
          errors = get_assignment_errors(assignment, pool_assignments, processed_assignemnts)
          if errors.empty?
            if assignment.pool.type == 1
              v = VolunteerAssignmentCSVRow.new(**assignment.attributes)
              v.pool = assignment.pool
              v.solicitor = assignment.available_solicitor
              v.prospect_contact = assignment.prospect_contact

              vol_report << v
            elsif assignment.pool.type == 2
              v = RMAssignmentCSVRow.new(**assignment.attributes)
              v.pool = assignment.pool
              v.solicitor = assignment.available_solicitor
              v.prospect_contact = assignment.prospect_contact

              rm_report << v
            else
              raise "unexpected pool type for assignment id=#{assignment.id}, pool type=#{assignment.pool.type}"
            end

            processed_assignemnts[assignment.pool_id] ||= {}
            processed_assignemnts[assignment.pool_id][assignment.prospect_contact_id] ||= {}
            processed_assignemnts[assignment.pool_id][assignment.prospect_contact_id][assignment.solicitor&.contact_id] = true

            STATS.inc_and_notify
          else
            msg = "#{errors.join('. ')}: #{assignment.attributes.to_json}"
            LOG.error msg
            errors_log.write(msg + "\n")
          end
        end
      end
    end
  end

  puts "#{vol_path}\n#{rm_path}\n#{errors_log_path}"
end 
STATS.notify
