#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option(false)
end

require File.expand_path('../config/environment', __dir__)
require 'csv'

def all_mysql_compound_ids(oid)
  assignments = VolunteerDB::Assignment.includes(:solicitor).where(oid: oid).to_a
  solicitors = VolunteerDB::Solicitor.where(oid: oid).to_a

  assignments.map(&:solicitor_compound_id).compact +
    assignments.map(&:prospect_compound_id) +
    solicitors.map(&:solicitor_compound_id)
end

def all_es_compound_ids(oid)
  scanner = ESContactHelpers.query_mapping_by_oid(:assignment, oid)
  scanner.fields_to_return = false
  scanner.to_a.map { |doc| doc['_id'] }
end

oids = SCRIPT_OPTIONS[:oid] ? [SCRIPT_OPTIONS[:oid]] : VolunteerDB::Solicitor.select('DISTINCT oid').to_a.map(&:oid).compact.sort

csv = CSV.open("assignments-out-of-sync-#{SCRIPT_OPTIONS[:oid] || 'all'}-#{Time.now.strftime('%Y-%m-%d')}.csv", 'wb')
csv << ['oid', 'compound_id', 'in_mysql', 'in_es']

oids.each do |oid|
  LOG.info "Verifying oid #{oid} is in sync"

  mysql_compound_ids = all_mysql_compound_ids(oid)
  es_compound_ids = all_es_compound_ids(oid)

  not_in_mysql = es_compound_ids - mysql_compound_ids
  not_in_es = mysql_compound_ids - es_compound_ids

  not_in_mysql.each do |compound_id|
    csv << [oid, compound_id, 'N', 'Y']
  end

  not_in_es.each do |compound_id|
    csv << [oid, compound_id, 'Y', 'N']
  end
end

csv.close
