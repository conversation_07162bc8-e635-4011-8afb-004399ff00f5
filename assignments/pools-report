#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)

def assignments_client
  @assignments_client ||= AssignmentsClient.create_client_with_app_creds
end

date = Time.now.strftime('%Y%m%d')

class PoolCSVRow < Struct.new(
        :id,
        :oid,
        :name,
        :pool_type,
        :giving_category_label,
        :creator_user_id,
        :selection_mode,
        :stage_group_id,
        :created_at,
        :updated_at,
        :total_dollars_raised,
        :pool_participation_rate,
        :assigned_prospect_rate,
        :total_prospect_count,
        :total_solicitor_count,
        :visit_threshold,
        :contact_threshold,
        :duration_type,
        :start_date,
        :end_date,
        :is_auto_rollover,
        :reset_stages,
        :unassign_prospects,
        :resource_url,
        :giving_page_url,
        :chat_enabled,
        :lead_updates_enabled,
        :participation_goal,
        :dollar_amount_goal,
        :pool_id
      )

  include CSVUtils::CSVRow

  members.each { |field| csv_column(field) }

  def self.from_hash(hash)
    pool = new
    hash.each do |key, value|
      pool.send("#{key}=", value)
    end
    pool
  end
end

VolunteerDB::Pool.select('DISTINCT oid').to_a.map(&:oid).each do |oid|
  LOG.info("fetch pools for oid #{oid}")

  pools = assignments_client.get_all_pools(oid)

  File.open("assignment-pools-oid-#{oid}-#{date}.json", 'wb') do |f|
    f.write(JSON.pretty_generate(pools))
  end

  if pools.size == 0
    LOG.info("no pools found for oid #{oid}")
    next
  end

  CSVUtils::CSVReport.new("assignment-pools-oid-#{oid}-#{date}.csv", PoolCSVRow) do |report|
    pools.map { |entry| PoolCSVRow.from_hash(entry) }.each { |pool| report << pool }
  end
end
