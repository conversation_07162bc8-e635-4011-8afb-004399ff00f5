#!/usr/bin/env ruby
# frozen_string_literal: true

=begin
Purpose: to report on number of records in big/small index as well as how many records are in MySQL
=end

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)
require 'elasticsearch_facet_scanner'

class OidAssignmentCSVRow
  include CSVUtils::CSVRow

  csv_column :oid
  csv_column :num_mysql_assignment_records
  csv_column :num_small_index_records
  csv_column :num_big_index_records
  csv_column :is_big_customer
  csv_column :records_in_wrong_index

  attr_reader :oid

  def initialize(oid)
    @oid = oid
  end

  def self.dna_client
    @dna_client ||= DNAClient.create_client_with_app_creds
  end

  def num_mysql_assignment_records
    @num_mysql_assignment_records ||= VolunteerDB::Assignment.where(oid: oid).count
  end

  def num_small_index_records
    @num_small_index_records ||= get_es_count("#{ESContactHelpers.index_prefix}-small-contacts")
  end

  def num_big_index_records
    @num_big_index_records ||= get_es_count("#{ESContactHelpers.index_prefix}-big-contacts")
  end

  def is_big_customer
    @is_big_customer ||= self.class.dna_client.is_big_customer?(oid) ? 'Y' : 'N'
  end

  def records_in_wrong_index
    result =
      if is_big_customer == 'Y'
        num_small_index_records > 0
      else
        num_big_index_records > 0
      end

    result ? 'Y' : 'N'
  end

  def get_es_count(index_name, mapping_name = :assignment)
    path = "/#{index_name}/#{mapping_name}/_count?q=#{ESContactHelpers::MAPPING_TO_OID_PROPERTY[mapping_name]}:#{oid}"
    res = HTTPClient.get(ESHelpers.uri_for_path(path))
    JSON.parse(res.body)['count']
  end
end

oids = VolunteerDB::Assignment.select('DISTINCT oid').to_a.map(&:oid).to_set

url = "#{ES_URL}/#{ESContactHelpers.index_prefix}-small-contacts/assignment/_search"
scanner = ElasticSearchFacetScanner.new(url, :contact_oid)
scanner.each do |result|
  oids << result['key']
end

url = "#{ES_URL}/#{ESContactHelpers.index_prefix}-big-contacts/assignment/_search"
scanner = ElasticSearchFacetScanner.new(url, :contact_oid)
scanner.each do |result|
  oids << result['key']
end

CSVUtils::CSVReport.new("#{ENV['RAILS_ENV']}-assignments-record-counts-#{Time.now.strftime('%Y-%m-%d')}.csv", OidAssignmentCSVRow) do |report|
  oids.to_a.sort.each do |oid|
    report << OidAssignmentCSVRow.new(oid)
  end
end
