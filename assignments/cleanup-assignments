#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)
require 'active_support/core_ext/array'
require 'csv'

volunteer_mysql_client = MySQLHelpers.create_client(:volunteers)

csv = CSV.open('deleted-assignments-records.csv', 'wb')
csv << ['id', 'type', 'record']

assignment_ids_without_a_pool = volunteer_mysql_client.query('SELECT assignments.id FROM assignments LEFT JOIN pools ON pools.id = assignments.pool_id WHERE pools.id IS NULL', as: :array).to_a.flatten

assignment_ids_without_a_pool.in_groups_of(1_000, false) do |batch_of_assignment_ids|
  assignments = VolunteerDB::Assignment.where(id: batch_of_assignment_ids).to_a
  assignments.each do |assignment|
    csv << [assignment.id, 'assignment', assignment.attributes.to_h.to_json]
  end

  VolunteerDB::Assignment.where(id: batch_of_assignment_ids).delete_all
end

assignment_ids_without_a_solicitor = volunteer_mysql_client.query('SELECT assignments.id FROM assignments LEFT JOIN solicitors ON solicitors.id = assignments.solicitor_id WHERE assignments.solicitor_id IS NOT NULL AND solicitors.id IS NULL', as: :array).to_a.flatten

assignment_ids_without_a_solicitor.in_groups_of(1_000, false) do |batch_of_assignment_ids|
  assignments = VolunteerDB::Assignment.where(id: batch_of_assignment_ids).to_a
  assignments.each do |assignment|
    csv << [assignment.id, 'assignment', assignment.attributes.to_h.to_json]
  end

  VolunteerDB::Assignment.where(id: batch_of_assignment_ids).delete_all
end

solicitor_ids_without_a_pool = volunteer_mysql_client.query('SELECT solicitors.id FROM solicitors LEFT JOIN pools ON pools.id = solicitors.pool_id WHERE pools.id IS NULL', as: :array).to_a.flatten

solicitor_ids_without_a_pool.in_groups_of(1_000, false) do |batch_of_solicitor_ids|
  solicitors = VolunteerDB::Solicitor.where(id: batch_of_solicitor_ids).to_a
  solicitors.each do |solicitor|
    csv << [solicitor.id, 'solicitor', solicitor.attributes.to_h.to_json]
  end

  VolunteerDB::Solicitor.where(id: batch_of_solicitor_ids).delete_all
end

secondary_prospect_ids_without_an_assignment = volunteer_mysql_client.query('SELECT secondary_prospects.id FROM secondary_prospects LEFT JOIN assignments ON assignments.id = secondary_prospects.assignment_id WHERE assignments.id IS NULL', as: :array).to_a.flatten

secondary_prospect_ids_without_an_assignment.in_groups_of(1_000, false) do |batch_of_secondary_prospect_ids|
  secondary_prospects = VolunteerDB::SecondaryProspect.where(id: batch_of_secondary_prospect_ids).to_a
  secondary_prospects.each do |secondary_prospect|
    csv << [secondary_prospect.id, 'secondary_prospect', secondary_prospect.attributes.to_h.to_json]
  end

  VolunteerDB::SecondaryProspect.where(id: batch_of_secondary_prospect_ids).delete_all
end

csv.close
