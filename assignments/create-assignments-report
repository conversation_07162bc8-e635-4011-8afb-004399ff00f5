#!/usr/bin/env ruby

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-t', '--pool-type POOL_TYPE', Integer, 'Type of pool') do |v|
    opts[:pool_type] = v
  end

  opts.require_option(:pool_type)
end

require_relative '../config/environment'

def org
  @org ||= AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])
end

def pools
  @pools ||= VolunteerDB::Pool.where(oid: org.id, type: SCRIPT_OPTIONS[:pool_type]).to_a
end

class AssignmentCSVRow < VolunteerDB::Assignment
  include CSVUtils::CSVRow

  csv_column('AssignmentID') { remote_id }
  csv_column('TeamName') { pool.name }
  csv_column('ProspectID') { prospect_contact.remote_id }
  csv_column('SolicitorID') { solicitor.remote_id }
  csv_column('SolicitorTitle') {  solicitor.solicitor_title }
  csv_column('Stage') { stage }
  csv_column('StageStartDate') { stage_start_date && Time.at(stage_start_date / 1000).strftime('%Y-%m-%d') }
  csv_column('AssignmentTitle') { title }
end

CSVUtils::CSVReport.new("#{org.slug}-assignments-#{Time.now.strftime('%Y-%m-%d')}.csv", AssignmentCSVRow) do |report|
  AssignmentCSVRow
    .includes(
      :pool,
      prospect_contact: [:identities],
      solicitor: [contact: [:identities]]
    )
    .where(
      pool_id: pools.map(&:id)
    )
    .each do |assignment|
    report << assignment
  end
end
