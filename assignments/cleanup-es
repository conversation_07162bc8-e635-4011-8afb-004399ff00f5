#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option(false)
  opts.add_run_remote
end

require File.expand_path('../config/environment', __dir__)
require 'csv'
require 'set'

def all_mysql_compound_ids(oid)
  assignments = VolunteerDB::Assignment.includes(:solicitor).where(oid: oid).to_a
  solicitors = VolunteerDB::Solicitor.where(oid: oid).to_a

  assignments.map(&:solicitor_compound_id).compact +
    assignments.map(&:prospect_compound_id) +
    solicitors.map(&:solicitor_compound_id)
end

oids = SCRIPT_OPTIONS[:oid] ? [SCRIPT_OPTIONS[:oid]] : VolunteerDB::Pool.select('DISTINCT oid').to_a.map(&:oid).compact.sort

docs_to_delete = []
delete_docs_proc = Proc.new do
  actions = docs_to_delete.map do |doc|
    {
      delete: doc.slice('_index', '_id', '_type', '_routing')
    }
  end

  ESHelpers.bulk_request(actions)

  STATS[:bulk_calls] += 1

  docs_to_delete = []
end

oids.each do |oid|
  mysql_compound_ids = all_mysql_compound_ids(oid).to_set

  scanner = ESContactHelpers.query_mapping_by_oid(:assignment, oid)
  scanner.fields_to_return = false
  scanner.each do |doc|
    STATS.inc_and_notify
    next if mysql_compound_ids.include?(doc['_id'])

    LOG.info "deleting assignment ES doc #{doc}"

    docs_to_delete << doc

    STATS[:docs_to_delete] += 1

    delete_docs_proc.call if docs_to_delete.size >= 100
  end
end

delete_docs_proc.call if docs_to_delete.size > 0

STATS.notify
