#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_run_remote
end

require File.expand_path('../config/environment', __dir__)

def assignment_client
  @assignment_client ||= AssignmentsClient.create_client_with_app_creds
end

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

stats = Hash.new(0)

pool_ids = VolunteerDB::Pool.where(oid: SCRIPT_OPTIONS[:oid], type: 1).map(&:id)
assignments = {}
stop = false
VolunteerDB::Assignment.includes(:solicitor).where(oid: SCRIPT_OPTIONS[:oid], pool_id: pool_ids).find_each do |assignment|
  next unless assignment.ask_amount_in_cents

  assignments[assignment.pool_id] ||= {}
  if (previous_assignment = assignments[assignment.pool_id][assignment.prospect_contact_id])
    LOG.error "2 assignments in same pool #{previous_assignment.id} other #{assignment.id}"
    stop = true
  end

  assignments[assignment.pool_id][assignment.prospect_contact_id] = assignment
end

exit(1) if stop

VolunteerDB::AssignmentsHistory.includes(:solicitor, :solicitors_history).where(oid: SCRIPT_OPTIONS[:oid], pool_id: pool_ids, record_action_taken: 3, update_source: '187265').where('created_at > ?', '2024-08-01').where.not(ask_amount_in_cents: nil).find_each do |history|
  current_assignment = assignments.dig(history.pool_id, history.prospect_contact_id)

  if current_assignment.nil?
    stats[:no_current_assignment] += 1
    next
  end

  if current_assignment.ask_amount_in_cents == history.ask_amount_in_cents
    stats[:correct_ask_amount] += 1
  elsif current_assignment.ask_amount_in_cents == (history.ask_amount_in_cents * 100)
    stats[:ask_amount_needs_correct] += 1
    LOG.info("updating assignment #{current_assignment.attributes.to_h.to_json} ask_amount_in_cents to #{history.ask_amount_in_cents}")
    current_assignment.ask_amount_in_cents = history.ask_amount_in_cents
    current_assignment.save!
    assignment_client.sync_assignment_to_es(current_assignment.oid, current_assignment.id)
  else
    stats[:incorrect_ask_amount] += 1
  end
end

LOG.info(stats.to_json)
