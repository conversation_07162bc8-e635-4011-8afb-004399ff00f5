#!/usr/bin/env ruby

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oids_option

  opts.on('-b', '--backup-host MYSQL_BACKUP_HOST', 'Specify a MySQL backup host') do |v|
    opts[:backup_host] = v
  end

  opts.on('-o', '--out OUTPUT_PATH', 'Specify output path for report')  do |v|
    opts[:out] = v
  end

  opts.require_option(:backup_host)
end

require_relative '../config/environment'
require 'active_record'
ActiveRecord::Base.logger = LOG

def volunteer_db_backup
    @volunteer_db_backup ||= DynamicActiveModelGenerator.create_database_models_for_backup('volunteers', SCRIPT_OPTIONS[:backup_host])
end
volunteer_db_backup()

out = SCRIPT_OPTIONS[:out] || "restoration-report-#{Time.now.strftime('%Y-%m-%d')}.csv"
puts out

record_counts = {}
oids_joined = SCRIPT_OPTIONS[:oids].join(',')

# Count active solicitors for the given orgs
VolunteerDB::Solicitor.connection.execute("select oid, count(*) from solicitors where oid in (#{oids_joined}) group by oid;").each do |row|
    oid = row[0]
    ct = row[1]

    record_counts[oid] ||= {}
    record_counts[oid]['active'] ||= {}
    record_counts[oid]['active']['solicitors'] = ct
end


# Count backup solicitors for the given orgs
# We do 
VolunteersBackupDB::Solicitor.connection.execute("select oid, count(*) from solicitors where oid in (#{oids_joined}) group by oid;").each do |row|
    oid = row[0]
    ct = row[1]

    record_counts[oid] ||= {}
    record_counts[oid]['backup'] ||= {}
    record_counts[oid]['backup']['solicitors'] = ct
end

# Remove active solicitors from backup counts who have been deleted, after the 26th. This reflects that these backups should not be restored, since they were deleted by the user.
# Must include the pool for oid, solicitors_history is missing oid since 2019
# According to the CW logs, the last exection time of contactCleanup was 2024-08-26T14:07:07 and it took about 10 minutes to run. Meaning any 
# deletes which happened after that time should be legitimate user deletes which we don't want to overwrite.
USER_DELETE_CUTOVER = '2024-08-26 14:17'
VolunteerDB::SolicitorsHistory.connection.execute("select p.oid, count(*) from solicitors_history s inner join pools p on p.id = s.pool_id where s.record_action_taken = 3 and s.created_at > '#{USER_DELETE_CUTOVER}' and p.oid in (#{oids_joined});").each do |row|
    oid = row[0]
    ct = row[1]

    record_counts[oid] ||= {}
    record_counts[oid]['backup'] ||= {}

    current = record_counts[oid]['backup']['solicitors'] || 0
    record_counts[oid]['backup']['solicitors'] = current - ct
end


# Count active prospects for the given orgs, prospects are assignment records with null solicitor_id
VolunteerDB::Assignment.connection.execute("select oid, count(*) from assignments where solicitor_id is null and oid in (#{oids_joined}) group by oid;").each do |row|
    oid = row[0]
    ct = row[1]

    record_counts[oid] ||= {}
    record_counts[oid]['active'] ||= {}
    record_counts[oid]['active']['prospects'] = ct
end

# Count backup prospects for the given orgs
VolunteersBackupDB::Assignment.connection.execute("select oid, count(*) from assignments where solicitor_id is null and oid in (#{oids_joined}) group by oid;").each do |row|
    oid = row[0]
    ct = row[1]

    record_counts[oid] ||= {}
    record_counts[oid]['backup'] ||= {}
    record_counts[oid]['backup']['prospects'] = ct
end

# Remove prospects from backup counts which:
# - were deleted after 2024-08-26 14:17 by the user (update source is not chronometer)
DB_RESTORED_AT = '2024-08-23 01:00:00'
CHRONOMETER = '187265'
VolunteerDB::AssignmentsHistory.connection.execute("select oid, count(*) from assignments_history where solicitor_id is null and record_action_taken = 3 and update_source != '#{CHRONOMETER}' and created_at > '#{DB_RESTORED_AT}' and oid in (#{oids_joined}) group by oid;").each do |row|
    oid = row[0]
    ct = row[1]

    record_counts[oid] ||= {}
    record_counts[oid]['backup'] ||= {}

    current = record_counts[oid]['backup']['prospects'] || 0
    record_counts[oid]['backup']['prospects'] = current - ct
end

# Count active assignments for the given orgs
VolunteerDB::Assignment.connection.execute("select oid, count(*) from assignments where solicitor_id is not null and oid in (#{oids_joined}) group by oid;").each do |row|
    oid = row[0]
    ct = row[1]

    record_counts[oid] ||= {}
    record_counts[oid]['active'] ||= {}
    record_counts[oid]['active']['assignments'] = ct
end

# Count backup assignments for the given orgs
VolunteersBackupDB::Assignment.connection.execute("select oid, count(*) from assignments where solicitor_id is not null and oid in (#{oids_joined}) group by oid;").each do |row|
    oid = row[0]
    ct = row[1]

    record_counts[oid] ||= {}
    record_counts[oid]['backup'] ||= {}
    record_counts[oid]['backup']['assignments'] = ct
end

# Remove assignments from backup counts which:
# - were deleted after 2024-08-26 14:17 by the user (update source is not chronometer)
VolunteerDB::AssignmentsHistory.connection.execute("select oid, count(*) from assignments_history where solicitor_id is not null and record_action_taken = 3 and update_source != '#{CHRONOMETER}' and created_at > '#{DB_RESTORED_AT}' and oid in (#{oids_joined}) group by oid;").each do |row|
    oid = row[0]
    ct = row[1]

    record_counts[oid] ||= {}
    record_counts[oid]['backup'] ||= {}

    current = record_counts[oid]['backup']['assignments'] || 0
    record_counts[oid]['backup']['assignments'] = current - ct
end

columns = [
    :org_slug, 
    :oid, 
    :record_type, 
    :backup_count,
    :active_count
]

CSV.open(out, 'w') do |csv|
    csv << columns
    AuthDB::Organization.where(id: SCRIPT_OPTIONS[:oids]).find_each do |org|
        ['assignments', 'prospects', 'solicitors'].each do |record_type|
            row = []
            row << org.slug
            row << org.id
            row << record_type
            ['backup', 'active'].each do |db_type|
                count = record_counts.dig(org.id, db_type, record_type) || 0
                row << count
            end
            csv << row
        end

    end
end