#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_run_remote
end

require File.expand_path('../config/environment', __dir__)
require 'active_record'
ActiveRecord::Base.logger = LOG

def org
  @org ||= AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])
end

def volunteer_stage_groups
  @volunteer_stage_groups ||= VolunteerDB::StageGroup.where(oid: SCRIPT_OPTIONS[:oid], type: 1).index_by(&:id)
end

def volunteer_pools
  @volunteer_pools ||= VolunteerDB::Pool.where(stage_group_id: volunteer_stage_groups.keys).index_by(&:id)
end

def volunteer_solicitors
  @volunteer_solicitors ||= VolunteerDB::Solicitor.where(pool_id: volunteer_pools.keys).group_by(&:contact_id)
end

def volunteer_affiliations
  @volunteer_affiliations ||= org.affiliations.includes(:user).where(contact_id: volunteer_solicitors.keys).index_by(&:contact_id)
end

def volunteer_affiliation_invitations
  @volunteer_affiliation_invitations ||= AuthDB::AffiliationInvitation.where(organization_id: org.id, contact_id: volunteer_solicitors.keys).index_by(&:contact_id)
end

def volunteer_total_sessions
  @volunteer_total_sessions ||=
    begin
      user_ids = volunteer_affiliations.values.map(&:user_id)
      total_sessions = AuthDB::Session.select('user_id, count(*) as cnt, max(created_at) as most_recent').where(organization_id: org.id, user_id: user_ids, session_type: 'SCOPED').group(:user_id).to_a
      total_archive_sessions = AuthDB::ArchivedSession.select('user_id, count(*) as cnt, max(created_at) as most_recent').where(organization_id: org.id, user_id: user_ids, session_type: 'SCOPED').group(:user_id).to_a

      totals = {}
      user_ids.each do |user_id|
        totals[user_id] = {
          cnt: 0,
          most_recent: nil
        }
      end

      total_sessions.each do |result|
        totals[result.user_id][:cnt] = result.cnt
        totals[result.user_id][:most_recent] = result.most_recent
      end

      total_archive_sessions.each do |result|
        totals[result.user_id][:cnt] += result.cnt
        totals[result.user_id][:most_recent] ||= result.most_recent
      end

      totals
    end
end

def volunteer_contacts
  @volunteer_contacts ||= SolicitorContactCSVRow.includes(:contact_attribute, :identities, :emails).where(id: volunteer_solicitors.keys).to_a.each do |contact|
    contact.volunteer_solicitors = volunteer_solicitors[contact.id]
    contact.volunteer_pools = contact.volunteer_solicitors.map { |s| volunteer_pools[s.pool_id] }
    contact.volunteer_affiliation = volunteer_affiliations[contact.id]
    contact.volunteer_total_sessions = volunteer_total_sessions[contact.volunteer_affiliation&.user_id] || {}
    contact.affiliation_invitation = volunteer_affiliation_invitations[contact.id]
  end
end

class SolicitorContactCSVRow < ContactDB::Contact
  include CSVUtils::CSVRow

  attr_accessor :volunteer_solicitors,
                :volunteer_pools,
                :volunteer_affiliation,
                :volunteer_total_sessions,
                :affiliation_invitation

  csv_column(:evertrue_contact_id) { id }
  csv_column(:evertrue_user_id) { volunteer_affiliation&.user&.id }
  csv_column(:constituent_id) { remote_id }
  csv_column(:email) { volunteer_affiliation&.user&.email || primary_email }
  csv_column :name
  csv_column :pool_memberships
  csv_column(:total_sessions) { volunteer_total_sessions[:cnt] || 0 }
  csv_column(:most_recent_session) { volunteer_total_sessions[:most_recent]&.iso8601 }

  def pool_memberships
    volunteer_pools.map(&:name).join(', ')
  end
end

file_name = TMP_DIR.join('reports', 'assignments', "#{org.slug}-#{org.id}-volunteers-#{Time.now.strftime('%Y-%m-%d')}.csv").to_s
puts file_name
FileUtils.mkdir_p(File.dirname(file_name))

CSVUtils::CSVReport.new(file_name, SolicitorContactCSVRow) do |report|
  volunteer_contacts.each do |contact|
    report << contact
  end
end
