#!/usr/bin/env ruby

# This script calls assignment refresh for all assignments with most recent data

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option(false)
  opts.add_run_remote
end

require File.expand_path('../config/environment', __dir__)
require 'active_record'

ActiveRecord::Base.logger = LOG

def volunteer_mysql_client
  @volunteer_mysql_client ||= MySQLHelpers.create_client(:volunteers)
end

def assignments_api_client
  @assignments_api_client ||= AssignmentsClient.create_client_with_app_creds
end

def refresh_assignment(oid, assignment_id)
  res = assignments_api_client.sync_assignment_to_es(
    oid,
    assignment_id
  )

  unless res.kind_of?(Net::HTTPSuccess)
    LOG.error("failed to refresh assignment for #{oid}/#{assignment_id}/ #{res.code}/#{res.body}")
    return false
  end

  true
end

def active_oids
  # use specified oid or find all oids with assignments
  oids = SCRIPT_OPTIONS[:oid] ? [SCRIPT_OPTIONS[:oid]] : VolunteerDB::Assignment.select('DISTINCT oid').to_a.map(&:oid)

  # now verify oids are valid and active
  AuthDB::Organization.where(id: oids, deleted: false).to_a.map(&:id)
end

def all_assignment_ids_with_most_recent_data(oid)
  sql = <<SQL
SELECT DISTINCT assignment_id
  FROM assignments_most_recent
  WHERE oid = #{oid}
SQL
          
  volunteer_mysql_client.query(sql, as: :array).map(&:first)
end

active_oids.each do |oid|
  LOG.info("working on oid #{oid}")

  all_assignment_ids_with_most_recent_data(oid).each do |assignment_id|
    STATS.inc_and_notify

    if refresh_assignment(oid, assignment_id)
      STATS.update(:refreshed_assigment)
    else
      STATS.update(:refresh_failed)
    end      
  end
end

STATS.notify(true)
