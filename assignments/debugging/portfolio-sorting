#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse!

require_relative '../../config/environment'

oid = 633
pool_id = 9631
solicitor_contact_id = 71668874
stage = 'Qualification'
et_interaction_types = [
  'Note',
  'EverTrue Comment',
  'DXO Ask'
]

class ContactInteractions < Struct.new(
        :contact_id,
        :interactions,
        :assignment
      )

  include TimeHelpers

  def <<(interaction)
    interactions << interaction
  end

  def contact
    @contact ||= ContactDB::Contact.find(contact_id)
  end

  def name
    contact.name
  end

  def most_recent_interaction
    @most_recent_interaction ||= interactions.sort_by(&:date_occurred).last
  end

  def interaction_id
    most_recent_interaction.id
  end

  def date_occurred
    most_recent_interaction.date_occurred
  end

  def date_occurred_at
    Time.at(date_occurred / 1000).iso8601
  end

  def most_recent_contact_date
    assignment.assignments_most_recents.detect { |a| a.most_recent_type == 1 }.date_occurred
  end

  def most_recent_contact_date_at
    Time.at(most_recent_contact_date / 1000).iso8601
  end

  def most_recent_contact_date_ago
    time_in_words(Time.at(most_recent_contact_date / 1000))
  end
end

solicitor = VolunteerDB::Solicitor.where(
  oid: oid,
  contact_id: solicitor_contact_id,
  pool_id: pool_id
).first

assignments = VolunteerDB::Assignment
                 .includes(
                   :assignments_most_recents
                 )
                .where(
                  solicitor_id: solicitor.id,
                  stage: stage
                ).to_a

contact_assignment_proc = proc do |contact_id|
  assignments.detect { |a| a.prospect_contact_id == contact_id }
end

prospect_contact_ids = assignments.map(&:prospect_contact_id)

interactions = UgcDB::Interaction
                 .joins(
                   :interaction_solicitors,
                   :interaction_targets
                 )
                 .where(
                   interaction_targets: {
                     target_id: prospect_contact_ids,
                     primary: true
                   },
                   interaction_solicitors: {
                     contact_id: solicitor_contact_id
                   },
                   oid: oid
                 )
                 .where
                 .not(
                   interaction_type: et_interaction_types
                 )
                 .to_a

contact_interactions = {}
interactions.each do |interaction|
  interaction.interaction_targets.each do |interaction_target|
    next unless interaction_target.primary?
    next unless prospect_contact_ids.include?(interaction_target.target_id)

    (contact_interactions[interaction_target.target_id] ||= ContactInteractions.new(
       interaction_target.target_id,
       [],
       contact_assignment_proc.call(interaction_target.target_id)
     )
    ) << interaction
  end
end

contact_interactions = contact_interactions.values
contact_interactions.sort_by!(&:date_occurred)

headings = {
  'contact_id' => :contact_id,
  'name' => :name,
  'interaction_id' => :interaction_id,
  'date_occurred' => :date_occurred_at,
  'contact_date' => :most_recent_contact_date_at,
  'contact_ago' => :most_recent_contact_date_ago
}
table = TableView.new(headings, row_type: :object)
table.render('MySQL Primary Target Results' => contact_interactions)

interactions = UgcDB::Interaction
                 .joins(
                   :interaction_solicitors,
                   :interaction_targets
                 )
                 .where(
                   interaction_targets: {
                     target_id: prospect_contact_ids
                   },
                   interaction_solicitors: {
                     contact_id: solicitor_contact_id
                   },
                   oid: oid
                 )
                 .where
                 .not(
                   interaction_type: et_interaction_types
                 )
                 .to_a

contact_interactions = {}
interactions.each do |interaction|
  interaction.interaction_targets.each do |interaction_target|
    next unless prospect_contact_ids.include?(interaction_target.target_id)

    (contact_interactions[interaction_target.target_id] ||= ContactInteractions.new(
       interaction_target.target_id,
       [],
       contact_assignment_proc.call(interaction_target.target_id)
     )
    ) << interaction
  end
end

contact_interactions = contact_interactions.values
contact_interactions.sort_by!(&:date_occurred)

table = TableView.new(headings, row_type: :object)
table.render('MySQL Primary and Secondary Target Results' => contact_interactions)

path = "/#{ESContactHelpers.index_name_for_oid(oid)}/contact/_search"
uri = ESHelpers.uri_for_path(path)
body = File.read('assignments/debugging/portfolio-sorting.es.query.json')
body.gsub!('%oid%', oid.to_s)
body.gsub!('%pool_id%', pool_id.to_s)
body.gsub!('%solicitor_contact_id%', solicitor_contact_id.to_s)
body.gsub!('%stage%', stage)
query = JSON.parse(body)
res = HTTPClient.post(uri, query.to_json)
payload = JSON.parse(res.body)

class ContactHitResult < Struct.new(
        :contact_id,
        :score, # date_occurred ?
        :assignment
      )

  include TimeHelpers

  def contact
    @contact ||= ContactDB::Contact.find(contact_id)
  end

  def name
    contact.name
  end

  def date_occurred_at
    Time.at(score / 1000).iso8601
  end

  def most_recent_contact_date
    assignment.assignments_most_recents.detect { |a| a.most_recent_type == 1 }.date_occurred
  end

  def most_recent_contact_date_at
    Time.at(most_recent_contact_date / 1000).iso8601
  end

  def most_recent_contact_date_ago
    time_in_words(Time.at(most_recent_contact_date / 1000))
  end
end

contact_hit_results = payload['hits']['hits'].map do |hit|
  ContactHitResult.new(
    hit['_id'],
    hit['_score'],
    contact_assignment_proc.call(hit['_id'].to_i)
  )
end

headings = {
  'contact_id' => :contact_id,
  'name' => :name,
  'score' => :score,
  'date_occurred' => :date_occurred_at,
  'contact_date' => :most_recent_contact_date_at,
  'contact_ago' => :most_recent_contact_date_ago
}
table = TableView.new(headings, row_type: :object)
table.render('ES Results' => contact_hit_results)
