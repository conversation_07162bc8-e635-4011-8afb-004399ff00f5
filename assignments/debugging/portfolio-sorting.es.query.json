{
  "from": 0,
  "size": 15,
  "query": {
    "bool": {
      "filter": [
        {
          "bool": {
            "must": {
              "term": {
                "oid": %oid%
              }
            },
            "must_not": {
              "term": {
                "deleted": true
              }
            }
          }
        },
        {
          "bool": {
            "must": {
              "has_child": {
                "query": {
                  "bool": {
                    "filter": {
                      "bool": {
                        "must": [
                          {
                            "term": {
                              "parent_role": "prospect"
                            }
                          },
                          {
                            "term": {
                              "pool_id": %pool_id%
                            }
                          },
                          {
                            "term": {
                              "solicitor_contact_id": %solicitor_contact_id%
                            }
                          },
                          {
                            "terms": {
                              "assignment_stage.untouched": [
                                "%stage%"
                              ]
                            }
                          },
                          {
                            "term": {
                              "contact_oid": %oid%
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                "child_type": "assignment"
              }
            }
          }
        }
      ],
      "should": {
        "has_child": {
          "query": {
            "bool": {
              "must": {
                "function_score": {
                  "functions": [
                    {
                      "field_value_factor": {
                        "field": "date_occurred",
                        "missing": 1.0
                      }
                    }
                  ]
                }
              },
              "filter": {
                "bool": {
                  "must": [
                    {
                      "range": {
                        "date_occurred": {
                          "from": null,
                          "to": "now",
                          "include_lower": true,
                          "include_upper": true
                        }
                      }
                    },
                    {
                      "nested": {
                        "query": {
                          "term": {
                            "solicitor.contact_id": %solicitor_contact_id%
                          }
                        },
                        "path": "solicitor"
                      }
                    },
                    {
                      "term": {
                        "oid": %oid%
                      }
                    }
                  ],
                  "must_not": [
                    {
                      "terms": {
                        "interaction_type.untouched": [
                          "Note",
                          "EverTrue Comment",
                          "DXO Ask"
                        ]
                      }
                    },
                    {
                      "term": {
                        "deleted": true
                      }
                    },
                    {
                      "term": {
                        "deleted": true
                      }
                    }
                  ]
                }
              }
            }
          },
          "child_type": "contact_note",
          "score_mode": "max"
        }
      }
    }
  },
  "fields": "id",
  "sort": [
    {
      "_score": {
        "order": "asc",
        "missing": "_last",
        "unmapped_type": "string"
      }
    },
    {
      "_score": {}
    },
    {
      "id": {
        "order": "asc"
      }
    }
  ]
}
