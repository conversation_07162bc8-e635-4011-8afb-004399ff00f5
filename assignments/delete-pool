#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_run_remote

  opts.on('-p', '--pool POOL_ID') do |v|
    opts[:pool_id] = v.to_i
  end

  opts.require_option(:pool_id)
end

require File.expand_path('../config/environment', __dir__)

pool = VolunteerDB::Pool.where(oid: SCRIPT_OPTIONS[:oid]).find(SCRIPT_OPTIONS[:pool_id])

STATS.total = pool.assignments.count

LOG.info "Deleteing pool #{pool.id} and all #{pool.assignments.count} assignments"

def assignments_client
  @assignments_client ||= AssignmentsClient.create_client_with_app_creds
end

pool.assignments.each do |assignment|
  STATS.inc_and_notify
  res = assignments_client.delete_assignment(pool.oid, assignment.id)
  if res.kind_of?(Net::HTTPSuccess)
    LOG.info("Deleted assignment #{assignment.id} from pool #{pool.id}")
    STATS.update(:deleted_assignments)
  else
    LOG.error("Failed to delete assignment #{assignment.id} from pool #{pool.id}, error #{res.code}/#{res.body}")
    STATS.update(:failed_to_delete_assignments)
  end
end

LOG.info("Deleting pool #{pool.id}")

res = assignments_client.delete_pool(pool.oid, pool.id)
if res.kind_of?(Net::HTTPSuccess)
  LOG.info("Deleted pool #{pool.id}")
else
  LOG.error("Failed to delete  pool #{pool.id}, error #{res.code}/#{res.body}")
end

STATS.notify(true)
