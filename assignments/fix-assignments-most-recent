#!/usr/bin/env ruby

# This script can be used to update/fix the data in assignments_most_recent. 
# It finds all distinct solicitor/prospect combinations by oid. It will then call out
# to the UGC interactions refresh endpoint. This will refresh the interaction_solicitors_most_recent
# table and queue a SYNC request to kafka to update assignments_most_recent with this refreshed data.
# 
# The script runs on an oid basis, or globally if no oid is provided.
# ./assignments/fix-assignments-most-recent -e <staging|production> -o <oid>
require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option(false)
  opts.add_run_remote
end

require File.expand_path('../config/environment', __dir__)
require 'csv'
require 'active_record'

ActiveRecord::Base.logger = LOG

volunteer_mysql_client = MySQLHelpers.create_client(:volunteers)
ugc_mysql_client = MySQLHelpers.create_client(:ugc)

def volunteer_mysql_client
  @volunteer_mysql_client ||= AssignmentsClient.create_app_client
end

def ugc_api_client
  @ugc_api_client ||= UgcClient.create_app_client
end

def refresh_most_recent_interaction(oid, solicitor_contact_id, prospect_contact_id)
  res = ugc_api_client.refresh_most_recent_interaction(
    oid,
    solicitor_contact_id,
    prospect_contact_id
  )

  unless res.kind_of?(Net::HTTPSuccess)
    LOG.error("failed to refresh most recent interaction for  #{oid}/#{solicitor_contact_id}/#{prospect_contact_id}/ #{res.code}/#{res.body}")
    return false
  end
  true
end

oids = SCRIPT_OPTIONS[:oid] ? [SCRIPT_OPTIONS[:oid]] : AuthDB::Organization.where(id: VolunteerDB::Assignment.select('DISTINCT oid').to_a.map(&:oid)).to_a.map(&:id)

oids.each do |oid|
  LOG.info("working on oid #{oid}")
  volunteer_mysql_client.query(
    "SELECT DISTINCT a.prospect_contact_id, s.contact_id as solicitor_contact_id 
    FROM assignments a
    INNER JOIN solicitors s ON s.id = a.solicitor_id AND s.oid = #{oid}")
    .each do |result|    
      STATS.inc_and_notify
      prospect_contact_id = result['prospect_contact_id']
      solicitor_contact_id = result['solicitor_contact_id']
      if refresh_most_recent_interaction(oid, solicitor_contact_id, prospect_contact_id)
        STATS[:upsert_success] += 1
      else
        STATS[:upsert_fail] += 1
      end      
    end
end

STATS.notify
