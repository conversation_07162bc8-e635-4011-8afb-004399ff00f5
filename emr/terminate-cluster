#!/usr/bin/env ruby

# Purpose: to terminate an EMR cluster

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-c', '--cluster CLUSTER_ID', 'Cluster ID') do |v|
    opts[:cluster_id] = v
  end

  opts.require_option(:cluster_id)
end

require File.expand_path('../config/environment', __dir__)

cmd = "aws --profile #{EnvironmentLoader.instance.aws_profile} emr terminate-clusters --cluster-ids #{SCRIPT_OPTIONS[:cluster_id]}"

puts cmd
exec(cmd)
