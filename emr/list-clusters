#!/usr/bin/env ruby

# Purpose: to list out EMR clusters and their current state

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:days_ago] = 3

  opts.on('-d', '--days-ago DAYS_AGO', 'Number of days to look back') do |v|
    opts[:days_ago] = v.to_i
  end

  opts.on('-f', '--filter FILTER', 'Filter results based on name') do |v|
    opts[:filter] = v.downcase
  end

  opts.require_option(:days_ago)
end

require File.expand_path('../config/environment', __dir__)

created_after = (Time.now - (SCRIPT_OPTIONS[:days_ago] * 86_400)).iso8601

clusters_data = JSON.parse(`aws --profile #{EnvironmentLoader.instance.aws_profile} emr list-clusters --created-after #{created_after}`)['Clusters']

class Cluster
  include Schema::All
  include TimeHelpers

  attribute :id, :string, alias: 'Id'
  attribute :name, :string, alias: 'Name'
  has_one :status, alias: 'Status' do
    attribute :state, :string, alias: 'State'

    has_one :state_change_reason, alias: 'StateChangeReason' do
      attribute :code, :string, alias: 'Code'
      attribute :message, :string, alias: 'Message'
    end

    has_one :timeline, alias: 'Timeline' do
      attribute :creation_date_time, :time, alias: 'CreationDateTime'
      attribute :ready_date_time, :time, alias: 'ReadyDateTime'
      attribute :end_date_time, :time, alias: 'EndDateTime'
    end
  end
  attribute :normalized_instance_hours, :integer, alias: 'NormalizedInstanceHours'
  attribute :cluster_arn, :string, alias: 'ClusterArn'

  def cluster_state
    if status.state_change_reason && status.state_change_reason.message
      status.state + ' (' + status.state_change_reason.message + ')'
    else
      status.state
    end
  end

  def running_for
    return nil unless status.timeline&.ready_date_time

    if status.timeline.end_date_time
      time_in_words(status.timeline.ready_date_time, status.timeline.end_date_time)
    else
      time_in_words(status.timeline.ready_date_time)
    end
  end

  def created
    time_in_words(status.timeline.creation_date_time) + ' ago'
  end

  def running?
    case status.state
    when 'TERMINATED',
         'TERMINATING',
         'TERMINATED_WITH_ERRORS'
      false
    else
      true
    end
  end

  def created_on_ms
    status.timeline.creation_date_time.to_i * 1000
  end
end

clusters = clusters_data.map { |payload| Cluster.from_hash(payload) }
clusters = clusters.select { |cluster| cluster.name.downcase.include?(SCRIPT_OPTIONS[:filter]) } if SCRIPT_OPTIONS[:filter]

headings = {
  'Id' => :id,
  'Name' => :name,
  'State' => :cluster_state,
  'Running For' => :running_for,
  'Created' => :created,
  'Created On (ms)' => :created_on_ms
}
table = TableView.new(headings, row_type: :object)
table.render('Active Clusters' => clusters.select(&:running?), 'Terminated Clusters' => clusters.reject(&:running?))
