#!/usr/bin/env ruby

require 'json'

env = ARGV[0] # either prod or stage
oid = ARGV[1] # ex: 529
s3_windfall_url = ARGV[2] # ex: 's3://et-enrichment-prod/windfall/20210714/windfall_enriched_529_20210714.csv'

timestamp = (Time.now.to_f * 1000).to_i

profile =
  if env == 'prod'
    'evertrueprod'
  else
    'evertruestage'
  end

ec2_attributes =
  if env == 'prod'
    {
      "InstanceProfile" => "EMR_EC2_DefaultRole",
      "ServiceAccessSecurityGroup" => "sg-054ef3f9613921acb",
      "SubnetId" => "subnet-0b8868f279cbd6f93",
      "EmrManagedSlaveSecurityGroup" => "sg-08e351c70c58a3f96",
      "EmrManagedMasterSecurityGroup" => "sg-0ecb3154c08c59765"
    }
  else
    {
      "InstanceProfile" => "EMR_EC2_DefaultRole",
      "ServiceAccessSecurityGroup" => "sg-0fea159e44fee497c",
      "SubnetId" => "subnet-05c0be3de9663b2f1",
      "EmrManagedSlaveSecurityGroup" => "sg-0a2616708d54b21b9",
      "EmrManagedMasterSecurityGroup" => "sg-02986ac9741552411"
    }
  end

log_uri =
  if env == 'prod'
    's3n://emr.prod.evertrue.com/logs/'
  else
    's3n://emr.stage.evertrue.com/logs/'
  end

steps = [
  {
    "Args" => [
      "spark-submit",
      "--class",
      "com.et.enrichment.spark.WindfallImport",
      "s3://codebuild.evertrue.com/builds/enrichment/master/EnrichmentSpark/WindfallImport-1.0-SNAPSHOT-jar-with-dependencies.jar",
      s3_windfall_url,
      oid.to_s
    ],
    "Type" => "CUSTOM_JAR",
    "ActionOnFailure" => "CONTINUE",
    "Jar" => "command-runner.jar",
    "Properties" => "",
    "Name" => "WindfallImport"
  },
  {
    "Args" => [
      "spark-submit",
      "--class",
      "com.et.enrichment.spark.EnrichmentOrgEsSync",
      "s3://codebuild.evertrue.com/builds/enrichment/master/EnrichmentSpark/EnrichmentEsSync-1.0-SNAPSHOT-shaded.jar",
      oid.to_s,
      timestamp.to_s
    ],
    "Type" => "CUSTOM_JAR",
    "ActionOnFailure" => "CONTINUE",
    "Jar" => "command-runner.jar",
    "Properties" => "",
    "Name" => "EnrichmentOrgEsSync"
  }
]

instance_groups = [
  {
    "InstanceCount" => 1,
    "EbsConfiguration" => {
      "EbsBlockDeviceConfigs" => [
        {
          "VolumeSpecification" => {
            "SizeInGB" => 32,
            "VolumeType" => "gp2"
          },
          "VolumesPerInstance" => 2
        }
      ]
    },
    "InstanceGroupType" => "MASTER",
    "InstanceType" => "m5a.xlarge",
    "Name" => "MASTER"
  },
  {
    "InstanceCount" => 3,
    "EbsConfiguration" => {
      "EbsBlockDeviceConfigs" => [
        {
          "VolumeSpecification" => {
            "SizeInGB" => 32,
            "VolumeType" => "gp2"
          },
          "VolumesPerInstance" => 2
        }
      ]
    },
    "InstanceGroupType" => "CORE",
    "InstanceType" => "m5a.xlarge",
    "Name" => "CORE"
  }
]

configurations = [
  {
    "Classification" => "spark",
    "Properties" => {
      "maximizeResourceAllocation" => "true"
    }
  },
  {
    "Classification" => "spark-defaults",
    "Properties" => {
      "spark.executor.extraJavaOptions" => "-Dcom.et.env=prod",
      "spark.driver.extraJavaOptions" => "-Dcom.et.env=prod"
    }
  }
]

cmd = "aws --profile #{profile} emr create-cluster --applications Name=Spark --ec2-attributes '#{ec2_attributes.to_json}' --service-role EMR_DefaultRole --release-label emr-5.29.0 --log-uri '#{log_uri}' --steps '#{steps.to_json}' --name 'EnrichmentSpark:Windfall' --instance-groups '#{instance_groups.to_json}' --configurations '#{configurations.to_json}' --scale-down-behavior TERMINATE_AT_TASK_COMPLETION --region us-east-1"

puts cmd
