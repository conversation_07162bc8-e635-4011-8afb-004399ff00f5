#!/usr/bin/env ruby

# Purpose: to download a EMR cluster logs

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-c', '--cluster CLUSTER_ID', 'Cluster ID') do |v|
    opts[:cluster_id] = v
  end

  opts.require_option(:cluster_id)
end

require File.expand_path('../config/environment', __dir__)

EMR_LOG_FOLDERS = ['containers', 'steps']
EMR_LOG_FILES = [
  'prelaunch.out.gz',
  'stderr.gz',
  'stdout.gz',
  'controller.gz'
]

download_dir = TMP_DIR.join('emr', 'clusters', SCRIPT_OPTIONS[:cluster_id])

s3_bucket =
  if ENV['RAILS_ENV'] == 'production'
    'emr.prod.evertrue.com'
  else
    'emr.stage.evertrue.com'
  end

s3_path = "s3://#{s3_bucket}/logs/#{SCRIPT_OPTIONS[:cluster_id]}"

# download files about running EMR task
EMR_LOG_FOLDERS.each do |folder|
  cmd = "aws --profile #{EnvironmentLoader.instance.aws_profile} s3 sync #{s3_path}/#{folder}/ #{download_dir}/#{folder}/"
  puts cmd
  `#{cmd}`
  raise("command failed") unless $?.success?
end

# aggregate log files
EMR_LOG_FOLDERS.each do |folder|
  EMR_LOG_FILES.each do |file_type|
    files = Dir.glob("#{download_dir}/#{folder}/**/#{file_type}").sort
    next unless files.size > 0

    log_file = "#{download_dir}/#{folder}-#{file_type.sub('.gz', '.log')}"
    File.unlink(log_file) if File.exist?(log_file)
    FileUtils.touch(log_file)
    puts "creating log file #{log_file}"
    files.each do |file|
      File.open(log_file, 'a') { |f| f.puts ">> LOG: #{s3_path}#{file.sub(download_dir.to_s, '')}" }
      `gzip -c -d #{file} >> #{log_file}`
    end
  end
end
