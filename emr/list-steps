#!/usr/bin/env ruby

# Purpose: to list out EMR clusters and their current state

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-c', '--cluster CLUSTER_ID', 'Cluster ID') do |v|
    opts[:cluster_id] = v
  end

  opts.require_option(:cluster_id)
end

require File.expand_path('../config/environment', __dir__)

exec("aws --profile #{EnvironmentLoader.instance.aws_profile} emr list-steps --cluster #{SCRIPT_OPTIONS[:cluster_id]} --query 'Steps[].[Id, Name, Status.State, Config.Args[4], Config.Args[5]]' --output table")
