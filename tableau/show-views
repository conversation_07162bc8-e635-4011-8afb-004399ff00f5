#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)

class TableauViewSchema
  include Schema::All

  has_one :workbook do
    attribute :id, :string
  end

  has_one :owner do
    attribute :id, :string
  end

  has_one :project do
    attribute :id, :string
  end

  has_one :location do
    attribute :id, :string
    attribute :type, :string
  end

  attribute :name, :string
  attribute :content_url, :string, alias: :contentUrl
  attribute :created_at, :time, alias: :createdAt
  attribute :updated_at, :time, alias: :updatedAt
  attribute :view_url_name, :string, alias: :viewUrlName
end


site = AuthDB::TableauSite.find_by!(slug: 'EverTrue')
tableau_api_client = TableauApiClient.create_client_with_default_credentials(site.slug)
tableau_views = tableau_api_client.resources.get_views(site_id: site.remote_site_id)['views']['view'].map { |payload| TableauViewSchema.from_hash(payload) }.sort_by(&:content_url)


table_view = TableView.new(
  TableView.create_default_headings([:name, :content_url, :view_url_name, :created_at, :updated_at]),
  {row_type: :object}
)
table_view.render('Tableau Views' => tableau_views)
