#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)
require "date"

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_input_file
end

require File.expand_path('../config/environment', __dir__)

DATE_FIELDS = %i(start_date due_date).freeze

# key: what goes in the payload
# value: what is on the CSV Header
def field_mappings 
  {
    :oid => :oid,
    :user_id => :owner_user_id,
    :journey_id => :cadence_id,
    :contact_id => :target_contact_id,
    :current_day_number => :day,
    :current_step_number => :step_number,
    :original_start_date => :start_date,
    :current_step_due_date => :due_date
  }
end

def build_payload(row)
  return {}.tap do |hash|
    field_mappings.each do |k, v|
      if(DATE_FIELDS.include?(v))
        date_string = row.field(v)
        format = if date_string.include? "/"
          "%m/%d/%y"
        else 
          '%Y-%m-%d'
        end

        hash[k] = Date.strptime(row.field(v), format)&.to_s
      else 
        hash[k] = row.field(v)&.to_i
      end
    end
  end
end

options = {
  :headers => true,
  :header_converters => [:downcase, :symbol]
}

p "STARTING DELETION"
CSV.foreach(SCRIPT_OPTIONS[:file], "r", **options) do |row|
  payload = build_payload(row)
  p "JOURNEY ID: #{payload[:journey_id]}, USER ID: #{payload[:user_id]}, CONTACT ID: #{payload[:contact_id]}"
  contact_journey = JourneyDB::ContactJourney.find_by(journey_id: payload[:journey_id], user_id: payload[:user_id], contact_id: payload[:contact_id])
  next unless contact_journey.present?
  p "deleting stuff for CONTACT JOURNEY ID: #{contact_journey.id}"
  tasks = JourneyDB::Task.where(contact_journey_id: contact_journey.id)
  JourneyDB::TaskAudit.delete_by(task_id: tasks.ids)
  JourneyDB::JourneyMove.delete_by(task_id: tasks.ids)
  tasks.delete_all
  contact_journey.delete
  p "-----------"
end
p "END DELETION"