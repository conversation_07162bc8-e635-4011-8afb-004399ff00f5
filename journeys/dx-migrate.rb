#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)
require "date"

# Purpose: Move user and contact info from CSV (retrieved from Salesloft) into Journeys DB

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
  opts.add_input_file
end

require File.expand_path('../config/environment', __dir__)

DATE_FIELDS = %i(start_date due_date).freeze

def journey_client
  @journey_client ||= JourneyClient.create_client_with_app_creds
end

# key: what goes in the payload
# value: what is on the CSV Header
def field_mappings 
  {
    :oid => :oid,
    :user_id => :owner_user_id,
    :journey_id => :cadence_id,
    :contact_id => :target_contact_id,
    :current_day_number => :day,
    :current_step_number => :step_number,
    :original_start_date => :start_date,
    :current_step_due_date => :due_date
  }
end

def missing_fields(row)
  [].tap do |missing|
    field_mappings.each do |k, v| 
      unless row.has_key?(v) && !row.field(v).nil?
        missing << v
      end
    end
  end
end

def build_payload(row)
  return {}.tap do |hash|
    field_mappings.each do |k, v|
      if(DATE_FIELDS.include?(v))
        date_string = row.field(v)
        format = if date_string.include? "/"
          "%m/%d/%y"
        else 
          '%Y-%m-%d'
        end

        hash[k] = Date.strptime(row.field(v), format)&.to_s
      else 
        hash[k] = row.field(v)&.to_i
      end
    end
  end
end

options = {
  :headers => true,
  :header_converters => [:downcase, :symbol]
}

def print_message(payload)
  LOG.info "----"
  LOG.info "Starting Migration for: "
  %i(oid user_id contact_id journey_id).each do |key|
    LOG.info "#{key} #{payload[key]} "
  end
end 

errors = {}
success_count = 0

CSV.foreach(SCRIPT_OPTIONS[:file], "r", **options) do |row|
  # validate before hitting API
  missing = missing_fields(row)
  if missing.size > 0
    LOG.info "Skipping. Missing values: #{missing}"
    next
  end

  payload = build_payload(row)
  print_message(payload)
  success_count += journey_client.migrate_dxo(payload, errors)
end

LOG.info "MIGRATION COMPLETE"
LOG.info "Successfully Migrated #{success_count} Users"

unless errors.empty?
  LOG.info "The following Migrations Failed: "
  errors.each do |k, v|
    LOG.info "User ID : #{k}"
    LOG.info "Error Message: #{v}"
  end
end