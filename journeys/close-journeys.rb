#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)
require "date"

# Purpose: Move user and contact info from CSV (retrieved from Salesloft) into Journeys DB

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.on('-u', '--user USER_ID') do |v|
    opts[:user_id] = v.to_i
  end

  opts.on('-j', '--journey JOURNEY_ID') do |v|
    opts[:journey_id] = v.to_i
  end

  opts.require_option(:user_id)
end

require File.expand_path('../config/environment', __dir__)

def journey_client
  @journey_client ||= JourneyClient.create_client_with_app_creds
end


contact_journeys = if(SCRIPT_OPTIONS[:journey_id])
  JourneyDB::ContactJourney.where(user_id: SCRIPT_OPTIONS[:user_id], journey_id: SCRIPT_OPTIONS[:journey_id])
else 
  JourneyDB::ContactJourney.where(user_id: SCRIPT_OPTIONS[:user_id])
end

contact_journeys.each do |cj| 
  journey = JourneyDB::Journey.find(cj.journey_id)
  if (SCRIPT_OPTIONS[:oid] == journey.oid)
    payload = {
      :old_journey => {
        :id =>  cj.journey_id,
        :reason => "SKIPPED_MANUAL"
      },
      :contact_id => cj.contact_id,
      :user_id => cj.user_id,
      :oid => SCRIPT_OPTIONS[:oid]
    }
    
    journey_client.close_contact_journey(payload)
  else 
    p "skipping, wrong OID"
  end 
end 

