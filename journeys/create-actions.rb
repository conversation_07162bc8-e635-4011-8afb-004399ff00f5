#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)
require "date"

# Purpose: Create Actions from a CSV 

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_input_file
end

require File.expand_path('../config/environment', __dir__)

def journey_client
  @journey_client ||= JourneyClient.create_client_with_app_creds
end

def oid_map
  {
    :bostoncollegecentral => 572,
    :bu => 16,
    :citadel => 825,
    :davidson => 602,
    :latech => 689,
    :liberty => 787,
    :lvc => 550,
    :marylandcollegepark => 829,
    :twu => 778,
    :uconn => 560,
    :umassdartmouth => 614,
    :umasslowell => 615,
    :uncg => 752,
    :uniowa => 632,
    :unlv => 812,
    :usna => 789,
    :wku => 598,
    :wyoming => 715,
    :test => 158
  }
end

def build_payload(row)
  return {}.tap do |hash|
    hash[:oid] = oid_map[row.field(:oid)].to_i
    hash[:date_occurred] = row.field(:time).to_i
    hash[:outreach_method] = "EMAIL"
    hash[:completed_by_id] = row.field(:user_id)
    hash[:creator_user_id] = row.field(:user_id)
    hash[:updater_user_id] = row.field(:user_id)
  end
end

options = {
  :headers => true,
  :header_converters => [:downcase, :symbol]
}

CSV.foreach(SCRIPT_OPTIONS[:file], "r", **options) do |row|
  journey_client.create_action build_payload(row)
end