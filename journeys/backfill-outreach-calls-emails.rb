#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)
SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)
require "date"
require 'nokogiri'

# Purpose: Backfill Outreach Calls and Emails from Journeys Actions
begin
  puts 'Starting backfill process...'

  JourneyDB::Action.find_each(batch_size: 1000) do |action|
    if action.outreach_method == 'PHONE'
      next if OutreachDB::Call.where(
        oid: action.oid,
        creator_user_id: action.completed_by_id,
        contact_id: action.contact_id,
        completed_at: action.date_occurred,
        created_at: action.created_at
      ).exists?

      puts "Attempting to create call for action ID: #{action.id}"
      OutreachDB::Call.create!(
        contact_id: action.contact_id,
        oid: action.oid,
        creator_user_id: action.completed_by_id,
        interaction_id: action.interaction_id,
        trip_id: action.trip_id,
        task_id: action.task_id,
        two_way_communication: action.two_way_communication.nil? ? true : action.two_way_communication,
        transcript: action.transcript,
        duration_in_seconds: action.duration_in_seconds || 0,
        completed_at: action.date_occurred,
        updated_at: action.updated_at,
        deleted_at: action.deleted_at,
        created_at: action.created_at,
        status: action.outreach_result ? 'SUCCESS' : 'FAILED'
      )

    elsif action.outreach_method == 'EMAIL'
      next if OutreachDB::Email.where(
        oid: action.oid,
        creator_user_id: action.completed_by_id,
        sent_at: action.date_occurred,
        created_at: action.created_at
      ).exists?

      puts "Attempting to create email for action ID: #{action.id}"
      created_email = OutreachDB::Email.create!(
        oid: action.oid,
        creator_user_id: action.completed_by_id,
        interaction_id: action.interaction_id,
        trip_id: action.trip_id,
        task_id: action.task_id,
        sent_at: action.date_occurred,
        updated_at: action.updated_at,
        deleted_at: action.deleted_at,
        created_at: action.created_at,
        subject: action.subject || '',
        body: action.body || '',
        parsed_body: action.body ? Nokogiri::HTML(action.body).text : '',
        status: 'SENT',
        unread: true,
        starred: false
      )

      OutreachDB::EmailRecipient.create!(
        oid: action.oid,
        email_id: created_email.id,
        contact_id: action.contact_id,
        email_address: '', # Does not exist in our actions table
        recipient_type: 'TO'
      )
    end

    action.update!(deleted_at: Time.now)
  end

  puts 'Backfill process completed successfully!'
end