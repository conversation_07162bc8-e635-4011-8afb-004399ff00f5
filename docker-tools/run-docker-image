#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:image_tag] = 'latest'
  opts[:local_port] = '8080'
  opts[:container_port] = '8080'

  opts.on('-i', '--image-name IMAGE_NAME', 'Docker image name') do |v|
    opts[:image_name] = v
  end

  opts.on('-t', '--image-tag IMAGE_TAG', 'Docker image tag') do |v|
    opts[:image_tag] = v
  end

  opts.on('--local-port LOCAL_PORT', 'Port to use locally') do |v|
    opts[:local_port] = v
  end

  opts.on('--container-port CONTAINER_PORT', 'Port in use by the running process in the container') do |v|
    opts[:local_port] = v
  end

  opts.on('-c', '--command COMMAND', 'Command to run in the container') do |v|
    opts[:command] = v
  end

  opts.require_options(:image_name, :image_tag)
end

require File.expand_path('../config/environment', __dir__)

docker_environment_variables = {
  AWS_PROFILE: EnvironmentLoader.instance.aws_profile,
  RAILS_ENV: ENV['RAILS_ENV'],
  RACK_ENV: ENV['RAILS_ENV'],
  APP_ENV: ENV['RAILS_ENV'] == 'production' ? 'prod' : 'stage'
}

docker_publish = "#{SCRIPT_OPTIONS[:local_port]}:#{SCRIPT_OPTIONS[:container_port]}"

docker_run_command = [
  'docker',
  'run'
]

docker_environment_variables.each do |name, value|
  docker_run_command += ['--env', "#{name}=#{value}"]
end

docker_run_command += ['-v', "#{ENV['HOME']}/.aws:/root/.aws"]
docker_run_command += ['-p', docker_publish]
docker_run_command += ['-it', "#{SCRIPT_OPTIONS[:image_name]}:#{SCRIPT_OPTIONS[:image_tag]}"]

cmd = Shellwords.join(docker_run_command)
if SCRIPT_OPTIONS[:command]
  cmd += ' ' + SCRIPT_OPTIONS[:command]
end
print(cmd + "\n")
exec(cmd)
