#!/usr/bin/env ruby

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse!

require_relative '../config/environment'
require 'active_support/core_ext/time'

days_ago = (Time.now - (3 * 86_400)).to_i * 1000;

failed_exports = ExporterDB::Export.where('created_at >= ? ', days_ago).where.not(state: 1).to_a
failed_exports.select!(&:stalled?)

table = TableView.new(TableView.create_default_headings([
    :id,
    :oid,
    :type,
    :humanized_state,
    :created_ago
]), row_type: :object)

table.render('Failed Exports' => failed_exports)
