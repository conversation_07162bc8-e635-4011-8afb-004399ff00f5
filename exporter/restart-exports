#!/usr/bin/env ruby

# This script allows you to restart a single export or multiple exports


require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
    opts.on('-f', '--exports-file [FILE PATH]', 'comma delimited file of export job ids to retry') do |v|
        opts[:exports_file] = v
    end
end

require_relative '../config/environment'

job_id = ARGV.shift
raise 'cannot specify single job id when exports file is specified' if SCRIPT_OPTIONS[:exports_file] && job_id

job_ids = []
job_ids << job_id if job_id

if SCRIPT_OPTIONS[:exports_file]
    File.read(SCRIPT_OPTIONS[:exports_file]).split(",").each do |job_id|
        job_ids << job_id.strip.to_i
    end
end

exporter_client = ExporterClient.create_client_with_app_creds

job_ids.each do |job_id|
    export = ExporterDB::Export.find_by_id(job_id)

    puts "#{export.oid}/#{job_id}: state=#{export.state}/#{export.humanized_state}"

    if !export.job_descriptor
        puts "\tno job descriptor, skipping..."
        next
    end
    

    begin
        if export.state > 4
            puts "\tresetting state to 3..."

        export.update_attribute :state, 3
        end

        if  export.restart_count >= 3
            puts "\tresetting restart_count to 0..."

            export.update_attribute :restart_count, 0
        end
    rescue => e
        puts "\tfailed to update export: #{e.message}"
        next
    end

    begin
        res = exporter_client.restart_export(export.oid, export.id)
        if res.code == '202'
            puts "\tjob restarted."
        else
            puts "\tfailed to restart job: #{res.body}"
            next
        end
    rescue => e
        puts "\tfailed to restart job: #{e.message}"
        next
    end
end