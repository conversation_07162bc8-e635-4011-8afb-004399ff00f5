#!/usr/bin/env ruby

# This script allows you to get the status of a single job or list of jobs


require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
    opts.on('-f', '--exports-file [FILE PATH]', 'comma delimited file of export job ids to retry') do |v|
        opts[:exports_file] = v
    end
end

require_relative '../config/environment'

job_id = ARGV.shift
raise 'cannot specify single job id when exports file is specified' if SCRIPT_OPTIONS[:exports_file] && job_id

job_ids = []
job_ids << job_id if job_id

if SCRIPT_OPTIONS[:exports_file]
    File.read(SCRIPT_OPTIONS[:exports_file]).split(",").each do |job_id|
        job_ids << job_id.strip.to_i
    end
end

timstamp_formatter = Proc.new { |timestamp| 
    timestamp ? Time.at(timestamp.to_i / 1000).iso8601 : ""
}

exporter_client = ExporterClient.create_client_with_app_creds

job_ids.each do |job_id|
    export = ExporterDB::Export.find_by_id(job_id)

    puts "#{export.oid}/#{job_id}: state=#{export.humanized_state} since #{timstamp_formatter.call(export.updated_at)}"
end