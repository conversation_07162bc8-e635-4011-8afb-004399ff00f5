#!/usr/bin/env ruby

# Purpose: to restart any failed exports from N days ago

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:days_ago] = 3

  opts.add_run_remote
  opts.add_scheduling_options

  opts.on('-d', '--days DAYS_AGON', Integer, 'Number of days to look back for failed exports') do |v|
    opts[:days_agon] = v
  end
end

require_relative '../config/environment'

MAX_ALLOWED_SPARK_JOBS = 10

def days_ago
  (Time.now - (SCRIPT_OPTIONS[:days_ago] * 86_400)).to_i * 1000
end

def failed_exports
  @failed_exports ||=
    begin
      one_hour_ago = (Time.now - 3600).to_i * 1000
      ExporterDB::Export.where('created_at >= ? AND updated_at < ?', days_ago, one_hour_ago).where.not(state: 1).to_a
    end
end

def exporter_client
  @exporter_client ||= ExporterClient.create_client_with_app_creds
end

def spark_has_capacity_to_run_job?
  SparkHelpers.num_running_jobs < MAX_ALLOWED_SPARK_JOBS
end

def can_restart_export?(export)
  unless export.job_descriptor
    LOG.warn("no job_descriptor for export #{export.id}, skipping")
    return false
  end

  true
end

def reset_export_state!(export)
  if export.state > 4
    LOG.info("reseting export state to 3 for export #{export.id}")
    export.state = 3
  end

  if  export.restart_count >= 3
    LOG.info("resetting restart_count to 0 for export #{export.id}")
    export.restart_count = 0
  end

  export.save!
end

def wait_for_spark_to_start_job
  LOG.info("waiting for spark to pick up job")
  sleep 30
end

def restart_export(export)
  res = exporter_client.restart_export(export.oid, export.id)
  if res.code == '202'
    LOG.info("restarted export #{export.id}")
  else
    LOG.error("failed to restart export #{export.id}: #{res.body}")
  end
end

LOG.info("restarting #{failed_exports.size} exports")

STATS.total = failed_exports.size
STATS.notification_interval = 5

while (failed_export = failed_exports.shift)
  STATS.inc_and_notify

  next unless can_restart_export?(failed_export)

  while !spark_has_capacity_to_run_job?
    sleep 5
  end

  reset_export_state!(failed_export)

  restart_export(failed_export)

  wait_for_spark_to_start_job
end

STATS.notify(true)
