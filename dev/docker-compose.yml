services:
  et_repair_shop_cli:
    image: et_repair_shop_cli
    build:
      context: ../
      dockerfile: dev/Dockerfile
    command: bash -l
    environment:
      USER: ${USER:-}
      EVERTRUE_EMAIL: ${EVERTRUE_EMAIL:-}
      GITHUB_REPOS_ACCESS_TOKEN: ${GITHUB_REPOS_ACCESS_TOKEN:-}
      GITHUB_PERSONAL_ACCESS_TOKEN: ${GITHUB_PERSONAL_ACCESS_TOKEN:-}
    volumes:
      - ../.:/et_repair_shop
      - ${HOME:-.}/.aws:/root/.aws
