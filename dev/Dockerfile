FROM ruby:3.3
MAINTAINER EverTrue

RUN apt-get update

# 1Pass instruction for install the CLI https://developer.1password.com/docs/cli/get-started/
RUN curl -sS https://downloads.1password.com/linux/keys/1password.asc | gpg --dearmor --output /usr/share/keyrings/1password-archive-keyring.gpg
RUN echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/1password-archive-keyring.gpg] https://downloads.1password.com/linux/debian/$(dpkg --print-architecture) stable main" | tee /etc/apt/sources.list.d/1password.list
RUN mkdir -p /etc/debsig/policies/AC2D62742012EA22/
RUN curl -sS https://downloads.1password.com/linux/debian/debsig/1password.pol | tee /etc/debsig/policies/AC2D62742012EA22/1password.pol
RUN mkdir -p /usr/share/debsig/keyrings/AC2D62742012EA22
RUN curl -sS https://downloads.1password.com/linux/keys/1password.asc | gpg --dearmor --output /usr/share/debsig/keyrings/AC2D62742012EA22/debsig.gpg
RUN apt update && apt install 1password-cli

RUN mkdir -p /root/.config/op
RUN chmod 700 /root/.config/op

RUN apt-get upgrade -y
RUN apt-get install -y build-essential
RUN apt-get install -y redis-tools
RUN apt-get install -y default-mysql-client
RUN apt-get install -y awscli
RUN apt-get install -y npm
RUN apt-get install -y freetds-dev

# install mssql
RUN npm install -g sql-cli

RUN mkdir /et_repair_shop
WORKDIR /et_repair_shop

COPY ./Gemfile .
COPY ./Gemfile.lock .

RUN export GEMFILE_CHECKSUM=$(md5sum Gemfile.lock)
RUN echo "$GEMFILE_CHECKSUM"; bundle; bundle clean --force

ENV AWS_PAGER=""
ENV PAGER=""
ENV PATH=".:$PATH"
