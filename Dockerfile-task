FROM ruby:3.3
MAINTAINER EverTrue

RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y build-essential redis-tools freetds-dev

ARG BUNDLE_GEM__FURY__IO

WORKDIR /et_repair_shop
COPY . ./

RUN mkdir -p tmp && \
    mkdir -p log

RUN bundle

ARG GIT_COMMIT
RUN echo $GIT_COMMIT > git_commit

ENV PATH=".:$PATH"

# Clean up APT when done.
RUN apt-get clean && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*
