#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-d', '--database DATABASE', 'Database name') do |v|
    opts[:database] = v
  end

  opts.require_option(:database)
end

require File.expand_path('../config/environment', __dir__)

class SlowQueryStat
  include Comparable

  attr_accessor :query,
                :normalized_query,
                :total_query_time,
                :total_queries,
                :total_rows_sent,
                :total_rows_examined

  def initialize(query, normalized_query)
    @query = query
    @normalized_query = normalized_query
    @total_query_time = 0
    @total_queries = 0
    @total_rows_sent = 0
    @total_rows_examined = 0
  end

  def update(slow_query)
    @total_queries += 1
    @total_query_time += (query_time_in_secs(slow_query['query_time'])  * 1000).to_i
    @total_rows_sent += slow_query['rows_sent']
    @total_rows_examined += slow_query['rows_examined']
  end

  def average_query_time
    total_query_time / total_queries
  end

  def average_rows_sent
    total_rows_sent / total_queries
  end

  def average_rows_examined
    total_rows_examined / total_queries
  end

  def <=>(other)
    if self.total_queries == other.total_queries
      other.average_query_time <=> self.average_query_time
    else
      other.total_queries <=> self.total_queries
    end
  end
end

def normalize_sql(sql)
  sql
    .gsub(/= *\d+/, '= ?')
    .gsub(/= *('|").*?\1/, '= ?')
    .gsub(/in *\(.+?\)/i, 'in (?)')
end

def query_time_in_secs(query_time)
  query_time.hour * 3600 +
    query_time.min * 60 +
    query_time.sec +
    query_time.subsec
end

def mysql_client
  @mysql_client ||= MySQLHelpers.create_client(SCRIPT_OPTIONS[:database])
end

slow_queries = {}
add_query_proc = proc do |slow_query|
  normalized_query = normalize_sql(slow_query['sql_text'])
  stat = (slow_queries[normalized_query] ||= SlowQueryStat.new(slow_query['sql_text'], normalized_query))
  stat.update(slow_query)
end

mysql_client.query('SELECT * FROM mysql.slow_log WHERE start_time >= DATE_SUB(now(), interval 1 day)').each do |result|
  add_query_proc.call(result)
end

CSV.open('slow_queries.csv', 'wb') do |csv|
  csv << ['total_queries', 'average_query_time', 'average_rows_sent', 'average_rows_examined', 'sql_text']

  slow_queries.values.sort.each do |slow_query|
    csv << [
      slow_query.total_queries,
      slow_query.average_query_time,
      slow_query.average_rows_sent,
      slow_query.average_rows_examined,
      slow_query.query
    ]
  end
end
