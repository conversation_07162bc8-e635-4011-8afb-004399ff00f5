#!/usr/bin/env ruby

require 'logger'
require 'mysql2'
require 'json'

LOG = Logger.new(__FILE__ + '.log')
LOG.formatter = Logger::Formatter.new

database_uri = ARGV[0] || raise('no database uri specified')
database_uri = URI(database_uri)
database_name = database_uri.path[1..-1]

mysql_client = Mysql2::Client.new(
  host: database_uri.host,
  username: database_uri.user,
  password: Addressable::URI.unescape(database_uri.password.to_s),
  database: database_name
)

def run_query(mysql_client, query)
  LOG.info(query)
  mysql_client.query(query)
end

table_sizes = run_query(mysql_client, "SELECT table_name, data_length, index_length, (data_length+index_length)/power(1024,3) tablesize_gb FROM information_schema.tables WHERE table_schema='#{database_name}' ORDER BY tablesize_gb").to_a

table_sizes.each do |result|
  table_name = result['table_name']

  started_at = Time.now
  optimize_results = run_query(mysql_client, "OPTIMIZE TABLE `#{table_name}`").to_a
  took = Time.now - started_at

  new_result = run_query(mysql_client, "SELECT table_name, data_length, index_length, (data_length+index_length)/power(1024,3) tablesize_gb FROM information_schema.tables WHERE table_schema='#{database_name}' AND table_name='#{table_name}'").first

  deltas = {
    tablesize_gb: result['tablesize_gb'].to_f - new_result['tablesize_gb'].to_f,
    data_length: result['data_length'].to_i - new_result['data_length'].to_i,
    index_length: result['index_length'].to_i - new_result['index_length'].to_i
  }

  LOG.info("took #{took} to optimize table #{table_name}, changes #{deltas.to_json}")
end
