#!/usr/bin/env ruby

# Purpose: to fill in cURL command headers

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.banner = 'Usage: ./bin/curl [options] -- [curl options]'

  opts[:app_name] = 'csv_importer'
  opts[:show_command] = false

  opts.add_oid_option(false)

  opts.on('-a', '--app APP_NAME', 'Specify the app name') do |v|
    opts[:app_name] = v
  end

  opts.on('--email EMAIL', 'Specify an email of a user with a recent session') do |v|
    opts[:email] = v
  end

  opts.on('-s', '--show', 'Output cURL command') do
    opts[:show_command] = true
  end
end

require File.expand_path('../config/environment', __dir__)

client =
  if SCRIPT_OPTIONS[:email]
    raise("must specifiy an oid when using email option") unless SCRIPT_OPTIONS[:oid]

    app_name = SCRIPT_OPTIONS[:app_name] == 'csv_importer' ? 'givingtree' : SCRIPT_OPTIONS[:app_name]
    BaseApiClient.create_client_for_logged_in_user(SCRIPT_OPTIONS[:oid], SCRIPT_OPTIONS[:email], app_name)
  else
    BaseApiClient.create_client_with_app_creds(SCRIPT_OPTIONS[:app_name])
  end

headers = client.req_headers

command = "curl"

headers.each do |header, value|
  command += " -H '#{header}: #{value}'"
end

ARGV.each do |arg|
  command += ' ' + Shellwords.escape(arg)
end

if SCRIPT_OPTIONS[:show_command]
  print(command + "\n")
else
  exec(command)
end
