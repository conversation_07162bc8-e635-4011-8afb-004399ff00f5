#!/usr/bin/env ruby
# frozen_string_literal: true

def exit_with_error(error)
  $stderr.puts "Usage mysql-rds-connect <rds> [<mysql args>]"
  $stderr.puts ""
  $stderr.puts "Error: #{error}"
  exit 1
end

options = {
  rds: ARGV.shift,
  host: ARGV.shift
}

ENV['RAILS_ENV'] = 'staging'

exit_with_error('no rds specified') unless options[:rds]

require File.expand_path('../config/environment', __dir__)
require 'shellwords'

exit_with_error("no configuration for #{options[:rds]}") unless (rds_config = ETOnePassword.new.rds_config(options[:rds]))

class MySQLConfig < Struct.new(:host,
                               :port,
                               :user,
                               :password
                              )

  def to_client_config
    "[client]\n" + self.class.members.map { |m| "#{m}=\"#{self[m]}\"" }.join("\n")
  end
end

mysql_config_file = TMP_DIR.join(".tmp.#{options[:rds]}.conf")

mysql_config = MySQLConfig.new(
  options[:host] || rds_config['host'],
  rds_config['port'],
  rds_config['user'],
  rds_config['password']
)

File.open(mysql_config_file, 'wb') { |f| f.write mysql_config.to_client_config }

child_pid = fork

if child_pid
  # parent process starts the mysql client
  exec("mysql --defaults-file=#{mysql_config_file} #{ARGV.map { |v| Shellwords.escape(v) }.join(' ')}")
else
  # child process cleans up the mysql credentials file
  sleep 0.5
  File.unlink mysql_config_file
end
