#!/usr/bin/env ruby
# frozen_string_literal: true

require File.expand_path('../config/script_options', __dir__)

OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-m', '--message [MESSAGE]', 'Slack message to send') do |v|
    opts[:slack_message] = v
  end

  opts.require_option(:slack_message)
end

require File.expand_path('../config/environment', __dir__)

ETNotify.configure do |config|
  config.slack_ids = OPTIONS[:slack_notify]
end

ETNotify.message(OPTIONS[:slack_message])
