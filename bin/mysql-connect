#!/usr/bin/env ruby
# frozen_string_literal: true

def exit_with_error(error)
  $stderr.puts "Usage mysql-connect <environment> <database> [<mysql args>]"
  $stderr.puts ""
  $stderr.puts "Error: #{error}"
  exit 1
end

options = {
  environment: ARGV.shift,
  database: ARGV.shift
}

ENV['RAILS_ENV'] = case options[:environment]
                   when 'production',
                        'prod'
                     'production'
                   when 'staging',
                        'stage'
                     'staging'
                   when 'development',
                        'dev'
                     'development'
                   when 'test'
                     'test'
                   else
                     exit_with_error 'missing or invalid environment specified'
                   end

exit_with_error('no database specified') unless options[:database]

require File.expand_path('../config/environment', __dir__)
require 'shellwords'

exit_with_error("no configuration for #{options[:database]}") unless (database_url = ENV['DATABASE_URL_' + options[:database].upcase])
database_url = DBUtils.translate_database_url(database_url)

class MySQLConfig < Struct.new(:host,
                               :port,
                               :user,
                               :password,
                               :database
                              )

  def self.from_uri(uri)
    new(
      uri.host,
      uri.port,
      Addressable::URI.unescape(uri.user),
      Addressable::URI.unescape(uri.password.to_s),
      uri.path[1..-1]
    )
  end

  def to_client_config
    "[client]\n" + self.class.members.map { |m| "#{m}=#{self[m]}" }.join("\n") + "\n"
  end
end

mysql_config_file = TMP_DIR.join(".tmp.#{options[:database]}.conf")

File.open(mysql_config_file, 'wb') { |f| f.write MySQLConfig.from_uri(URI(database_url)).to_client_config }

child_pid = fork

if child_pid
  # parent process starts the mysql client
  exec("mysql --defaults-file=#{mysql_config_file} #{ARGV.map { |v| Shellwords.escape(v) }.join(' ')}")
else
  # child process cleans up the mysql credentials file
  sleep 0.5
  File.unlink mysql_config_file
end
