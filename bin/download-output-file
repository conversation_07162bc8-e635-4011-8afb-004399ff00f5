#!/usr/bin/env ruby
# frozen_string_literal: true

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-o', '--output FILE', 'Download to file') do |v|
    opts[:output] = v
  end
end

raise("no file specified") unless ARGV[0]

require File.expand_path('../config/environment', __dir__)

RepairshopFileUploader.download_file(ARGV[0], SCRIPT_OPTIONS[:output])
