#!/usr/bin/env ruby

require_relative('../config/script_options')

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-t', '--task TASK_NAME', 'Name of the task in the config/ecs-tasks/ folder') do |v|
    opts[:task] = v
  end

  opts.require_option(:task)
end

require_relative('../config/environment')

task_definition_name = ECS_CONFIG.dig('tasks', SCRIPT_OPTIONS[:task], 'task_definition')
container_overrides = ECS_CONFIG.dig('tasks', SCRIPT_OPTIONS[:task], 'container_overrides')
unless task_definition_name
  $stderr.puts "#{SCRIPT_OPTIONS[:task]} not found in config/ecs-tasks.yml"
  exit 1
end

runner = ECSTaskRunner.new(task_definition_name)

if container_overrides
  runner.task_options[:overrides] = {
    container_overrides: [
      container_overrides
    ]
  }
end

runner.run(SCRIPT_OPTIONS)
