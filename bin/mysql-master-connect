#!/usr/bin/env ruby
# frozen_string_literal: true

def exit_with_error(error)
  $stderr.puts "Usage mysql-master-connect <environment> <database> [<mysql args>]"
  $stderr.puts ""
  $stderr.puts "Error: #{error}"
  exit 1
end

options = {
  environment: ARGV.shift,
  database: ARGV.shift
}

ENV['RAILS_ENV'] = case options[:environment]
                   when 'production',
                        'prod'
                     'production'
                   when 'old_production',
                        'oldprod',
                        'old_prod'
                     'old_production'
                   when 'staging',
                        'stage'
                     'staging'
                   when 'old_staging',
                        'oldstage',
                        'old_stage'
                     'old_staging'
                   else
                     exit_with_error 'missing or invalid environment specified'
                   end

exit_with_error('no database specified') unless options[:database]

require File.expand_path('../config/environment', __dir__)
require 'shellwords'

exit_with_error("no configuration for #{options[:database]}") unless (db_config = ETOnePassword.new.db_config(options[:database]))

class MySQLConfig < Struct.new(:host,
                               :port,
                               :user,
                               :password,
                               :database
                              )

  def to_client_config
    "[client]\n" + self.class.members.map { |m| "#{m}=\"#{self[m]}\"" }.join("\n")
  end
end

mysql_config_file = TMP_DIR.join(".tmp.#{options[:database]}.conf")

mysql_config = MySQLConfig.new(
  db_config['host'],
  db_config['port'],
  db_config['user'],
  db_config['password'],
  db_config['database']
)

File.open(mysql_config_file, 'wb') { |f| f.write mysql_config.to_client_config }

child_pid = fork

if child_pid
  # parent process starts the mysql client
  exec("mysql --defaults-file=#{mysql_config_file} #{ARGV.map { |v| Shellwords.escape(v) }.join(' ')}")
else
  # child process cleans up the mysql credentials file
  sleep 0.5
  File.unlink mysql_config_file
end
