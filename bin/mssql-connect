#!/usr/bin/env ruby
# frozen_string_literal: true

def exit_with_error(error)
  $stderr.puts "Usage mssql-connect <environment> <database> [<mysql args>]"
  $stderr.puts ""
  $stderr.puts "Error: #{error}"
  exit 1
end

options = {
  environment: ARGV.shift,
  database: ARGV.shift
}

ENV['RAILS_ENV'] = case options[:environment]
                   when 'production',
                        'prod'
                     'production'
                   when 'staging',
                        'stage'
                     'staging'
                   when 'development',
                        'dev'
                     'development'
                   when 'test'
                     'test'
                   else
                     exit_with_error 'missing or invalid environment specified'
                   end

exit_with_error('no database specified') unless options[:database]

require File.expand_path('../config/environment', __dir__)
require 'shellwords'

exit_with_error("no configuration for #{options[:database]}") unless (database_url = ENV['DATABASE_URL_' + options[:database].upcase])

class MSSQLConfig < Struct.new(:host,
                               :port,
                               :user,
                               :password,
                               :database
                              )

  def self.from_uri(uri)
    new(
      uri.host,
      uri.port,
      Addressable::URI.unescape(uri.user),
      Addressable::URI.unescape(uri.password.to_s),
      uri.path[1..-1]
    )
  end

  def to_mssql_args
    "-s #{Shellwords.escape(host)} -u #{Shellwords.escape(user)} -p #{Shellwords.escape(password)} -o #{port} -d #{Shellwords.escape(database)}"
  end
end

config = MSSQLConfig.from_uri(URI(database_url))

exec("mssql #{config.to_mssql_args}")
