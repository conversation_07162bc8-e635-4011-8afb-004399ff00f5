#!/bin/bash

if [ ! -e "/usr/local/Cellar/openssl@1.1" ]; then
    echo "openssl@1.1 required"
    echo "run brew bundle in et_repair_shop directory"
    exit 1
fi

if [ ! -e "/usr/local/Cellar/openssl@1.0" ]; then
    echo "openssl@1.0 required"
    if [ -e "Brewfile" ]; then
        brew bundle
    else
        echo "run brew bundle in et_repair_shop/rvm/catalina directory"
        exit 1
    fi
fi

# install versions of ruby < 2.4
for version in 2.1.9 2.2.0 2.2.8 2.2.10 2.3.0 2.3.3; do
    if [ -e "$HOME/.rvm/rubies/ruby-$version" ]; then
        echo "skipping $version already installed, rvm uninstall $version to reinstall"
    else
        echo "installing ruby $version"
        rvm install $version --with-openssl-dir=$(brew --prefix openssl@1.0) --rubygems 2.7.8
    fi
done

for version in 2.4.0 2.4.9 2.4.10 2.5.1 2.6.3 2.6.5; do
    if [ -e "$HOME/.rvm/rubies/ruby-$version" ]; then
        echo "skipping $version already installed, rvm uninstall $version to reinstall"
    else
        echo "installing ruby $version"
        rvm install $version
    fi
done
