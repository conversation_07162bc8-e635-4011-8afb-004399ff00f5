#!/usr/bin/env ruby

# Purpose: to tail the pm1_logfile_log table

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)

# PledgemineDB::PM1LogfileLog(id_log: integer, time_log: datetime, loc_log: integer, entry_log: varchar_max, job_num_log: integer, trj_num_log: integer, txj_num_log: integer, pmj_num_log: integer, pxj_num_log: integer, is_error_dispatched_log: integer, times_reported_log: integer, vfy_job_num_log: integer)

def display_logfile_log(logfile_log)
  date = logfile_log.time_log.strftime('%Y-%m-%d %H:%M:%S')
  puts "#{date}[#{logfile_log.id_log}]: #{logfile_log.entry_log}"
end


last_logfile_log = PledgemineDB::PM1LogfileLog.last

display_logfile_log(last_logfile_log)

while true
  logfile_logs = PledgemineDB::PM1LogfileLog.where('id_log > ?', last_logfile_log.id_log).limit(1000)
  last_logfile_log = logfile_logs.last unless logfile_logs.empty?
  logfile_logs.each { |logfile_log| display_logfile_log(logfile_log) }
  sleep(0.1)
end
