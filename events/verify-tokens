#!/usr/bin/env ruby

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse!

require_relative '../config/environment'

def valid_token?(access_token)
  headers = {
    'Authorization' => "Bearer #{access_token}",
    'Content-Type' => 'application/json'
  }

  res = HTTPClient.get(
    URI('https://www.eventbriteapi.com/v3/users/me/organizations/'),
    headers
  )

  res.kind_of?(Net::HTTPSuccess)
end

EventDB::Token.where(deleted: false, inactive: false).where.not(token: nil).each do |token|
  next if valid_token?(token.token)

  org = AuthDB::Organization.where(id: token.oid).first

  puts "EventBrite token for #{token.oid}/#{org&.slug}, token #{token.id}/#{token.token} is invalid"
end
