#!/usr/bin/env ruby

require 'net/http'
require 'nokogiri'

task_url = ARGV[0]
raise('no task url specified') unless task_url

def get_body(url)
  uri = URI(url)

  req = Net::HTTP::Get.new(uri.request_uri)

  res = Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == 'https') do |http|
    http.request(req)
  end

  res.body
end

def fetch_task_worker_logs(task_url)
  logs = {
    stdout_urls: [],
    stderr_urls: []
  }
  doc = Nokogiri::HTML(get_body(task_url))
  doc.search('table tr').each do |element|
    _, stdout_url, stderr_url = *element.search('a').map { |link| link.attributes['href'].to_s }
    logs[:stdout_urls] << stdout_url
    logs[:stderr_urls] << stderr_url
  end
  logs
end

def get_log_redirect(url)
  uri = URI(url)

  req = Net::HTTP::Get.new(uri.request_uri)

  res = Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == 'https') do |http|
    http.request(req)
  end

  res['Location']
end

def find_previous_log_url(doc)
  doc.search('a').each do |element|
    next unless element.text.include?('Previous')

    href = element.attributes['href'].to_s
    if href.include?('?appId')
      return href
    else
      break
    end
  end

  nil
end

def download_log(file, base_url, query_params)
  while query_params
    doc = Nokogiri::HTML(get_body(base_url + query_params))
    file.write(doc.search('pre').first.text)
    query_params = find_previous_log_url(doc)
  end
end

logs = fetch_task_worker_logs(task_url)

spark_stdout_log = File.open('spark-stdout.log', 'wb')
spark_stderr_log = File.open('spark-stderr.log', 'wb')

logs[:stdout_urls].each do |url|
  base_url, query_params = get_log_redirect(url).split('?')
  query_params = '?' + query_params
  download_log(spark_stdout_log, base_url, query_params)
end

logs[:stderr_urls].each do |url|
  base_url, query_params = get_log_redirect(url).split('?')
  query_params = '?' + query_params
  download_log(spark_stderr_log, base_url, query_params)
end

spark_stdout_log.close
spark_stderr_log.close
