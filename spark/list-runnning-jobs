#!/usr/bin/env ruby

# Purpose: to list applications on the Master Spark Node

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)

class SparkApplication
  include Schema::All

  attribute :id, :string
  attribute :name, :string
  attribute :coresGranted, :integer
  attribute :maxCores, :integer
  attribute :memoryPerExecutorMB, :integer

  has_many :attempts do
    attribute :startTime, :string
    attribute :endTime, :string
    attribute :sparkUser, :string
    attribute :completed, :boolean
  end

  def completed?
    attempts.all?(&:completed)
  end

  def state
    if completed?
      'COMPLETED'
    else
      'RUNNING'
    end
  end
end

payload = JSON.parse(SparkHelpers.get_applications.body)
applications = payload.map { |item| SparkApplication.from_hash(item) }

headings = {
  'Id' => :id,
  'Name' => :name,
  'State' => :state
}
table = TableView.new(headings, row_type: :object)
table.render('Spark Jobs' => applications)
