#!/usr/bin/env ruby

# Purpose: to list applications on the Master Spark Node

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse!

require_relative '../config/environment'
require 'active_support/time'

class SparkApplication
  include Schema::All

  attribute :id, :string
  attribute :name, :string

  has_many :attempts do
    attribute :startTime, :string
    attribute :endTime, :string
    attribute :sparkUser, :string
    attribute :completed, :boolean
  end

  def noramlized_name
    name.split(' ', 2).first
  end
end

now = Time.now

historical_applications = {}

365.times.each do |days|
  max_date = now - days.days
  min_date = max_date - 1.days

  payload = JSON.parse(SparkHelpers.get_historical_applications(min_date.strftime('%Y-%m-%d'), max_date.strftime('%Y-%m-%d')).body)
  applications = payload.map { |item| SparkApplication.from_hash(item) }

  applications.each do |app|
    next if historical_applications[app.noramlized_name]

    historical_applications[app.noramlized_name] = app.attempts.first.endTime
  end
end

print JSON.pretty_generate(historical_applications) + "\n"
