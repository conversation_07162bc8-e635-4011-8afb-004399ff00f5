
Added a aws-secrets tool to the et_repair_shop.

It does just 2 things right now.
It can get secrets and save them to a file.
It can patch/update secrets, displays changes that will be applied and prompts you before making changes.
It does not remove secrets.

```
aws-secrets -h

Usage: aws-secrets (get|patch) <secret-id> <file>
    -h, --help                       Prints this help
    -p, --profile AWS_PROFILE        AWS profile
    -r, --region AWS_REGION          AWS region, default us-east-1
        --save                       Save secrets to a file
```

Example get:

```
aws-secrets get 'stage/purger' --profile evertruestage
# outputs

{
  "DATABASE_URL_AUTH": "mysql2://xxx:<EMAIL>:3306/auth"
}
```

Example put:

file: new.stage.purger.json
```
{
  "DATABASE_URL_IMPORTER": "mysql2://xxx:<EMAIL>:3306/importer"
}
```

```
aws-secrets patch 'stage/purger' new.stage.purger.json --profile evertruestage
# outputs

# creates a backup of existing secrets
secrets saved to stage.purger.json.2019-07-24T13:37:08.bak

# displays changes to apply
{
  "DATABASE_URL_IMPORTER": {
    "new_value": "mysql2://importer:<EMAIL>:3306/importer"
  }
}

# prompts you to make changes
apply changes to 'stage/purger' (y/n): y

# displays response from AWS
updated 'stage/purger'
{
  "arn": "arn:aws:secretsmanager:us-east-1:034192497236:secret:stage/purger-rDkpl8",
  "name": "stage/purger",
  "version_id": "d7abbf13-ba35-4d3e-9395-f5b339e685c1",
  "version_stages": [
    "AWSCURRENT"
  ]
}

# displays new secrets
{
  "DATABASE_URL_AUTH": "mysql2://xxx:<EMAIL>:3306/auth",
  "DATABASE_URL_IMPORTER": "mysql2://xxx:<EMAIL>:3306/importer"
}
```

