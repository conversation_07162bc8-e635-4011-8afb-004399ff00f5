#!/usr/bin/env ruby

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
end

require_relative '../config/environment'

def org
  @org ||= AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])
end

def org_regex
  @org_regex ||= Regexp.new('^' + org.id.to_s)
end

def flow_matches_org?(flow_name)
  org_regex.match?(flow_name)
end

def appflow_client
  @appflow_client ||= AwsAppflowHelper.create_client
end

class AppflowTaskCSVRow < Struct.new(
        :flow,
        :task
      )
  include CSVUtils::CSVRow

  # Full description of all the fields that are available
  # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/Appflow/Client.html#describe_flow-instance_method
  csv_column(:flow_name) { flow.flow_name }
  csv_column(:salesforce_object) { flow.source_flow_config.source_connector_properties.salesforce.object }
  csv_column(:salesforce_source_fields) { task.source_fields.join(', ') }
  csv_column(:destination_field) { task.destination_field }
  csv_column(:bucket_name) { flow.destination_flow_config_list[0].destination_connector_properties.s3.bucket_name }
  csv_column(:bucket_prefix) { flow.destination_flow_config_list[0].destination_connector_properties.s3.bucket_prefix }
  csv_column(:file_type) { flow.destination_flow_config_list[0].destination_connector_properties.s3.s3_output_format_config.file_type }
  csv_column(:preserve_source_data_typing) { flow.destination_flow_config_list[0].destination_connector_properties.s3.s3_output_format_config.preserve_source_data_typing }
end

# find all flows that match the org
org_flow_names = []
AwsAppflowHelper.each_flow(appflow_client) do |flow|
  next unless flow_matches_org?(flow.flow_name)

  org_flow_names << flow.flow_name
end

org_flows = org_flow_names.map do |flow_name|
  LOG.info("describing appflow #{flow_name}")
  appflow_client.describe_flow(flow_name: flow_name)
end

file = TMP_DIR.join('appflows', org.id.to_s, "applows-#{org.id}-#{org.slug}-#{Time.now.strftime('%Y-%m-%d')}.csv").to_s
FileUtils.mkdir_p(File.dirname(file))
CSVUtils::CSVReport.new(file, AppflowTaskCSVRow) do |report|
  org_flows.each do |flow|
    flow.tasks.each do |task|
      next unless task.task_type == 'Map'

      report << AppflowTaskCSVRow.new(flow, task)
    end
  end
end

puts file
