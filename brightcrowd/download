#!/usr/bin/env ruby

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-u', '--username USERNAME', 'BrightCrowd username') do |v|
    opts[:username] = v
  end

  opts.on('-p', '--password PASSWORD', 'BrightCrowd password') do |v|
    opts[:password] = v
  end

  opts.require_option(:username)
  opts.require_option(:password)
end.parse!

require_relative '../config/environment'

class BrightCrowdOauthClient < Struct.new(
        :username,
        :password
      )
  include ClientApiBuilder::Router

  base_url 'https://bcb-staging.auth.us-east-1.amazoncognito.com'
  body_builder :query_params

  header 'Authorization', :basic_authorization
  header 'Content-Type', 'application/x-www-form-urlencoded'

  route :create_token, '/oauth2/token', body: {grant_type: 'client_credentials', scope: 'bcb.partner/book.read'}
  
  def basic_authorization
    'Basic ' + Base64.strict_encode64(username + ':' + password)
  end
end

class BrightCrowdApiClient < Struct.new(
        :access_token
      )
  include ClientApiBuilder::Router

  base_url 'https://api.brightcrowd.com'

  header 'Authorization', :bearer_authorization
  header 'Accept', 'application/json'

  namespace('/partner') do
    route :get_books, '/books'
    route :get_pages, '/books/:book_id/pages'
  end

  def bearer_authorization
    'Bearer ' + access_token
  end
end

base_dir = TMP_DIR.join('brightcrowd')
FileUtils.mkdir_p(base_dir)

oauth_client = BrightCrowdOauthClient.new(SCRIPT_OPTIONS[:username], SCRIPT_OPTIONS[:password])
oauth_payload = oauth_client.create_token()

api_client = BrightCrowdApiClient.new(oauth_payload['access_token'])
books_payload = api_client.get_books()
File.open(base_dir.join('books.json'), 'wb') do |f|
  f.write(JSON.pretty_generate(books_payload) + "\n")
end

books_payload['books'].each do |book|
  book_id = book['id']
  puts "fetching pages for #{book_id}"

  pages_payload = api_client.get_pages(book_id: book_id)

  File.open(base_dir.join("pages.#{book_id}.json"), 'wb') do |f|
    f.write(JSON.pretty_generate(pages_payload) + "\n")
  end
end
