#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:cluster] = 'worker'

  opts.on('-c', '--cluster CLUSTER', 'Cluster type api, worker, importer_worker') do |v|
    opts[:cluster] = v
  end

  opts.require_option(:cluster)
end

require File.expand_path('../config/environment', __dir__)
include CommandLineHelpers
include TimeHelpers

def ecs_client
  @ecs_client ||= ECSHelpers.create_client
end

def cluster_name
  SCRIPT_OPTIONS[:cluster]
end

def container_instance_arns
  @container_instance_arns ||= ecs_client.list_container_instances(cluster: ECS_CONFIG['ecs']['cluster'][cluster_name]).container_instance_arns
end

def container_instances
  @container_instances ||= ecs_client.describe_container_instances(cluster: ECS_CONFIG['ecs']['cluster'][cluster_name], container_instances: container_instance_arns).container_instances
end

def list_tasks(container_instance)
  ecs_client.list_tasks(cluster: ECS_CONFIG['ecs']['cluster'][cluster_name], container_instance: container_instance).task_arns
end

def running_tasks
  unless defined?(@running_tasks)
    @running_tasks = []
    container_instance_arns.each do |container_instance_arn|
      tasks = list_tasks(container_instance_arn)
      next if tasks.empty?

      ecs_client.describe_tasks(cluster: ECS_CONFIG['ecs']['cluster'][cluster_name], tasks: tasks).tasks.each do |task|
        @running_tasks << task
      end
    end
  end
  @running_tasks
end

headings = {
  'Task ID' => :task_id,
  'Task Definition' => :task_definition,
  'Created' => :created,
  'Command' => :command
}

data = {}
container_instances.each do |container_instance|
  data["EC2 Instance: #{container_instance.ec2_instance_id}"] =
    begin
      running_tasks
        .select { |t| t.container_instance_arn == container_instance.container_instance_arn }
        .map do |t|
        {
          task_id: t.task_arn.split('/').last,
          task_definition: t.task_definition_arn.split('/').last,
          command: t.overrides.container_overrides.first.command,
          created: time_in_words(t.created_at)
        }
      end
    end
end

TableView.new(headings).render(data)
