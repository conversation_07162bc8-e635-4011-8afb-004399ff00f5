#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:cluster] = 'api'

  opts.on('-c', '--cluster CLUSTER', 'Cluster type api, worker, importer_worker') do |v|
    opts[:cluster] = v
  end

  opts.require_option(:cluster)
end

require File.expand_path('../config/environment', __dir__)
include CommandLineHelpers
include TimeHelpers

def ecs_client
  @ecs_client ||= ECSHelpers.create_client
end

def cluster_name
  SCRIPT_OPTIONS[:cluster]
end

def container_instance_arns
  @container_instance_arns ||= ecs_client.list_container_instances(cluster: ECS_CONFIG['ecs']['cluster'][cluster_name]).container_instance_arns
end

def container_instances
  @container_instances ||= ecs_client.describe_container_instances(cluster: ECS_CONFIG['ecs']['cluster'][cluster_name], container_instances: container_instance_arns).container_instances
end

headings = {
  'EC2 Instances ID' => :ec2_instance_id,
  'Status' => :status,
  'Remaining Memory' => :remaining_memory,
  'Remaining CPU' => :remaining_cpu,
  'Running Tasks' => :running_tasks_count,
  'Pending Tasks' => :pending_tasks_count
}

instances = []
container_instances.each do |container_instance|
  instances << {
    ec2_instance_id: container_instance.ec2_instance_id,
    status: container_instance.status,
    remaining_memory: container_instance.remaining_resources.detect { |r| r.name == 'MEMORY' }.integer_value,
    remaining_cpu: container_instance.remaining_resources.detect { |r| r.name == 'CPU' }.integer_value,
    running_tasks_count: container_instance.running_tasks_count,
    pending_tasks_count: container_instance.pending_tasks_count
  }
end

TableView.new(headings).render('Instances' => instances)
