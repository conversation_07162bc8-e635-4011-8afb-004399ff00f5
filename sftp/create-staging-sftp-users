#!/usr/bin/env ruby

# Goto https://password-generator.evertrue.com/ to generate a password.

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-p', '--password PASSWORD', 'Password hash') do |v|
    opts[:password] = v
  end

  opts.require_option(:password)
end

ENV['RAILS_ENV'] = 'staging'
require File.expand_path('../config/environment', __dir__)

raise("only works in stage") unless ENV['RAILS_ENV'] == 'staging'

S3_BUCKET = 'et-sftp-stage'
ROLE = 'arn:aws:iam::034192497236:role/et-sftp-rw-role'

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

if (sftp_user = AuthDB::SftpUser.find_by(username: org.slug))
  sftp_user.password = SCRIPT_OPTIONS[:password]
  sftp_user.save!
else
  AuthDB::SftpUser.create!(
    organization_id: org.id,
    username: org.slug,
    password: SCRIPT_OPTIONS[:password],
    home_directory_details: [{Entry: '/', Target: "/#{S3_BUCKET}/users/#{org.slug}"}].to_json,
    role: ROLE
  )
end

FOLDERS = [
  'uploads',
  'exports',
  'reports',
  'integrations'
]

s3_client = Aws::S3::Client.new(region: 'us-east-1', profile: 'evertruestage')

AuthDB::SftpUser.all.each do |sftp_user|
  target_home_directory = JSON.parse(sftp_user.home_directory_details).first['Target']
  next unless target_home_directory =~ /\/(users\/.*)/

  base_folder = $1

  FOLDERS.each do |sub_folder|
    folder = base_folder + '/' + sub_folder + '/'
    puts "create folder #{folder}"

    s3_client.put_object(
      bucket: S3_BUCKET,
      key: folder
    )
  end
end
