#!/usr/bin/env ruby

# Purpose: to manage Evertrue employee <PERSON><PERSON> accounts

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-u', '--username USERNAME', 'Specify a username in the unix-users repo') do |v|
    opts[:username] = v
  end

  opts.require_option(:username)
end

require File.expand_path('../config/environment', __dir__)

S3_BUCKET =
  if ENV['RAILS_ENV'] == 'production'
    'et-sftp-prod'
  else
    'et-sftp-stage'
  end

ROLE =
  if ENV['RAILS_ENV'] == 'production'
    'arn:aws:iam::************:role/et-sftp-rw-role'
  else
    'arn:aws:iam::************:role/et-sftp-rw-role'
  end

user_data = JSON.parse(File.read("#{ENV['HOME']}/dev/unix-users/data_bags/users/#{SCRIPT_OPTIONS[:username]}.json"))

sftp_user = AuthDB::SftpUser.find_by(username: SCRIPT_OPTIONS[:username])

if user_data['action'] == 'remove'
  sftp_user&.destroy
  return
end

sftp_user ||=
  begin
    AuthDB::SftpUser.new(
      organization_id: 1,
      username: SCRIPT_OPTIONS[:username],
      home_directory_details: [{Entry: '/', Target: "/#{S3_BUCKET}"}].to_json,
      role: ROLE
    )
  end

public_keys =
  if user_data['ssh_keys'] && !user_data['ssh_keys'].empty?
    user_data['ssh_keys'].map { |k| k.split(' ')[0..1].join(' ') }
  else
    nil
  end

sftp_user.comment = user_data['comment']
sftp_user.password = user_data['password']
sftp_user.public_keys = public_keys&.to_json

sftp_user.save!
