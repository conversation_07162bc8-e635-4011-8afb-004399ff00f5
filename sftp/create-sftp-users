#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
  opts.add_scheduling_options
end

require File.expand_path('../config/environment', __dir__)

raise("only works in prod") unless ENV['RAILS_ENV'] == 'production'

S3_BUCKET = 'et-sftp-prod'
ROLE = 'arn:aws:iam::923017004626:role/et-sftp-rw-role'

orgs = AuthDB::Organization.all.index_by(&:slug)
sftp_users = AuthDB::SftpUser.all.index_by(&:username)

github_client = GithubClient.new
upload_users = JSON.parse(github_client.get_file_content('unix-users', 'data_bags/users/upload.json'))
upload_users.each do |username, data|
  next if username == 'id'

  org_slug =
    if username == 'yale4243'
      'yaleuniversity'
    elsif username == 'csutestv1'
      'csutestv1'
    elsif username == 'bowdoin-thankview'
      'bowdoincollege'
    else
      username
        .sub(/\-[^\-]+$/, '')
        .sub(/\d+$/, '')
    end

  unless (org = orgs[org_slug])
    puts "no org found for user #{username} using slug #{org_slug}"
    next
  end

  password =
    if !data['password']&.empty?
      data['password']
    else
      nil
    end

  public_keys =
    if data['ssh_keys'] && !data['ssh_keys'].empty?
      data['ssh_keys'].map { |k| k.split(' ')[0..1].join(' ') }.to_json
    else
      nil
    end

  partner =
    if !data['partner']&.empty?
      data['partner']
    else
      nil
    end

  home_directory_target =
    if partner
      "/#{S3_BUCKET}/users/#{org.slug}/#{partner}"
    else
      "/#{S3_BUCKET}/users/#{org.slug}"
    end

  if (sftp_user = sftp_users[username])
    sftp_user.password = password
    sftp_user.public_keys = public_keys
    sftp_user.comment = data['comment']
    sftp_user.partner = partner
    sftp_user.home_directory_details = [{Entry: '/', Target: home_directory_target}].to_json

    if sftp_user.changed?
      puts "updating credentials for username #{username}"
      sftp_user.save!
    end

    next
  end

  puts "creating sftp user for username #{username} for org #{org.id} using home directory #{home_directory_target}"

  AuthDB::SftpUser.create!(
    organization_id: org.id,
    username: username,
    password: password,
    public_keys: public_keys,
    comment: data['comment'],
    partner: partner,
    home_directory_details: [{Entry: '/', Target: home_directory_target}].to_json,
    role: ROLE
  )
end

FOLDERS = [
  'uploads',
  'exports',
  'reports/uploads',
  'reports/exports',
  'integrations',
  '.internal'
]

s3_client = Aws::S3::Client.new(EnvironmentLoader.instance.aws_config)

puts 'verifying all sftp users have required folders'

AuthDB::SftpUser.all.each do |sftp_user|
  target_home_directory = JSON.parse(sftp_user.home_directory_details).first['Target']
  next unless target_home_directory =~ /\/(users\/.*)/

  base_folder = $1

  FOLDERS.each do |sub_folder|
    folder = base_folder + '/' + sub_folder + '/'

    begin
      s3_client.head_object(
        bucket: S3_BUCKET,
        key: folder
      )

      next
    rescue Aws::S3::Errors::NotFound
    end

    puts "creating folder #{folder}"

    s3_client.put_object(
      bucket: S3_BUCKET,
      key: folder
    )
  end
end
