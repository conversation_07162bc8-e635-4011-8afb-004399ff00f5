#!/usr/bin/env ruby
#
# This script will create files of contact tag delete requests in a `bulk_requests` directory.
# Each file will contain delete requests for contact_tag docs for a certain tag name.
# If you don't want to delete a certain tag's contact_tag docs, then you can remove their
# files from that directory before executing on them.
#
# It will put 1000 requests in a file. You can change that by editing the 
# REQUESTS_PER_FILE variable below. 
#
#  ./find-all-contact-tags -e stage -i oid
#
# After running this script you can run the `execute_requests` script in the es directory,
# which will send the bulk requests to Elastic Search.
# 
# Example for stage:
#
#   ../es/execute_build_requests http://stage-searchmaster-v2-contacts-1c:9200/_bulk
#
# NOTE: there may be a lot of different tags (or tagged contacts) and therefore a lot of files.  This script could be 
# improved upon by taking a list of tags we care about and only paying attention to those. 
#


require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_run_remote

  opts.on('--perform-delete', 'Whether or not to delete tags found (default 0 (false))') do |v|
    opts[:perform_delete] = true
  end
end


delete = false 
if SCRIPT_OPTIONS[:perform_delete] == true
  delete = true 
end

oid = SCRIPT_OPTIONS[:oid].to_s
puts "Input: \n\toid #{oid}\n\tperform delete: #{delete}"

require File.expand_path('../config/environment', __dir__)
require 'elasticsearch_facet_scanner'
require 'fileutils'

# number of requests per file
REQUESTS_PER_FILE = 1000

def save_bulk_request_of_docs_to_delete(dir, docs_to_delete, cnt, tag_name)
  STATS.update(:pending_bulk_requests)
  File.open("#{dir}/delete_docs_#{cnt}_#{tag_name}.request", 'wb') do |f|
    docs_to_delete.each do |doc|
      f.puts({delete: {_index: doc['_index'], _id: doc['_id'], _type: doc['_type'], _routing: doc['_routing']}}.to_json)
    end
  end
end

es_schema = ESSchema.new(ES_URL)
es_schema.docs.each do |info|
  next unless info[:mapping_name] =~ /contact_tag/

  puts "searching #{info.inspect}"
  started_at = Time.now
  url = ES_URL + '/' + info[:index_name] + '/' + info[:mapping_name] + '/_search'

  cnt = 0
  dir = "contact-tags/bulk_requests/#{info[:index_name]}/#{info[:mapping_name]}"
  FileUtils.mkdir_p(dir)

  docs_to_delete = []

  # first grab all the tag names from an aggregation of them
  facet_scanner = ElasticSearchFacetScanner.new(url, "tag_name")
  facet_scanner.each do |bucket|

    puts "Found Tag name: #{bucket['key']}"

    # now perform a query for all contact_tag docs with that tag name
    query = {
        "bool": {
            "must": [ 
              { 
                "term": {
                    "tag_name": bucket['key']
                }
              },
              {
                "term":  {
                    "oid": oid
                }
              }
            ]
          }
        }

    es_scanner = ElasticSearchScanner.new(url, query)
    es_scanner.fields_to_return = false;

    es_scanner.each do |result|

      STATS.inc_and_notify

      docs_to_delete << result

      if docs_to_delete.size >= REQUESTS_PER_FILE
        cnt += 1
        save_bulk_request_of_docs_to_delete(dir, docs_to_delete, cnt, bucket['key'])
        docs_to_delete = []
      end
    end

    if docs_to_delete.size > 0
      cnt += 1
      save_bulk_request_of_docs_to_delete(dir, docs_to_delete, cnt, bucket['key'])
      docs_to_delete = []

    end
  end

  puts "creating delete requests took: #{Time.now - started_at} to search #{info.inspect}"
  
  if delete == true

    puts "Deleting tag docs that were found now"

    es_bulk_uri = ESHelpers.uri_for_path('/_bulk')
    Dir.glob('contact-tags/bulk_requests/**/**.request').each do |bulk_delete_request_file|
      res = HTTPClient.post(es_bulk_uri, File.read(bulk_delete_request_file))

      if res.kind_of?(Net::HTTPSuccess)
        STATS.update(:processed_bulk_requests)
        LOG.info "processed #{bulk_delete_request_file}"
        FileUtils.mv(bulk_delete_request_file, bulk_delete_request_file.sub('.request', '.done'))
      else
        STATS.update(:failed_bulk_requests)
        LOG.error "failed to process #{bulk_delete_request_file}, reason #{res.code}/#{res.body}"
      end
    end
  end

  STATS.notify(true)
  puts "overall time #{Time.now - started_at} to search #{info.inspect}"
end 
