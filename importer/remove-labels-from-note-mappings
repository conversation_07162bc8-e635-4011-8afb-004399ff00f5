#!/usr/bin/env ruby

require File.expand_path('../config/environment', __dir__)

def find_all_note_mapping_hashes_with_binding(client, prop_name)
  sql = <<SQL
SELECT
  id, oid
FROM
  note_mapping_hash
WHERE
  id IN(SELECT DISTINCT hash_id FROM note_mapping_binding_property WHERE prop_name = "#{Mysql2::Client.escape(prop_name)}")
SQL

  client.query(sql)
end

importer_mysql_client = MySQLHelpers.create_client(:importer)
importer_api_client = ImporterClient.create_app_client

find_all_note_mapping_hashes_with_binding(importer_mysql_client, 'label.value').to_a.each do |mapping_hash|
  mapping_payload = importer_api_client.get_mapping(mapping_hash['oid'], 'note', mapping_hash['id'])
  mapping_payload['updated_at'] = (Time.now.to_f * 1000).to_i
  mapping_payload.delete('label')
  importer_api_client.update_mapping(mapping_hash['oid'], 'note', mapping_hash['id'], mapping_payload)
end

