#!/usr/bin/env ruby

# This script is intended to provide detailed and contextual job status on a specific job id. Different options can be provided
# to provide more data to the user.

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
    opts.on('-l', '--event-limit [EVENT_LIMIT]') do |v|
        opts[:event_limit] = v.to_i
    end

    opts.on('--force-get-errors', 'force the retrieval of errors regardless of import status') do 
        opts[:force_get_errors] = true
    end

    opts.on('--get-logs [STATUS]', 'Get the logs for the current status, or given status if specified') do |status|
        puts status
        opts[:get_logs] = status ? status : nil
    end

    opts.on('-v', 'Verbose mode') do
        opts[:verbose] = true
    end

    opts.on('--show-all-stats', 'The stats table is very wide, and not all stats are shown by default. This gets all stats') do
        opts[:show_all_stats] = true
    end

    opts.on('--show-status-info', 'Shows details on all statuses') do 
        opts[:show_status_info] = true
    end
end

require_relative '../config/environment'

job_id = ARGV.shift
raise 'missing job id' unless job_id

event_limit = SCRIPT_OPTIONS[:event_limit] || 10
verbose = SCRIPT_OPTIONS[:verbose] || false
show_all_stats = SCRIPT_OPTIONS[:show_all_stats] || false
show_status_info = SCRIPT_OPTIONS[:show_status_info] || false

job = ImporterDB::Job.find(job_id)
oid = job.oid

timstamp_formatter = Proc.new { |timestamp| 
    timestamp ? Time.at(timestamp.to_i / 1000).iso8601 : ""
}

overview_table = TableView.new(TableView.create_default_headings([
    :id,
    :oid,
    :type,
    :s3_filename,
    :is_automatic?,
    :total_progress,
    :current_status_name,
    :current_step,
    :current_step_progress
]), row_type: :object)
overview_table.render('Overview' => [job])
                    
# Display job stats
if (verbose || show_all_stats) && !job.csv_converter_stats.empty?
    reject_columns = [
        :id,
        :oid,
        :job_id,
        :created_at,
        :create_total,
        :create_failure_total,
        :update_total,
        :update_failure_total,
        :delete_total,
        :delete_failure_total,
        :unchange_total,
        :skip_total,
        :warning_total,
        :job_type_info
    ]

    puts ""
    TableView.render_db_results(
        job.csv_converter_stats, {
            reject_columns: show_all_stats ? [] : reject_columns,
            formatters: {
                started_at: timstamp_formatter,
                created_at: timstamp_formatter,
                updated_at: timstamp_formatter
            }
        }
    )
end

# Display detailed job status info
puts ""
TableView.render_db_results(job.job_statuses, {
    formatters: {
        started_at: timstamp_formatter,
        ended_at: timstamp_formatter
    }
}) if verbose || show_status_info

if job.has_error? || SCRIPT_OPTIONS[:force_get_errors]
    puts ""
    puts "loading errors..."
 
    error_events_count = ImporterDB::ErrorEvent.where(job_id: job_id, oid: oid).count
    error_events = ImporterDB::ErrorEvent
        .where(job_id: job_id, oid: oid)
        .limit(event_limit)
        .order('level_id ASC')

    TableView.render_db_results(error_events)
end

# based on the give status,  get the associated running status to lookup the logs
def resolve_running_status(status)
    case status
    when /FILECHECK/
        'FILECHECK_RUNNING'
    when /PREPROCESS/
        'PREPROCESS_RUNNING'
    when /IMPORT/
        'IMPORT_RUNNING'
    else
        status
    end
end

if job.has_error? || SCRIPT_OPTIONS[:get_logs]
    if SCRIPT_OPTIONS[:get_logs]
        # if a status is specified, don't try to modify it
        status = SCRIPT_OPTIONS[:get_logs]
    else
        # otherwise, use the current status but try to find the running status for the current status (logs are only
        # available for _RUNNING statuses)
        status = resolve_running_status(job.current_status.status)
    end
    
    puts ""
    puts "finding logs for status #{status}..."

    cmd = "./aws/find-import-job-log-stream #{job_id} -s #{status} -e #{SCRIPT_OPTIONS[:environment]}"
    puts cmd
    puts `#{cmd}`
end