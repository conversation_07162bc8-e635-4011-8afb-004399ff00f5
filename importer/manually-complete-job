#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)
include CommandLineHelpers

JOB_COMPLETE_STATUSES = [
  'CANCELLED',
  'FAILED',
  'OK',
  'WARN'
]

job_id = ARGV[0].to_i
raise('missing job_id') unless job_id > 0

mysql_client = MySQLHelpers.create_client(:importer)

job = mysql_client.query("SELECT * FROM job WHERE id = #{job_id}").first

raise("no job #{job_id} found in the importer") unless job

last_job_status = mysql_client.query("SELECT * FROM job_status WHERE job_id = #{job_id} ORDER BY id DESC LIMIT 1").first
raise("no job status found for #{job_id}, are you sure this job ran?") unless last_job_status
raise("something went wrong last_job_status ended_at is already set") if last_job_status['ended_at']

if JOB_COMPLETE_STATUSES.include?(last_job_status['status'])
  puts "Job is already considered complete, current status is #{bold_string(last_job_status['status'])}"
  exit
end

prompt = "Job status is currently #{bold_string(last_job_status['status'])}, do you want to change it to #{bold_string('OK')}?"

if yes_no(prompt) == :yes
  ended_at = (Time.now.to_f * 1000.0).to_i
  mysql_client.query("UPDATE job_status SET ended_at = #{ended_at} WHERE id = #{last_job_status['id']}")
  mysql_client.query("INSERT INTO job_status (job_id, status, started_at) VALUES(#{job_id}, \"OK\", #{ended_at})")
end
