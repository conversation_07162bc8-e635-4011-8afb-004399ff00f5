#!/usr/bin/env ruby

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-c', '--contact CONTACT_ID', Integer, 'Contact ID') do |v|
    opts[:contact_id] = v
  end

  opts.require_option(:contact_id)
end

require_relative '../config/environment'

dynamodb_client = ImporterDynamodbCacheHelpers.create_dynamodb_client

response = ImporterDynamodbCacheHelpers.get_contact_cache_item(
  dynamodb_client,
  SCRIPT_OPTIONS[:oid],
  SCRIPT_OPTIONS[:contact_id]
)

if response.item
  puts "Found cache data"
  print JSON.pretty_generate(response.item) + "\n"
else
  puts "No cache data found"
end
