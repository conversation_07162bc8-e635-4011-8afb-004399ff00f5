#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
    opts.on('-o', '--output PATH', 'Output directory') do |v|
        opts[:output_path] = v
    end
    opts.on('--to-stdout', 'Output the data directly to stdout instead of saving to a dir') do 
        opts[:to_stdout] = true
    end
end

job_id = ARGV.shift

raise "job-id is required" unless job_id

require 'open-uri'
require 'fileutils'
require File.expand_path('../config/environment', __dir__)

output_path = SCRIPT_OPTIONS[:output_path]

oid = ImporterDB::Job.find_by_id(job_id).oid
bucket_name = "import.evertrue.com"
env = SCRIPT_OPTIONS[:environment] =~ /prod/ ? "prod" : "stage"
s3_uri = "s3://#{bucket_name}/#{env}/#{oid}/job/#{job_id}/"
local_path = output_path ? output_path : File.join(TMP_DIR, "importers", oid.to_s, job_id.to_s)

FileUtils.mkdir_p local_path

cmd = "aws s3 cp #{s3_uri} #{local_path} --recursive"
puts cmd
`#{cmd}`