#!/usr/bin/env ruby

# Purpose: compare the remote ids in a general info full ipmort to another import file and output all unknown ids

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-f', '--full-job JOB_ID', Integer, 'Job ID of a full import file') do |v|
    opts[:full_job_id] = v
  end

  opts.on('-j', '--job JOB_ID', Integer, 'Job ID to compuare to the full job') do |v|
    opts[:job_id] = v
  end

  opts.require_option(:full_job_id)
  opts.require_option(:job_id)
end

require File.expand_path('../config/environment', __dir__)
require 'active_record'
ActiveRecord::Base.logger = LOG

full_job = ImporterDB::Job.where(oid: SCRIPT_OPTIONS[:oid], id: SCRIPT_OPTIONS[:full_job_id]).first
job = ImporterDB::Job.where(oid: SCRIPT_OPTIONS[:oid], id: SCRIPT_OPTIONS[:job_id]).first

importer_client = ImporterClient.create_client_with_app_creds

job_data = importer_client.get_job(full_job.oid, full_job.id)
full_job_remote_id_header = importer_client.get_mapping(full_job.oid, nil, job_data['mapping_hash_id']).dig('identity', 'header_value')

LOG.info("full import file #{full_job.s3_filename} remote id header is #{full_job_remote_id_header}")

job_data = importer_client.get_job(job.oid, job.id)
job_remote_id_header = importer_client.get_mapping(job.oid, nil, job_data['mapping_hash_id']).dig('identity', 'header_value')

LOG.info("import file #{job.s3_filename} remote id header is #{job_remote_id_header}")

importer_s3_client = ImporterS3Client.new

full_s3_object = importer_s3_client.get_object(full_job.s3_key)
full_csv = CSVUtils::CSVIterator.new(CSV.new(full_s3_object.body))

s3_object = importer_s3_client.get_object(job.s3_key)
csv = CSVUtils::CSVIterator.new(CSV.new(s3_object.body))

all_remote_ids = full_csv.map { |row| row[full_job_remote_id_header] }.to_set
LOG.info("found #{all_remote_ids.size} remote ids in full import file")

out = CSV.open(job.s3_filename.sub('.csv', '.unknown.csv'), 'wb')
out << csv.first.keys

csv.each do |row|
  STATS.inc_and_notify
  if all_remote_ids.include?(row[job_remote_id_header])
    STATS.update(:known_ids)
  else
    STATS.update(:unknown_ids)
    out << row.values
  end
end

out.close
STATS.notify(true)
