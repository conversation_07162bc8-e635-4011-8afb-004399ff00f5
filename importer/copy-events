#!/usr/bin/env ruby


require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
    opts.add_run_remote
end

require_relative '../config/environment'
require 'active_record'

ActiveRecord::Base.logger = LOG

batch_size = 10_000
current_id = 4132577914
max_id = 4294967295

def insert_range(min, max)
    id_range = (min .. max).to_a
    sql = "INSERT INTO event (oid, job_id, event_code, remote_id, message, source, created_at, operation, data_type, type) SELECT oid, job_id, event_code, remote_id, message, source, created_at, operation, data_type, type FROM event_old WHERE id IN(#{id_range.join(',')});"
    ImporterDB::Event.connection.execute(sql)
end

while current_id <= max_id
    next_id = current_id + batch_size
    
    insert_range(current_id, [next_id, max_id].min)
    current_id = next_id
end






