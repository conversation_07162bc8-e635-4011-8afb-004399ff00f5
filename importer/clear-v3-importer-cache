#!/usr/bin/env ruby

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_run_remote

  opts.on('-t', '--table TABLE_TYPE', 'Specify the table type to clear assignments, contacts, interactions, proposals, gifts') do |v|
    opts[:table_type] = v
  end

  opts.require_option(:table_type)
end

require_relative '../config/environment'

def dynamodb_client
  @dynamodb_client ||= ImporterDynamodbCacheHelpers.create_dynamodb_client
end

def each_item(base_query)
  res = dynamodb_client.query(base_query)
  res[:items].each do |item|
    yield item
  end

  while res[:last_evaluated_key]
    query = base_query.merge(exclusive_start_key: res[:last_evaluated_key])

    res = dynamodb_client.query(query)
    res[:items].each do |item|
      yield item
    end
  end
end

table_name = ImporterDynamodbCacheHelpers.get_table_name(SCRIPT_OPTIONS[:table_type])
unless table_name
  raise("no table name for type #{SCRIPT_OPTIONS[:table_type]}")
end

base_query = {
  table_name: table_name,
  limit: 100,
  expression_attribute_values: {
    ":oid" => SCRIPT_OPTIONS[:oid]
  }, 
  key_condition_expression: "oid = :oid",
  projection_expression: 'evertrue_id'
}

delete_requests = []
delete_requests_proc = proc do
  STATS.update(:delete_requests)
  res = dynamodb_client.batch_write_item(
    request_items: {
      table_name => delete_requests
    }
  )
  delete_requests = []
end

each_item(base_query) do |item|
  STATS.inc_and_notify

  delete_requests << {
    delete_request: {
      key: {
        oid: SCRIPT_OPTIONS[:oid],
        evertrue_id: item['evertrue_id'].to_i
      }
    }
  }

  delete_requests_proc.call if delete_requests.size >= 25
end

delete_requests_proc.call if delete_requests.size > 0

STATS.notify(true)
