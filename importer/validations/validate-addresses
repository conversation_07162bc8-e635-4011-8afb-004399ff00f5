#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:remote_id_field_name] = 'PersonID'

  opts.add_run_remote
  opts.add_oid_option
  opts.add_input_file

  opts.on('--remote-id REMOTE_ID', 'Remote ID column name') do |v|
    opts[:remote_id_field_name] = v
  end

  opts.on('--save-output-files') do
    opts[:save_output_files] = true
  end
end

require_relative '../../config/environment'

class AddressSchema
  include Schema::All
  include Schema::Normalize
  include CSVUtils::CSVRow
  include Comparable

  attr_accessor :et_contact_remote_id,
                :comparison_result

  attribute :type, :string_or_nil, aliases: ['AddressType']
  attribute :address_1, :string_or_nil, aliases: ['AddressLine1']
  attribute :address_2, :string_or_nil, aliases: ['AddressLine2']
  attribute :address_3, :string_or_nil, aliases: ['AddressLine3']
  attribute :city, :string_or_nil, aliases: ['AddressCity']
  attribute :state, :string_or_nil, aliases: ['AddressState']
  attribute :country, :string_or_nil, aliases: ['AddressCountry']
  attribute :zip_code, :string_or_nil, aliases: ['AddressZip']
  attribute :primary, :boolean, aliases: ['AddressIsPrimary']

  FIELD_NAMES_FOR_COMPARISON = self.schema.keys.reject { |k| self.schema[k][:alias_of] }

  csv_column(:et_contact_remote_id)
  FIELD_NAMES_FOR_COMPARISON.each do |field|
    csv_column(field)
  end
  csv_column(:comparison_result)

  def <=>(other)
    FIELD_NAMES_FOR_COMPARISON.each do |field_name|
      result = send(field_name) <=> other.send(field_name)
      return result unless result == 0
    end

    0
  end
end

ImporterContactFileComparer.new(
  org: AuthDB::Organization.find(SCRIPT_OPTIONS[:oid]),
  import_file: SCRIPT_OPTIONS[:file],
  schema_class: AddressSchema,
  model_class: ContactDB::Address,
  remote_id_field_name: SCRIPT_OPTIONS[:remote_id_field_name],
  save_output_files: SCRIPT_OPTIONS[:save_output_files]
).compare_and_report

STATS.notify(true)
