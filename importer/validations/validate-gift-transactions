#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:remote_id_field_name] = 'PersonID'

  opts.add_run_remote
  opts.add_oid_option
  opts.add_input_file

  opts.on('--remote-id REMOTE_ID', 'Remote ID column name') do |v|
    opts[:remote_id_field_name] = v
  end

  opts.on('--save-output-files') do
    opts[:save_output_files] = true
  end
end

require_relative '../../config/environment'

class GiftTransactionSchema
  include Schema::All
  include Schema::Normalize
  include CSVUtils::CSVRow
  include Comparable

  TIMESTAMP_OFFSET = 12 * 3600 * 1000

  attr_accessor :et_contact_remote_id,
                :comparison_result

  attribute :amount, :float, aliases: ['GiftAmount']
  attribute :gift_remote_id, :string, aliases: ['GiftID']
  attribute :type, :string, aliases: ['GiftType']
  attribute :occurred_at, :timestamp, aliases: ['GiftDate']
  attribute :contact_remote_id, :string, aliases: ['PersonID']
  attribute :legal_credit, :boolean, aliases: ['LegalCredit']
  attribute :transaction_type, :string, aliases: ['TransactionType']
  attribute :designation, :string, aliases: ['Designation']

  FIELD_NAMES_FOR_COMPARISON = self.schema.keys.reject { |k| self.schema[k][:alias_of] || self.schema[k][:association] }

  csv_column(:et_contact_remote_id)
  FIELD_NAMES_FOR_COMPARISON.each do |field|
    csv_column(field)
  end
  csv_column(:comparison_result)

  def <=>(other)
    FIELD_NAMES_FOR_COMPARISON.each do |field_name|
      result = send(field_name) <=> other.send(field_name)
      return result unless result == 0
    end

    0
  end

  def parse_timestamp(field_name, parsing_errors, value)
    case value
    when Date
      value.to_time.utc.to_i * 1000 + TIMESTAMP_OFFSET
    when Time
      value.utc.to_i * 1000 + TIMESTAMP_OFFSET
    when String
      begin
        Date.parse(value).to_time.utc.to_i * 1000 + TIMESTAMP_OFFSET
      rescue ArgumentError
        parsing_errors.add(field_name, ::Schema::ParsingErrors::INVALID)
        nil
      end
    when Integer
      value
    when nil
      nil
    else
      parsing_errors.add(field_name, ::Schema::ParsingErrors::UNHANDLED_TYPE)
      nil
    end
  end
end

class GiftTransactionImporterContactFileComparer < ImporterContactFileComparer
  def record_as_json(record)
    hash = super(record)
    hash['designation'] = record.gift_designation&.designation
    hash
  end

  def get_records(identities)
    model_class
      .where(
        oid: org.id,
        contact_remote_id: identities.keys
      )
      .to_a
      .group_by do |model|
      identities[model.contact_remote_id].contact_id
    end
  end
end

GiftTransactionImporterContactFileComparer.new(
  org: AuthDB::Organization.find(SCRIPT_OPTIONS[:oid]),
  import_file: SCRIPT_OPTIONS[:file],
  schema_class: GiftTransactionSchema,
  model_class: GiftDB::GiftTransaction.includes(:gift_designation),
  remote_id_field_name: SCRIPT_OPTIONS[:remote_id_field_name],
  save_output_files: SCRIPT_OPTIONS[:save_output_files]
).compare_and_report

STATS.notify(true)
