#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:remote_id_field_name] = 'PersonID'

  opts.add_run_remote
  opts.add_oid_option
  opts.add_input_file

  opts.on('--remote-id REMOTE_ID', 'Remote ID column name') do |v|
    opts[:remote_id_field_name] = v
  end

  opts.on('--save-output-files') do
    opts[:save_output_files] = true
  end
end

require_relative '../../config/environment'

class EducationSchema
  include Schema::All
  include Schema::Normalize
  include CSVUtils::CSVRow
  include Comparable

  attr_accessor :et_contact_remote_id,
                :comparison_result

  attribute :school_name, :string_or_nil, aliases: ['EduSchoolName']
  attribute :year, :integer, aliases: ['EduGraduationYear']
  attribute :degree_key, :string_or_nil, aliases: ['EduDegree']
  attribute :fed_school_code, :string_or_nil
  attribute :reunion_year, :integer
  attribute :college, :string_or_nil
  attribute :department, :string_or_nil
  attribute :degree_level, :string_or_nil
  # not supported by importer
  # attribute :primary, :boolean, aliases: ['x_EduPrimary', 'EduPrimary']
  attribute :majors, :array, separator: ',', data_type: :string, fields: ['EduMajor1']

  normalize :majors, :sort

  FIELD_NAMES_FOR_COMPARISON = self.schema.keys.reject { |k| self.schema[k][:alias_of] || self.schema[k][:association] }

  csv_column(:et_contact_remote_id)
  FIELD_NAMES_FOR_COMPARISON.each do |field|
    csv_column(field)
  end
  csv_column(:comparison_result)

  def <=>(other)
    FIELD_NAMES_FOR_COMPARISON.each do |field_name|
      result = send(field_name) <=> other.send(field_name)
      return result unless result == 0
    end

    0
  end
end

class EducationImporterContactFileComparer < ImporterContactFileComparer
  def record_as_json(record)
    hash = record.as_json
    hash['majors'] = record.education_majors.map(&:major)
    hash
  end

  def from_row(remote_id, row)
    row['majors'] = schema_class.schema[:majors][:fields].map { |f| row[f].blank? ? nil : row[f] }
    row['majors'].compact!
    super(remote_id, row)
  end
end

EducationImporterContactFileComparer.new(
  org: AuthDB::Organization.find(SCRIPT_OPTIONS[:oid]),
  import_file: SCRIPT_OPTIONS[:file],
  schema_class: EducationSchema,
  model_class: ContactDB::Education.includes(:education_majors),
  remote_id_field_name: SCRIPT_OPTIONS[:remote_id_field_name],
  save_output_files: SCRIPT_OPTIONS[:save_output_files]
).compare_and_report

STATS.notify(true)
