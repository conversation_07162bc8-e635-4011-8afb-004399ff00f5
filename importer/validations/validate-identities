#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
  opts.add_oid_option
  opts.add_input_file

  opts.on('--save-output-files') do
    opts[:save_output_files] = true
  end
end

require_relative '../../config/environment'

class ContactAttributeSchema
  include Schema::All
  include Schema::Normalize
  include CSVUtils::CSVRow
  include Comparable

  attr_accessor :comparison_result,
                :different_fields

  attribute :remote_id, :string, aliases: ['PersonID']
  attribute :name_prefix, :string, aliases: ['PersonPrefix']
  attribute :name_first, :string, aliases: ['PersonFirstName']
  attribute :name_nick, :string, aliases: ['PersonNickname']
  attribute :name_middle, :string, aliases: ['PersonMiddleName']
  attribute :name_last, :string, aliases: ['Person<PERSON>astName']
  attribute :year, :integer, aliases: ['PersonGraduationYear']
  attribute :name_suffix, :string, aliases: ['PersonSuffix']
  attribute :name_maiden, :string, aliases: ['PersonPrevLastName']
  attribute :deceased, :boolean, aliases: ['PersonIsDeceased']
  attribute :gender, :string, aliases: ['PersonGender']
  attribute :ethnicity, :string, aliases: ['PersonEthnicity']
  attribute :mentor, :boolean
  attribute :summary_code, :string
  attribute :age, :integer
  attribute :age_range_lower, :integer
  attribute :age_range_upper, :integer
  attribute :birth_month, :integer
  attribute :birth_day, :integer
  attribute :birth_year, :integer
  attribute :marital_status, :string
  attribute :employment_status, :string
  attribute :image_url, :string

  FIELD_NAMES_FOR_COMPARISON = self.schema.keys.reject { |k| self.schema[k][:alias_of] || self.schema[k][:association] }

  FIELD_NAMES_FOR_COMPARISON.each do |field|
    csv_column(field)
  end
  csv_column(:comparison_result)
  csv_column(:different_fields) { different_fields&.join(', ')&.force_encoding('ASCII-8BIT') }

  def <=>(other)
    FIELD_NAMES_FOR_COMPARISON.each do |field_name|
      result = send(field_name) <=> other.send(field_name)
      (@different_fields ||= []) << "#{field_name}:#{other.send(field_name).inspect}" unless result == 0
    end

    @different_fields ? 1 : 0
  end
end

class IdentityImporterFileComparer < ImporterFileComparer
  def get_records(remote_ids)
    model_class
      .joins(contact: :identities)
      .where(
        identities: {
          oid: org.id,
          type: 1,
          value: remote_ids
        }
      )
      .index_by(&remote_id_field_name)
  end

  def record_as_json(record)
    hash = super(record)
    hash['remote_id'] = record.remote_id
    hash
  end
end

comparer = IdentityImporterFileComparer.new(
  org: AuthDB::Organization.find(SCRIPT_OPTIONS[:oid]),
  import_file: SCRIPT_OPTIONS[:file],
  schema_class: ContactAttributeSchema,
  model_class: ContactDB::ContactAttribute.includes(contact: :identities),
  remote_id_field_name: :remote_id,
  save_output_files: SCRIPT_OPTIONS[:save_output_files]
)
comparer.importer_file_type = 'identities'
comparer.compare_and_report

STATS.notify(true)
