#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
  opts.add_oid_option
  opts.add_input_file

  opts.on('--save-output-files') do
    opts[:save_output_files] = true
  end
end

require_relative '../../config/environment'

class ConstituencySchema
  include Schema::All
  include Schema::Normalize
  include CSVUtils::CSVRow
  include Comparable

  attr_accessor :et_contact_remote_id,
                :comparison_result

  attribute :status, :string_or_nil, aliases: ['Constituency', 'ConsCode']

  FIELD_NAMES_FOR_COMPARISON = self.schema.keys.reject { |k| self.schema[k][:alias_of] }

  csv_column(:et_contact_remote_id)
  FIELD_NAMES_FOR_COMPARISON.each do |field|
    csv_column(field)
  end
  csv_column(:comparison_result)

  def <=>(other)
    FIELD_NAMES_FOR_COMPARISON.each do |field_name|
      result = send(field_name) <=> other.send(field_name)
      return result unless result == 0
    end

    0
  end
end

ImporterContactFileComparer.new(
  org: AuthDB::Organization.find(SCRIPT_OPTIONS[:oid]),
  import_file: SCRIPT_OPTIONS[:file],
  schema_class: ConstituencySchema,
  model_class: ContactDB::Constituency,
  remote_id_field_name: 'PersonID',
  save_output_files: SCRIPT_OPTIONS[:save_output_files]
).compare_and_report

STATS.notify(true)
