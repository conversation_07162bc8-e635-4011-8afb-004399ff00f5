#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
  opts.add_oid_option
  opts.add_input_file

  opts.on('--save-output-files') do
    opts[:save_output_files] = true
  end
end

require_relative '../../config/environment'

class ImporterInteractionSchema
  include Schema::All
  include Schema::Normalize
  include CSVUtils::CSVRow
  include Comparable

  TIMESTAMP_OFFSET = 12 * 3600 * 1000

  attr_accessor :comparison_result,
                :different_field

  attribute :remote_id, :string, aliases: ['InteractionID']
  attribute :date_occurred, :timestamp, aliases: ['Date']
  attribute :text, :string, aliases: ['Body_truncated']
  attribute :summary, :string, aliases: ['Summary']
  attribute :interaction_type, :string, aliases: ['Type']
  attribute :author_name, :string, aliases: ['AuthorName']
  attribute :author_remote_user_id, :string, aliases: ['AuthorID']
  attribute :primary_contact_remote_id, :string, aliases: ['ConstituentID']

  FIELD_NAMES_FOR_COMPARISON = self.schema.keys.reject { |k| self.schema[k][:alias_of] || self.schema[k][:association] }

  FIELD_NAMES_FOR_COMPARISON.each do |field|
    csv_column(field)
  end
  csv_column(:comparison_result)
  csv_column(:different_field)

  def <=>(other)
    FIELD_NAMES_FOR_COMPARISON.each do |field_name|
      result = send(field_name) <=> other.send(field_name)
      unless result == 0
        @different_field = field_name
        return result
      end
    end

    0
  end

  def parse_timestamp(field_name, parsing_errors, value)
    case value
    when Date
      value.to_time.utc.to_i * 1000 + TIMESTAMP_OFFSET
    when Time
      value.utc.to_i * 1000 + TIMESTAMP_OFFSET
    when String
      begin
        Date.parse(value).to_time.utc.to_i * 1000 + TIMESTAMP_OFFSET
      rescue ArgumentError
        parsing_errors.add(field_name, ::Schema::ParsingErrors::INVALID)
        nil
      end
    when Integer
      value
    when nil
      nil
    else
      parsing_errors.add(field_name, ::Schema::ParsingErrors::UNHANDLED_TYPE)
      nil
    end
  end
end

class InteractionImporterFileComparer < ImporterFileComparer
  def record_as_json(record)
    hash = record.as_json
    hash['primary_contact_remote_id'] = record.primary_contact_remote_id
    hash
  end
end

InteractionImporterFileComparer.new(
  org: AuthDB::Organization.find(SCRIPT_OPTIONS[:oid]),
  import_file: SCRIPT_OPTIONS[:file],
  schema_class: ImporterInteractionSchema,
  model_class: UgcDB::Interaction.includes(:interaction_targets),
  remote_id_field_name: :remote_id,
  save_output_files: SCRIPT_OPTIONS[:save_output_files]
).compare_and_report

STATS.notify(true)
