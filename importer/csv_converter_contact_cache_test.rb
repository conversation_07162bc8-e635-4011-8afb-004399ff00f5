#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
end

require File.expand_path('../config/environment', __dir__)
require 'set'

def create_local_mysql_client
  Mysql2::Client.new(
    host: 'localhost',
    username: 'root',
    database: 'importer',
    flags: Mysql2::Client::MULTI_STATEMENTS
  )
end

OBJECT_NAMES = [
  'name_first',
  'addresses',
  'deceased',
  'gender',
  'sports',
  'name_suffix',
  'year',
  'name_maiden',
  'phones',
  'educations',
  'employments',
  'giving',
  'name_last',
  'name_middle',
  'emails',
  'name_prefix',
  'relationships',
  'giving_annual_donations',
  'name_nick',
  'is_deleted',
  'solicitation_codes',
  'constituencies',
  'pledge_list',
  'member_list',
  'ethnicity',
  'assignees',
  'exclusions',
  'summary_code',
  'extracurricular_activities',
  'awards_list',
  'giving_categories'
]

def create_random_cache_updates(oid, contact_id)
  time = (Time.now.to_f * 1000).to_i
  OBJECT_NAMES.map do |object_name|
    {
      oid: oid,
      contact_id: contact_id,
      object_name: object_name,
      hash: rand(10_000_000),
      created_at: time,
      updated_at: time
    }
  end
end


INSERT_CACHE_IN_BATCH_SQL = <<-SQL
INSERT INTO csv_converter_contact_cache
  (oid, contact_id, object_name, hash, created_at, updated_at)
  VALUES(%<oid>d, %<contact_id>d, '%<object_name>s', %<hash>d, %<created_at>d, %<updated_at>d)
  ON DUPLICATE KEY UPDATE hash = VALUES(hash), updated_at = VALUES(updated_at);
SQL

def insert_cache_in_batch(mysql_client, cache_updates)
  sql = cache_updates.map do |cache_update|
    sprintf(INSERT_CACHE_IN_BATCH_SQL, cache_update)
  end.join

  mysql_client.query(sql)
  mysql_client.abandon_results!
end

def insert_cache_in_batch_with_transaction(mysql_client, cache_updates)
  sql = cache_updates.map do |cache_update|
    sprintf(INSERT_CACHE_IN_BATCH_SQL, cache_update)
  end.join

  mysql_client.query("BEGIN; #{sql} COMMIT")
  mysql_client.abandon_results!
end

INSERT_CACHE_IN_BULK = <<-SQL
INSERT INTO csv_converter_contact_cache
  (oid, contact_id, object_name, hash, created_at, updated_at)
  VALUES %<values>s
  ON DUPLICATE KEY UPDATE hash = VALUES(hash), updated_at = VALUES(updated_at)
SQL

INSERT_CACHE_IN_BULK_VALUES = <<-SQL
(%<oid>d, %<contact_id>d, '%<object_name>s', %<hash>d, %<created_at>d, %<updated_at>d)
SQL

def insert_cache_in_bulk(mysql_client, cache_updates)
  values = cache_updates.map do |cache_update|
    sprintf(INSERT_CACHE_IN_BULK_VALUES, cache_update)
  end.join(',')

  sql = sprintf(INSERT_CACHE_IN_BULK, values: values)
  mysql_client.query(sql)
  mysql_client.abandon_results!
end

contact_ids = Set.new
while contact_ids.size < 100_000
  contact_ids << 10_000_000 + rand(1_000_000)
end

worker_thead = WorkerThreads.new(2) do |queue|
  mysql_client = create_local_mysql_client

  queue.each do |contact_id|
    insert_cache_in_bulk(
      mysql_client,
      create_random_cache_updates(SCRIPT_OPTIONS[:oid], contact_id)
    )
  end
end

contact_ids.each do |contact_id|
  worker_thead.enq(contact_id)
end

worker_thead.shutdown
