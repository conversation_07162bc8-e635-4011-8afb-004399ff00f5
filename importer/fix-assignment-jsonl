#!/usr/bin/env ruby

# This script modified import v3's jsonl files to update assignments action to create.
# When imported, this will force the importer to associate solicitor <> prospect 

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-f', '--file JSONL_FILE', 'The file to modify') do |v|
    opts[:file] = v
  end

  opts.on('--output-path PATH', 'Output path to write to, this is required because we cannot modify the filename without it failing to import') do |v|
    opts[:output_path] = v
  end

  opts.require_option(:file)
  opts.require_option(:output_path)
end

require_relative '../config/environment'

LOG.info("Processing #{SCRIPT_OPTIONS[:file]}")

def set_metadata_action(obj, action)
    obj["_metadata"]["action"] = action
end


output_file = "#{SCRIPT_OPTIONS[:output_path]}/#{File.basename(SCRIPT_OPTIONS[:file])}"
LOG.info("Writing to #{output_file}")

File.open(output_file, 'w') do |output|
    File.foreach(SCRIPT_OPTIONS[:file]) do |row|
        if row.strip.empty?
          next
        end

        pool = JSON.parse(row)

        if pool['assignments'].length > 0
            LOG.info("#{pool['pool_id']}/#{pool['pool_name']} updating pool and assignments" ) 
            set_metadata_action(pool, 'update')
            pool['assignments'].each do |assignment|
                set_metadata_action(assignment, 'create')
            end
        else
            LOG.info("#{pool['pool_id']}/#{pool['pool_name']} has no assignments")
        end
        output.write(pool.to_json + "\n")
    end
end


puts "#{output_file}"