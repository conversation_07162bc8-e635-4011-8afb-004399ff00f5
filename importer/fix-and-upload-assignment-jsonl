#/usr/bin/env bash

USAGE="./fix-andupload-assignment-jsonl [stage|prod] [oid] [job_id]"

ENV=$1
if [ -z $ENV ]; then
    echo "$USAGE" && exit 1
fi

OID=$2
if [ -z $OID ]; then
    echo "$USAGE" && exit 1
fi

JOB_ID=$3
if [ -z $JOB_ID ]; then
    echo "$USAGE" && exit 1
fi

BASE_PATH="tmp/fix-and-download-assignment-jsonl/$ENV/$JOB_ID"
LOCAL_ORIGINAL_PATH="$BASE_PATH/original"
LOCAL_FIX_PATH="$BASE_PATH/fixed"
S3_PATH="s3://import.evertrue.com/$ENV/$OID/job/$JOB_ID/"

mkdir -p "$LOCAL_ORIGINAL_PATH"
mkdir -p "$LOCAL_FIX_PATH"


./importer/download-job-files -e "$ENV" "$JOB_ID" --output "$LOCAL_ORIGINAL_PATH" || exit 1

JSONL_FILE=$(find "$LOCAL_ORIGINAL_PATH" -type f -name "*.jsonl")

./importer/fix-assignment-jsonl -e "$ENV" -f "$JSONL_FILE" --output-path "$LOCAL_FIX_PATH" || exit 1

aws s3 sync $LOCAL_FIX_PATH $S3_PATH