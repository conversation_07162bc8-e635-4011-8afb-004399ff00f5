#!/usr/bin/env ruby

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse!

require_relative '../config/environment'

job_1_id = ARGV.shift

raise "missing job 1 id" unless job_1_id

job_2_id = ARGV.shift

raise "missing job 2 id" unless job_2_id


job_1_headers = JSON.parse(ImporterDB::Job.find(job_1_id).header_columns).map{ |col_data| col_data["import_column"]}
raise "cannot find job with id #{job_1_id}" unless job_1_headers

job_2_headers = JSON.parse(ImporterDB::Job.find(job_1_id).header_columns).map{ |col_data| col_data["import_column"]}


def find_missing_headers(cols_a, cols_b)
    missing = []
    cols_a.each do |col|
        missing << col if !cols_b.include?(col)
    end
    missing
end

missing_from_1 = find_missing_headers(job_2_headers, job_1_headers)
missing_from_2 = find_missing_headers(job_1_headers, job_2_headers)

if missing_from_1.count == 0 && missing_from_2.count == 0
    puts "headers are identical!"
else
    puts "job-id=#{job_1_id} has columns that job-id=#{job_2_id} doesn't have:\n\t#{missing_from_2.join(",")}" if missing_from_2.count > 0
    puts "job-id=#{job_2_id} has columns that job-id=#{job_1_id} doesn't have:\n\t#{missing_from_1.join(",")}" if missing_from_1.count > 0
end