#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
end

require File.expand_path('../config/environment', __dir__)

class PresenceKSCSVRow < Struct.new(:data)
  include CSVUtils::CSVRow

  FIELDS = [
    'oid',
    'type',
    'sub_type',
    'remote_id',
    'job_id',
    'updated_at'
  ]

  FIELDS.each do |field|
    define_method field do
      data[field]
    end
  end

  FIELDS.each do |field|
    csv_column field
  end
  csv_column :updated_date

  def updated_date
    Time.at(updated_at / 1000).strftime('%Y-%m-%d')
  end
end

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

CSVUtils::CSVReport.new("#{org.slug}-#{org.id}-gift-presence.csv", PresenceKSCSVRow) do |report|
  PresenceKS.new(SCRIPT_OPTIONS[:oid]).each_presence_records('GIFT') do |presence|
    report << PresenceKSCSVRow.new(presence)
    STATS.inc_and_notify
  end
end
