#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:limit] = 200

  opts.add_oids_option

  opts.on('-t', '--types JOB_TYPES', 'Job types') do |v|
    opts[:types] = v.split(',').map(&:strip).reject(&:empty?)
  end

  opts.on('-l', '--limit NUM_JOBS', 'Number of jobs to display') do |v|
    opts[:limit] = v.to_i
  end

  opts.on('--csv', 'Output to a CSV file') do |v|
    opts[:csv] = true
  end
end

require File.expand_path('../config/environment', __dir__)

include CommandLineHelpers
include TimeHelpers
ImporterDB # loads the active record models for the importer

HEADINGS = {
  ID: :id,
  OID: :oid,
  FILE: :s3_filename,
  STARTED: :display_started_date,
  VERSION: :version,
  JOB_TYPE: :type,
  MAPPING_ID: :mapping_hash_id,
  TOTAL_RECORDS: :total_records,
  CREATED: :total_created,
  UPDATED: :total_updated,
  DELETED: :total_deleted,
  PRUNED: :total_pruned,
  RECORDS_TO_PRUNE: :total_to_be_pruned,
  IMPORTER_DURATION: :running_duration,
  PREPROCESS_DURATION: :preprocess_duration
}

class ImporterDB::Job
  include CSVUtils::CSVRow

  HEADINGS.values.each do |csv_column_name|
    csv_column csv_column_name
  end

  self.inheritance_column = nil

  def started_at
    Time.at(created_at / 1_000.0)
  end

  def display_started_date
    started_at.to_date.iso8601
  end

  def display_started_ago
    time_in_words(started_at)
  end

  def import_type_name
    TYPES.each do |name, job_types|
      return name if job_types.include?(type)
    end
    raise("no name for #{type}")
  end

  def csv_converter_import_stat
    unless defined?(@csv_converter_import_stat)
      @csv_converter_import_stat = csv_converter_stats.detect { |s| s.conversion_type == 'IMPORT' }
    end
    @csv_converter_import_stat
  end

  def csv_converter_transcode_stat
    unless defined?(@csv_converter_transcode_stat)
      @csv_converter_transcode_stat = csv_converter_stats.detect { |s| s.conversion_type == 'TRANSCODE' }
    end
    @csv_converter_transcode_stat
  end

  def csv_converter_file_check_stat
    unless defined?(@csv_converter_file_check_stat)
      @csv_converter_file_check_stat = csv_converter_stats.detect { |s| s.conversion_type == 'FILE_CHECK' }
    end
    @csv_converter_file_check_stat
  end

  def stat
    @stat ||= stats.last ? JSON.parse(stats.last.stat) : {}
  end

  def total_records
    if version == 3
      csv_converter_transcode_stat&.start_row_count
    else
      stat.dig('succeeded', 'inspected')
    end
  end

  def total_created
    if version == 3
      csv_converter_transcode_stat&.create_total
    else
      stat.dig('succeeded', 'created')
    end
  end

  def total_updated
    if version == 3
      csv_converter_transcode_stat&.update_total
    else
      stat.dig('succeeded', 'updated')
    end
  end

  def total_deleted
    if version == 3
      csv_converter_transcode_stat&.delete_total
    else
      stat.dig('succeeded', 'deleted')
    end
  end

  def total_pruned
    stat.dig('pruning', 'succeeded')
  end

  def total_to_be_pruned
    stat.dig('pruning', 'total_gifts_to_be_pruned')
  end

  def running_duration
    running_status = job_statuses.detect { |j| j.status == 'RUNNING' || j.status == 'IMPORT_RUNNING' }
    return nil unless running_status
    return nil unless running_status.ended_at

    (running_status.ended_at - running_status.started_at) / 1000
  end

  def preprocess_duration
    status = job_statuses.detect { |j| j.status == 'PREPROCESS_RUNNING' }
    return nil unless status
    return nil unless status.ended_at

    (status.ended_at - status.started_at) / 1000
  end
end

scope = ImporterDB::Job.where(oid: SCRIPT_OPTIONS[:oids])
scope = scope.where(type: SCRIPT_OPTIONS[:types]) if SCRIPT_OPTIONS[:types]
scope = scope.includes(:stats, :job_statuses, :csv_converter_stats).order('id DESC')
scope = scope.limit(SCRIPT_OPTIONS[:limit]) if SCRIPT_OPTIONS[:limit]
jobs = scope.all.to_a

if SCRIPT_OPTIONS[:csv]
  CSVUtils::CSVReport.new('job-stats.csv', ImporterDB::Job) do |report|
    jobs.each { |job| report << job }
  end
else
  table_view = TableView.new(HEADINGS, row_type: :object)
  table_view.render(Imports: jobs)
end
