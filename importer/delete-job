#!/usr/bin/env ruby

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_run_remote

  opts.on('-j', '--jobs JOB_ID(s)', 'Specify the job ids to delete comma separated') do |v|
    opts[:job_ids] = v.split(',').map(&:strip).map(&:to_i)
  end

  opts.require_option(:job_ids)
end

require_relative '../config/environment'

CHILD_TABLES = [
  :job_statuses,
  :stats,
  :csv_converter_stats,
  :csv_filecheck_results,
  :error_events,
  :events
]

jobs = ImporterDB::Job.where(oid: SCRIPT_OPTIONS[:oid]).where(id: SCRIPT_OPTIONS[:job_ids]).to_a

jobs.each do |job|
  unless job.safe_to_delete?
    $stderr.puts("Not safe to delete this job, its current status is #{job.current_status.status}")
    exit 1
  end
end

jobs.each do |job|
  # delete all relations to the job first
  CHILD_TABLES.each do |child_table|
    relation = job.send(child_table)
    count = relation.count
    next unless count > 0

    LOG.info "deleting #{count} records from #{child_table} for job #{job.id}/#{job.oid}"
    relation.delete
  end

  LOG.info "deleting job #{job.id}/#{job.oid}"
  job.delete
end
