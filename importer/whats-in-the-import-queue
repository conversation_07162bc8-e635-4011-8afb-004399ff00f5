#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)
include CommandLineHelpers
ImporterDB # loads the active record models for the importer

def ecs_client
  @ecs_client ||= ECSHelpers.create_client
end

def container_instance_arns
  @container_instance_arns ||= ecs_client.list_container_instances(cluster: ECS_CONFIG['ecs']['cluster']['importer_worker']).container_instance_arns
end

def container_instances
  @container_instances ||= ecs_client.describe_container_instances(cluster: ECS_CONFIG['ecs']['cluster']['importer_worker'], container_instances: container_instance_arns).container_instances
end

def list_tasks(container_instance)
  ecs_client.list_tasks(cluster: ECS_CONFIG['ecs']['cluster']['importer_worker'], container_instance: container_instance).task_arns
end

def running_tasks
  unless defined?(@running_tasks)
    @running_tasks = []
    container_instance_arns.each do |container_instance_arn|
      tasks = list_tasks(container_instance_arn)
      next if tasks.empty?

      ecs_client.describe_tasks(cluster: ECS_CONFIG['ecs']['cluster']['importer_worker'], tasks: tasks).tasks.each do |task|
        @running_tasks << task
      end
    end
  end
  @running_tasks
end

# adding methods and logic to the active record job class
class ImporterJob < ImporterDB::Job
  ELAPSED_TIME_MAX = 600 # 10 minutes

  MAX_JOBS_TO_RUN = {
    'Contact' => 6,
    'Gift' => 4,
    'UGC' => 5,
    'Assignment' => 1
  }

  attr_accessor :job_number_by_oid,
                :task_arn

  def id_display
    job_number_by_oid == 1 ? highlight_string(id.to_s) : id.to_s
  end

  def full_display
    prune? ? '*' : ''
  end

  def task_arn_display
    return '' unless job_number_by_oid == 1

    if task_arn
      return task_arn
    end

    return '' if job_status.include?('QUEUED')

    if job_status.include?('RUNNING') || elapsed_time > ELAPSED_TIME_MAX
      highlight_string('!!! JOB IS NOT RUNNING !!!')
    end
  end
end

running_import_jobs = running_tasks.select { |task| task.task_definition_arn.include?(':task-definition/importer-worker:') }.map do |task|
  command = task.overrides.container_overrides.first.command
  {
    task_arn: task.task_arn,
    oid: command[0].to_i,
    job_id: command[1].to_i
  }
end

jobs = ImporterJob.joins(:job_statuses).select('job.*, job_status.status as job_status, job_status.started_at started_at').where('job_status.status' => ImporterJob::JOB_STATUS_QUEUED + ImporterJob::JOB_STATUS_RUNNING).where('job_status.ended_at' => nil).all.to_a

jobs.each do |job|
  running_task = running_import_jobs.detect { |t| t[:job_id] == job.id }
  job.task_arn = running_task[:task_arn].split('/').last if running_task
end

running_jobs = jobs.select(&:running?).sort_by { |j| -j.updated_at }
queued_jobs = jobs.select(&:queued?).sort_by(&:updated_at)

number_of_jobs_by_oid = Hash.new(0)

running_jobs.each { |j| j.job_number_by_oid = (number_of_jobs_by_oid[j.oid] += 1) }
queued_jobs.each { |j| j.job_number_by_oid = (number_of_jobs_by_oid[j.oid] += 1) }

headings = {
  STATE: :job_status,
  ID: :id,
  OID: :oid,
  JOB_TYPE: :type,
  FULL: :full_display,
  VERSION: :version,
  JOB_NO: :job_number_by_oid,
  ELAPSED_TIME: :waitng_for_in_words,
  TASK_ARN: :task_arn_display
}

max_characters_by_heading = Hash.new(0)
headings.keys.each do |heading_name|
  max_characters_by_heading[heading_name] = heading_name.to_s.size
end

jobs.each do |job|
  headings.each do |heading_name, job_method|
    size = job.send(job_method).to_s.size
    max_characters_by_heading[heading_name] = size if max_characters_by_heading[heading_name] < size
  end
end

JOB_DISPLAY_STR_FORMAT = max_characters_by_heading.values.map { |size| "%-#{size}s" }.join(' | ') + "\n"

display_job_proc = Proc.new do |job|
  str = sprintf(
    JOB_DISPLAY_STR_FORMAT,
    *headings.values.map { |job_method| job_method == :id && job.job_number_by_oid == 1 ? highlight_string(job.send(job_method).to_s) : job.send(job_method).to_s }
  )
  print str
end

new_line_separate = false
ImporterJob::TYPES.each do |import_type_name, job_types|
  running_jobs_by_type = running_jobs.select { |job| job.import_type_name == import_type_name }
  queued_jobs_by_type = queued_jobs.select { |job| job.import_type_name == import_type_name }

  print("\n") if new_line_separate
  new_line_separate = true

  puts underline_string("#{import_type_name} running: #{running_jobs_by_type.size}, queued: #{queued_jobs_by_type.size}, max: #{ImporterJob::MAX_JOBS_TO_RUN[import_type_name]}")
  puts ''

  print underline_string(sprintf(JOB_DISPLAY_STR_FORMAT, *headings.keys.map(&:to_s)))

  running_jobs_by_type.each do |job|
    display_job_proc.call(job)
  end

  queued_jobs_by_type.each do |job|
    display_job_proc.call(job)
  end
end

