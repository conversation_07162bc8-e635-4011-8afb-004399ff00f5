#!/usr/bin/env ruby

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oids_option
  opts.add_run_remote
end

require_relative '../config/environment'

def dynamodb_client
  @dynamodb_client ||= ImporterDynamodbCacheHelpers.create_dynamodb_client
end

def each_item(base_query)
  res = dynamodb_client.query(base_query)
  res[:items].each do |item|
    yield item
  end

  while res[:last_evaluated_key]
    query = base_query.merge(exclusive_start_key: res[:last_evaluated_key])

    res = dynamodb_client.query(query)
    res[:items].each do |item|
      yield item
    end
  end
end

table_name = ImporterDynamodbCacheHelpers.get_table_name(:assignments)

VolunteerDB::Pool.where(oid: SCRIPT_OPTIONS[:oids]).each do |pool|
  LOG.info("clearing cache for #{pool.oid}/#{pool.name}/#{pool.id}")

  base_query = {
    table_name: table_name,
    limit: 100,
    expression_attribute_values: {
    ":pool_id" => pool.id
    }, 
    key_condition_expression: "pool_id = :pool_id",
    projection_expression: 'cache_id'
  }

  delete_requests = []
  delete_requests_proc = proc do
    STATS.update(:delete_requests)
    res = dynamodb_client.batch_write_item(
      request_items: {
        table_name => delete_requests
      }
    )
    delete_requests = []
  end

  each_item(base_query) do |item|
    STATS.inc_and_notify

    delete_requests << {
      delete_request: {
        key: {
          pool_id: pool.id,
          cache_id: item['cache_id']
        }
      }
    }

    delete_requests_proc.call if delete_requests.size >= 25
  end

  delete_requests_proc.call if delete_requests.size > 0
end

STATS.notify(true)
