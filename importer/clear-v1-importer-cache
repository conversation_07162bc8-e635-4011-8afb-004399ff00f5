#!/usr/bin/env ruby

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
  opts.add_oid_option
  opts.add_input_file

  opts.on('-t', '--type IMPORT_TYPE', ['NOTE', 'PROPOSAL'], 'Specify the import type to delete: NOTE, PROPOSAL') do |v|
    opts[:import_type] = v
  end

  opts.on('-r', '--remote-id REMOTE_ID_COLUMN', 'Remote ID column name') do |v|
    opts[:remote_id_column] = v
  end

  opts.require_option(:import_type)
end

require_relative '../config/environment'

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])
csv = CSVUtils::CSVIterator.new(SCRIPT_OPTIONS[:file], liberal_parsing: true)

SCRIPT_OPTIONS[:remote_id_column] ||= csv.headers.first
raise("unknown column #{SCRIPT_OPTIONS[:remote_id_column]}") unless csv.headers.include?(SCRIPT_OPTIONS[:remote_id_column])

def memcache_clients
  @memcache_clients ||= ImporterMemcacheHelpers.create_clients
end

def delete_key(cache_key)
  ImporterMemcacheHelpers.delete_key(memcache_clients, cache_key)
end

def get_remote_id(row)
  row[SCRIPT_OPTIONS[:remote_id_column]]
end

csv.each do |row|
  remote_id = get_remote_id(row)
  cache_key = ImporterMemcacheHelpers.get_checksum_cache_key(org.id, remote_id, SCRIPT_OPTIONS[:import_type])

  if delete_key(cache_key)
    LOG.info("deleted cache key for #{org.id}/#{remote_id}")
  else
    LOG.info("no cache key found for #{org.id}/#{remote_id}")
  end
end
