#!/usr/bin/env ruby

require File.expand_path('../config/environment', __dir__)

def find_all_note_mapping_hashes_with_binding(client, prop_name)
  sql = <<SQL
SELECT
  id, oid
FROM
  note_mapping_hash
WHERE
  id IN(SELECT DISTINCT hash_id FROM note_mapping_binding_property WHERE prop_name = "#{Mysql2::Client.escape(prop_name)}")
SQL

  client.query(sql)
end

def fetch_org_slug(client, oid)
  client.query("SELECT slug FROM organizations WHERE id = #{oid}").first['slug']
end

def fetch_mapping_hashed_last_used(client, type, hash_id)
  result = client.query("SELECT created_at FROM job WHERE type = '#{type}' AND mapping_hash_id = #{hash_id} ORDER BY created_at DESC LIMIT 1").first
  result ? Time.at(result['created_at'] / 1000.0).strftime('%Y-%m-%d') : 'never'
end

importer_mysql_client = MySQLHelpers.create_client(:importer)
auth_mysql_client = MySQLHelpers.create_client(:auth)
importer_api_client = ImporterClient.create_app_client

note_mapping_dir = TMP_DIR.join('archive', ENV['RAILS_ENV'], 'note_mappings')
FileUtils.mkdir_p(note_mapping_dir) unless File.exist?(note_mapping_dir)

org_slugs = {}
org_slug_proc = Proc.new do |oid|
  (org_slugs[oid] ||= fetch_org_slug(auth_mysql_client, oid))
end

find_all_note_mapping_hashes_with_binding(importer_mysql_client, 'label.value').to_a.each do |mapping_hash|
  mapping_payload = importer_api_client.get_mapping(mapping_hash['oid'], 'note', mapping_hash['id'])
  org_slug = org_slug_proc.call(mapping_hash['oid'])
  last_used = fetch_mapping_hashed_last_used(importer_mysql_client, 'NOTES_CSV', mapping_hash['id'])

  archive_file = note_mapping_dir.join("#{last_used}-#{org_slug}-#{mapping_hash['oid']}-note-mapping-hash-#{mapping_hash['id']}.json")
  File.open(archive_file, 'wb') { |f| f.write JSON.pretty_generate(mapping_payload) }
end
