#!/usr/bin/env ruby

# Issue: during the ID migration the presence table was cleared and the next full import didn't contain the remote id
# Purpose: to ensure the presence table contains all the remote_ids from contact.identity

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
  opts.add_run_remote
end

require File.expand_path('../config/environment', __dir__)
require 'active_support/core_ext/array'
require 'active_record'
ActiveRecord::Base.logger = LOG

contact_remote_ids = ContactDB::Identity.select(:value).where(type: 1).joins(:contact).where(contact: {oid: SCRIPT_OPTIONS[:oid]}).to_a.map(&:value)
presence_remote_ids = ImporterDB::Presence.select(:remote_id).where(oid: SCRIPT_OPTIONS[:oid]).to_a.map(&:remote_id)

missing_remote_ids = contact_remote_ids - presence_remote_ids
extra_remote_ids = presence_remote_ids - contact_remote_ids

LOG.info "missing: #{missing_remote_ids.join(', ')}"
LOG.info "extra: #{extra_remote_ids.join(', ')}"

# TODO: improve this with a batch insert statement
timestamp = (Time.now.to_f * 1000).to_i
missing_remote_ids.in_groups_of(10_000, false).each do |remote_ids|
  ImporterDB::Presence.insert_all(
    remote_ids.map do |remote_id|
      {
        oid: SCRIPT_OPTIONS[:oid],
        remote_id: remote_id,
        last_job_id: 1,
        created_at: timestamp,
        updated_at: timestamp
      }
    end
  )
end

extra_remote_ids.in_groups_of(10_000, false).each do |remote_ids|
  ImporterDB::Presence.where(
    oid: SCRIPT_OPTIONS[:oid],
    remote_id: remote_ids
  ).delete_all
end
