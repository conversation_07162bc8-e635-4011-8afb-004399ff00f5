#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
  opts.add_oid_option(false)
end

require File.expand_path('../config/environment', __dir__)

def find_latest_contact_import(org)
  ImporterDB::Job.where(oid: org.id, type: ['CSV', 'CONTACT', 'CONTACT_IDENTITY', 'CONTACT_IDENTITY_FULL']).order('id DESC').first
end

def dna_client
  @dna_client ||= DNAClient.create_client_with_app_creds
end

def importer_s3_client
  @importer_s3_client ||= ImporterS3Client.new
end

orgs = AuthDB::Organization.where(deleted: false)
orgs = orgs.where(id: SCRIPT_OPTIONS[:oid]) if SCRIPT_OPTIONS[:oid]
orgs = orgs.to_a

NINETY_DAYS_AGO = (Time.now - (89 * 86_400)).to_i * 1000

settings = dna_client.get_bulk_setting_value(DNAClient::KEEP_LEADING_ZEROS)
oids_without_keeping_leading_zeros = settings['setting_values'].reject { |setting| setting['value'] }.map { |setting| setting['oid'] }

orgs.select! { |org| oids_without_keeping_leading_zeros.include?(org.id) }

# only check orgs that have done an import
oids_with_jobs = ImporterDB::Job.select('DISTINCT oid').where(oid: orgs.map(&:id)).map(&:oid)
orgs.select! { |org| oids_with_jobs.include?(org.id) }

LEADING_ZERO_REGEX = /^0\d+/

path = TMP_DIR.join('reports', 'contacts', "remote-ids-report-#{Time.now.utc.strftime('%Y-%m-%d')}.csv").to_s
FileUtils.mkdir_p(File.dirname(path))

out = CSV.open(path, 'wb')

out << ['oid', 'name', 'found_leading_zeros', 'job_id', 'file', 'remote_id', 'error']

orgs.each do |org|
  unless (job = find_latest_contact_import(org))
    LOG.warn("org #{org.id}: no contact job found")
    out << [org.id, org.name, nil, nil, nil, nil, 'no contact import found']
    next
  end

  s3_filename_url = job.s3_filename_url

  unless job.created_at > NINETY_DAYS_AGO
    LOG.info("org #{org.id}: skipping zero ID check, last job is too old, job #{job.id}, was created on #{job.created_at_to_time.strftime('%Y-%m-%d')}")
    out << [org.id, org.name, nil, job.id, File.basename(s3_filename_url), nil, 'latest job is too old to check']
    next
  end

  remote_id_col_name = job.get_contact_remote_id_header_name

  LOG.info("org #{org.id}: latest contact job #{job.id} version #{job.version}, was created on #{job.created_at_to_time.strftime('%Y-%m-%d')} searching #{s3_filename_url} column #{remote_id_col_name}")

  found_remote_id_with_zeros = nil
  begin
    io = S3Grep::Search.new(s3_filename_url, importer_s3_client.create_s3_client, S3Grep::Search.detect_compression(s3_filename_url)).to_io
    io.binmode if io.respond_to?(:binmode)
    csv = CSV.new(io, liberal_parsing: true)
    unless (headers = csv.shift)
      LOG.error("failed to get headers from #{s3_filename_url}")
      out << [org.id, org.name, nil, job.id, File.basename(s3_filename_url), nil, 'no headers found in S3 file']
      next
    end

    remote_id_idx = 0
    headers.each_with_index do |header, idx|
      next unless header == remote_id_col_name

      remote_id_idx = idx
      break
    end

    while (row = csv.shift)
      remote_id = row[remote_id_idx]
      if LEADING_ZERO_REGEX.match?(remote_id)
        LOG.info("row had a matching column with leading zeros, #{row}")
        found_remote_id_with_zeros = remote_id
        break
      end
    end
  rescue Aws::S3::Errors::NoSuchKey => e
    LOG.error("failed to fetch S3 file #{s3_filename_url}")
    out << [org.id, org.name, nil, job.id, File.basename(s3_filename_url), nil, 'failed to fetch S3 file']
    next
  rescue CSV::MalformedCSVError => e
    LOG.error("CSV parsing error in S3 file #{s3_filename_url}, error #{e}")
    out << [org.id, org.name, nil, job.id, File.basename(s3_filename_url), nil, 'failed to parse S3 file']
    next
  end

  out << [org.id, org.name, found_remote_id_with_zeros ? 'Y' : 'N', job.id, File.basename(s3_filename_url), found_remote_id_with_zeros, nil]
end
out.close

RepairshopFileUploader.upload_file_and_notify(path)
