#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:version] = 3
  opts[:limit] = 40

  opts.add_oid_option

  opts.on('--version VERSION', Integer, 'Job versions to list') do |v|
    opts[:version] = v
  end

  opts.on('-l', '--limit LIMIT', Integer, 'Number of jobs to list') do |v|
    opts[:limit] = v
  end
end

require File.expand_path('../config/environment', __dir__)

def org
  @org ||= AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])
end

jobs = ImporterDB::Job.includes(:job_statuses).where(oid: org.id, version: SCRIPT_OPTIONS[:version]).order('id DESC').limit(SCRIPT_OPTIONS[:limit]).to_a

headings = {
  'job_id' => :id,
  's3_filename' => :s3_filename,
  'version' => :version,
  'type' => :type,
  'created' => :created_at_in_words,
  'status' => :current_status_name,
  'safe_to_delete' => :safe_to_delete?
}
table = TableView.new(headings, row_type: :object)
table.render('Jobs' => jobs)
