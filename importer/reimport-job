#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-j', '--job JOB_ID', 'Job to reimport') do |v|
    opts[:job] = v.to_i
  end

  opts.on('--invalidate-cache', 'Update the job mapping updated_at to force all records to be re-imported') do |v|
    opts[:invalidate_cache] = true
  end

  opts.require_option :job
end

require File.expand_path('../config/environment', __dir__)

def touch_job_mapping_hash_updated_at(client, job)
  table_name =
    case job['type']
    when 'CSV'
      'mapping_hash'
    when 'TRANSACTIONAL_CSV'
      'gift_mapping_hash'
    when 'NOTES_CSV'
      'note_mapping_hash'
    when 'PROPOSAL_CSV'
      'proposal_mapping_hash'
    when 'ASSIGNMENT_CSV',
         'ASSIGNMENT_TEAM_CSV'
      'assignment_mapping_hash'
    else
      raise("unhandled job type #{job['type']}")
    end

  client.query("UPDATE #{table_name} SET updated_at = #{(Time.now.to_f * 1000).to_i} WHERE id = #{job['mapping_hash_id']}")
end

importer_mysql_client = MySQLHelpers.create_client(:importer)
auth_mysql_client = MySQLHelpers.create_client(:auth)
importer_s3_client = ImporterS3Client.new
importer_api_client = ImporterClient.create_app_client

job = importer_mysql_client.query("SELECT * FROM job WHERE oid = #{SCRIPT_OPTIONS[:oid]} AND id = #{SCRIPT_OPTIONS[:job]}").first
raise("job #{SCRIPT_OPTIONS[:job]} not found") unless job

touch_job_mapping_hash_updated_at(importer_mysql_client, job) if SCRIPT_OPTIONS[:invalidate_cache]

org_slug = auth_mysql_client.query("SELECT slug FROM organizations WHERE id = #{SCRIPT_OPTIONS[:oid]}").first['slug']

s3_filename = importer_s3_client.create_copy(org_slug, job['s3_filename'])

payload = job.slice('type', 'source', 'compression', 'mapping_hash_id', 'prune')
payload['notify'] = 0
payload['s3_filename'] = s3_filename

new_job = importer_api_client.create_job(SCRIPT_OPTIONS[:oid], payload)
puts new_job.inspect

importer_api_client.queue_job(SCRIPT_OPTIONS[:oid], new_job['id'])
