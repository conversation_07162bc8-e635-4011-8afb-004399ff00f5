#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)
require 'shellwords'

RECONCILER_REDIS_HOST = REDIS_CONFIG['reconciler']

def run_cmd(cmd)
  LOG.info cmd
  `#{cmd}`
end

def find_import_job_stats(job_id)
  run_cmd("redis-cli -h #{RECONCILER_REDIS_HOST} --scan --pattern '*-#{job_id}*'").split("\n")
end

def get_redis_type(key)
  run_cmd("redis-cli -h #{RECONCILER_REDIS_HOST} TYPE #{Shellwords.escape(key)}").strip
end

def log_redis_data(key)
  redis_cmd =
    case get_redis_type(key)
    when 'set'
      'SMEMBERS'
    when 'hash'
      'HGETALL'
    end

  run_cmd("redis-cli -h #{RECONCILER_REDIS_HOST} #{redis_cmd} #{Shellwords.escape(key)} > #{Shellwords.escape(key)}.log")
end

job_id = ARGV[0]
raise('no job id specified') unless job_id

find_import_job_stats(job_id).each do |key|
  log_redis_data(key)
end
