#!/usr/bin/env ruby

require 'optparse'

options = {
  oid: nil
}
OptionParser.new do |opts|
  opts.banner = 'Usage: ' + File.basename(__FILE__)

  opts.on('-h', '--help', 'Prints this help') do
    puts opts
    exit
  end

  opts.on('-o', '--oid OID', 'Required organization id') do |v|
    options[:oid] = v.to_i
  end
end.parse!

raise('no oid specified') unless options[:oid]

require File.expand_path('../config/environment', __dir__)

def find_missing_remote_ids(client, oid, remote_ids)
  escaped_remote_ids =  remote_ids.map { |id| '"' + Mysql2::Client.escape(id) + '"' }.join(',')
  existing_ids = client.query("SELECT value FROM identity WHERE oid = #{oid} AND type = 1 AND value IN(#{escaped_remote_ids})").to_a.map { |result| result['value'] }
  remote_ids - existing_ids
end

importer_mysql_client = MySQLHelpers.create_client(:importer)
contacts_mysql_client = MySQLHelpers.create_client(:contacts)

batch_presence = {}
missing_remote_ids = []
importer_mysql_client.query("SELECT * FROM presence WHERE oid = #{options[:oid]}").each do |presence|
  batch_presence[presence['remote_id']] = presence

  if batch_presence.size >= 10_000
    started_at = Time.now
    missing_remote_ids += find_missing_remote_ids(contacts_mysql_client, options[:oid], batch_presence.keys)
    LOG.info "took #{Time.now - started_at} to fetch remote ids"
    batch_presence = {}
  end
end

puts missing_remote_ids
