#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on("--no-prompt") do 
    opts[:no_prompt] = true
  end

  opts.on("--jobs JOBS", "List of jobs to cancel comma delimited") do |v|
    opts[:jobs] = v.split(',').map(&:strip)
  end
end

require File.expand_path('../config/environment', __dir__)
include CommandLineHelpers

JOB_COMPLETE_STATUSES = [
  'CANCELLED',
  'FAILED',
  'OK',
  'WARN'
]
jobs = SCRIPT_OPTIONS[:jobs] || []
if jobs.empty?
  jobs << ARGV[0].to_i
  raise('missing job_id') unless jobs.size > 0
end

mysql_client = MySQLHelpers.create_client(:importer)

jobs.each do |job_id|
  job = mysql_client.query("SELECT * FROM job WHERE id = #{job_id}").first

  raise("no job #{job_id} found in the importer") unless job

  last_job_status = mysql_client.query("SELECT * FROM job_status WHERE job_id = #{job_id} ORDER BY id DESC LIMIT 1").first
  raise("no job status found for #{job_id}, are you sure this job ran?") unless last_job_status
  raise("something went wrong last_job_status ended_at is already set") if last_job_status['ended_at']

  if JOB_COMPLETE_STATUSES.include?(last_job_status['status'])
    puts "Job #{job['oid']}/#{job_id} is already considered complete, current status is #{bold_string(last_job_status['status'])}"
    next
  end

  prompt = "Job #{job['oid']}/#{job_id} status is currently #{bold_string(last_job_status['status'])}, do you want to change it to #{bold_string('CANCELLED')}?"

  if SCRIPT_OPTIONS[:no_prompt] || yes_no(prompt) == :yes
    puts "Cancelling #{job['oid']}/#{job_id}"
    ended_at = (Time.now.to_f * 1000.0).to_i
    mysql_client.query("UPDATE job_status SET ended_at = #{ended_at} WHERE id = #{last_job_status['id']}")
    mysql_client.query("INSERT INTO job_status (job_id, status, started_at) VALUES(#{job_id}, \"CANCELLED\", #{ended_at})")
  end
end
