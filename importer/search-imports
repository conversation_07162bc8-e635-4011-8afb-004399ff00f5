#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:case_insensitive] = false
  opts[:job_type] = 'CSV'
  opts[:start_time] = Time.now - (7 * 86_400)

  opts.add_oid_option

  opts.on('-j', '--job JOB_ID', Integer, 'Job ID') do |v|
    opts[:job_id] = v
  end

  opts.on('-s', '--search PATTERN', 'Search pattern to find') do |v|
    opts[:search_pattern] = v
  end

  opts.on('-i', '--ignore-case', 'Make the search pattern case insensitive') do |v|
    opts[:case_insensitive] = true
  end

  # available job types can be found here https://github.com/evertrue/importer/blob/master/ImporterCore/src/main/java/com/et/importer/model/job/Type.java
  opts.on('-t', '--job-type TYPE', 'Specify a job type to search through') do |v|
    opts[:job_type] = v
  end

  opts.on('--start DAYS_AGO', Integer, 'Number of days of import files to search through') do |v|
    opts[:start_time] = Time.now - (v * 86_400)
  end

  opts.on('--file-pattern FILE_PATTERN', 'Specify a file pattern') do |v|
    opts[:file_pattern] = v
  end

  opts.require_option(:search_pattern)
end

require File.expand_path('../config/environment', __dir__)

regex_options = 0
if SCRIPT_OPTIONS[:case_insensitive]
  regex_options |= Regexp::IGNORECASE
end

regex = Regexp.new(SCRIPT_OPTIONS[:search_pattern], regex_options)

scope = ImporterDB::Job.where(oid: SCRIPT_OPTIONS[:oid])
scope =
  if SCRIPT_OPTIONS[:job_id]
    scope.where(id: SCRIPT_OPTIONS[:job_id])
  else
    scope.where(type: SCRIPT_OPTIONS[:job_type]).where('created_at > ?', (SCRIPT_OPTIONS[:start_time].to_i * 1000))
  end

importer_s3_client = ImporterS3Client.new

scope.order('created_at DESC').each do |job|
  begin
    s3_filename_url = job.s3_filename_url
    next if SCRIPT_OPTIONS[:file_pattern] && !s3_filename_url.include?(SCRIPT_OPTIONS[:file_pattern])

    LOG.info("searching #{s3_filename_url}")
    S3Grep::Search.search(s3_filename_url, importer_s3_client.create_s3_client, regex) do |line_number, line|
      puts "#{job.id}:#{s3_filename_url}:#{line_number} #{line}"
    end
  rescue Aws::S3::Errors::NoSuchKey => e
    puts "failed to fetch S3 file"
  end
end
