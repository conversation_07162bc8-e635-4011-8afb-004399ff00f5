#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)
include CommandLineHelpers
include TimeHelpers

def ecs_client
  @ecs_client ||= ECSHelpers.create_client
end

def container_instance_arns
  @container_instance_arns ||= ecs_client.list_container_instances(cluster: ECS_CONFIG['ecs']['cluster']['importer_worker']).container_instance_arns
end

def container_instances
  @container_instances ||= ecs_client.describe_container_instances(cluster: ECS_CONFIG['ecs']['cluster']['importer_worker'], container_instances: container_instance_arns).container_instances
end

def list_tasks(container_instance)
  ecs_client.list_tasks(cluster: ECS_CONFIG['ecs']['cluster']['importer_worker'], container_instance: container_instance).task_arns
end

def running_tasks
  unless defined?(@running_tasks)
    @running_tasks = []
    container_instance_arns.each do |container_instance_arn|
      tasks = list_tasks(container_instance_arn)
      next if tasks.empty?

      ecs_client.describe_tasks(cluster: ECS_CONFIG['ecs']['cluster']['importer_worker'], tasks: tasks).tasks.each do |task|
        @running_tasks << task
      end
    end
  end
  @running_tasks
end

# running_tasks.each do |task|
#   print JSON.pretty_generate(task.to_h) + "\n"
# end

running_import_jobs = running_tasks.select { |task| task.task_definition_arn.include?(':task-definition/importer-worker:') }.map do |task|
  command = task.overrides.container_overrides.first.command
  {
    task_arn: task.task_arn,
    oid: command[0].to_i,
    job_id: command[1].to_i
  }
end

jobs = ImporterDB::Job.joins(:job_statuses).select('job.*, job_status.status as job_status, job_status.started_at started_at').where(id: running_import_jobs.map { |j| j[:job_id] } ).where('job_status.ended_at' => nil).all.index_by(&:id)

get_job_proc = Proc.new do |task_arn|
  running_import_job = running_import_jobs.detect { |j| j[:task_arn] == task_arn }
  running_import_job && jobs[running_import_job[:job_id]]
end

headings = {
  'Task ID' => :task_id,
  'Task Definition' => :task_definition,
  'Created' => :created,
  'Command' => :command,
  'Job ID' => :job_id,
  'OID' => :oid,
  'Job Version' => :version,
  'Job Type' => :job_type
}

data = {}
container_instances.each do |container_instance|
  data["EC2 Instance: #{container_instance.ec2_instance_id}"] =
    begin
      running_tasks
        .select { |t| t.container_instance_arn == container_instance.container_instance_arn }
        .map do |t|
        job = get_job_proc.call(t.task_arn)

        {
          task_id: t.task_arn.split('/').last,
          task_definition: t.task_definition_arn.split('/').last,
          command: t.overrides.container_overrides.first.command,
          created: time_in_words(t.created_at),
          job_id: job&.id,
          oid: job&.oid,
          version: job&.version,
          job_type: job&.type
        }
      end
    end
end

TableView.new(headings).render(data)
