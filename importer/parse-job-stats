#!/usr/bin/env ruby

file_path = ARGV.shift 

raise 'missing file path' unless file_path

require_relative '../config/environment'

def is_span(line)
    line =~ /span=.*/
end

def parse_span(line)
    if match = line.match(/span=(.*), duration\(ms\)=(\d+),/)
        return match[1], match[2].to_i
    end
    nil
end

# ex. {span-name: {sum, count, max}}
span_data = {}

File.readlines(file_path).each do |line|
    if is_span line
        span_name, span_duration = parse_span(line)

        span_data[span_name] ||= {}
        span_data[span_name][:count] = span_data[span_name].fetch(:count, 0) + 1
        span_data[span_name][:sum] = span_data[span_name].fetch(:sum, 0) + span_duration
        span_data[span_name][:max] = [span_data[span_name].fetch(:max, 0), span_duration].max
    end
end

span_data.each do |span_name, data|
    puts "#{span_name}:"
    puts "\tavg: #{data[:sum]/data[:count]}"
    puts "\tmax: #{data[:max]}"
end
