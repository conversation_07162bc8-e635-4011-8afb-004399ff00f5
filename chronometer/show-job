#!/usr/bin/env ruby

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-j', '--job KEY', 'Job to display') do |v|
    opts[:job_key] = v
  end

  opts.require_option(:job_key)
end

require_relative '../config/environment'

def chronometer_client
  @chronometer_client ||= ChronometerClient.create_client_with_app_creds
end

res = chronometer_client.get_organization_jobs(SCRIPT_OPTIONS[:oid])

jobs = JSON.parse(res.body)
job = jobs.detect { |j| j.dig('job', 'key', 'name') == SCRIPT_OPTIONS[:job_key] }
print JSON.pretty_generate(job) + "\n"
