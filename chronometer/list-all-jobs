#!/usr/bin/env ruby

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse!

require_relative '../config/environment'

def chronometer_client
  @chronometer_client ||= ChronometerClient.create_client_with_app_creds
end

headings = {
  'Job ID' => :job_key,
  'OID' => :oid,
  'User ID' => :user_id,
  'Description' => :description,
  'Class' => :class
}

res = chronometer_client.get_all_jobs(1)

jobs = JSON.parse(res.body).map do |job|
  {
    job_key: job.dig('job', 'key', 'name'),
    oid: job.dig('job', 'jobDataMap', 'oid'),
    user_id: job.dig('job', 'jobDataMap', 'userId'),
    description: job.dig('job', 'description'),
    class: job.dig('job', 'jobClass')
  }
end

SORT_FIELDS = [:oid, :user_id, :description, :job_key]

jobs.sort! do |a, b|
  field = SORT_FIELDS.detect { |f| a[f] != b[f] } || :job_key
  a[field] <=> b[field]
end

TableView.new(headings).render('Jobs' => jobs)
