#!/usr/bin/env ruby

# Purpose: to remove all jobs related to orgs that no longer exists

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse!

require_relative '../config/environment'

def chronometer_client
  @chronometer_client ||= ChronometerClient.create_client_with_app_creds
end

headings = {
  'Job ID' => :job_key,
  'OID' => :oid,
  'User ID' => :user_id,
  'Description' => :description,
  'Class' => :class
}

res = chronometer_client.get_all_jobs(1)

jobs = {}

JSON.parse(res.body).map do |job|
  oid = job.dig('job', 'jobDataMap', 'oid')
  (jobs[oid] ||= []) << job.dig('job', 'key', 'name')
end

AuthDB::Organization.where(id: jobs.keys).to_a.each do |org|
  jobs.delete(org.id)
end

puts jobs.keys.join(', ')

jobs.each do |oid, job_keys|
  job_keys.each do |job_key|
    puts "deleting job #{job_key} for oid #{oid}"
    chronometer_client.delete_job(oid, job_key)
  end
end
