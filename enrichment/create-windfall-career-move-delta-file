#!/usr/bin/env ruby

# Purpose: to create an Windfall enrichment career moves delta file by comparing the current file to the previous file

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
  opts.add_oids_option(false)
end

require_relative '../config/environment'

class WindfallCareerSchema
  include Schema::All
  include Schema::ArrayHeaders
  include Schema::Arrays
  include Comparable

  attribute :action, :string
  attribute :changed_columns, :string
  attribute :contact_id, :integer, alias: 'Secondary ID'
  attribute :career_match_confidence, :float, alias: 'Career Match Confidence'
  attribute :career_data_last_updated_date, :string, alias: 'Career Data Last Updated Date'
  attribute :linkedin_url, :string, alias: 'LinkedIn URL'
  attribute :job_title, :string, alias: 'Job Title'
  attribute :job_level, :string, alias: 'Job Level'
  attribute :job_function, :string, alias: 'Job Function'
  attribute :job_start_date, :string, alias: 'Job Start Date'
  attribute :recently_changed_jobs, :string, alias: 'Recently Changed Jobs'
  attribute :recently_promoted, :string, alias: 'Recently Promoted'
  attribute :recently_retired, :string, alias: 'Recently Retired'
  attribute :recently_changed_companies, :string, alias: 'Recently Changed Companies'
  attribute :company_name, :string, alias: 'Company Name'
  attribute :company_domain, :string, alias: 'Company Domain'
  attribute :company_phone_number, :string, alias: 'Company Phone Number'
  attribute :company_address_line_one, :string, alias: 'Company Address Line One'
  attribute :company_address_line_two, :string, alias: 'Company Address Line Two'
  attribute :company_city, :string, alias: 'Company City'
  attribute :company_state, :string, alias: 'Company State'
  attribute :company_zip, :string, alias: 'Company Zip'
  attribute :company_naics_code, :string, alias: 'Company NAICS Code'
  attribute :company_revenue_range, :string, alias: 'Company Revenue Range'
  attribute :company_category, :string, alias: 'Company Category'
  attribute :company_size_range, :string, alias: 'Company Size Range'

  validates :contact_id, presence: true
  validates :career_match_confidence, presence: true

  def <=>(other)
    @changed_columns = []
    return_value = nil

    self.class.schema.each do |_, field_options|
      next if field_options[:alias_of]
      next if field_options[:name] == :action
      next if field_options[:name] == :changed_columns
      next if field_options[:name] == :career_data_last_updated_date

      value = public_send(field_options[:getter])
      other_value = other.public_send(field_options[:getter])
      next if value.nil? && other_value.nil?

      if value.nil?
        return_value ||= -1
        @changed_columns << field_options[:alias]
        next
      end

      if other_value.nil?
        return_value ||= 1
        @changed_columns << field_options[:alias]
        next
      end

      result = value <=> other_value
      if result != 0
        return_value ||= result
        @changed_columns << field_options[:alias]
      end
    end

    @changed_columns = @changed_columns&.join('|')
    return_value || 0
  end

  def career_data_last_updated_date_match?(other)
    career_data_last_updated_date == other.career_data_last_updated_date
  end

  def career_match_confidence_modified
    val = (career_match_confidence * 100).to_i
    val == 100 ? 93 : val
  end
end

class WindfallCareerCSVRow < Struct.new(
        :career_move,
        :contact
      )

  include CSVUtils::CSVRow

  csv_column('Remote ID') { contact.remote_id }
  csv_column('Full Name') { contact.name }
  csv_column('Client_NamePrefix') { contact&.contact_attribute&.name_prefix }
  csv_column("Client_NameFirst") { contact&.contact_attribute&.name_first }
  csv_column("Client_NameNick") { contact&.contact_attribute&.name_nick }
  csv_column("Client_NameMiddle") { contact&.contact_attribute&.name_middle }
  csv_column("Client_NameLast") { contact&.contact_attribute&.name_last }
  csv_column("Client_NameSuffix") { contact&.contact_attribute&.name_suffix }
  csv_column("Client_NameMaiden") { contact&.contact_attribute&.name_maiden }
  csv_column("Client_Deceased") { deceased }
  csv_column("Client_Email") { contact.primary_email }
  csv_column('Client_Constituency') { constituency }
  csv_column('Client_Grad_Year') { contact&.contact_attribute&.year }
  csv_column('Career Match Confidence') { career_move.career_match_confidence_modified }
  csv_column('Career Data Last Updated Date') { career_move.career_data_last_updated_date }
  csv_column('LinkedIn URL') { linkedin_url }
  csv_column('Job Title') { career_move.job_title }
  csv_column('Job Level') { career_move.job_level }
  csv_column('Job Function') { career_move.job_function }
  csv_column('Job Start Date') { career_move.job_start_date }
  csv_column('Recently Changed Jobs') { career_move.recently_changed_jobs }
  csv_column('Recently Promoted') { career_move.recently_promoted }
  csv_column('Recently Retired') { career_move.recently_retired }
  csv_column('Recently Changed Companies') { career_move.recently_changed_companies }
  csv_column('Company Name') { career_move.company_name }
  csv_column('Company Domain') { career_move.company_domain }
  csv_column('Company Phone Number') { career_move.company_phone_number }
  csv_column('Company Address Line One') { career_move.company_address_line_one }
  csv_column('Company Address Line Two') { career_move.company_address_line_two }
  csv_column('Company City') { career_move.company_city }
  csv_column('Company State') { career_move.company_state }
  csv_column('Company Zip') { career_move.company_zip }
  csv_column('Company NAICS Code') { career_move.company_naics_code }
  csv_column('Company Revenue Range') { career_move.company_revenue_range }
  csv_column('Company Category') { career_move.company_category }
  csv_column('Company Size Range') { career_move.company_size_range }
  csv_column('Change Status') { career_move.action }
  csv_column('Changed Columns') { career_move.changed_columns }

  def linkedin_url
    if career_move.linkedin_url
      'https://' + career_move.linkedin_url
    end
  end

  def deceased
    contact&.contact_attribute&.deceased? ? 'Y' : 'N'
  end

  def constituency
    contact.constituencies.first&.status
  end
end

def s3_client
  @s3_client ||= Aws::S3::Client.new()
end

def create_windfall_delta_file(org)
  LOG.info("diffing career_moves files for org #{org.id}")

  current_carrer_moves = {}
  previous_carrer_moves = {}

  unless (latest_windfall_enrichment_file = Windfall.get_latest_windfall_enriched_s3_file_path(org.id))
    LOG.warn("failed to find Windfall enrichment file, for org #{org.id}")
    return nil
  end

  Schema::CSVParser.new(Windfall.get_csv(latest_windfall_enrichment_file), WindfallCareerSchema).each do |row|
    next unless row.valid?

    current_carrer_moves[row.contact_id] = row
  end

  if current_carrer_moves.empty?
    LOG.warn("no career_moves data found in #{latest_windfall_enrichment_file}")
    return nil
  end

  if (previous_windfall_enrichment_file = Windfall.get_previous_windfall_enriched_s3_file_path(org.id))
    Schema::CSVParser.new(Windfall.get_csv(previous_windfall_enrichment_file), WindfallCareerSchema).each do |row|
      next unless row.valid?

      previous_carrer_moves[row.contact_id] = row
    end
  end

  latest_windfall_enrichment_file =~ /_(\d{8})\.csv/
  latest_timestamp = $1

  previous_windfall_enrichment_file.to_s =~ /_(\d{8})\.csv/
  previous_timestamp = $1 || 'full'

  LOG.info("diffing #{latest_windfall_enrichment_file} with #{previous_windfall_enrichment_file}")

  enrichment_dir = TMP_DIR.join('enrichment', 'windfall', 'deltas')
  FileUtils.mkdir_p(enrichment_dir)
  enrichment_windfall_deltas_file = enrichment_dir.join("#{org.slug}-#{org.id}-windfall-career-moves-updates-#{latest_timestamp}-#{previous_timestamp}.csv")
  out = CSV.open(enrichment_windfall_deltas_file, 'wb')
  out << WindfallCareerSchema.to_headers

  stats = Hash.new(0)
  stats['oid'] = org.id

  current_carrer_moves.each do |current_contact_id, current_carrer_move|
    stats[:num_current_carrer_moves] += 1
    previous_carrer_move = previous_carrer_moves[current_contact_id]

    if previous_carrer_move.nil?
      stats[:insert] += 1
      current_carrer_move.action = 'new'
      out << current_carrer_move.to_a
    elsif current_carrer_move == previous_carrer_move
      if previous_carrer_move.career_data_last_updated_date_match?(current_carrer_move)
        stats[:no_change] += 1
      else
        stats[:update_career_data_last_updated_date] += 1
      end
    elsif 
      stats[:update] += 1
      current_carrer_move.action = 'update'
      out << current_carrer_move.to_a
    end
  end

  previous_carrer_moves.each do |previous_contact_id, previous_carrer_move|
    stats[:num_previous_carrer_moves] += 1
    current_carrer_move = current_carrer_moves[previous_contact_id]
    next if current_carrer_move

    stats[:delete] += 1
    previous_carrer_move.action = 'delete'
    out << previous_carrer_move.to_a
  end

  out.close

  LOG.info("finished diffing career_moves files for org #{org.id}")
  LOG.info(stats.to_json)

  enrichment_windfall_deltas_file
end

def create_extended_windfall_delta_file(org, enrichment_windfall_deltas_file)
  LOG.info("extending career_moves file with contact data for org #{org.id}")

  enrichment_windfall_deltas_extended_file = enrichment_windfall_deltas_file.to_s.sub('.csv', '.extended.csv')
  out = CSV.open(enrichment_windfall_deltas_extended_file, 'w')
  out << WindfallCareerCSVRow.csv_headers

  career_moves = []
  append_career_moves_proc = proc do
    contacts = ContactDB::Contact
                 .includes(:contact_attribute, :constituencies, :emails, :identities)
                 .where(id: career_moves.map(&:contact_id))
                 .index_by(&:id)

    career_moves.each do |career_move|
      next unless (contact = contacts[career_move.contact_id])

      row = WindfallCareerCSVRow.new(career_move, contact).to_a
      row.each do |field|
        field.force_encoding('UTF-8') if field.is_a?(String)
      end
      out << row
    end

    career_moves = []
  end

  csv = CSVUtils::CSVIterator.new(enrichment_windfall_deltas_file.to_s)
  csv.each do |row|
    career_move = WindfallCareerSchema.from_hash(row)
    career_moves << career_move

    append_career_moves_proc.call() if career_moves.size >= 1000
  end

  append_career_moves_proc.call() if career_moves.size > 0

  out.close

  LOG.info("finished extending career_moves file with contact data for org #{org.id}")

  enrichment_windfall_deltas_extended_file
end

# s3://et-enrichment-prod/export/career_updates/oid=633/windfall_updates/20231014/Career_Moves_Export_20231014_125326.csv
def upload_career_moves_delta_file(org, enrichment_windfall_deltas_extended_file)
  enrichment_windfall_deltas_extended_file.to_s =~ /windfall-career-moves-updates-(\d{8})/
  date = $1 || raise("can not find date in #{enrichment_windfall_deltas_extended_file}")
  key = "export/career_updates/oid=#{org.id}/windfall_updates/#{date}/Career_Moves_Updates_Export_#{date}.csv"

  LOG.info("uploading #{enrichment_windfall_deltas_extended_file} to s3://#{Windfall.windfall_s3_bucket}/#{key}")

  Windfall.upload_file(enrichment_windfall_deltas_extended_file, key)
end

oids = SCRIPT_OPTIONS[:oids] || EnrichmentDB::Enrichment.select('DISTINCT oid').map(&:oid)
orgs = AuthDB::Organization.where(id: oids, deleted: false).to_a

STATS.total = orgs.size
STATS.notification_interval = 25

orgs.each do |org|
  STATS.inc_and_notify

  enrichment_windfall_deltas_file = create_windfall_delta_file(org)
  next unless enrichment_windfall_deltas_file

  enrichment_windfall_deltas_extended_file = create_extended_windfall_delta_file(org, enrichment_windfall_deltas_file)
  s3_url = upload_career_moves_delta_file(org, enrichment_windfall_deltas_extended_file)

  File.unlink(enrichment_windfall_deltas_file)
  File.unlink(enrichment_windfall_deltas_extended_file)

  LOG.notify("#{org.name}/#{org.id}: #{s3_url}", true)
end

STATS.notify(true)
