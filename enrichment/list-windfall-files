#!/usr/bin/env ruby

# Purpose: to create an Windfall enrichment career moves delta file by comparing the current file to the previous file

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
end

require_relative '../config/environment'

oid = SCRIPT_OPTIONS[:oid].to_s

Windfall.each_file do |file|
  next unless file =~ /windfall_enriched_(\d+)_\d{8}\.csv$/
  next unless $1 == oid

  puts file
end
