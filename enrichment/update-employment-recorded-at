#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)
require 'time'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
  opts.add_input_file
  opts[:source] = 'windfall'
  opts[:updated_at] = Time.parse('2023-06-01 00:00:00')

  opts.on('-s', '--source SOURCE', 'Enrichment source, ex: windfall') do |v|
    opts[:source] = v
  end

  opts.on('-u', '--updated SINCE_LAST_UPDATE', 'Updated time to start setting recorded_at from') do |v|
    opts[:updated_at] = Time.parse(v)
  end

  opts.require_option(:source)
  opts.require_option(:updated_at)
end

require File.expand_path('../config/environment', __dir__)

MAX_RETRIES = 10

JSON.parse(File.read(SCRIPT_OPTIONS[:file])).each do |oid, date|
  date =~ /(\d{4})(\d{2})(\d{2})/
  recorded_at = Date.new($1.to_i, $2.to_i, $3.to_i)

  LOG.info("Setting recorded_at to #{date} for oid #{oid}")

  STATS.reset
  STATS[:oid] = oid
  STATS.total = EnrichmentDB::Enrichment.where(oid: oid).count

  EnrichmentDB::Enrichment.where(oid: oid).find_in_batches(batch_size: 10_000) do |batch|
    STATS.inc_and_notify(batch.size)

    contact_ids = batch.map(&:contact_id)

    contact_ids.in_groups_of(100, false) do |contact_ids_to_update|
      attempts = 1

      begin
        EnrichmentDB::Employment
          .where('updated_at >= ?', SCRIPT_OPTIONS[:updated_at])
          .where(
            source: SCRIPT_OPTIONS[:source],
            contact_id: contact_ids_to_update
          )
          .update_all(
            recorded_at: recorded_at
          )

        STATS.update(:updated_employments, contact_ids_to_update.size)
      rescue => e
        STATS.update(:update_failures)
        LOG.warn("attempting to update employments failed, #{e.message}")
        raise(e) if attempts >= MAX_RETRIES
        attempts += 1
        retry
      end

      # workaround for employment_event uniqueness constratnt on [contact_id, recorded_at, source]
      employment_events = EnrichmentDB::EmploymentEvent
                            .where('updated_at >= ?', SCRIPT_OPTIONS[:updated_at])
                            .where(
                              source: SCRIPT_OPTIONS[:source],
                              contact_id: contact_ids_to_update
                            )
                            .group_by(&:contact_id)


      employment_event_ids = employment_events.reject do |contact_id, events|
        events.any? { |e| e.recorded_at == recorded_at }
      end.map do |contact_id, events|
        events.sort_by(&:recorded_at).last.id
      end

      next if employment_event_ids.empty?

      attempts = 1

      begin
        EnrichmentDB::EmploymentEvent
          .where(
            id: employment_event_ids
          )
          .update_all(
            recorded_at: recorded_at
          )

        STATS.update(:updated_employment_events, employment_event_ids.size)
      rescue => e
        STATS.update(:update_failures)
        LOG.warn("attempting to update employment_events failed, #{e.message}")
        raise(e) if attempts >= MAX_RETRIES
        attempts += 1
        retry
      end
    end
  end

  STATS.notify(true)
end
