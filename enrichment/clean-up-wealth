#!/usr/bin/env ruby

# Purpose: to clean up enrichment.wealth_attributes data for a specified org

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
  opts.add_oids_option

  opts.on('-d', '--debug', 'Skip updating DB') do
    opts[:debug] = true
  end
end

require File.expand_path('../config/environment', __dir__)

class WindfallCSVRow
  include Schema::Model
  include Schema::ArrayHeaders
  include Schema::Arrays
  include CSVUtils::CSVRow

  attr_accessor :contact_id
  
  attribute :remote_id, :string, alias: 'Record ID'
  attribute :net_worth, :integer, alias: 'Net Worth'
  attribute :net_worth_last_updated, :date, alias: 'Net Worth Last Calculated'

  csv_column(:contact_id)
  csv_column(:net_worth)
  csv_column(:household_income) { nil }
  csv_column(:rating) { nil }
  csv_column(:source)
  csv_column(:net_worth_last_updated) { net_worth_last_updated.strftime('%Y-%m-%d') }
  csv_column(:remote_id)

  def valid?
    !! net_worth_last_updated
  end

  def source
    (net_worth.nil? || net_worth < 1_000_000) ? 'windfall_wealth_plus' : 'windfall'
  end
end

class EnrichmentWealthAttributeCSVRow < EnrichmentDB::WealthAttribute
  include CSVUtils::CSVRow

  has_one :enrichment, class_name: 'EnrichmentDB::Enrichment', primary_key: :contact_id, foreign_key: :contact_id

  csv_column(:contact_id)
  csv_column(:net_worth)
  csv_column(:household_income)
  csv_column(:rating)
  csv_column(:source)
  csv_column(:net_worth_last_updated) { net_worth_last_updated&.strftime('%Y-%m-%d') }
  csv_column(:id)
end

def batch_assign_contact_id(oid, rows)
  identities = ContactDB::Identity.where(oid: oid, type: 1, value: rows.map(&:remote_id)).joins(:contact).index_by(&:value)

  rows.each do |row|
    row.contact_id = identities[row.remote_id]&.contact_id
  end
end


SCRIPT_OPTIONS[:oids].each do |oid|
  wealth_data_dir = TMP_DIR.join('enrichment', oid.to_s)
  FileUtils.mkdir_p(wealth_data_dir)

  windfall_wealth_attributes_csv = wealth_data_dir.join('windfall_wealth_attributes.csv').to_s
  windfall_wealth_attributes_sorted_csv = wealth_data_dir.join('windfall_wealth_attributes_sorted.csv').to_s
  enrichment_wealth_attributes_csv = wealth_data_dir.join('enrichment_wealth_attributes.csv').to_s

  unless (latest_windfall_enrichment_file = Windfall.get_latest_windfall_enriched_s3_file_path(oid))
    $stderr.puts 'failed to find Windfall enrichment file'
    next
  end

  LOG.info("latest Windfall enrichment file for #{oid} is s3://#{Windfall.windfall_s3_bucket}/#{latest_windfall_enrichment_file}")

  rows = []
  append_windfall_wealth_attributes_rows = proc do |report|
    batch_assign_contact_id(oid, rows)

    rows.each do |row|
      next unless row.contact_id

      report << row
    end

    rows = []
  end

  CSVUtils::CSVReport.new(windfall_wealth_attributes_csv, WindfallCSVRow) do |report|
    Schema::CSVParser.new(Windfall.get_csv(latest_windfall_enrichment_file), WindfallCSVRow).each do |row|
      next unless row.valid?

      rows << row

      append_windfall_wealth_attributes_rows.call(report) if rows.size >= 1_000
    end

    append_windfall_wealth_attributes_rows.call(report) if rows.size > 0
  end

  # sort the file by contact_id (0), if the 2 rows have the same contact_id sort by the source (4)
  CSVUtils::CSVSort.new(windfall_wealth_attributes_csv, windfall_wealth_attributes_sorted_csv, 250_000).sort do |a, b|
    a[0] == b[0] ? a[4] <=> b[4] : a[0].to_i <=> b[0].to_i
  end

  CSVUtils::CSVReport.new(enrichment_wealth_attributes_csv, EnrichmentWealthAttributeCSVRow) do |report|
    EnrichmentWealthAttributeCSVRow.joins(:enrichment).where(enrichment: {oid: oid}).order(:contact_id, :source).each do |row|
      report << row
    end
  end

  comparison_columns = [
    'net_worth',
    'household_income',
    'rating',
    'source',
    'net_worth_last_updated'
  ]

  comparer = CSVUtils::CSVCompare.new(windfall_wealth_attributes_sorted_csv, comparison_columns) do |src_record, dest_record|
    if src_record['contact_id'] == dest_record['contact_id']
      src_record['source'] <=> dest_record['source']
    else
      src_record['contact_id'].to_i <=> dest_record['contact_id'].to_i
    end
  end

  create_csv = CSV.open(wealth_data_dir.join('create.csv'), 'wb')
  create_csv << WindfallCSVRow.csv_headers
  update_csv = CSV.open(wealth_data_dir.join('update.csv'), 'wb')
  update_csv << WindfallCSVRow.csv_headers
  delete_csv = CSV.open(wealth_data_dir.join('delete.csv'), 'wb')
  delete_csv << EnrichmentWealthAttributeCSVRow.csv_headers

  comparer.compare(enrichment_wealth_attributes_csv) do |action, row|
    case action
    when :create
      create_csv << row.values
    when :update
      update_csv << row.values
    when :delete
      delete_csv << row.values
    end
  end

  create_csv.close
  update_csv.close
  delete_csv.close

  if SCRIPT_OPTIONS[:debug]
    next
  end

  wealth_attribute_ids = []
  delete_wealth_attributes = proc do
    contact_ids = EnrichmentDB::WealthAttribute.select(:contact_id).where(id: wealth_attribute_ids).to_a.map(&:contact_id)
    EnrichmentDB::Enrichment.transaction do
      EnrichmentDB::Enrichment.where(contact_id: contact_ids).update_all('updated_at = NOW()')
      EnrichmentDB::WealthAttribute.where(id: wealth_attribute_ids).delete_all
    end
    wealth_attribute_ids = []
  end

  csv = CSVUtils::CSVIterator.new(wealth_data_dir.join('delete.csv').to_s)

  if csv.size == 0
    LOG.notify("finished enrichment wealth clean up for #{oid}, nothing to delete", true)
    next
  end

  csv.each do |row|
    wealth_attribute_ids << row['id'].to_i

    delete_wealth_attributes.call() if wealth_attribute_ids.size >= 1_000
  end

  delete_wealth_attributes.call() if wealth_attribute_ids.size > 0

  LOG.notify("finished enrichment wealth clean up for #{oid}", true)
end
