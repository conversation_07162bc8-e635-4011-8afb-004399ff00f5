#!/usr/bin/env ruby

# Purpose: to output all the commands to run to clean up wealth data

require_relative '../config/script_options'
require 'time'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  now = Time.now
  opts[:starting_at] = Time.new(now.year, now.month, 14, 0, 0, 0, 0).to_i * 1000

  opts.on('-s', '--start-date DATE', 'Start date of enrichment import') do |v|
    year, month, day = v.split('-').map { |p| p.sub(/^0/, '').to_i }
    opts[:starting_at] = Time.new(year, month, day, 0, 0, 0, 0).to_i * 1000
  end
end

require_relative '../config/environment'

OIDS_TO_UPDATE_FIRST = [
  633, # uva
  577, # wpaog
  711, # buffalo
  436, # oklamhomastate
  603, # yale
  16,  # bu
  657  # osu
]

def dna_client
  @dna_client ||= DNAClient.create_client_with_app_creds
end

def windfall_enabled_oids
  @windfall_enabled_oids ||= dna_client.get_bulk_gate('windfall')['organizations'].select { |oid, info| info['enabled'] }.map { |oid, _| oid.to_i }
end

def wealth_attributes_updated_since
  @wealth_attributes_updated_since ||= Time.at(SCRIPT_OPTIONS[:starting_at] / 1000)
end

puts "# Updated OIDs are based on wealth records with an updated since #{Time.at(SCRIPT_OPTIONS[:starting_at] / 1000).utc} (#{SCRIPT_OPTIONS[:starting_at]})"
puts ""

# Find all OIDs that have updated wealth records since the EMR process ran
oids = EnrichmentDB::Enrichment
        .joins('JOIN wealth_attributes ON enrichment.contact_id = wealth_attributes.contact_id')
        .select('DISTINCT oid')
        .where('wealth_attributes.updated_at >= ?', wealth_attributes_updated_since)
        .map(&:oid)

# Filter the list of OIDs to active non-test orgs
oids = AuthDB::Organization
         .where(test_org: false, deleted: false, id: oids)
         .map(&:id)

missing_oids = windfall_enabled_oids - oids
if missing_oids.size > 0
  puts "# !!!WARNING!!!! The following oids have no wealth updates: #{missing_oids.join(', ')} !!!WARNING!!!"
end

puts "# Found #{oids.size} active non-test orgs with enrichment wealth updates"
puts ""

# Run the clean-up-wealth process
puts "# Run clean-up-wealth to delete extra entries in the wealth_attributes table"
puts "./enrichment/clean-up-wealth -e #{APP_ENV_PREFIX} --run-remote -o #{oids.join(',')}"
puts ""

puts "# Staring updating ElasticSearch after clean-up-wealth completes"
puts ""

puts "# Goto the EnrichmentEsSync bin folder"
puts "cd ../enrichment/EnrichmentSpark/EnrichmentEsSync/bin"
puts ""

first_set_of_oids = OIDS_TO_UPDATE_FIRST & oids
missing_oids = OIDS_TO_UPDATE_FIRST - oids
if missing_oids.size > 0
  puts "# !!!WARNING!!!! The following HIGH PROFILE OIDs have no wealth updates: #{missing_oids.join(', ')} !!!WARNING!!!"
end

puts "# First set of OIDs to sync ES wealth data"
puts "./emr-submit-enrichment-org-es-sync.sh"
puts "Enter environment [stage|prod] > #{APP_ENV_PREFIX}"
puts "Enter oid(s) > #{first_set_of_oids.join(' ')}"
puts "Enter cutoff timestamp in epoch millis > #{SCRIPT_OPTIONS[:starting_at]}"
puts ""

remaing_oids = oids - first_set_of_oids

wealth_attribute_counts = {}
EnrichmentDB::WealthAttribute
  .joins('JOIN enrichment ON enrichment.contact_id = wealth_attributes.contact_id')
  .where(enrichment: {oid: remaing_oids})
  .where('wealth_attributes.updated_at >= ?', wealth_attributes_updated_since)
  .select('enrichment.oid, COUNT(*) as cnt')
  .group('oid')
  .order('cnt DESC')
  .each do |record|
  wealth_attribute_counts[record.oid] = record.cnt
end

LOG.info(JSON.pretty_generate(wealth_attribute_counts))

paid_wealth_attribute_counts = {}
non_paid_wealth_attribute_counts = {}
wealth_attribute_counts.each do |oid, cnt|
  if windfall_enabled_oids.include?(oid)
    paid_wealth_attribute_counts[oid] = cnt
  else
    non_paid_wealth_attribute_counts[oid] = cnt
  end
end

MAX_RECORDS_TO_UPDATE_PER_A_SYNC = 2_000_000
groups_of_oids = []
current_count = 0
current_oids = []
append_group_of_oids_proc = proc do
  groups_of_oids << current_oids
  current_oids = []
  current_count = 0
end

paid_wealth_attribute_counts.each do |oid, cnt|
  if (current_count + cnt) > MAX_RECORDS_TO_UPDATE_PER_A_SYNC || (current_oids.size >= 20)
    append_group_of_oids_proc.call()
  end

  current_count += cnt
  current_oids << oid
end

if current_oids.size > 0
  append_group_of_oids_proc.call()
end

non_paid_wealth_attribute_counts.each do |oid, cnt|
  if (current_count + cnt) > MAX_RECORDS_TO_UPDATE_PER_A_SYNC || (current_oids.size >= 20)
    append_group_of_oids_proc.call()
  end

  current_count += cnt
  current_oids << oid
end

if current_oids.size > 0
  append_group_of_oids_proc.call()
end

groups_of_oids.each do |list_of_oids|
  total_records_to_update = list_of_oids.map { |oid| wealth_attribute_counts[oid] }.sum
  paid = paid_wealth_attribute_counts[list_of_oids.first].nil? ? 'non-paid' : 'paid'
  puts "# The follwing #{paid} set of orgs have #{total_records_to_update} wealth records to update"
  puts "./emr-submit-enrichment-org-es-sync.sh"
  puts "Enter environment [stage|prod] > #{APP_ENV_PREFIX}"
  puts "Enter oid(s) > #{list_of_oids.join(' ')}"
  puts "Enter cutoff timestamp in epoch millis > #{SCRIPT_OPTIONS[:starting_at]}"
  puts ""
end
