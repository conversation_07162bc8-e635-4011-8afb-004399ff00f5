#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('--email EMAIL', 'Email') do |v|
    opts[:email] = v
  end

  opts.on('-g', '--group TABLEAU_GROUP', 'Tableau Group') do |v|
    opts[:tableau_group] = v
  end

  opts.require_option(:email)
  opts.require_option(:tableau_group)
end

require File.expand_path('../config/environment', __dir__)

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])
user = AuthDB::User.find_by!(email: SCRIPT_OPTIONS[:email])
tableau_group = AuthDB::TableauGroup.find_by!(group_name: SCRIPT_OPTIONS[:tableau_group])

unless (affiliation = org.affiliations.where(user_id: user.id).first)
  $stderr.puts "user is not affiliated with org, go here to add the affiliation https://super.evertrue.com/users/#{user.id}/affiliations"
  exit 1
end

auth_client = AuthClient.create_client_with_app_creds

auth_client.manually_add_user_to_tableua_groups(org.id, user.id, tableau_group.group_name)
