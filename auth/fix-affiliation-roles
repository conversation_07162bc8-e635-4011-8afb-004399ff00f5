#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
end

require File.expand_path('../config/environment', __dir__)

VALID_ROLES = ["GivingTree Owner", "GivingTree User", "Volunteer", "Volunteer Lead"]

def auth_client
  @auth_client ||= AuthClient.create_client_with_app_creds
end

roles = AuthDB::Role.all.to_a.index_by(&:id)

File.foreach('affil.log') do |line|
  affiliation_id = Integer(line)

  unless (affiliation = AuthDB::Affiliation.includes(:user, affiliation_roles: :role).where(id: affiliation_id).first)
    LOG.warn("affiliation #{affiliation_id} not found")
    next
  end

  LOG.info("affiliation #{affiliation_id} role is #{affiliation.affiliation_roles.map { |ar| ar.role.name }.join(', ')}")

  if affiliation.affiliation_roles.detect { |ar| VALID_ROLES.include?(ar.role.name) }
    LOG.info("affiliation #{affiliation_id} already has the correct role(s) #{affiliation.affiliation_roles.map { |ar| ar.role.name }.join(', ')}")
    next
  end

  affiliation_role_versions = AuthDB::Version.where(item_type: 'AffiliationRole', user_affected: affiliation.user_id, oid: affiliation.organization_id).to_a.reverse

  add_affiliation_role_versions = []
  while (version = affiliation_role_versions.first)
    break unless version.event == 'create'

    add_affiliation_role_versions << affiliation_role_versions.shift
  end

  removed_affiliation_role_versions = []
  while (version = affiliation_role_versions.shift)
    break unless version.event == 'destroy'

    removed_affiliation_role_versions << version
  end

  add_role_ids = add_affiliation_role_versions.map { |v| YAML.load(v.object_changes)['role_id'].last }
  unless add_role_ids.all? { |id| roles[id] }
    LOG.error("affiliation #{affiliation_id} add_role_ids are not found #{add_role_ids.reject { |id| roles[id] }.join(', ')}")
    next
  end

  previous_role_ids = removed_affiliation_role_versions.map { |v| YAML.load(v.object)['role_id'] }
  unless previous_role_ids.all? { |id| roles[id] }
    LOG.error("affiliation #{affiliation_id} previous_role_ids are not found #{previous_role_ids.reject { |id| roles[id] }.join(', ')}")
    next
  end

  LOG.info("updating affiliation #{affiliation_id} roles to #{previous_role_ids.map { |id| id.to_s + '/' + roles[id].name }.join(', ')} was set to #{add_role_ids.map { |id| id.to_s + '/' + roles[id].name }.join(', ')}")

  auth_client.update_affiliation(
    affiliation.organization_id,
    affiliation.id,
    {
      contact_id: affiliation.contact_id,
      match_remote_id: 'true',
      role_ids: previous_role_ids
    })
end
