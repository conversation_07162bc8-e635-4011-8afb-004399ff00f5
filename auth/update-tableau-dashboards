#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote

  opts.on('-p', '--path DASHBOARD_PATH', 'Tableau dashboard path') do |v|
    opts[:path] = v
  end
end

require File.expand_path('../config/environment', __dir__)

def evertrue_site
  @evertrue_site ||= AuthDB::TableauSite.where(slug: 'EverTrue').first
end

def tableau_api_client
  @tableau_api_client ||= TableauApiClient.create_client_with_default_credentials(evertrue_site.slug)
end

AuthDB::TableauDashboard.find_each do |dashboard|
  next unless dashboard.active?
  next if SCRIPT_OPTIONS[:path] && SCRIPT_OPTIONS[:path] != dashboard.path
  next if dashboard.path == '/ClarityDataReadinessScorecard/ClarityReadinessScorecard'

  puts dashboard.path
  dashboard_view = tableau_api_client.get_view_by_path(
    site_id: evertrue_site.remote_site_id,
    path: dashboard.path
  )

  dashboard_image_view = tableau_api_client.get_image_view_by_path(
    site_id: evertrue_site.remote_site_id,
    path: dashboard.path
  )

  workbook = tableau_api_client.resources.get_workbook(
    site_id: evertrue_site.remote_site_id,
    workbook_id: dashboard_view['workbook']['id']
  )

  dashboard.update!(
    name: dashboard_view['name'],
    description: workbook['workbook']['description'],
    view_id: dashboard_view['id'],
    workbook_id: dashboard_view['workbook']['id'],
    image_view_id: dashboard_image_view['id'],
    image_workbook_id: dashboard_image_view['workbook']['id'],
  )

  begin
    dashboard_image = dashboard.tableau_dashboard_images.where(image_type: 'image').first
    dashboard_image ||= AuthDB::TableauDashboardImage.new(
      tableau_dashboard: dashboard,
      mime_type: 'image/png',
      image_type: 'image'
    )

    io = StringIO.new
    tableau_api_client.resources.get_view_image(
      site_id: evertrue_site.remote_site_id,
      view_id: dashboard.image_view_id,
      io: io
    )

    io.rewind
    dashboard_image.image = io.read
    dashboard_image.fetched_at = Time.now
    dashboard_image.save!
  rescue => e
    $stderr.puts "error fetching image for dashboard #{dashboard.path}, #{e}"
    next
  end

  begin
    dashboard_image = dashboard.tableau_dashboard_images.where(image_type: 'preview').first
    dashboard_image ||= AuthDB::TableauDashboardImage.new(
      tableau_dashboard: dashboard,
      mime_type: 'image/png',
      image_type: 'preview'
    )

    io = StringIO.new
    tableau_api_client.resources.get_view_preview_image(
      site_id: evertrue_site.remote_site_id,
      workbook_id: dashboard.image_workbook_id,
      view_id: dashboard.image_view_id,
      io: io
    )

    io.rewind
    dashboard_image.image = io.read
    dashboard_image.fetched_at = Time.now
    dashboard_image.save!
  rescue => e
    $stderr.puts "error fetching preview for dashboard #{dashboard.path}, #{e}"
    next
  end
end
