#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
end

require File.expand_path('../config/environment', __dir__)

def org
  @org ||= AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])
end

AuthDB::TableauUserGroup
class AuthDB::TableauUserGroup
  def user_id
    tableau_user.user.id
  end

  def name
    tableau_user.user.name
  end

  def email
    tableau_user.user.email
  end

  def group_name
    tableau_group.group_name
  end
end


tableau_user_gropus = AuthDB::TableauUserGroup.includes(:tableau_group, tableau_user: [:user]).joins(:tableau_user).where(tableau_user: {organization_id: org.id}).where.not(tableau_user: {user_id: nil}).to_a

headings = {
  'user_id' => :user_id,
  'name' => :name,
  'email' => :email,
  'manually_added' => :manually_added,
  'group_name' => :group_name,
  'marked_for_deletion' => :marked_for_deletion
}
table = TableView.new(headings, row_type: :object)
table.render('Tableau User Groups' => tableau_user_gropus)
