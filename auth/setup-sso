#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-n', '--name IDENTITY_PROVIDER_NAME', 'Identity provider name') do |v|
    opts[:name] = v
  end

  opts.on('-d', '--domain-suffix DOMAIN_SUFFIX', 'Email domain suffix') do |v|
    opts[:primary_domain_suffix] = v
  end

  opts.on('-x', '--federation-xml FEDERATION_XML_URL', 'Federation XML') do |v|
    if v =~ /^@/
      opts[:federation_xml] = File.read(v[1..-1])
    else
      opts[:federation_xml_url] = v
    end
  end
end

require File.expand_path('../config/environment', __dir__)
include CommandLineHelpers

def org
  @org ||= AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])
end

def auth_client
  @auth_client ||= AuthClient.create_client_for_logged_in_user(org.id)
end

def givingtree_app
  @givingtree_app ||= AuthDB::Application.find_by!(name: 'givingtree')
end

def cognito_client
  @cognito_client ||= Aws::CognitoIdentityProvider::Client.new(profile: 'default')
end

def add_supported_identity_provider(identity_provider_name)
  COGNITO_CONFIG['apps'].each do |app|
    resp = cognito_client.describe_user_pool_client(
      user_pool_id: COGNITO_CONFIG['user_pool_id'],
      client_id: app['client_id']
    )

    payload = resp.user_pool_client.to_h
    File.open("aws-cognito-#{COGNITO_CONFIG['name']}-app-#{app['name']}-#{Time.now.strftime('%Y-%m-%dT%H%M%S')}.json", 'wb') do |f|
      f.write(JSON.pretty_generate(payload))
    end
    [
      :last_modified_date,
      :creation_date,
      :client_secret
    ].each do |field|
      payload.delete(field)
    end

    if payload[:supported_identity_providers].include?(identity_provider_name)
      LOG.info("#{identity_provider_name} is already in supported_identity_providers for #{COGNITO_CONFIG['name']} app #{app['name']}")
      next
    end

    LOG.info("Adding #{identity_provider_name} to supported_identity_providers for #{COGNITO_CONFIG['name']} app #{app['name']}")
    payload[:supported_identity_providers] << identity_provider_name

    resp = cognito_client.update_user_pool_client(payload)

    unless resp.user_pool_client.supported_identity_providers.include?(identity_provider_name)
      LOG.error("Failed to add #{identity_provider_name} to supported_identity_providers for #{COGNITO_CONFIG['name']} app #{app['name']}")
    end
  end
end

def default_name
  name = org.name.gsub('&', 'And')
  name.gsub!(/[^a-zA-Z0-9]/, '')
  if name =~ /test$/i
    name.sub!(/test/i, '-Test')
  elsif org.test_org?
    name += '-Test'
  else
    name += '-Prod'
  end
  name
end

def default_primary_domain_suffix
  org.affiliations.first.user.email.split('@').last
end

def existing_identity_provider
  @existing_identity_provider ||= org.identity_providers.first
end

if SCRIPT_OPTIONS[:federation_xml].nil? && SCRIPT_OPTIONS[:federation_xml_url].nil?
  $stderr.puts 'Federation XML is required'
  exit 1
end

if SCRIPT_OPTIONS[:federation_xml_url]
  response = Net::HTTP.get_response(URI(SCRIPT_OPTIONS[:federation_xml_url]))
  unless response.code == '200'
    $stderr.puts "received response code #{response.code} when fetching metadata URL"
    exit 1
  end
end

# verify user has logged in recently
auth_client.get_me()

identity_provider_payload = {
  identity_provider: {
    name: existing_identity_provider&.name || SCRIPT_OPTIONS[:name] || default_name(),
    primary_domain_suffix: SCRIPT_OPTIONS[:primary_domain_suffix] || existing_identity_provider&.primary_domain_suffix || default_primary_domain_suffix(),
    federation_xml: SCRIPT_OPTIONS[:federation_xml],
    federation_xml_url: SCRIPT_OPTIONS[:federation_xml_url]
  }
}

print JSON.pretty_generate(identity_provider_payload) + "\n"

exit unless yes_no('Identity provider configuration is correct?') == :yes

if existing_identity_provider
  puts "update identity provider #{existing_identity_provider.id}"
  response = auth_client.update_identity_provider(org.id, existing_identity_provider.id, identity_provider_payload)
else
  puts "create identity provider"
  response = auth_client.create_identity_provider(org.id, identity_provider_payload)
end

unless response.kind_of?(Net::HTTPSuccess)
  $stderr.puts "Failed to create/update identity provider #{response.code}/#{response.body}"
  exit 1
end

unless existing_identity_provider
  $stderr.puts "identity provider not created"
  exit 1
end

unless existing_identity_provider.restrictions.first
  puts "Create identity provider SSO restriction"

  restriction_payload = {
    identity_provider_id: existing_identity_provider.id,
    type: 'SsoRestriction'
  }

  response = auth_client.create_restriction(org.id, restriction_payload)

  unless response.kind_of?(Net::HTTPSuccess)
    $stderr.puts "Failed to identity provider restriction #{response.code}/#{response.body}"
    exit 1
  end
end

add_supported_identity_provider(existing_identity_provider.name)
