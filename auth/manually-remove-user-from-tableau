#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('--email EMAIL', 'Email') do |v|
    opts[:email] = v
  end

  opts.require_option(:email)
end

require File.expand_path('../config/environment', __dir__)

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])
user = AuthDB::User.find_by!(email: SCRIPT_OPTIONS[:email])

unless (affiliation = org.affiliations.where(user_id: user.id).first)
  $stderr.puts "user is not affiliated with org, go here to add the affiliation https://super.evertrue.com/users/#{user.id}/affiliations"
  exit 1
end

unless user.tableau_users.count > 0
  $stderr.puts "user has no tableau setup"
  exit 0
end

auth_client = AuthClient.create_client_with_app_creds

user.tableau_users.where(organization_id: org.id).map(&:tableau_user_groups).flatten.each do |tableau_user_group|
  tableau_group = tableau_user_group.tableau_group
  unless tableau_user_group.manually_added?
    LOG.info("skipping removal since user wasn't manually added to #{tableau_group.group_name}")
    next
  end

  LOG.info("removing #{user.email}/#{user.id} from Tableau Group #{tableau_group.group_name}")
  auth_client.manually_remove_user_from_tableua_groups(org.id, user.id, tableau_group.group_name)
end
