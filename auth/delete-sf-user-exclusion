#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-u', '--user USER_ID', Integer, 'User ID to remove from the exclusion\'s lists') do |v|
    opts.options[:user_id] = v
  end

  opts.require_option(:user_id)
end

require File.expand_path('../config/environment', __dir__)

exclusion = AuthDB::SfUserSyncExclusion.where(organization_id: SCRIPT_OPTIONS[:oid], user_id: SCRIPT_OPTIONS[:user_id]).first
exclusion.delete
