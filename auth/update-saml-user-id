#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('--email EMAIL', 'Email') do |v|
    opts[:email] = v
  end

  opts.on('-s', '--saml SAML_USER_ID', 'OPTIONAL defaults to email') do |v|
    opts[:saml_user_id] = v
  end

  opts.require_option(:email)
end

require File.expand_path('../config/environment', __dir__)
include CommandLineHelpers

def auth_client
  @auth_client ||= AuthClient.create_client_with_app_creds
end

def org
  @org ||= AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])
end

def user
  @user ||= AuthDB::User.find_by!(email: SCRIPT_OPTIONS[:email])
end

affiliation = org.affiliations.where(user_id: user.id).first!

SCRIPT_OPTIONS[:saml_user_id] ||= user.email.upcase

payload = {
  saml_user_id: SCRIPT_OPTIONS[:saml_user_id]
}

if yes_no("Change #{user.name}/#{user.id} SAML User ID to #{SCRIPT_OPTIONS[:saml_user_id]}") == :yes
  auth_client.update_user(org.id, user.id, payload)
end
