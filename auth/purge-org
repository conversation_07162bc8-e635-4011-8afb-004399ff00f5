#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
end

require File.expand_path('../config/environment', __dir__)
include CommandLineHelpers

def org
  @org ||= AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])
end

def auth_client
  @auth_client ||= AuthClient.create_client_for_logged_in_user(org.id, nil, 'console')
end

new_purge = JSON.parse(auth_client.new_purge(org.id).body)

print JSON.pretty_generate(new_purge) + "\n"

exit(1) if new_purge['message']

`open #{new_purge['captcha_image']}`

captcha_secret = get_input('Enter Captcha Secret')

delete_org = JSON.parse(auth_client.delete_org(org.id, new_purge['purge_token'], captcha_secret).body)

print JSON.pretty_generate(delete_org) + "\n"
