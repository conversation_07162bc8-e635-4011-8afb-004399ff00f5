#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)
require 'active_record'
ActiveRecord::Base.logger = LOG

class AffiliationCSVRow < AuthDB::Affiliation
  include CSVUtils::CSVRow

  csv_column :user_id
  csv_column :organization_id, header: :oid
  csv_column :id, header: :affiliation_id
  csv_column(:name) { user&.name }
  csv_column(:email) { user&.email }
  csv_column(:alumni) { alumni? ? 'Y' : 'N' }
  csv_column(:no_roles) { no_roles? ? 'Y' : 'N' }
  csv_column(:deleted) { user&.deleted? ? 'Y' : 'N' }
  csv_column(:missing_user) { user.nil? ? 'Y' : 'N' }
end

CSVUtils::CSVReport.new('affiliation-alumni-report.csv', AffiliationCSVRow) do |report|
  AffiliationCSVRow.includes(:user, organization: [:roles], affiliation_roles: [:role]).find_each do |affiliation|
    if affiliation.alumni? ||
       affiliation.no_roles?
      report << affiliation
    end
  end
end
