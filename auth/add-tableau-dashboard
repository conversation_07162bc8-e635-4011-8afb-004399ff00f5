#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-p', '--path TABLEAU_DASHBOARD_PATH', 'Tableau dashboard path') do |v|
    opts[:path] = v
  end

  opts.on('-g', '--group TABLEAU_GROUP', 'Tableau group') do |v|
    opts[:group] = v
  end

  opts.on('-d', '--dashboard-gropu DASHBOARD_GROUP', 'Dashboard group') do |v|
    opts[:dashboard_group] = v
  end

  opts.on('-s', '--sort-order SORT_ORDER', Integer, 'Sort order') do |v|
    opts[:sort_order] = v
  end

  opts.require_option(:path)
  opts.require_option(:group)
  opts.require_option(:dashboard_group)
  opts.require_option(:sort_order)
end

require File.expand_path('../config/environment', __dir__)

def evertrue_site
  @evertrue_site ||= AuthDB::TableauSite.where(slug: 'EverTrue').first
end

def tableau_api_client
  @tableau_api_client ||= TableauApiClient.create_client_with_default_credentials(evertrue_site.slug)
end

tableau_group = AuthDB::TableauGroup.find_by!(group_name: SCRIPT_OPTIONS[:group])
tableau_dashboard_group = AuthDB::TableauDashboardGroup.find_by!(name: SCRIPT_OPTIONS[:dashboard_group])

dashboard_view = tableau_api_client.get_view_by_path(
  site_id: evertrue_site.remote_site_id,
  path: SCRIPT_OPTIONS[:path]
)

dashboard_image_view = tableau_api_client.get_image_view_by_path(
  site_id: evertrue_site.remote_site_id,
  path: SCRIPT_OPTIONS[:path]
)

workbook = tableau_api_client.resources.get_workbook(
  site_id: evertrue_site.remote_site_id,
  workbook_id: dashboard_view['workbook']['id']
)

dashboard = AuthDB::TableauDashboard.create!(
  tableau_group: tableau_group,
  path: SCRIPT_OPTIONS[:path],
  name: dashboard_view['name'],
  description: workbook['workbook']['description'],
  view_id: dashboard_view['id'],
  workbook_id: dashboard_view['workbook']['id'],
  image_view_id: dashboard_image_view['id'],
  image_workbook_id: dashboard_image_view['workbook']['id'],
  active: true,
  tableau_dashboard_group: tableau_dashboard_group,
  sort_order: SCRIPT_OPTIONS[:sort_order]
)

dashboard_image = dashboard.tableau_dashboard_images.where(image_type: 'image').first
dashboard_image ||= AuthDB::TableauDashboardImage.new(
  tableau_dashboard: dashboard,
  mime_type: 'image/png',
  image_type: 'image'
)

io = StringIO.new
tableau_api_client.resources.get_view_image(
  site_id: evertrue_site.remote_site_id,
  view_id: dashboard.image_view_id,
  io: io
)

io.rewind
dashboard_image.image = io.read
dashboard_image.fetched_at = Time.now
dashboard_image.save!

dashboard_image = dashboard.tableau_dashboard_images.where(image_type: 'preview').first
dashboard_image ||= AuthDB::TableauDashboardImage.new(
  tableau_dashboard: dashboard,
  mime_type: 'image/png',
  image_type: 'preview'
)

io = StringIO.new
tableau_api_client.resources.get_view_preview_image(
  site_id: evertrue_site.remote_site_id,
  workbook_id: dashboard_image_view['workbook']['id'],
  view_id: dashboard.image_view_id,
  io: io
)

io.rewind
dashboard_image.image = io.read
dashboard_image.fetched_at = Time.now
dashboard_image.save!
