#!/usr/bin/env ruby

require 'yaml'
require 'mysql2'
require 'logger'
require 'csv'
require 'net/http'
require 'json'

ES_URL = 'http://prod-searchcoordinator-v2-contacts-1b.priv.evertrue.com:9200'

LOG = Logger.new(__FILE__ + '.log')
LOG.formatter = Logger::Formatter.new

def mysql_client_auth
  @mysql_client_users ||=
    begin
      config = YAML.load_file("#{ENV['HOME']}/.evertrue/prod-dbs.yml")['auth']
      Mysql2::Client.new(
        host: config['host'],
        username: config['username'],
        password: config['password'],
        database: config['database']
      )
    end
end

def mysql_client_contacts
  @mysql_client_contacts ||=
    begin
      config = YAML.load_file("#{ENV['HOME']}/.evertrue/prod-dbs.yml")['contacts']
      Mysql2::Client.new(
        host: config['host'],
        username: config['username'],
        password: config['password'],
        database: config['database']
      )
    end
end

def mysql_client_importer
  @mysql_client_importer ||=
    begin
      config = YAML.load_file("#{ENV['HOME']}/.evertrue/prod-dbs.yml")['importer']
      Mysql2::Client.new(
        host: config['host'],
        username: config['username'],
        password: config['password'],
        database: config['database']
      )
    end
end

def mysql_client_ugc
  @mysql_client_ugc ||=
    begin
      config = YAML.load_file("#{ENV['HOME']}/.evertrue/prod-dbs.yml")['ugc']
      Mysql2::Client.new(
        host: config['host'],
        username: config['username'],
        password: config['password'],
        database: config['database']
      )
    end
end

def mysql_client_volunteers
  @mysql_client_volunteers ||=
    begin
      config = YAML.load_file("#{ENV['HOME']}/.evertrue/prod-dbs.yml")['volunteers']
      Mysql2::Client.new(
        host: config['host'],
        username: config['username'],
        password: config['password'],
        database: config['database']
      )
    end
end

class ContactClient
  DEFAULT_BASE_URL = 'https://api.evertrue.com'

  def initialize(app_key, auth_token, base_url=DEFAULT_BASE_URL, provider='EvertrueAppToken')
    @app_key = app_key
    @auth_token = auth_token
    @base_url = base_url
    @provider = provider
  end

  def get_dna_gate(oid, dna_gate)
    uri = URI(@base_url + "/dna/gates/#{dna_gate}?oid=#{oid}")

    req = Net::HTTP::Get.new(uri.request_uri, req_headers)

    res = Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == 'https') do |http|
      http.request(req)
    end

    JSON.parse(res.body)
  end

  private

  def req_headers
    {
      'Content-Type' => 'application/json',
      'Accept' => 'application/json',
      'Application-Key' => @app_key,
      'Authorization-Provider' => @provider,
      'Authorization' => @auth_token,
      'ET-Update-Source' => 'csv_importer'
    }
  end
end

class OrgReport
  ES_NOTE_DOC_NAME = 'contact_note'

  attr_accessor :id,
                :name,
                :created_at,
                :num_affiliations,
                :last_affiliation_updated_at,
                :num_contacts,
                :last_contact_updated_at,
                :last_import_at,
                :num_proposals,
                :last_proposal_updated_at,
                :num_pools,
                :last_pool_updated_at

  include Comparable

  def initialize(id, name, created_at)
    self.id = id
    self.name = name
    self.created_at = created_at
  end

  def uses_mysql(contact_client)
    contact_client.get_dna_gate(id, 'mysql_read_and_write').dig('features', 'mysql_read_and_write', 'enabled') ? 'Y' : 'N'
  end

  def num_notes
    @num_notes ||= es_child_doc_count(id, ES_NOTE_DOC_NAME)
  end

  def last_note_updated_at
    unless defined?(@last_note_updated_at)
      @last_note_updated_at = es_child_doc_max_updated_at(id, ES_NOTE_DOC_NAME, 'updated_at')
    end
    @last_note_updated_at
  end

  def last_modification_time
    [last_affiliation_updated_at, last_contact_updated_at, last_import_at, last_proposal_updated_at, last_pool_updated_at, last_note_updated_at, created_at].compact.max
  end

  def <=>(other)
    if last_modification_time.nil?
      -1
    elsif other.last_modification_time.nil?
      1
    else
      last_modification_time <=> other.last_modification_time
    end
  end
end

def to_time(time)
  if time.is_a?(Integer) || time.is_a?(Float)
    Time.at(time / 1_000)
  else
    time
  end
end

def assign_last_updated_at(mysql_client, org_reports, table_name, stat_name, group_by_column_name, updated_at_column_name='updated_at')
  query = "SELECT #{group_by_column_name}, MAX(#{updated_at_column_name}) AS #{stat_name} FROM #{table_name} WHERE #{group_by_column_name} IN(#{org_reports.map(&:id).join(',')}) GROUP BY #{group_by_column_name}"
  LOG.info(query)
  mysql_client.query(query).each do |result|
    org_report = org_reports.detect { |r| r.id == result[group_by_column_name] }
    org_report.send("#{stat_name}=", to_time(result[stat_name]))
  end
end

def assign_counts(mysql_client, org_reports, table_name, stat_name, group_by_column_name)
  query = "SELECT #{group_by_column_name}, COUNT(*) AS #{stat_name} FROM #{table_name} WHERE #{group_by_column_name} IN(#{org_reports.map(&:id).join(',')}) GROUP BY #{group_by_column_name}"
  LOG.info(query)
  mysql_client.query(query).each do |result|
    org_report = org_reports.detect { |r| r.id == result[group_by_column_name] }
    org_report.send("#{stat_name}=", result[stat_name])
  end
end

def to_date(time)
  if time.nil?
    nil
  else
    time.strftime('%Y-%m-%d')
  end
end

# child doc: contact_note
# has: oid, updated_at

def es_child_doc_count(oid, child_doc_name)
  uri = URI(ES_URL + "/prod-contacts/#{child_doc_name}/_count?routing=#{oid}")
  req = Net::HTTP::Get.new(uri.request_uri)
  req.body = {
    query: {
      term: {
        oid: oid
      }
    }
  }.to_json

  res = Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == 'https') do |http|
    http.request(req)
  end

  JSON.parse(res.body)['count']
end

def es_child_doc_max_updated_at(oid, child_doc_name, es_field)
  uri = URI(ES_URL + "/prod-contacts/#{child_doc_name}/_search?routing=#{oid}")
  req = Net::HTTP::Get.new(uri.request_uri)
  req.body = {
    aggs: {
      max_updated_at: {
        max: {
          field: es_field
        }
      }
    },
    query: {
      term: {
        oid: oid
      }
    }
  }.to_json

  res = Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == 'https') do |http|
    http.request(req)
  end

  updated_at = JSON.parse(res.body).dig('aggregations', 'max_updated_at' , 'value')
  updated_at ? to_time(updated_at) : nil
end

contact_client = ContactClient.new('ef571795d45d5be4994a3beebbf2fcb9d24466d45cbf6877250ba822420d3c64', 'ODpGZG9zTHozYmpjZ0xMc1JUUndCbg==')

org_reports = mysql_client_auth.query('SELECT id, name, created_at FROM organizations').map { |result| OrgReport.new(result['id'], result['name'], result['created_at']) }

assign_last_updated_at(mysql_client_auth, org_reports, 'affiliations', 'last_affiliation_updated_at', 'organization_id')
assign_counts(mysql_client_auth, org_reports, 'affiliations', 'num_affiliations', 'organization_id')

assign_last_updated_at(mysql_client_contacts, org_reports, 'contact', 'last_contact_updated_at', 'oid')
assign_counts(mysql_client_contacts, org_reports, 'contact', 'num_contacts', 'oid')

assign_last_updated_at(mysql_client_importer, org_reports, 'job', 'last_import_at', 'oid')

assign_last_updated_at(mysql_client_ugc, org_reports, 'proposal', 'last_proposal_updated_at', 'oid')
assign_counts(mysql_client_ugc, org_reports, 'proposal', 'num_proposals', 'oid')

assign_last_updated_at(mysql_client_volunteers, org_reports, 'pools', 'last_pool_updated_at', 'oid')
assign_counts(mysql_client_volunteers, org_reports, 'pools', 'num_pools', 'oid')

org_reports.sort!

str = CSV.generate do |csv|
  csv << [
    'id',
    'name',
    'uses_mysql',
    'last_modification_date',
    'created_at',
    'num_affiliations',
    'last_affiliation_updated_at',
    'num_contacts',
    'last_contact_updated_at',
    'last_import_at',
    'num_proposals',
    'last_proposal_updated_at',
    'num_pools',
    'last_pool_updated_at',
    'num_notes',
    'last_note_updated_at'
  ]

  org_reports.each do |org_report|
    csv << [
      org_report.id,
      org_report.name,
      org_report.uses_mysql(contact_client),
      to_date(org_report.last_modification_time),
      to_date(org_report.created_at),
      org_report.num_affiliations,
      to_date(org_report.last_affiliation_updated_at),
      org_report.num_contacts,
      to_date(org_report.last_contact_updated_at),
      to_date(org_report.last_import_at),
      org_report.num_proposals,
      to_date(org_report.last_proposal_updated_at),
      org_report.num_pools,
      to_date(org_report.last_pool_updated_at),
      org_report.num_notes,
      to_date(org_report.last_note_updated_at)
    ]
  end
end

puts str
