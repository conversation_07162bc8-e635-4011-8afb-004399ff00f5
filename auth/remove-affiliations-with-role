#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:roles] = ['Alumni']
  opts.add_oid_option

  opts.on('-r', '--roles [ROLE(s)]', 'Comma separated list of affiliation roles, default is Alumni') do |v|
    opts.options[:roles] = v.split(',')
  end

  opts.require_option(:roles)
end

require File.expand_path('../config/environment', __dir__)

auth_client = AuthClient.create_client_with_app_creds # default is csv_importer

roles = AuthDB::Role.where(organization_id: SCRIPT_OPTIONS[:oid], name: SCRIPT_OPTIONS[:roles]).to_a
affiliation_roles = AuthDB::AffiliationRole.where(role_id: roles.map(&:id)).includes(:affiliation).to_a

affiliation_roles.each do |affiliation_role|
  affiliation_role.affiliation # affiliation to delete
end
