#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_run_remote
  opts.add_oid_option

  opts.on('-r', '--role ROLE', 'Role to update remote ID') do |v|
    opts[:role] = v
  end

  opts.on('--update', 'Update affiliation remote_user_id when not set') do
    opts[:update_affiliation] = true
  end
end

require File.expand_path('../config/environment', __dir__)

def auth_client
  @auth_client ||= AuthClient.create_client_with_app_creds
end

def matching_email?(affiliation)
  return false unless affiliation.contact

  affiliation.contact.emails.any? { |email| email.email.downcase == affiliation.user.email.downcase }
end

def find_matching_contact(affiliation)
  ContactDB::Identity.where(oid: affiliation.organization_id, type: 1, value: affiliation.user.email).first.try(:contact)
end

def user_email_matches_contact_email(affiliation)
  return nil unless affiliation.contact

  matching_email?(affiliation) ? 'Y' : 'N'
end

def matching_remote_ids(affiliation)
  return nil unless affiliation.remote_user_id
  return nil unless affiliation.contact

  affiliation.remote_user_id == affiliation.contact.remote_id ? 'Y' : 'N'
end

def recommended_remote_user_id(affiliation, matching_contact)
  return nil if matching_remote_ids(affiliation) == 'Y'
  return nil if affiliation.contact.nil? && matching_contact.nil?

  affiliation.contact.try(&:remote_id) || matching_contact.try(:remote_id)
end

org = AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])

# initially sort roles by name
roles_sort_order = org.roles.sort_by(&:name).map(&:id)

# now move GivingTree User to the top
if (user_role = org.roles.detect { |r| r.name == 'GivingTree User' })
  roles_sort_order.delete(user_role.id)
  roles_sort_order.unshift(user_role.id)
end

# now move GivingTree Owner to the top
if (owner_role = org.roles.detect { |r| r.name == 'GivingTree Owner' })
  roles_sort_order.delete(owner_role.id)
  roles_sort_order.unshift(owner_role.id)
end

CSV.open("affiliations-#{org.slug}-#{org.id}-remote-ids-report.csv", 'wb') do |csv|
  csv << [
    'oid',
    'name',
    'user.email',
    'user.id',
    'affiliation.id',
    'affiliation.remote_user_id',
    'affiliation.contact_id',
    'contact.primay_email',
    'contact.remote_id',
    'user_email_matches_contact_email',
    'matching_remote_ids',
    'existing_contact_with_matching_email.id',
    'existing_contact_with_matching_email.remote_id',
    'affiliation.roles',
    'recommended_remote_user_id'
  ]

  affiliations = AuthDB::Affiliation
    .includes(:user, affiliation_roles: [:role], contact: [:identities, :emails], organization: [:roles])
    .where(organization_id: SCRIPT_OPTIONS[:oid])

  if SCRIPT_OPTIONS[:role]
    affiliations = affiliations
                     .joins(affiliation_roles: [:role])
                     .where(role: {name: SCRIPT_OPTIONS[:role]})
  end

  affiliations = affiliations.to_a

  affiliations.sort_by! do |a|
    if matching_remote_ids(a) == 'N'
      0
    elsif user_email_matches_contact_email(a) == 'N'
      1
    else
      if a.affiliation_roles.size == 0
        100
      else
        a.affiliation_roles.map { |ar| roles_sort_order.index(ar.role_id) }.min + 2
      end
    end
  end

  affiliations.each do |affiliation|

    # skip alumni users
    next if affiliation.alumni?

    # skip deleted users
    next if affiliation.user.deleted?

    matching_contact =
      if !matching_email?(affiliation)
        find_matching_contact(affiliation)
      else
        nil
      end

    csv << [
      affiliation.organization_id,
      affiliation.user.name,
      affiliation.user.email,
      affiliation.user.id,
      affiliation.id,
      affiliation.remote_user_id,
      affiliation.contact_id,
      affiliation.try(:contact).try(:primary_email),
      affiliation.try(:contact).try(:remote_id),
      user_email_matches_contact_email(affiliation),
      matching_remote_ids(affiliation),
      matching_contact.try(:id),
      matching_contact.try(:remote_id),
      affiliation.affiliation_roles.map { |ar| ar.role.name }.join(', '),
      recommended_remote_user_id(affiliation, matching_contact)
    ]

    next unless SCRIPT_OPTIONS[:update_affiliation]
    next if affiliation.remote_user_id
    next unless (new_remote_user_id = recommended_remote_user_id(affiliation, matching_contact))

    LOG.info("update remote_user_id for affiliation #{affiliation.id} setting to #{new_remote_user_id}")

    auth_client.update_affiliation(
      affiliation.organization_id,
      affiliation.id,
      {
        contact_id: affiliation.contact_id,
        match_remote_id: 'true',
        role_ids: affiliation.affiliation_roles.map(&:role_id),
        skip_roles_overwrite: true
      }
    )
  end
end
