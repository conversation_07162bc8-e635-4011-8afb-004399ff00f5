#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
end

require File.expand_path('../config/environment', __dir__)

def org
  @org ||= AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])
end

AuthDB::Affiliation
class AuthDB::Affiliation
  def name
    user.name
  end

  def email
    user.email
  end

  def saml_user_id
    user.saml_user_id
  end

  def roles
    affiliation_roles.map do |affiliation_role|
      affiliation_role.role.name
    end
  end

  def display_roles
    @display_roles ||= roles.join(', ')
  end

  def deleted?
    user.deleted?
  end

  def super_user?
    user.super_user?
  end
end

affiliations = org.affiliations.includes(:user, affiliation_roles: [:role]).to_a
affiliations.reject!(&:alumni?)
affiliations.reject!(&:deleted?)
affiliations.reject!(&:super_user?)

headings = {
  'user_id' => :user_id,
  'remote_user_id' => :remote_user_id,
  'name' => :name,
  'email' => :email,
  'saml_user_id' => :saml_user_id,
  'roles' => :display_roles
}
table = TableView.new(headings, row_type: :object)
table.render('Users' => affiliations)
