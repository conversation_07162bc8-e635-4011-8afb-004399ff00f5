#!/usr/bin/env ruby

require File.expand_path('../config/environment', __dir__)

sql = <<-S<PERSON>
    select distinct u.id AS user_id, a.id AS affiliation_id, a.organization_id, GROUP_CONCAT(r.name SEPARATOR ',') AS role_names from users u
    inner join affiliations a on a.user_id = u.id
    inner join affiliation_roles ar on ar.affiliation_id = a.id
    inner join roles r on r.id = ar.role_id
    where u.id not in (
        select distinct u.id from users u
        inner join affiliations a on a.user_id = u.id
        inner join affiliation_roles ar on ar.affiliation_id = a.id
        inner join roles r on r.id = ar.role_id
        where r.name in ('Console User', 'GivingTree', 'GivingTree Owner', 'GivingTree User', 'Volunteer', 'Volunteer Lead')
    )
    and u.email not like '%evertrue.com'
    and (a.contact_id is not null or a.remote_user_id is not null)
    group by u.id, a.id, a.organization_id
SQL

res = AuthDB::Affiliation.connection.select_all(sql).rows.map do |user_id, affiliation_id, organization_id, role_names|
    [Inte<PERSON>(user_id), <PERSON><PERSON><PERSON>(affiliation_id), <PERSON><PERSON><PERSON>(organization_id), role_names]
end

count = res.size

LOG.info "------------------------------------------------------------"
LOG.info "orphaning #{count} community-only users from their contacts "
LOG.info "------------------------------------------------------------"

res.each do |r|
    begin
        LOG.info "orphaning contact for user_id=#{r[0]}; affiliation_id=#{r[1]}; organization_id=#{r[2]}; role_names=#{r[3]}"

        a = AuthDB::Affiliation.find(r[1])
        a.update!(remote_user_id: nil, contact_id: nil)
    rescue Exception => e
        LOG.error "exception occured. error was: #{e}"
        LOG.error e.backtrace
    end
end

LOG.info "------------------------------------------------------------"
LOG.info "done!"
LOG.info "------------------------------------------------------------"

