#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option

  opts.on('-p', '--path TABLEAU_DASHBOARD_PATH', 'Tableau dashboard path') do |v|
    opts[:path] = v
  end

  opts.require_option(:path)
end

require_relative '../../config/environment'

def org
  @org ||= AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])
end

def tableau_dashboard
  @tableau_dashboard ||= AuthDB::TableauDashboard.where(path: SCRIPT_OPTIONS[:path]).first
end

def auth_client
  @auth_client ||= AuthClient.create_client_with_app_creds
end

unless tableau_dashboard
  $stderr.puts "no Tableau dashboard found for path #{SCRIPT_OPTIONS[:path]}"
  exit 1
end

auth_client.delete_tableau_dashboard_exclusion(org.id, tableau_dashboard.path)

print JSON.pretty_generate(JSON.parse(auth_client.get_tableau_dashboard_exclusions(org.id).body)) + "\n"
