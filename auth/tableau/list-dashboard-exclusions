#!/usr/bin/env ruby

require_relative '../../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.add_oid_option
end

require_relative '../../config/environment'

def org
  @org ||= AuthDB::Organization.find(SCRIPT_OPTIONS[:oid])
end

def auth_client
  @auth_client ||= AuthClient.create_client_with_app_creds
end

print JSON.pretty_generate(JSON.parse(auth_client.get_tableau_dashboard_exclusions(org.id).body)) + "\n"
