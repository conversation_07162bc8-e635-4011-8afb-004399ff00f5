#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)
require 'csv'
require 'active_record'

ActiveRecord::Base.logger = LOG

class AuthDB::Affiliation
  include CSVUtils::CSVRow

  belongs_to :contact, class_name: 'ContactDB::Contact'
  has_many :contact_identities, class_name: 'ContactDB::Identity', primary_key: :contact_id, foreign_key: :contact_id

  csv_column :affiliation_id, method: :id
  csv_column :oid, method: :organization_id
  csv_column :org_status
  csv_column :user_id
  csv_column :user_status
  csv_column :contact_id, header: 'affiliation.contact_id'
  csv_column :contact_status
  csv_column :remote_user_id
  csv_column :remote_user_id_matches_contact_identity
  csv_column :contact_identity_oid
  csv_column :contact_identity_remote_id
  csv_column :contact_identity_user_id
  csv_column('remote_id_identity.remote_id') { remote_id_identity.try(:value) }
  csv_column('remote_id_identity.contact_id') { remote_id_identity.try(:contact_id) }

  def remote_ids
    return nil unless remote_user_id

    @remote_ids ||=
      if remote_user_id =~ /^0/
        [remote_user_id, remote_user_id.sub(/^0+/, '')]
      else
        [remote_user_id]
      end
  end

  def remote_id_identity
    unless defined?(@remote_id_identity)
      @remote_id_identity = remote_ids ? ContactDB::Identity.where(oid: organization_id, type: 1, value: remote_ids).first : nil
    end
    @remote_id_identity
  end

  def org_status
    if organization.try(:deleted)
      'ORG_DELETED'
    elsif organization
      'ORG_ACTIVE'
    else
      'ORG_MISSING'
    end
  end

  def user_status
    if user_id.nil?
      'USER_NOT_SET'
    elsif user.try(:deleted)
      'USER_DELETED'
    elsif user
      'USER_ACTIVE'
    else
      'USER_MISSING'
    end
  end

  def contact_status
    if contact_id.nil?
      'CONTACT_NOT_SET'
    elsif contact.try(:deleted)
      'CONTACT_DELETED'
    elsif contact
      'CONTACT_ACTIVE'
    else
      'CONTACT_MISSING'
    end
  end

  def contact_identity_oid
    contact_identities.first.try(:oid)
  end

  def contact_identity_remote_id
    contact_identities.detect { |i| i.type == 1 }.try(:value)
  end

  def contact_identity_user_id
    contact_identities.detect { |i| i.type == 4 }.try(:value)
  end

  def remote_user_id_matches_contact_identity
    return unless remote_user_id
    return unless contact_id

    if contact_identity_remote_id.nil?
      'UNABLE_TO_MATCH_MISSING_IDENTITY'
    elsif remote_user_id == contact_identity_remote_id
      'REMOTE_USER_ID_MATCHES_CONTACT'
    elsif remote_user_id.sub(/^0+/, '') == contact_identity_remote_id.sub(/^0+/, '')
      'REMOTE_USER_ID_MATCHES_CONTACT_WITH_ZEROS_REMOVED'
    else
      'REMOTE_USER_ID_DOES_NOT_MATCHES_CONTACT'
    end
  end
end

csv_file = TMP_DIR.join('affiliations-and-identities.csv').to_s

CSVUtils::CSVReport.new(csv_file, AuthDB::Affiliation) do |report|
  AuthDB::Affiliation.includes(:user, :contact_identities, :organization, :contact).find_each do  |affiliation|
    report << affiliation
  end
end

puts csv_file
