#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)

database_name = ARGV[0]
raise "unknown database #{database_name}" unless DynamicActiveModelGenerator::DATABASES.include?(database_name)

database = DynamicActiveModelGenerator.create_database_models(database_name)

# https://sequelize.org/master/manual/model-basics.html#data-types

def get_column_type(column)
  case column.type
  when nil,
       ''
    column.sql_type_metadata.sql_type
  else
    column.type
  end
end

def sequelize_type(column)
  case get_column_type(column)
  when :integer
    column.limit == 8 ? 'Sequelize.BIGINT' : 'Sequelize.INTEGER'
  when :string,
       :boolean,
       :float,
       :double
    'Sequelize.' + column.type.to_s.upcase
  when :text
      "Sequelize.TEXT(#{column.limit})"
  when :datetime
    'Sequelize.DATE'
  when :date
    'Sequelize.DATEONLY'
  when :decimal
    if column.precision && column.scale
      "Sequelize.DECIMAL(#{column.precision}, #{column.scale})"
    else
      'Sequelize.DECIMAL'
    end
  when :binary
    'Sequelize.STRING.BINARY'
  when 'multipolygon'
    'Sequelize.GEOMETRY'
  else
    raise("unhandled column type #{column.inspect} for field #{column.name}")
  end
end

def is_primary_key(model, column)
  model.primary_key == column.name
end

def sequelize_field_definition(model)
  model.columns.map do |column|
    {
      name: column.name,
      type: sequelize_type(column),
      primary: is_primary_key(model, column)
    }
  end
end

def generate_sequelize_table_definition(model)
  definition = sequelize_field_definition(model)

  str = "  db.define('#{model.table_name}', {\n"
  
  str += sequelize_field_definition(model).map do |info|
    "    #{info[:name]}: { type: #{info[:type]}" + (info[:primary] ? ", primaryKey: true" : '') + ' }'
  end.join(",\n")

  str += "\n  }, {\n    timestamps: false,\n    underscored: true,\n    freezeTableName: true\n  });"

  str
end

def generate_sequelize_database_setup(database_name, pool_size = 20)
<<STR
import Sequelize from 'sequelize';

export default secret => {
  const dbHost = secret['#{database_name}.db.host'],
    dbUser = secret['#{database_name}.db.user'],
    dbPass = secret['#{database_name}.db.pass'];

  const db = new Sequelize('#{database_name}', dbUser, dbPass, {
    host: dbHost,
    dialect: 'mysql',
    pool: {
      max: #{pool_size}
    },
    logging: false
  });
STR
end

def generate_sequelize_export(models)
  
  str = "  return {\n"
  str += models.map do |model|
    "    #{model.table_name.classify}: db.models.#{model.table_name}"
  end.join(",\n")
  str += "\n  };\n};\n"
  str
end

def schema_type(column)
  type =
    case get_column_type(column)
    when :integer
      column.limit == 8 ? 'BigInt' : 'Int'
    when :string,
         :text,
         :binary
      'String'
    when :boolean
      'Boolean'
    when :float,
         :double,
         :decimal
      'Number'
    when :datetime
      'DateTime'
    when :date
      'Date'
    when 'multipolygon'
      'DataTypes.GEOMETRY'
    else
      raise("unhandled column type #{column.type} for field #{column.name}")
    end

  column.null ? type : "#{type}!"
end

def schema_fields(model, ignore_columns)
  model.columns.reject { |c| ignore_columns.include?(c.name) }.map do |column|
    {
      name: column.name,
      type: schema_type(column)
    }
  end
end

def generate_schema(model, ignore_columns)
  classified_table_name = model.table_name.classify

  str = "const #{classified_table_name}TC = schemaComposer.createObjectTC({\n  name: '#{classified_table_name}',\n  fields: {\n"
  str += schema_fields(model, ignore_columns).map { |info| "    #{info[:name]}: '#{info[:type]}'" }.join(",\n")
  str += "\n  }\n});"
  str
end

models = database.models.reject { |m| m.table_name =~ /^lhma/ || m.table_name == 'schema_migrations' || m.table_name == 'ar_internal_metadata' }

dir = BASE_DIR.join('graphql-server', 'models', database_name)
FileUtils.mkdir_p(dir) unless File.exist?(dir)

File.open("#{dir}/db.js", 'wb') do |f|
  f.write(generate_sequelize_database_setup(database_name) + "\n")
  f.write(models.map { |model| generate_sequelize_table_definition(model) }.join("\n\n") + "\n\n")
  f.write(generate_sequelize_export(models))
end

File.open("#{dir}/schema.js", 'wb') do |f|
  f.write "import { schemaComposer } from 'graphql-compose';\n\n"
  f.write(models.map { |model| generate_schema(model, ['private', 'update_source', 'privatized_source']) }.join("\n\n") + "\n\n")
end
