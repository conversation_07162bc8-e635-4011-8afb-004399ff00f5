import Sequelize from 'sequelize';

export default secret => {
  const dbHost = secret['enrichment.db.host'],
    dbUser = secret['enrichment.db.user'],
    dbPass = secret['enrichment.db.pass'];

  const db = new Sequelize('enrichment', dbUser, dbPass, {
    host: dbHost,
    dialect: 'mysql',
    pool: {
      max: 20
    },
    logging: false
  });

  db.define('address', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    type: { type: Sequelize.STRING },
    address_1: { type: Sequelize.STRING },
    address_2: { type: Sequelize.STRING },
    address_3: { type: Sequelize.STRING },
    city: { type: Sequelize.STRING },
    state_prov: { type: Sequelize.STRING },
    country: { type: Sequelize.STRING },
    postal_code: { type: Sequelize.STRING },
    weight: { type: Sequelize.INTEGER },
    confidence_score: { type: Sequelize.INTEGER },
    comparison_hash: { type: Sequelize.STRING },
    source: { type: Sequelize.STRING },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('company', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    name: { type: Sequelize.STRING },
    function: { type: Sequelize.STRING },
    source: { type: Sequelize.STRING },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('contact_interest', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    interest_id: { type: Sequelize.INTEGER },
    source: { type: Sequelize.STRING },
    weight: { type: Sequelize.INTEGER },
    confidence_score: { type: Sequelize.INTEGER },
    comparison_hash: { type: Sequelize.STRING },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('contact_location', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    location_normalized_id: { type: Sequelize.INTEGER },
    source: { type: Sequelize.STRING },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('email', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    email: { type: Sequelize.STRING },
    type: { type: Sequelize.STRING },
    source: { type: Sequelize.INTEGER },
    weight: { type: Sequelize.INTEGER },
    confidence_score: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('employment', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    title: { type: Sequelize.TEXT(65535) },
    company_name: { type: Sequelize.TEXT(65535) },
    company_id: { type: Sequelize.INTEGER },
    weight: { type: Sequelize.INTEGER },
    confidence_score: { type: Sequelize.INTEGER },
    source: { type: Sequelize.STRING },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('enrichment', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    oid: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('enrichment_attributes', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    name_full: { type: Sequelize.STRING },
    name_prefix: { type: Sequelize.STRING },
    name_first: { type: Sequelize.STRING },
    name_nick: { type: Sequelize.STRING },
    name_middle: { type: Sequelize.STRING },
    name_last: { type: Sequelize.STRING },
    name_suffix: { type: Sequelize.STRING },
    name_maiden: { type: Sequelize.STRING },
    age: { type: Sequelize.INTEGER },
    age_range_lower: { type: Sequelize.INTEGER },
    age_range_upper: { type: Sequelize.INTEGER },
    deceased: { type: Sequelize.BOOLEAN },
    gender: { type: Sequelize.STRING },
    ethnicity: { type: Sequelize.STRING },
    primary_profile_image: { type: Sequelize.STRING },
    location_general: { type: Sequelize.TEXT(65535) },
    weight: { type: Sequelize.INTEGER },
    confidence_score: { type: Sequelize.INTEGER },
    comparison_hash: { type: Sequelize.STRING },
    source: { type: Sequelize.STRING },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('interest', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    name: { type: Sequelize.STRING },
    source: { type: Sequelize.STRING },
    remote_id: { type: Sequelize.STRING },
    lft: { type: Sequelize.INTEGER },
    rgt: { type: Sequelize.INTEGER },
    depth: { type: Sequelize.INTEGER },
    children_count: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE },
    type: { type: Sequelize.STRING }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('interest_parent', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    interest_id: { type: Sequelize.INTEGER },
    parent_interest_id: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('location_mapping', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    location_raw: { type: Sequelize.TEXT(65535) },
    location_raw_md5: { type: Sequelize.STRING },
    location_normalized_id: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('location_normalized', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    city: { type: Sequelize.STRING },
    state_code: { type: Sequelize.STRING },
    state_full: { type: Sequelize.STRING },
    metro_area: { type: Sequelize.STRING },
    country: { type: Sequelize.STRING },
    country_abbrev: { type: Sequelize.STRING },
    location_md5: { type: Sequelize.STRING },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('phone', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    phone: { type: Sequelize.STRING },
    type: { type: Sequelize.STRING },
    weight: { type: Sequelize.INTEGER },
    confidence_score: { type: Sequelize.INTEGER },
    source: { type: Sequelize.STRING },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('remote_source_identity', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    value: { type: Sequelize.STRING },
    oid: { type: Sequelize.INTEGER },
    contact_id: { type: Sequelize.BIGINT },
    source: { type: Sequelize.STRING },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('social_profile', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    type: { type: Sequelize.STRING },
    username: { type: Sequelize.TEXT(65535) },
    bio: { type: Sequelize.TEXT(65535) },
    url: { type: Sequelize.TEXT(65535) },
    image_url: { type: Sequelize.TEXT(65535) },
    followers: { type: Sequelize.INTEGER },
    following: { type: Sequelize.INTEGER },
    weight: { type: Sequelize.INTEGER },
    confidence_score: { type: Sequelize.INTEGER },
    source: { type: Sequelize.STRING },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('wealth_attributes', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    net_worth: { type: Sequelize.BIGINT },
    household_income: { type: Sequelize.BIGINT },
    rating: { type: Sequelize.STRING },
    source: { type: Sequelize.STRING },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE },
    net_worth_last_updated: { type: Sequelize.DATEONLY }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('wealth_indicator', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    type: { type: Sequelize.STRING },
    source: { type: Sequelize.STRING },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('wealth_life_event', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    type: { type: Sequelize.STRING },
    source: { type: Sequelize.STRING },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('wealth_physical_asset', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    type: { type: Sequelize.STRING },
    source: { type: Sequelize.STRING },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  return {
    Address: db.models.address,
    Company: db.models.company,
    ContactInterest: db.models.contact_interest,
    ContactLocation: db.models.contact_location,
    Email: db.models.email,
    Employment: db.models.employment,
    Enrichment: db.models.enrichment,
    EnrichmentAttribute: db.models.enrichment_attributes,
    Interest: db.models.interest,
    InterestParent: db.models.interest_parent,
    LocationMapping: db.models.location_mapping,
    LocationNormalized: db.models.location_normalized,
    Phone: db.models.phone,
    RemoteSourceIdentity: db.models.remote_source_identity,
    SocialProfile: db.models.social_profile,
    WealthAttribute: db.models.wealth_attributes,
    WealthIndicator: db.models.wealth_indicator,
    WealthLifeEvent: db.models.wealth_life_event,
    WealthPhysicalAsset: db.models.wealth_physical_asset
  };
};
