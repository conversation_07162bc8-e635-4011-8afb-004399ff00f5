import { schemaComposer } from 'graphql-compose';

const AddressTC = schemaComposer.createObjectTC({
  name: 'Address',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    type: 'String',
    address_1: 'String',
    address_2: 'String',
    address_3: 'String',
    city: 'String',
    state_prov: 'String',
    country: 'String',
    postal_code: 'String',
    weight: 'Int',
    confidence_score: 'Int',
    comparison_hash: 'String!',
    source: 'String!',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const CompanyTC = schemaComposer.createObjectTC({
  name: 'Company',
  fields: {
    id: 'BigInt!',
    name: 'String!',
    function: 'String',
    source: 'String!',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const ContactInterestTC = schemaComposer.createObjectTC({
  name: 'ContactInterest',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    interest_id: 'Int!',
    source: 'String!',
    weight: 'Int',
    confidence_score: 'Int',
    comparison_hash: 'String!',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const ContactLocationTC = schemaComposer.createObjectTC({
  name: 'ContactLocation',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    location_normalized_id: 'Int',
    source: 'String',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const EmailTC = schemaComposer.createObjectTC({
  name: 'Email',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    email: 'String!',
    type: 'String',
    source: 'Int!',
    weight: 'Int',
    confidence_score: 'Int',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const EmploymentTC = schemaComposer.createObjectTC({
  name: 'Employment',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    title: 'String',
    company_name: 'String',
    company_id: 'Int',
    weight: 'Int',
    confidence_score: 'Int',
    source: 'String!',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const EnrichmentTC = schemaComposer.createObjectTC({
  name: 'Enrichment',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    oid: 'Int!',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const EnrichmentAttributeTC = schemaComposer.createObjectTC({
  name: 'EnrichmentAttribute',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    name_full: 'String',
    name_prefix: 'String',
    name_first: 'String',
    name_nick: 'String',
    name_middle: 'String',
    name_last: 'String',
    name_suffix: 'String',
    name_maiden: 'String',
    age: 'Int',
    age_range_lower: 'Int',
    age_range_upper: 'Int',
    deceased: 'Boolean',
    gender: 'String',
    ethnicity: 'String',
    primary_profile_image: 'String',
    location_general: 'String',
    weight: 'Int',
    confidence_score: 'Int',
    comparison_hash: 'String!',
    source: 'String!',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const InterestTC = schemaComposer.createObjectTC({
  name: 'Interest',
  fields: {
    id: 'BigInt!',
    name: 'String!',
    source: 'String!',
    remote_id: 'String!',
    lft: 'Int',
    rgt: 'Int',
    depth: 'Int!',
    children_count: 'Int!',
    created_at: 'DateTime!',
    updated_at: 'DateTime!',
    type: 'String'
  }
});

const InterestParentTC = schemaComposer.createObjectTC({
  name: 'InterestParent',
  fields: {
    id: 'BigInt!',
    interest_id: 'Int',
    parent_interest_id: 'Int',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const LocationMappingTC = schemaComposer.createObjectTC({
  name: 'LocationMapping',
  fields: {
    id: 'BigInt!',
    location_raw: 'String',
    location_raw_md5: 'String',
    location_normalized_id: 'Int',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const LocationNormalizedTC = schemaComposer.createObjectTC({
  name: 'LocationNormalized',
  fields: {
    id: 'BigInt!',
    city: 'String',
    state_code: 'String',
    state_full: 'String',
    metro_area: 'String',
    country: 'String',
    country_abbrev: 'String',
    location_md5: 'String',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const PhoneTC = schemaComposer.createObjectTC({
  name: 'Phone',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    phone: 'String!',
    type: 'String',
    weight: 'Int',
    confidence_score: 'Int',
    source: 'String!',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const RemoteSourceIdentityTC = schemaComposer.createObjectTC({
  name: 'RemoteSourceIdentity',
  fields: {
    id: 'BigInt!',
    value: 'String',
    oid: 'Int',
    contact_id: 'BigInt',
    source: 'String',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const SocialProfileTC = schemaComposer.createObjectTC({
  name: 'SocialProfile',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    type: 'String!',
    username: 'String',
    bio: 'String',
    url: 'String',
    image_url: 'String',
    followers: 'Int',
    following: 'Int',
    weight: 'Int',
    confidence_score: 'Int',
    source: 'String!',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const WealthAttributeTC = schemaComposer.createObjectTC({
  name: 'WealthAttribute',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    net_worth: 'BigInt',
    household_income: 'BigInt',
    rating: 'String',
    source: 'String',
    created_at: 'DateTime!',
    updated_at: 'DateTime!',
    net_worth_last_updated: 'Date'
  }
});

const WealthIndicatorTC = schemaComposer.createObjectTC({
  name: 'WealthIndicator',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    type: 'String',
    source: 'String',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const WealthLifeEventTC = schemaComposer.createObjectTC({
  name: 'WealthLifeEvent',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    type: 'String',
    source: 'String',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const WealthPhysicalAssetTC = schemaComposer.createObjectTC({
  name: 'WealthPhysicalAsset',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    type: 'String',
    source: 'String',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

