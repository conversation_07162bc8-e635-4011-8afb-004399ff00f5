import { schemaComposer } from 'graphql-compose';

const ContactJourneyTC = schemaComposer.createObjectTC({
  name: 'ContactJourney',
  fields: {
    id: 'BigInt!',
    contact_id: 'Int!',
    user_id: 'Int!',
    active: 'Boolean',
    start_date: 'DateTime!',
    end_date: 'DateTime',
    journey_id: 'Int!',
    creator_user_id: 'Int!',
    updater_user_id: 'Int!',
    created_at: 'DateTime!',
    updated_at: 'DateTime'
  }
});

const JourneyTaskTC = schemaComposer.createObjectTC({
  name: 'JourneyTask',
  fields: {
    id: 'BigInt!',
    journey_id: 'BigInt!',
    task_template_id: 'BigInt!',
    step_number: 'Int!',
    day_number: 'Int!',
    active: 'Boolean!',
    description: 'String',
    creator_user_id: 'Int!',
    updater_user_id: 'Int',
    created_at: 'DateTime!',
    updated_at: 'DateTime'
  }
});

const JourneyTC = schemaComposer.createObjectTC({
  name: 'Journey',
  fields: {
    id: 'BigInt!',
    oid: 'Int',
    name: 'String!',
    journey_type: 'String!',
    duration_in_days: 'Int!',
    number_of_steps: 'Int!',
    structure_description: 'String',
    objective_description: 'String',
    success_description: 'String',
    active: 'Int!',
    comments: 'String',
    creator_user_id: 'BigInt!',
    updater_user_id: 'BigInt',
    created_at: 'DateTime!',
    updated_at: 'DateTime'
  }
});

const TaskActionTC = schemaComposer.createObjectTC({
  name: 'TaskAction',
  fields: {
    id: 'BigInt!',
    task_id: 'Int!',
    date_occurred: 'DateTime',
    outreach_method: 'String',
    outreach_tech_status: 'String',
    outreach_result: 'String',
    outreach_source: 'String',
    subject: 'String',
    body: 'String',
    transcript: 'String',
    inboud: 'Boolean',
    two_way_communication: 'Boolean',
    duration_in_seconds: 'Int',
    interaction_id: 'Int',
    creator_user_id: 'Int!',
    updater_user_id: 'Int!',
    created_at: 'DateTime!',
    updated_at: 'DateTime'
  }
});

const TaskTemplateTC = schemaComposer.createObjectTC({
  name: 'TaskTemplate',
  fields: {
    id: 'BigInt!',
    oid: 'Int',
    title: 'String!',
    description: 'String',
    outreach: 'Boolean!',
    active: 'Boolean!',
    comments: 'String',
    creator_user_id: 'Int!',
    updater_user_id: 'Int',
    created_at: 'DateTime!',
    updated_at: 'DateTime'
  }
});

const TaskTC = schemaComposer.createObjectTC({
  name: 'Task',
  fields: {
    id: 'BigInt!',
    oid: 'Int!',
    owner_user_id: 'BigInt!',
    target_contact_id: 'BigInt',
    action_type: 'String!',
    title: 'String!',
    description: 'String',
    due_date: 'DateTime',
    completed_at: 'DateTime',
    deleted_at: 'DateTime',
    creator_user_id: 'BigInt!',
    updater_user_id: 'BigInt',
    created_at: 'DateTime!',
    updated_at: 'DateTime',
    contact_journey_id: 'Int',
    journey_task_id: 'Int',
    status: 'String',
    sentiment: 'String',
    comments: 'String',
    task_template_id: 'Int'
  }
});

