import Sequelize from 'sequelize';

export default secret => {
  const dbHost = secret['journeys.db.host'],
    dbUser = secret['journeys.db.user'],
    dbPass = secret['journeys.db.pass'];

  const db = new Sequelize('journeys', dbUser, dbPass, {
    host: dbHost,
    dialect: 'mysql',
    pool: {
      max: 20
    },
    logging: false
  });

  db.define('contact_journeys', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.INTEGER },
    user_id: { type: Sequelize.INTEGER },
    active: { type: Sequelize.BOOLEAN },
    start_date: { type: Sequelize.DATE },
    end_date: { type: Sequelize.DATE },
    journey_id: { type: Sequelize.INTEGER },
    creator_user_id: { type: Sequelize.INTEGER },
    updater_user_id: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('journey_tasks', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    journey_id: { type: Sequelize.BIGINT },
    task_template_id: { type: Sequelize.BIGINT },
    step_number: { type: Sequelize.INTEGER },
    day_number: { type: Sequelize.INTEGER },
    active: { type: Sequelize.BOOLEAN },
    description: { type: Sequelize.STRING },
    creator_user_id: { type: Sequelize.INTEGER },
    updater_user_id: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('journeys', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    oid: { type: Sequelize.INTEGER },
    name: { type: Sequelize.STRING },
    journey_type: { type: Sequelize.STRING },
    duration_in_days: { type: Sequelize.INTEGER },
    number_of_steps: { type: Sequelize.INTEGER },
    structure_description: { type: Sequelize.STRING },
    objective_description: { type: Sequelize.STRING },
    success_description: { type: Sequelize.STRING },
    active: { type: Sequelize.INTEGER },
    comments: { type: Sequelize.STRING },
    creator_user_id: { type: Sequelize.BIGINT },
    updater_user_id: { type: Sequelize.BIGINT },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('task_actions', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    task_id: { type: Sequelize.INTEGER },
    date_occurred: { type: Sequelize.DATE },
    outreach_method: { type: Sequelize.STRING },
    outreach_tech_status: { type: Sequelize.STRING },
    outreach_result: { type: Sequelize.STRING },
    outreach_source: { type: Sequelize.STRING },
    subject: { type: Sequelize.STRING },
    body: { type: Sequelize.STRING },
    transcript: { type: Sequelize.STRING },
    inboud: { type: Sequelize.BOOLEAN },
    two_way_communication: { type: Sequelize.BOOLEAN },
    duration_in_seconds: { type: Sequelize.INTEGER },
    interaction_id: { type: Sequelize.INTEGER },
    creator_user_id: { type: Sequelize.INTEGER },
    updater_user_id: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('task_templates', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    oid: { type: Sequelize.INTEGER },
    title: { type: Sequelize.STRING },
    description: { type: Sequelize.STRING },
    outreach: { type: Sequelize.BOOLEAN },
    active: { type: Sequelize.BOOLEAN },
    comments: { type: Sequelize.STRING },
    creator_user_id: { type: Sequelize.INTEGER },
    updater_user_id: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('tasks', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    oid: { type: Sequelize.INTEGER },
    owner_user_id: { type: Sequelize.BIGINT },
    target_contact_id: { type: Sequelize.BIGINT },
    action_type: { type: Sequelize.STRING },
    title: { type: Sequelize.STRING },
    description: { type: Sequelize.STRING },
    due_date: { type: Sequelize.DATE },
    completed_at: { type: Sequelize.DATE },
    deleted_at: { type: Sequelize.DATE },
    creator_user_id: { type: Sequelize.BIGINT },
    updater_user_id: { type: Sequelize.BIGINT },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE },
    contact_journey_id: { type: Sequelize.INTEGER },
    journey_task_id: { type: Sequelize.INTEGER },
    status: { type: Sequelize.STRING },
    sentiment: { type: Sequelize.STRING },
    comments: { type: Sequelize.STRING },
    task_template_id: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  return {
    ContactJourney: db.models.contact_journeys,
    JourneyTask: db.models.journey_tasks,
    Journey: db.models.journeys,
    TaskAction: db.models.task_actions,
    TaskTemplate: db.models.task_templates,
    Task: db.models.tasks
  };
};
