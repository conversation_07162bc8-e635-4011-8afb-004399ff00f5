import Sequelize from 'sequelize';

export default secret => {
  const dbHost = secret['ugc.db.host'],
    dbUser = secret['ugc.db.user'],
    dbPass = secret['ugc.db.pass'];

  const db = new Sequelize('ugc', dbUser, dbPass, {
    host: dbHost,
    dialect: 'mysql',
    pool: {
      max: 20
    },
    logging: false
  });

  db.define('interaction_contact_mentions', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    interaction_id: { type: Sequelize.BIGINT },
    alias: { type: Sequelize.STRING },
    contact_id: { type: Sequelize.BIGINT },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('interaction_custom_field_metadata', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    oid: { type: Sequelize.INTEGER },
    data_type: { type: Sequelize.STRING },
    ui_control_type: { type: Sequelize.STRING },
    required: { type: Sequelize.BOOLEAN },
    default_value: { type: Sequelize.STRING },
    display_name: { type: Sequelize.STRING },
    es_field: { type: Sequelize.STRING },
    deleted: { type: Sequelize.BOOLEAN },
    sort_order: { type: Sequelize.INTEGER },
    active: { type: Sequelize.BOOLEAN },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('interaction_custom_field_values', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    oid: { type: Sequelize.INTEGER },
    interaction_custom_field_id: { type: Sequelize.BIGINT },
    value: { type: Sequelize.STRING },
    sort_order: { type: Sequelize.INTEGER },
    active: { type: Sequelize.BOOLEAN },
    deleted: { type: Sequelize.BOOLEAN },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('interaction_custom_fields', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    interaction_id: { type: Sequelize.BIGINT },
    interaction_custom_field_id: { type: Sequelize.BIGINT },
    value: { type: Sequelize.TEXT(65535) },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('interaction_labels', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    interaction_id: { type: Sequelize.BIGINT },
    name: { type: Sequelize.STRING },
    value: { type: Sequelize.TEXT(65535) },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('interaction_solicitors', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    interaction_id: { type: Sequelize.BIGINT },
    remote_user_id: { type: Sequelize.STRING },
    name: { type: Sequelize.STRING },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('interaction_targets', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    interaction_id: { type: Sequelize.BIGINT },
    target_id: { type: Sequelize.BIGINT },
    target_type: { type: Sequelize.INTEGER },
    remote_id: { type: Sequelize.STRING },
    primary: { type: Sequelize.BOOLEAN },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('interaction_types', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    oid: { type: Sequelize.INTEGER },
    type: { type: Sequelize.STRING },
    is_visit_type: { type: Sequelize.BOOLEAN },
    sort_order: { type: Sequelize.INTEGER },
    active: { type: Sequelize.BOOLEAN },
    deleted: { type: Sequelize.BOOLEAN },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('interaction_user_mentions', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    interaction_id: { type: Sequelize.BIGINT },
    alias: { type: Sequelize.STRING },
    user_id: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('interactions', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    oid: { type: Sequelize.INTEGER },
    remote_id: { type: Sequelize.STRING },
    creator_user_id: { type: Sequelize.INTEGER },
    updating_user_id: { type: Sequelize.INTEGER },
    date_occurred: { type: Sequelize.BIGINT },
    text: { type: Sequelize.TEXT(65535) },
    summary: { type: Sequelize.TEXT(65535) },
    interaction_type: { type: Sequelize.STRING },
    deleted: { type: Sequelize.BOOLEAN },
    trip_id: { type: Sequelize.BIGINT },
    proposal_id: { type: Sequelize.BIGINT },
    meeting_id: { type: Sequelize.BIGINT },
    application: { type: Sequelize.INTEGER },
    author_name: { type: Sequelize.STRING },
    author_user_id: { type: Sequelize.INTEGER },
    author_remote_user_id: { type: Sequelize.STRING },
    category: { type: Sequelize.INTEGER },
    update_source: { type: Sequelize.INTEGER },
    creation_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('proposal', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    oid: { type: Sequelize.INTEGER },
    remote_id: { type: Sequelize.STRING },
    title: { type: Sequelize.STRING },
    description: { type: Sequelize.TEXT(65535) },
    status: { type: Sequelize.STRING },
    status_start_date: { type: Sequelize.BIGINT },
    created_date: { type: Sequelize.BIGINT },
    original_ask_amount: { type: Sequelize.DECIMAL(13, 2) },
    original_ask_date: { type: Sequelize.BIGINT },
    ask_amount: { type: Sequelize.DECIMAL(13, 2) },
    ask_date: { type: Sequelize.BIGINT },
    expected_amount: { type: Sequelize.DECIMAL(13, 2) },
    expected_date: { type: Sequelize.BIGINT },
    funded_amount: { type: Sequelize.DECIMAL(13, 2) },
    funded_date: { type: Sequelize.BIGINT },
    active: { type: Sequelize.INTEGER },
    deleted: { type: Sequelize.INTEGER },
    creator_user_id: { type: Sequelize.INTEGER },
    updating_user_id: { type: Sequelize.INTEGER },
    update_source: { type: Sequelize.STRING },
    creation_source: { type: Sequelize.STRING },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('proposal_contact', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    proposal_id: { type: Sequelize.BIGINT },
    type: { type: Sequelize.STRING },
    user_id: { type: Sequelize.BIGINT },
    contact_id: { type: Sequelize.BIGINT },
    remote_user_id: { type: Sequelize.STRING },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('proposal_custom_field', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    proposal_id: { type: Sequelize.BIGINT },
    proposal_custom_field_property_id: { type: Sequelize.BIGINT },
    value: { type: Sequelize.TEXT(65535) },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('proposal_custom_field_property', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    oid: { type: Sequelize.INTEGER },
    data_type: { type: Sequelize.INTEGER },
    ui_control_type: { type: Sequelize.INTEGER },
    required: { type: Sequelize.BOOLEAN },
    default_value: { type: Sequelize.STRING },
    display_name: { type: Sequelize.STRING },
    deleted: { type: Sequelize.BOOLEAN },
    sort_order: { type: Sequelize.INTEGER },
    active: { type: Sequelize.BOOLEAN },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('proposal_custom_field_property_picklist_values', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    proposal_custom_field_property_id: { type: Sequelize.BIGINT },
    value: { type: Sequelize.STRING },
    sort_order: { type: Sequelize.INTEGER },
    active: { type: Sequelize.BOOLEAN },
    deleted: { type: Sequelize.BOOLEAN },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('proposal_label', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    proposal_id: { type: Sequelize.BIGINT },
    name: { type: Sequelize.STRING },
    value: { type: Sequelize.TEXT(65535) },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('proposal_solicitor', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    proposal_id: { type: Sequelize.BIGINT },
    type: { type: Sequelize.STRING },
    remote_user_id: { type: Sequelize.STRING },
    user_id: { type: Sequelize.INTEGER },
    contact_id: { type: Sequelize.BIGINT },
    name: { type: Sequelize.STRING },
    amount: { type: Sequelize.DECIMAL(13, 2) },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('proposal_stage', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    oid: { type: Sequelize.INTEGER },
    stage_group_id: { type: Sequelize.INTEGER },
    stage_name: { type: Sequelize.STRING },
    sort_order: { type: Sequelize.INTEGER },
    active: { type: Sequelize.BOOLEAN },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('proposal_stage_group', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    oid: { type: Sequelize.INTEGER },
    group_name: { type: Sequelize.STRING },
    default: { type: Sequelize.BOOLEAN },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('task_reminders', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    oid: { type: Sequelize.INTEGER },
    send_email: { type: Sequelize.BOOLEAN },
    send_push: { type: Sequelize.BOOLEAN },
    remind_at: { type: Sequelize.DATE },
    task_id: { type: Sequelize.BIGINT },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE },
    app_id: { type: Sequelize.INTEGER },
    reminder_sent: { type: Sequelize.BOOLEAN }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('tasks', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    oid: { type: Sequelize.INTEGER },
    summary: { type: Sequelize.TEXT(65535) },
    description: { type: Sequelize.TEXT(65535) },
    completed: { type: Sequelize.BOOLEAN },
    due_date: { type: Sequelize.DATE },
    contact_id: { type: Sequelize.BIGINT },
    interaction_id: { type: Sequelize.BIGINT },
    trip_id: { type: Sequelize.INTEGER },
    assignee_user_id: { type: Sequelize.INTEGER },
    app_id: { type: Sequelize.INTEGER },
    created_by_user_id: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('ugc_custom_fields', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    oid: { type: Sequelize.INTEGER },
    ugc_type: { type: Sequelize.INTEGER },
    data_type: { type: Sequelize.INTEGER },
    ui_control_type: { type: Sequelize.INTEGER },
    required: { type: Sequelize.BOOLEAN },
    default_value: { type: Sequelize.STRING },
    display_name: { type: Sequelize.STRING },
    deleted: { type: Sequelize.BOOLEAN },
    sort_order: { type: Sequelize.INTEGER },
    active: { type: Sequelize.BOOLEAN },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('ugc_custom_field_values', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    ugc_custom_field_id: { type: Sequelize.BIGINT },
    value: { type: Sequelize.STRING },
    sort_order: { type: Sequelize.INTEGER },
    active: { type: Sequelize.BOOLEAN },
    deleted: { type: Sequelize.BOOLEAN },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('ugc_types', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    oid: { type: Sequelize.INTEGER },
    type: { type: Sequelize.STRING },
    data_type: { type: Sequelize.INTEGER },
    sort_order: { type: Sequelize.INTEGER },
    active: { type: Sequelize.BOOLEAN },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('ugc_type_categories', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    ugc_type_id: { type: Sequelize.INTEGER },
    category: { type: Sequelize.INTEGER },
    display_name: { type: Sequelize.STRING },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  return {
    AuditProposal: db.models.audit_proposal,
    AuditProposalContact: db.models.audit_proposal_contact,
    AuditProposalCustomField: db.models.audit_proposal_custom_field,
    AuditProposalLabel: db.models.audit_proposal_label,
    AuditProposalSolicitor: db.models.audit_proposal_solicitor,
    InteractionContactMention: db.models.interaction_contact_mentions,
    InteractionCustomFieldMetadatum: db.models.interaction_custom_field_metadata,
    InteractionCustomFieldValue: db.models.interaction_custom_field_values,
    InteractionCustomField: db.models.interaction_custom_fields,
    InteractionLabel: db.models.interaction_labels,
    InteractionSolicitor: db.models.interaction_solicitors,
    InteractionTarget: db.models.interaction_targets,
    InteractionType: db.models.interaction_types,
    InteractionUserMention: db.models.interaction_user_mentions,
    Interaction: db.models.interactions,
    Proposal: db.models.proposal,
    ProposalContact: db.models.proposal_contact,
    ProposalCustomField: db.models.proposal_custom_field,
    ProposalCustomFieldProperty: db.models.proposal_custom_field_property,
    ProposalCustomFieldPropertyPicklistValue: db.models.proposal_custom_field_property_picklist_values,
    ProposalLabel: db.models.proposal_label,
    ProposalMigrationTracking: db.models.proposal_migration_tracking,
    ProposalSolicitor: db.models.proposal_solicitor,
    ProposalStage: db.models.proposal_stage,
    ProposalStageGroup: db.models.proposal_stage_group,
    TaskReminder: db.models.task_reminders,
    Task: db.models.tasks,
    UgcCustomField: db.models.ugc_custom_fields,
    UgcCustomFieldValue: db.models.ugc_custom_field_values,
    UgcType: db.models.ugc_types,
    UgcTypeCategory: db.models.ugc_type_categories
  };
};
