import { schemaComposer } from 'graphql-compose';

const AuditProposalTC = schemaComposer.createObjectTC({
  name: 'AuditProposal',
  fields: {
    id: 'BigInt!',
    proposal_id: 'BigInt!',
    oid: 'Int!',
    remote_id: 'String',
    title: 'String',
    description: 'String',
    status: 'String',
    status_start_date: 'BigInt',
    created_date: 'BigInt',
    original_ask_amount: 'Number',
    original_ask_date: 'BigInt',
    ask_amount: 'Number',
    ask_date: 'BigInt',
    expected_amount: 'Number',
    expected_date: 'BigInt',
    funded_amount: 'Number',
    funded_date: 'BigInt',
    active: 'Int',
    deleted: 'Int',
    creator_user_id: 'Int',
    updating_user_id: 'Int',
    creation_source: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt',
    audit_action: 'Int!',
    audit_user_id: 'Int!',
    audited_at: 'BigInt!'
  }
});

const AuditProposalContactTC = schemaComposer.createObjectTC({
  name: 'AuditProposalContact',
  fields: {
    id: 'BigInt!',
    audit_id: 'BigInt!',
    proposal_id: 'BigInt!',
    type: 'String',
    user_id: 'BigInt!',
    contact_id: 'BigInt',
    remote_user_id: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt'
  }
});

const AuditProposalCustomFieldTC = schemaComposer.createObjectTC({
  name: 'AuditProposalCustomField',
  fields: {
    id: 'BigInt!',
    audit_id: 'BigInt!',
    proposal_id: 'BigInt!',
    proposal_custom_field_property_id: 'BigInt!',
    value: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const AuditProposalLabelTC = schemaComposer.createObjectTC({
  name: 'AuditProposalLabel',
  fields: {
    id: 'BigInt!',
    audit_id: 'BigInt!',
    proposal_id: 'BigInt!',
    name: 'String',
    value: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt'
  }
});

const AuditProposalSolicitorTC = schemaComposer.createObjectTC({
  name: 'AuditProposalSolicitor',
  fields: {
    id: 'BigInt!',
    audit_id: 'BigInt!',
    proposal_id: 'BigInt!',
    type: 'String',
    remote_user_id: 'String',
    user_id: 'Int',
    contact_id: 'BigInt',
    name: 'String',
    amount: 'Number',
    created_at: 'BigInt!',
    updated_at: 'BigInt'
  }
});

const InteractionContactMentionTC = schemaComposer.createObjectTC({
  name: 'InteractionContactMention',
  fields: {
    id: 'BigInt!',
    interaction_id: 'BigInt!',
    alias: 'String!',
    contact_id: 'BigInt!',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const InteractionCustomFieldMetadatumTC = schemaComposer.createObjectTC({
  name: 'InteractionCustomFieldMetadatum',
  fields: {
    id: 'BigInt!',
    oid: 'Int',
    data_type: 'String',
    ui_control_type: 'String',
    required: 'Boolean!',
    default_value: 'String',
    display_name: 'String',
    es_field: 'String',
    deleted: 'Boolean',
    sort_order: 'Int',
    active: 'Boolean',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const InteractionCustomFieldValueTC = schemaComposer.createObjectTC({
  name: 'InteractionCustomFieldValue',
  fields: {
    id: 'BigInt!',
    oid: 'Int',
    interaction_custom_field_id: 'BigInt',
    value: 'String',
    sort_order: 'Int',
    active: 'Boolean',
    deleted: 'Boolean',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const InteractionCustomFieldTC = schemaComposer.createObjectTC({
  name: 'InteractionCustomField',
  fields: {
    id: 'BigInt!',
    interaction_id: 'BigInt!',
    interaction_custom_field_id: 'BigInt!',
    value: 'String',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const InteractionLabelTC = schemaComposer.createObjectTC({
  name: 'InteractionLabel',
  fields: {
    id: 'BigInt!',
    interaction_id: 'BigInt!',
    name: 'String',
    value: 'String',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const InteractionSolicitorTC = schemaComposer.createObjectTC({
  name: 'InteractionSolicitor',
  fields: {
    id: 'BigInt!',
    interaction_id: 'BigInt!',
    remote_user_id: 'String',
    name: 'String!',
    user_id: 'Int',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const InteractionTargetTC = schemaComposer.createObjectTC({
  name: 'InteractionTarget',
  fields: {
    id: 'BigInt!',
    interaction_id: 'BigInt!',
    target_id: 'BigInt!',
    target_type: 'Int!',
    remote_id: 'String',
    primary: 'Boolean',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const InteractionTypeTC = schemaComposer.createObjectTC({
  name: 'InteractionType',
  fields: {
    id: 'BigInt!',
    oid: 'Int',
    type: 'String',
    is_visit_type: 'Boolean',
    sort_order: 'Int',
    active: 'Boolean',
    deleted: 'Boolean',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const InteractionUserMentionTC = schemaComposer.createObjectTC({
  name: 'InteractionUserMention',
  fields: {
    id: 'BigInt!',
    interaction_id: 'BigInt!',
    alias: 'String!',
    user_id: 'Int!',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const InteractionTC = schemaComposer.createObjectTC({
  name: 'Interaction',
  fields: {
    id: 'BigInt!',
    oid: 'Int!',
    remote_id: 'String',
    creator_user_id: 'Int',
    updating_user_id: 'Int',
    date_occurred: 'BigInt!',
    text: 'String',
    summary: 'String',
    interaction_type: 'String!',
    deleted: 'Boolean',
    trip_id: 'BigInt',
    proposal_id: 'BigInt',
    meeting_id: 'BigInt',
    application: 'Int',
    author_name: 'String',
    author_user_id: 'Int',
    author_remote_user_id: 'String',
    category: 'Int',
    creation_source: 'Int',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const ProposalTC = schemaComposer.createObjectTC({
  name: 'Proposal',
  fields: {
    id: 'BigInt!',
    oid: 'Int!',
    remote_id: 'String',
    title: 'String',
    description: 'String',
    status: 'String',
    status_start_date: 'BigInt',
    created_date: 'BigInt',
    original_ask_amount: 'Number',
    original_ask_date: 'BigInt',
    ask_amount: 'Number',
    ask_date: 'BigInt',
    expected_amount: 'Number',
    expected_date: 'BigInt',
    funded_amount: 'Number',
    funded_date: 'BigInt',
    active: 'Int',
    deleted: 'Int',
    creator_user_id: 'Int',
    updating_user_id: 'Int',
    creation_source: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt'
  }
});

const ProposalContactTC = schemaComposer.createObjectTC({
  name: 'ProposalContact',
  fields: {
    id: 'BigInt!',
    proposal_id: 'BigInt!',
    type: 'String',
    user_id: 'BigInt!',
    contact_id: 'BigInt',
    remote_user_id: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt'
  }
});

const ProposalCustomFieldTC = schemaComposer.createObjectTC({
  name: 'ProposalCustomField',
  fields: {
    id: 'BigInt!',
    proposal_id: 'BigInt!',
    proposal_custom_field_property_id: 'BigInt!',
    value: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const ProposalCustomFieldPropertyTC = schemaComposer.createObjectTC({
  name: 'ProposalCustomFieldProperty',
  fields: {
    id: 'BigInt!',
    oid: 'Int',
    data_type: 'Int!',
    ui_control_type: 'Int',
    required: 'Boolean!',
    default_value: 'String',
    display_name: 'String!',
    deleted: 'Boolean',
    sort_order: 'Int!',
    active: 'Boolean',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const ProposalCustomFieldPropertyPicklistValueTC = schemaComposer.createObjectTC({
  name: 'ProposalCustomFieldPropertyPicklistValue',
  fields: {
    id: 'BigInt!',
    proposal_custom_field_property_id: 'BigInt',
    value: 'String',
    sort_order: 'Int',
    active: 'Boolean',
    deleted: 'Boolean',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const ProposalLabelTC = schemaComposer.createObjectTC({
  name: 'ProposalLabel',
  fields: {
    id: 'BigInt!',
    proposal_id: 'BigInt!',
    name: 'String',
    value: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt'
  }
});

const ProposalMigrationTrackingTC = schemaComposer.createObjectTC({
  name: 'ProposalMigrationTracking',
  fields: {
    id: 'BigInt!',
    oid: 'Int!',
    proposal_id: 'BigInt',
    contact_id: 'BigInt',
    status: 'String',
    error_message: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt'
  }
});

const ProposalSolicitorTC = schemaComposer.createObjectTC({
  name: 'ProposalSolicitor',
  fields: {
    id: 'BigInt!',
    proposal_id: 'BigInt!',
    type: 'String',
    remote_user_id: 'String',
    user_id: 'Int',
    contact_id: 'BigInt',
    name: 'String',
    amount: 'Number',
    created_at: 'BigInt!',
    updated_at: 'BigInt'
  }
});

const ProposalStageTC = schemaComposer.createObjectTC({
  name: 'ProposalStage',
  fields: {
    id: 'BigInt!',
    oid: 'Int!',
    stage_group_id: 'Int!',
    stage_name: 'String!',
    sort_order: 'Int!',
    active: 'Boolean!',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const ProposalStageGroupTC = schemaComposer.createObjectTC({
  name: 'ProposalStageGroup',
  fields: {
    id: 'BigInt!',
    oid: 'Int!',
    group_name: 'String!',
    default: 'Boolean!',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const TaskReminderTC = schemaComposer.createObjectTC({
  name: 'TaskReminder',
  fields: {
    id: 'BigInt!',
    oid: 'Int!',
    send_email: 'Boolean!',
    send_push: 'Boolean!',
    remind_at: 'DateTime!',
    task_id: 'BigInt!',
    created_at: 'DateTime!',
    updated_at: 'DateTime!',
    app_id: 'Int!',
    reminder_sent: 'Boolean!'
  }
});

const TaskTC = schemaComposer.createObjectTC({
  name: 'Task',
  fields: {
    id: 'BigInt!',
    oid: 'Int!',
    summary: 'String!',
    description: 'String',
    completed: 'Boolean!',
    due_date: 'DateTime',
    contact_id: 'BigInt',
    interaction_id: 'BigInt',
    trip_id: 'Int',
    assignee_user_id: 'Int!',
    app_id: 'Int!',
    created_by_user_id: 'Int!',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const UgcTypeTC = schemaComposer.createObjectTC({
  name: 'UgcType',
  fields: {
    id: 'BigInt!',
    oid: 'Int',
    type: 'String',
    data_type: 'Int',
    sort_order: 'Int',
    active: 'Boolean',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const UgcTypeCategoryTC = schemaComposer.createObjectTC({
  name: 'UgcTypeCategory',
  fields: {
    id: 'BigInt!',
    ugc_type_id: 'Int',
    category: 'Int',
    display_name: 'String',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});
