import { schemaComposer } from 'graphql-compose';

const STATEAkTC = schemaComposer.createObjectTC({
  name: 'STATEAk',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEAlTC = schemaComposer.createObjectTC({
  name: 'STATEAl',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEArTC = schemaComposer.createObjectTC({
  name: 'STATEAr',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEAzTC = schemaComposer.createObjectTC({
  name: 'STATEAz',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATECaTC = schemaComposer.createObjectTC({
  name: 'STATECa',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATECoTC = schemaComposer.createObjectTC({
  name: 'STATECo',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATECtTC = schemaComposer.createObjectTC({
  name: 'STATECt',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEDcTC = schemaComposer.createObjectTC({
  name: 'STATEDc',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEDeTC = schemaComposer.createObjectTC({
  name: 'STATEDe',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEFlTC = schemaComposer.createObjectTC({
  name: 'STATEFl',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEGaTC = schemaComposer.createObjectTC({
  name: 'STATEGa',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEHiTC = schemaComposer.createObjectTC({
  name: 'STATEHi',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEIumTC = schemaComposer.createObjectTC({
  name: 'STATEIum',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEIdTC = schemaComposer.createObjectTC({
  name: 'STATEId',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEIlTC = schemaComposer.createObjectTC({
  name: 'STATEIl',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEInTC = schemaComposer.createObjectTC({
  name: 'STATEIn',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEKTC = schemaComposer.createObjectTC({
  name: 'STATEK',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEKyTC = schemaComposer.createObjectTC({
  name: 'STATEKy',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATELaTC = schemaComposer.createObjectTC({
  name: 'STATELa',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEMaTC = schemaComposer.createObjectTC({
  name: 'STATEMa',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEMdTC = schemaComposer.createObjectTC({
  name: 'STATEMd',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEMeTC = schemaComposer.createObjectTC({
  name: 'STATEMe',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEMiTC = schemaComposer.createObjectTC({
  name: 'STATEMi',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEMnTC = schemaComposer.createObjectTC({
  name: 'STATEMn',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEMoTC = schemaComposer.createObjectTC({
  name: 'STATEMo',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEMTC = schemaComposer.createObjectTC({
  name: 'STATEM',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEMtTC = schemaComposer.createObjectTC({
  name: 'STATEMt',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATENcTC = schemaComposer.createObjectTC({
  name: 'STATENc',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATENdTC = schemaComposer.createObjectTC({
  name: 'STATENd',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATENeTC = schemaComposer.createObjectTC({
  name: 'STATENe',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATENhTC = schemaComposer.createObjectTC({
  name: 'STATENh',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATENjTC = schemaComposer.createObjectTC({
  name: 'STATENj',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATENmTC = schemaComposer.createObjectTC({
  name: 'STATENm',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATENvTC = schemaComposer.createObjectTC({
  name: 'STATENv',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATENyTC = schemaComposer.createObjectTC({
  name: 'STATENy',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEOhTC = schemaComposer.createObjectTC({
  name: 'STATEOh',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEOkTC = schemaComposer.createObjectTC({
  name: 'STATEOk',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEOrTC = schemaComposer.createObjectTC({
  name: 'STATEOr',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEPaTC = schemaComposer.createObjectTC({
  name: 'STATEPa',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATERiTC = schemaComposer.createObjectTC({
  name: 'STATERi',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEScTC = schemaComposer.createObjectTC({
  name: 'STATESc',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATESdTC = schemaComposer.createObjectTC({
  name: 'STATESd',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATETnTC = schemaComposer.createObjectTC({
  name: 'STATETn',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATETxTC = schemaComposer.createObjectTC({
  name: 'STATETx',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEUtTC = schemaComposer.createObjectTC({
  name: 'STATEUt',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEVaTC = schemaComposer.createObjectTC({
  name: 'STATEVa',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEVtTC = schemaComposer.createObjectTC({
  name: 'STATEVt',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEWaTC = schemaComposer.createObjectTC({
  name: 'STATEWa',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEWiTC = schemaComposer.createObjectTC({
  name: 'STATEWi',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEWvTC = schemaComposer.createObjectTC({
  name: 'STATEWv',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const STATEWyTC = schemaComposer.createObjectTC({
  name: 'STATEWy',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

const UNITEDStateTC = schemaComposer.createObjectTC({
  name: 'UNITEDState',
  fields: {
    id: 'String!',
    geom: 'DataTypes.GEOMETRY!',
    B25076_001E: 'Int',
    B25077_001E: 'Int',
    B25078_001E: 'Int',
    B19013_001E: 'Int'
  }
});

