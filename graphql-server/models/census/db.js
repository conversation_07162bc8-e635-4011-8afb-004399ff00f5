import Sequelize from 'sequelize';

export default secret => {
  const dbHost = secret['census.db.host'],
    dbUser = secret['census.db.user'],
    dbPass = secret['census.db.pass'];

  const db = new Sequelize('census', dbUser, dbPass, {
    host: dbHost,
    dialect: 'mysql',
    pool: {
      max: 20
    },
    logging: false
  });

  db.define('STATE_AK', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_AL', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_AR', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_AZ', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_CA', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_CO', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_CT', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_DC', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_DE', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_FL', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_GA', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_HI', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_IA', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_ID', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_IL', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_IN', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_KS', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_KY', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_LA', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_MA', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_MD', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_ME', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_MI', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_MN', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_MO', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_MS', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_MT', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_NC', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_ND', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_NE', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_NH', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_NJ', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_NM', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_NV', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_NY', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_OH', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_OK', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_OR', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_PA', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_RI', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_SC', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_SD', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_TN', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_TX', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_UT', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_VA', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_VT', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_WA', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_WI', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_WV', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('STATE_WY', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('UNITED_STATES', {
    id: { type: Sequelize.STRING, primaryKey: true },
    geom: { type: Sequelize.GEOMETRY },
    B25076_001E: { type: Sequelize.INTEGER },
    B25077_001E: { type: Sequelize.INTEGER },
    B25078_001E: { type: Sequelize.INTEGER },
    B19013_001E: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  return {
    STATEAk: db.models.STATE_AK,
    STATEAl: db.models.STATE_AL,
    STATEAr: db.models.STATE_AR,
    STATEAz: db.models.STATE_AZ,
    STATECa: db.models.STATE_CA,
    STATECo: db.models.STATE_CO,
    STATECt: db.models.STATE_CT,
    STATEDc: db.models.STATE_DC,
    STATEDe: db.models.STATE_DE,
    STATEFl: db.models.STATE_FL,
    STATEGa: db.models.STATE_GA,
    STATEHi: db.models.STATE_HI,
    STATEIum: db.models.STATE_IA,
    STATEId: db.models.STATE_ID,
    STATEIl: db.models.STATE_IL,
    STATEIn: db.models.STATE_IN,
    STATEK: db.models.STATE_KS,
    STATEKy: db.models.STATE_KY,
    STATELa: db.models.STATE_LA,
    STATEMa: db.models.STATE_MA,
    STATEMd: db.models.STATE_MD,
    STATEMe: db.models.STATE_ME,
    STATEMi: db.models.STATE_MI,
    STATEMn: db.models.STATE_MN,
    STATEMo: db.models.STATE_MO,
    STATEM: db.models.STATE_MS,
    STATEMt: db.models.STATE_MT,
    STATENc: db.models.STATE_NC,
    STATENd: db.models.STATE_ND,
    STATENe: db.models.STATE_NE,
    STATENh: db.models.STATE_NH,
    STATENj: db.models.STATE_NJ,
    STATENm: db.models.STATE_NM,
    STATENv: db.models.STATE_NV,
    STATENy: db.models.STATE_NY,
    STATEOh: db.models.STATE_OH,
    STATEOk: db.models.STATE_OK,
    STATEOr: db.models.STATE_OR,
    STATEPa: db.models.STATE_PA,
    STATERi: db.models.STATE_RI,
    STATESc: db.models.STATE_SC,
    STATESd: db.models.STATE_SD,
    STATETn: db.models.STATE_TN,
    STATETx: db.models.STATE_TX,
    STATEUt: db.models.STATE_UT,
    STATEVa: db.models.STATE_VA,
    STATEVt: db.models.STATE_VT,
    STATEWa: db.models.STATE_WA,
    STATEWi: db.models.STATE_WI,
    STATEWv: db.models.STATE_WV,
    STATEWy: db.models.STATE_WY,
    UNITEDState: db.models.UNITED_STATES
  };
};
