import { schemaComposer } from 'graphql-compose';

const AffiliationAttributeTC = schemaComposer.createObjectTC({
  name: 'AffiliationAttribute',
  fields: {
    id: 'Int!',
    affiliation_id: 'Int',
    school_division_department_id: 'Int',
    title: 'String',
    persona: 'String',
    seniority: 'String',
    user_profile_picture_url: 'String',
    user_profile_picture_source: 'String',
    user_profile_picture_last_updated: 'DateTime',
    nps_score: 'Int',
    nps_score_date: 'Date',
    created_at: 'DateTime',
    updated_at: 'DateTime'
  }
});

const AffiliationInvitationTC = schemaComposer.createObjectTC({
  name: 'AffiliationInvitation',
  fields: {
    id: 'Int!',
    organization_id: 'Int',
    invited_by: 'Int',
    application_id: 'Int',
    name: 'String',
    email: 'String',
    role_ids: 'String',
    contact_id: 'String',
    affiliation_id: 'Int',
    title: 'String',
    persona: 'String',
    seniority: 'String',
    school_division_department_id: 'Int',
    token: 'String',
    lazy_token: 'String',
    ems_message_id: 'Int',
    accepted_at: 'DateTime',
    invite_email_sent_at: 'DateTime',
    created_at: 'DateTime',
    updated_at: 'DateTime',
    saml_user_id: 'String',
    user_id: 'Int'
  }
});

const AffiliationRequestTC = schemaComposer.createObjectTC({
  name: 'AffiliationRequest',
  fields: {
    id: 'Int!',
    organization_id: 'Int',
    user_id: 'Int',
    data: 'String',
    affiliation_id: 'Int',
    approved: 'Boolean',
    moderated_by: 'Int',
    moderated_at: 'DateTime',
    created_at: 'DateTime',
    updated_at: 'DateTime',
    pending_email_sent_at: 'DateTime',
    approved_email_sent_at: 'DateTime'
  }
});

const AffiliationRoleTC = schemaComposer.createObjectTC({
  name: 'AffiliationRole',
  fields: {
    id: 'Int!',
    affiliation_id: 'Int!',
    role_id: 'Int!',
    creator_user_id: 'Int',
    created_at: 'DateTime',
    updated_at: 'DateTime'
  }
});

const AffiliationTC = schemaComposer.createObjectTC({
  name: 'Affiliation',
  fields: {
    id: 'Int!',
    user_id: 'Int',
    organization_id: 'Int',
    remote_user_id: 'String',
    contact_id: 'Int',
    legacy_user_id: 'Int',
    created_at: 'DateTime',
    updated_at: 'DateTime'
  }
});

const ApplicationTC = schemaComposer.createObjectTC({
  name: 'Application',
  fields: {
    id: 'Int!',
    name: 'String',
    super_user_id: 'Int',
    key: 'String',
    token: 'String',
    lazy_token: 'String',
    admin_only: 'Boolean',
    is_linkedin_required: 'Boolean',
    allow_unconfirmed_users: 'Boolean',
    allow_delete_orgs: 'Boolean',
    session_ttl_minutes: 'Int',
    created_at: 'DateTime',
    updated_at: 'DateTime'
  }
});

const ArchivedSessionTC = schemaComposer.createObjectTC({
  name: 'ArchivedSession',
  fields: {
    id: 'Int!',
    user_id: 'Int',
    prime_token_id: 'Int',
    organization_id: 'Int',
    application_id: 'Int',
    oids: 'String',
    token: 'String',
    lazy_token: 'String',
    strategy: 'String',
    session_type: 'String',
    sudo_mode_until: 'DateTime',
    expire_at: 'DateTime',
    terminated_at: 'DateTime',
    change_notify_queued_at: 'DateTime',
    change_notify_sent_at: 'DateTime',
    created_at: 'DateTime',
    updated_at: 'DateTime'
  }
});

const CsvInviteTC = schemaComposer.createObjectTC({
  name: 'CsvInvite',
  fields: {
    id: 'Int!',
    organization_id: 'Int',
    created_at: 'DateTime',
    updated_at: 'DateTime',
    csv_file_name: 'String',
    csv_content_type: 'String',
    csv_file_size: 'Int',
    csv_updated_at: 'DateTime'
  }
});

const DelayedJobTC = schemaComposer.createObjectTC({
  name: 'DelayedJob',
  fields: {
    id: 'Int!',
    priority: 'Int!',
    attempts: 'Int!',
    handler: 'String!',
    last_error: 'String',
    run_at: 'DateTime',
    locked_at: 'DateTime',
    failed_at: 'DateTime',
    locked_by: 'String',
    queue: 'String',
    created_at: 'DateTime',
    updated_at: 'DateTime'
  }
});

const IdentityTC = schemaComposer.createObjectTC({
  name: 'Identity',
  fields: {
    id: 'Int!',
    identity_schema_id: 'Int!',
    user_id: 'Int!',
    value: 'String',
    hashed_value: 'String',
    proxy_app_id: 'Int',
    created_at: 'DateTime',
    updated_at: 'DateTime'
  }
});

const IdentityProviderTC = schemaComposer.createObjectTC({
  name: 'IdentityProvider',
  fields: {
    id: 'Int!',
    organization_id: 'Int',
    name: 'String',
    primary_domain_suffix: 'String',
    federation_xml: 'String',
    federation_xml_url: 'String',
    created_at: 'DateTime',
    updated_at: 'DateTime'
  }
});

const IdentitySchemaTC = schemaComposer.createObjectTC({
  name: 'IdentitySchema',
  fields: {
    id: 'Int!',
    application_id: 'Int!',
    is_credential: 'Boolean!',
    key: 'String!',
    created_at: 'DateTime',
    updated_at: 'DateTime'
  }
});

const LinkTokenTC = schemaComposer.createObjectTC({
  name: 'LinkToken',
  fields: {
    id: 'Int!',
    user_id: 'Int',
    app_id: 'Int',
    client_app_id: 'Int',
    secret_id: 'String',
    token: 'String',
    expire_at: 'DateTime',
    terminated_at: 'DateTime',
    ems_message_id: 'Int',
    link_token_email_sent_at: 'DateTime',
    created_at: 'DateTime',
    updated_at: 'DateTime'
  }
});

const OrganizationTC = schemaComposer.createObjectTC({
  name: 'Organization',
  fields: {
    id: 'Int!',
    slug: 'String',
    name: 'String',
    test_org: 'Boolean',
    mfa_required: 'Boolean',
    session_ttl_minutes: 'Int',
    prime_token_ttl_hours: 'Int',
    csm_score: 'Int',
    max_contact_count: 'Int',
    sso_method: 'Int',
    deleted: 'Boolean',
    purged_by: 'String',
    purge_checksum: 'String',
    purge_at: 'DateTime',
    created_at: 'DateTime',
    updated_at: 'DateTime'
  }
});

const PasswordPolicyTC = schemaComposer.createObjectTC({
  name: 'PasswordPolicy',
  fields: {
    id: 'Int!',
    organization_id: 'Int!',
    min_length: 'Int!',
    max_length: 'Int',
    include_lowercase_letter: 'Boolean',
    include_uppercase_letter: 'Boolean',
    include_number: 'Boolean',
    include_special_character: 'Boolean',
    max_age_in_days: 'Int',
    max_previous_passwords: 'Int',
    description: 'String',
    created_at: 'DateTime',
    updated_at: 'DateTime'
  }
});

const PhotoTC = schemaComposer.createObjectTC({
  name: 'Photo',
  fields: {
    id: 'Int!',
    affiliation_attribute_id: 'Int',
    created_at: 'DateTime',
    updated_at: 'DateTime',
    photo_file_name: 'String',
    photo_content_type: 'String',
    photo_file_size: 'Int',
    photo_updated_at: 'DateTime'
  }
});

const PreviousPasswordTC = schemaComposer.createObjectTC({
  name: 'PreviousPassword',
  fields: {
    id: 'Int!',
    user_id: 'Int!',
    encrypted_password: 'String!',
    created_at: 'DateTime',
    updated_at: 'DateTime'
  }
});

const PrimeTokenTC = schemaComposer.createObjectTC({
  name: 'PrimeToken',
  fields: {
    id: 'Int!',
    user_id: 'Int',
    device_id: 'String',
    trusted: 'Boolean',
    device_useragent: 'String',
    original_ip: 'String',
    original_ip_location: 'String',
    last_ip: 'String',
    last_ip_location: 'String',
    token: 'String',
    lazy_token: 'String',
    strategy: 'String',
    session_count: 'Int',
    last_session_at: 'DateTime',
    last_confirmed_at: 'DateTime',
    expire_at: 'DateTime',
    terminated_at: 'DateTime',
    created_at: 'DateTime',
    updated_at: 'DateTime'
  }
});

const PurgeOrganizationStepTC = schemaComposer.createObjectTC({
  name: 'PurgeOrganizationStep',
  fields: {
    id: 'Int!',
    purge_organization_id: 'Int',
    step_name: 'String',
    stats: 'String',
    started_at: 'DateTime',
    completed_at: 'DateTime',
    last_error: 'String',
    last_error_at: 'DateTime',
    created_at: 'DateTime',
    updated_at: 'DateTime'
  }
});

const PurgeOrganizationTC = schemaComposer.createObjectTC({
  name: 'PurgeOrganization',
  fields: {
    id: 'Int!',
    organization_id: 'Int!',
    purged_by: 'String',
    purge_at: 'DateTime',
    started_at: 'DateTime',
    completed_at: 'DateTime',
    created_at: 'DateTime',
    updated_at: 'DateTime'
  }
});

const RestrictionTC = schemaComposer.createObjectTC({
  name: 'Restriction',
  fields: {
    id: 'Int!',
    organization_id: 'Int',
    application_id: 'Int',
    type: 'String',
    identity_provider_id: 'Int',
    created_at: 'DateTime',
    updated_at: 'DateTime'
  }
});

const RoleTC = schemaComposer.createObjectTC({
  name: 'Role',
  fields: {
    id: 'Int!',
    name: 'String',
    can_see_private_data: 'Boolean',
    organization_id: 'Int',
    default: 'Boolean',
    remote_id: 'String',
    created_at: 'DateTime',
    updated_at: 'DateTime'
  }
});

const SchoolDivisionDepartmentTC = schemaComposer.createObjectTC({
  name: 'SchoolDivisionDepartment',
  fields: {
    id: 'Int!',
    organization_id: 'Int!',
    value: 'String!',
    created_at: 'DateTime!',
    updated_at: 'DateTime!'
  }
});

const SessionTC = schemaComposer.createObjectTC({
  name: 'Session',
  fields: {
    id: 'Int!',
    user_id: 'Int',
    prime_token_id: 'Int',
    organization_id: 'Int',
    application_id: 'Int',
    oids: 'String',
    token: 'String',
    lazy_token: 'String',
    strategy: 'String',
    session_type: 'String',
    sudo_mode_until: 'DateTime',
    expire_at: 'DateTime',
    terminated_at: 'DateTime',
    created_at: 'DateTime',
    updated_at: 'DateTime',
    change_notify_queued_at: 'DateTime',
    change_notify_sent_at: 'DateTime'
  }
});

const SmsMessageTC = schemaComposer.createObjectTC({
  name: 'SmsMessage',
  fields: {
    id: 'Int!',
    user_id: 'Int',
    prime_token_id: 'Int',
    session_id: 'Int',
    sms_type: 'String',
    to: 'String',
    country_code: 'String',
    carrier: 'String',
    body_checksum: 'String',
    otp_checksum: 'String',
    from: 'String',
    twilio_sid: 'String',
    used_at: 'DateTime',
    created_at: 'DateTime',
    updated_at: 'DateTime'
  }
});

const TwilioReplyTC = schemaComposer.createObjectTC({
  name: 'TwilioReply',
  fields: {
    id: 'Int!',
    from: 'String',
    from_city: 'String',
    from_country: 'String',
    from_state: 'String',
    from_zip: 'String',
    sms_message_sid: 'String',
    sms_sid: 'String',
    message_sid: 'String',
    sms_status: 'String',
    body: 'String',
    num_media: 'String',
    call_sid: 'String',
    caller: 'String',
    caller_city: 'String',
    caller_country: 'String',
    caller_state: 'String',
    caller_zip: 'String',
    call_status: 'String',
    direction: 'String',
    called: 'String',
    called_city: 'String',
    called_country: 'String',
    called_state: 'String',
    called_zip: 'String',
    to: 'String',
    to_city: 'String',
    to_country: 'String',
    to_state: 'String',
    to_zip: 'String',
    account_sid: 'String',
    api_version: 'String',
    application_sid: 'String'
  }
});

const UnmatchedIdentityTC = schemaComposer.createObjectTC({
  name: 'UnmatchedIdentity',
  fields: {
    id: 'Int!',
    unmatched_login_id: 'Int',
    identity_schema_id: 'Int',
    value: 'String'
  }
});

const UnmatchedLoginTC = schemaComposer.createObjectTC({
  name: 'UnmatchedLogin',
  fields: {
    id: 'Int!',
    name: 'String',
    email: 'String',
    avatar: 'String',
    data: 'String',
    application_id: 'Int',
    proxy_app_id: 'Int',
    manual_match_user_id: 'Int',
    created_at: 'DateTime',
    updated_at: 'DateTime'
  }
});

const UserTC = schemaComposer.createObjectTC({
  name: 'User',
  fields: {
    id: 'Int!',
    email: 'String!',
    email_locked: 'Boolean',
    name: 'String',
    test_user: 'Boolean',
    mfa_state: 'String',
    mfa_enabled_at: 'DateTime',
    otp_passcode_sent_at: 'DateTime',
    last_auto_otp_sent_at: 'DateTime',
    otp_prime_token_id: 'Int',
    otp_session_id: 'Int',
    otp_confirmation_sent_at: 'DateTime',
    otp_confirmed_at: 'DateTime',
    otp_resends: 'Int',
    otp_failed_attempts: 'Int',
    otp_locked_until: 'DateTime',
    otp_medium: 'Int!',
    otp_mobile_raw: 'String',
    otp_mobile: 'String',
    otp_country_code: 'String',
    otp_carrier: 'String',
    otp_carrier_network_code: 'String',
    otp_carrier_country_code: 'String',
    otp_secret_key_enc: 'String',
    sign_in_count: 'Int',
    current_sign_in_at: 'DateTime',
    last_sign_in_at: 'DateTime',
    current_sign_in_ip: 'String',
    last_sign_in_ip: 'String',
    first_session_created_at: 'DateTime',
    last_session_created_at: 'DateTime',
    first_affiliation_created_at: 'DateTime',
    final_revoked_date: 'DateTime',
    static_revoked_date: 'Boolean',
    created_at: 'DateTime',
    updated_at: 'DateTime',
    changed_at: 'DateTime',
    migrate_password: 'Boolean',
    password_migrated_at: 'DateTime',
    deleted: 'Boolean',
    super_user: 'Boolean',
    super_admin: 'Boolean',
    can_delete_orgs: 'Boolean',
    encrypted_password: 'String!',
    password_set_at: 'DateTime',
    confirmation_token: 'String',
    confirmed_at: 'DateTime',
    confirmation_sent_at: 'DateTime',
    unconfirmed_email: 'String',
    reset_password_token: 'String',
    reset_password_url: 'String',
    reset_password_sent_at: 'DateTime',
    failed_attempts: 'Int',
    unlock_token: 'String',
    locked_at: 'DateTime',
    registration_app: 'Int',
    last_account_oid: 'Int',
    saml_user_id: 'String'
  }
});

const VersionTC = schemaComposer.createObjectTC({
  name: 'Version',
  fields: {
    id: 'Int!',
    item_type: 'String!',
    item_id: 'Int!',
    event: 'String!',
    whodunnit: 'String',
    oid: 'Int',
    user_affected: 'Int',
    object: 'String',
    object_changes: 'String',
    userchange: 'String',
    app: 'Int',
    auth_provider: 'String',
    ip: 'String',
    user_agent: 'String',
    sqs_msgid: 'String',
    sqs_md5: 'String',
    sqs_message: 'String',
    created_at: 'DateTime',
    change_notify_sent_at: 'DateTime',
    change_notify_job_queued_at: 'DateTime',
    change_notify_attempts: 'Int'
  }
});

