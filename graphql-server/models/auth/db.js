import Sequelize from 'sequelize';

export default secret => {
  const dbHost = secret['auth.db.host'],
    dbUser = secret['auth.db.user'],
    dbPass = secret['auth.db.pass'];

  const db = new Sequelize('auth', dbUser, dbPass, {
    host: dbHost,
    dialect: 'mysql',
    pool: {
      max: 20
    },
    logging: false
  });

  db.define('affiliation_attributes', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    affiliation_id: { type: Sequelize.INTEGER },
    school_division_department_id: { type: Sequelize.INTEGER },
    title: { type: Sequelize.STRING },
    persona: { type: Sequelize.STRING },
    seniority: { type: Sequelize.STRING },
    user_profile_picture_url: { type: Sequelize.TEXT(65535) },
    user_profile_picture_source: { type: Sequelize.TEXT(65535) },
    user_profile_picture_last_updated: { type: Sequelize.DATE },
    nps_score: { type: Sequelize.INTEGER },
    nps_score_date: { type: Sequelize.DATEONLY },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('affiliation_invitations', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    organization_id: { type: Sequelize.INTEGER },
    invited_by: { type: Sequelize.INTEGER },
    application_id: { type: Sequelize.INTEGER },
    name: { type: Sequelize.STRING },
    email: { type: Sequelize.STRING },
    role_ids: { type: Sequelize.STRING },
    contact_id: { type: Sequelize.STRING },
    affiliation_id: { type: Sequelize.INTEGER },
    title: { type: Sequelize.STRING },
    persona: { type: Sequelize.STRING },
    seniority: { type: Sequelize.STRING },
    school_division_department_id: { type: Sequelize.INTEGER },
    token: { type: Sequelize.STRING },
    lazy_token: { type: Sequelize.STRING },
    ems_message_id: { type: Sequelize.INTEGER },
    accepted_at: { type: Sequelize.DATE },
    invite_email_sent_at: { type: Sequelize.DATE },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE },
    saml_user_id: { type: Sequelize.STRING },
    user_id: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('affiliation_requests', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    organization_id: { type: Sequelize.INTEGER },
    user_id: { type: Sequelize.INTEGER },
    data: { type: Sequelize.STRING },
    affiliation_id: { type: Sequelize.INTEGER },
    approved: { type: Sequelize.BOOLEAN },
    moderated_by: { type: Sequelize.INTEGER },
    moderated_at: { type: Sequelize.DATE },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE },
    pending_email_sent_at: { type: Sequelize.DATE },
    approved_email_sent_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('affiliation_roles', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    affiliation_id: { type: Sequelize.INTEGER },
    role_id: { type: Sequelize.INTEGER },
    creator_user_id: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('affiliations', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    user_id: { type: Sequelize.INTEGER },
    organization_id: { type: Sequelize.INTEGER },
    remote_user_id: { type: Sequelize.STRING },
    contact_id: { type: Sequelize.INTEGER },
    legacy_user_id: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('applications', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    name: { type: Sequelize.STRING },
    super_user_id: { type: Sequelize.INTEGER },
    key: { type: Sequelize.STRING },
    token: { type: Sequelize.STRING },
    lazy_token: { type: Sequelize.STRING },
    admin_only: { type: Sequelize.BOOLEAN },
    is_linkedin_required: { type: Sequelize.BOOLEAN },
    allow_unconfirmed_users: { type: Sequelize.BOOLEAN },
    allow_delete_orgs: { type: Sequelize.BOOLEAN },
    session_ttl_minutes: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('archived_sessions', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    user_id: { type: Sequelize.INTEGER },
    prime_token_id: { type: Sequelize.INTEGER },
    organization_id: { type: Sequelize.INTEGER },
    application_id: { type: Sequelize.INTEGER },
    oids: { type: Sequelize.STRING },
    token: { type: Sequelize.STRING },
    lazy_token: { type: Sequelize.STRING },
    strategy: { type: Sequelize.STRING },
    session_type: { type: Sequelize.STRING },
    sudo_mode_until: { type: Sequelize.DATE },
    expire_at: { type: Sequelize.DATE },
    terminated_at: { type: Sequelize.DATE },
    change_notify_queued_at: { type: Sequelize.DATE },
    change_notify_sent_at: { type: Sequelize.DATE },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('csv_invites', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    organization_id: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE },
    csv_file_name: { type: Sequelize.STRING },
    csv_content_type: { type: Sequelize.STRING },
    csv_file_size: { type: Sequelize.INTEGER },
    csv_updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('delayed_jobs', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    priority: { type: Sequelize.INTEGER },
    attempts: { type: Sequelize.INTEGER },
    handler: { type: Sequelize.TEXT(********) },
    last_error: { type: Sequelize.TEXT(********) },
    run_at: { type: Sequelize.DATE },
    locked_at: { type: Sequelize.DATE },
    failed_at: { type: Sequelize.DATE },
    locked_by: { type: Sequelize.STRING },
    queue: { type: Sequelize.STRING },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('identities', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    identity_schema_id: { type: Sequelize.INTEGER },
    user_id: { type: Sequelize.INTEGER },
    value: { type: Sequelize.TEXT(********) },
    hashed_value: { type: Sequelize.STRING },
    proxy_app_id: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('identity_providers', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    organization_id: { type: Sequelize.INTEGER },
    name: { type: Sequelize.STRING },
    primary_domain_suffix: { type: Sequelize.STRING },
    federation_xml: { type: Sequelize.TEXT(65535) },
    federation_xml_url: { type: Sequelize.STRING },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('identity_schemas', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    application_id: { type: Sequelize.INTEGER },
    is_credential: { type: Sequelize.BOOLEAN },
    key: { type: Sequelize.STRING },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('link_tokens', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    user_id: { type: Sequelize.INTEGER },
    app_id: { type: Sequelize.INTEGER },
    client_app_id: { type: Sequelize.INTEGER },
    secret_id: { type: Sequelize.STRING },
    token: { type: Sequelize.STRING },
    expire_at: { type: Sequelize.DATE },
    terminated_at: { type: Sequelize.DATE },
    ems_message_id: { type: Sequelize.INTEGER },
    link_token_email_sent_at: { type: Sequelize.DATE },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('organizations', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    slug: { type: Sequelize.STRING },
    name: { type: Sequelize.STRING },
    test_org: { type: Sequelize.BOOLEAN },
    mfa_required: { type: Sequelize.BOOLEAN },
    session_ttl_minutes: { type: Sequelize.INTEGER },
    prime_token_ttl_hours: { type: Sequelize.INTEGER },
    csm_score: { type: Sequelize.INTEGER },
    max_contact_count: { type: Sequelize.INTEGER },
    sso_method: { type: Sequelize.INTEGER },
    deleted: { type: Sequelize.BOOLEAN },
    purged_by: { type: Sequelize.STRING },
    purge_checksum: { type: Sequelize.STRING },
    purge_at: { type: Sequelize.DATE },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('password_policies', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    organization_id: { type: Sequelize.INTEGER },
    min_length: { type: Sequelize.INTEGER },
    max_length: { type: Sequelize.INTEGER },
    include_lowercase_letter: { type: Sequelize.BOOLEAN },
    include_uppercase_letter: { type: Sequelize.BOOLEAN },
    include_number: { type: Sequelize.BOOLEAN },
    include_special_character: { type: Sequelize.BOOLEAN },
    max_age_in_days: { type: Sequelize.INTEGER },
    max_previous_passwords: { type: Sequelize.INTEGER },
    description: { type: Sequelize.TEXT(65535) },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('photos', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    affiliation_attribute_id: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE },
    photo_file_name: { type: Sequelize.STRING },
    photo_content_type: { type: Sequelize.STRING },
    photo_file_size: { type: Sequelize.INTEGER },
    photo_updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('previous_passwords', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    user_id: { type: Sequelize.INTEGER },
    encrypted_password: { type: Sequelize.STRING },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('prime_tokens', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    user_id: { type: Sequelize.INTEGER },
    device_id: { type: Sequelize.STRING },
    trusted: { type: Sequelize.BOOLEAN },
    device_useragent: { type: Sequelize.STRING },
    original_ip: { type: Sequelize.STRING },
    original_ip_location: { type: Sequelize.STRING },
    last_ip: { type: Sequelize.STRING },
    last_ip_location: { type: Sequelize.STRING },
    token: { type: Sequelize.STRING },
    lazy_token: { type: Sequelize.STRING },
    strategy: { type: Sequelize.STRING },
    session_count: { type: Sequelize.INTEGER },
    last_session_at: { type: Sequelize.DATE },
    last_confirmed_at: { type: Sequelize.DATE },
    expire_at: { type: Sequelize.DATE },
    terminated_at: { type: Sequelize.DATE },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('purge_organization_steps', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    purge_organization_id: { type: Sequelize.INTEGER },
    step_name: { type: Sequelize.STRING },
    stats: { type: Sequelize.TEXT(65535) },
    started_at: { type: Sequelize.DATE },
    completed_at: { type: Sequelize.DATE },
    last_error: { type: Sequelize.TEXT(65535) },
    last_error_at: { type: Sequelize.DATE },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('purge_organizations', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    organization_id: { type: Sequelize.INTEGER },
    purged_by: { type: Sequelize.STRING },
    purge_at: { type: Sequelize.DATE },
    started_at: { type: Sequelize.DATE },
    completed_at: { type: Sequelize.DATE },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('restrictions', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    organization_id: { type: Sequelize.INTEGER },
    application_id: { type: Sequelize.INTEGER },
    type: { type: Sequelize.STRING },
    identity_provider_id: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('roles', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    name: { type: Sequelize.STRING },
    can_see_private_data: { type: Sequelize.BOOLEAN },
    organization_id: { type: Sequelize.INTEGER },
    default: { type: Sequelize.BOOLEAN },
    remote_id: { type: Sequelize.STRING },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('school_division_departments', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    organization_id: { type: Sequelize.INTEGER },
    value: { type: Sequelize.STRING },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('sessions', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    user_id: { type: Sequelize.INTEGER },
    prime_token_id: { type: Sequelize.INTEGER },
    organization_id: { type: Sequelize.INTEGER },
    application_id: { type: Sequelize.INTEGER },
    oids: { type: Sequelize.STRING },
    token: { type: Sequelize.STRING },
    lazy_token: { type: Sequelize.STRING },
    strategy: { type: Sequelize.STRING },
    session_type: { type: Sequelize.STRING },
    sudo_mode_until: { type: Sequelize.DATE },
    expire_at: { type: Sequelize.DATE },
    terminated_at: { type: Sequelize.DATE },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE },
    change_notify_queued_at: { type: Sequelize.DATE },
    change_notify_sent_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('sms_messages', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    user_id: { type: Sequelize.INTEGER },
    prime_token_id: { type: Sequelize.INTEGER },
    session_id: { type: Sequelize.INTEGER },
    sms_type: { type: Sequelize.STRING },
    to: { type: Sequelize.STRING },
    country_code: { type: Sequelize.STRING },
    carrier: { type: Sequelize.STRING },
    body_checksum: { type: Sequelize.STRING },
    otp_checksum: { type: Sequelize.STRING },
    from: { type: Sequelize.STRING },
    twilio_sid: { type: Sequelize.STRING },
    used_at: { type: Sequelize.DATE },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('twilio_replies', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    from: { type: Sequelize.STRING },
    from_city: { type: Sequelize.STRING },
    from_country: { type: Sequelize.STRING },
    from_state: { type: Sequelize.STRING },
    from_zip: { type: Sequelize.STRING },
    sms_message_sid: { type: Sequelize.STRING },
    sms_sid: { type: Sequelize.STRING },
    message_sid: { type: Sequelize.STRING },
    sms_status: { type: Sequelize.STRING },
    body: { type: Sequelize.STRING },
    num_media: { type: Sequelize.STRING },
    call_sid: { type: Sequelize.STRING },
    caller: { type: Sequelize.STRING },
    caller_city: { type: Sequelize.STRING },
    caller_country: { type: Sequelize.STRING },
    caller_state: { type: Sequelize.STRING },
    caller_zip: { type: Sequelize.STRING },
    call_status: { type: Sequelize.STRING },
    direction: { type: Sequelize.STRING },
    called: { type: Sequelize.STRING },
    called_city: { type: Sequelize.STRING },
    called_country: { type: Sequelize.STRING },
    called_state: { type: Sequelize.STRING },
    called_zip: { type: Sequelize.STRING },
    to: { type: Sequelize.STRING },
    to_city: { type: Sequelize.STRING },
    to_country: { type: Sequelize.STRING },
    to_state: { type: Sequelize.STRING },
    to_zip: { type: Sequelize.STRING },
    account_sid: { type: Sequelize.STRING },
    api_version: { type: Sequelize.STRING },
    application_sid: { type: Sequelize.STRING }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('unmatched_identities', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    unmatched_login_id: { type: Sequelize.INTEGER },
    identity_schema_id: { type: Sequelize.INTEGER },
    value: { type: Sequelize.TEXT(65535) }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('unmatched_logins', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    name: { type: Sequelize.STRING },
    email: { type: Sequelize.STRING },
    avatar: { type: Sequelize.STRING },
    data: { type: Sequelize.TEXT(65535) },
    application_id: { type: Sequelize.INTEGER },
    proxy_app_id: { type: Sequelize.INTEGER },
    manual_match_user_id: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('users', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    email: { type: Sequelize.STRING },
    email_locked: { type: Sequelize.BOOLEAN },
    name: { type: Sequelize.STRING },
    test_user: { type: Sequelize.BOOLEAN },
    mfa_state: { type: Sequelize.STRING },
    mfa_enabled_at: { type: Sequelize.DATE },
    otp_passcode_sent_at: { type: Sequelize.DATE },
    last_auto_otp_sent_at: { type: Sequelize.DATE },
    otp_prime_token_id: { type: Sequelize.INTEGER },
    otp_session_id: { type: Sequelize.INTEGER },
    otp_confirmation_sent_at: { type: Sequelize.DATE },
    otp_confirmed_at: { type: Sequelize.DATE },
    otp_resends: { type: Sequelize.INTEGER },
    otp_failed_attempts: { type: Sequelize.INTEGER },
    otp_locked_until: { type: Sequelize.DATE },
    otp_medium: { type: Sequelize.INTEGER },
    otp_mobile_raw: { type: Sequelize.STRING },
    otp_mobile: { type: Sequelize.STRING },
    otp_country_code: { type: Sequelize.STRING },
    otp_carrier: { type: Sequelize.STRING },
    otp_carrier_network_code: { type: Sequelize.STRING },
    otp_carrier_country_code: { type: Sequelize.STRING },
    otp_secret_key_enc: { type: Sequelize.STRING.BINARY },
    sign_in_count: { type: Sequelize.INTEGER },
    current_sign_in_at: { type: Sequelize.DATE },
    last_sign_in_at: { type: Sequelize.DATE },
    current_sign_in_ip: { type: Sequelize.STRING },
    last_sign_in_ip: { type: Sequelize.STRING },
    first_session_created_at: { type: Sequelize.DATE },
    last_session_created_at: { type: Sequelize.DATE },
    first_affiliation_created_at: { type: Sequelize.DATE },
    final_revoked_date: { type: Sequelize.DATE },
    static_revoked_date: { type: Sequelize.BOOLEAN },
    created_at: { type: Sequelize.DATE },
    updated_at: { type: Sequelize.DATE },
    changed_at: { type: Sequelize.DATE },
    migrate_password: { type: Sequelize.BOOLEAN },
    password_migrated_at: { type: Sequelize.DATE },
    deleted: { type: Sequelize.BOOLEAN },
    super_user: { type: Sequelize.BOOLEAN },
    super_admin: { type: Sequelize.BOOLEAN },
    can_delete_orgs: { type: Sequelize.BOOLEAN },
    encrypted_password: { type: Sequelize.STRING },
    password_set_at: { type: Sequelize.DATE },
    confirmation_token: { type: Sequelize.STRING },
    confirmed_at: { type: Sequelize.DATE },
    confirmation_sent_at: { type: Sequelize.DATE },
    unconfirmed_email: { type: Sequelize.STRING },
    reset_password_token: { type: Sequelize.STRING },
    reset_password_url: { type: Sequelize.STRING },
    reset_password_sent_at: { type: Sequelize.DATE },
    failed_attempts: { type: Sequelize.INTEGER },
    unlock_token: { type: Sequelize.STRING },
    locked_at: { type: Sequelize.DATE },
    registration_app: { type: Sequelize.INTEGER },
    last_account_oid: { type: Sequelize.INTEGER },
    saml_user_id: { type: Sequelize.STRING }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('versions', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    item_type: { type: Sequelize.STRING },
    item_id: { type: Sequelize.INTEGER },
    event: { type: Sequelize.STRING },
    whodunnit: { type: Sequelize.STRING },
    oid: { type: Sequelize.INTEGER },
    user_affected: { type: Sequelize.INTEGER },
    object: { type: Sequelize.TEXT(********) },
    object_changes: { type: Sequelize.TEXT(65535) },
    userchange: { type: Sequelize.TEXT(********) },
    app: { type: Sequelize.INTEGER },
    auth_provider: { type: Sequelize.STRING },
    ip: { type: Sequelize.STRING },
    user_agent: { type: Sequelize.STRING },
    sqs_msgid: { type: Sequelize.STRING },
    sqs_md5: { type: Sequelize.STRING },
    sqs_message: { type: Sequelize.TEXT(**********) },
    created_at: { type: Sequelize.DATE },
    change_notify_sent_at: { type: Sequelize.DATE },
    change_notify_job_queued_at: { type: Sequelize.DATE },
    change_notify_attempts: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  return {
    AffiliationAttribute: db.models.affiliation_attributes,
    AffiliationInvitation: db.models.affiliation_invitations,
    AffiliationRequest: db.models.affiliation_requests,
    AffiliationRole: db.models.affiliation_roles,
    Affiliation: db.models.affiliations,
    Application: db.models.applications,
    ArchivedSession: db.models.archived_sessions,
    CsvInvite: db.models.csv_invites,
    DelayedJob: db.models.delayed_jobs,
    Identity: db.models.identities,
    IdentityProvider: db.models.identity_providers,
    IdentitySchema: db.models.identity_schemas,
    LinkToken: db.models.link_tokens,
    Organization: db.models.organizations,
    PasswordPolicy: db.models.password_policies,
    Photo: db.models.photos,
    PreviousPassword: db.models.previous_passwords,
    PrimeToken: db.models.prime_tokens,
    PurgeOrganizationStep: db.models.purge_organization_steps,
    PurgeOrganization: db.models.purge_organizations,
    Restriction: db.models.restrictions,
    Role: db.models.roles,
    SchoolDivisionDepartment: db.models.school_division_departments,
    Session: db.models.sessions,
    SmsMessage: db.models.sms_messages,
    TwilioReply: db.models.twilio_replies,
    UnmatchedIdentity: db.models.unmatched_identities,
    UnmatchedLogin: db.models.unmatched_logins,
    User: db.models.users,
    Version: db.models.versions
  };
};
