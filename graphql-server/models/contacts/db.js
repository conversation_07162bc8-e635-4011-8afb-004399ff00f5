import Sequelize from 'sequelize';

export default secret => {
  const dbHost = secret['contacts.db.host'],
    dbUser = secret['contacts.db.user'],
    dbPass = secret['contacts.db.pass'];

  const db = new Sequelize('contacts', dbUser, dbPass, {
    host: dbHost,
    dialect: 'mysql',
    pool: {
      max: 20
    },
    logging: false
  });

  db.define('address', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    object_guid: { type: Sequelize.STRING },
    type: { type: Sequelize.STRING },
    address_1: { type: Sequelize.STRING },
    address_2: { type: Sequelize.STRING },
    address_3: { type: Sequelize.STRING },
    city: { type: Sequelize.STRING },
    state: { type: Sequelize.STRING },
    country: { type: Sequelize.STRING },
    zip_code: { type: Sequelize.STRING },
    primary: { type: Sequelize.BOOLEAN },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('address_census_data', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    address_id: { type: Sequelize.BIGINT },
    house_value_lq: { type: Sequelize.INTEGER },
    house_value_median: { type: Sequelize.INTEGER },
    house_value_uq: { type: Sequelize.INTEGER },
    income_median: { type: Sequelize.INTEGER },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('address_geo_coordinates', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    address_id: { type: Sequelize.BIGINT },
    lat: { type: Sequelize.FLOAT },
    lng: { type: Sequelize.FLOAT },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('assignee', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    object_guid: { type: Sequelize.STRING },
    name: { type: Sequelize.STRING },
    remote_user_id: { type: Sequelize.STRING },
    user_id: { type: Sequelize.INTEGER },
    type: { type: Sequelize.STRING },
    status: { type: Sequelize.STRING },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('avatar', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    object_guid: { type: Sequelize.STRING },
    url: { type: Sequelize.STRING },
    service_id: { type: Sequelize.STRING },
    service_type: { type: Sequelize.STRING },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('blacklist_setting', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    object_guid: { type: Sequelize.STRING },
    type: { type: Sequelize.STRING },
    blacklisted: { type: Sequelize.BOOLEAN },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('constituency', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    object_guid: { type: Sequelize.STRING },
    status: { type: Sequelize.STRING },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('contact', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    oid: { type: Sequelize.INTEGER },
    roles: { type: Sequelize.STRING },
    roles_override: { type: Sequelize.STRING },
    private: { type: Sequelize.BOOLEAN },
    deleted: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    school_identifier: { type: Sequelize.STRING },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('contact_attributes', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    name_prefix: { type: Sequelize.STRING },
    name_first: { type: Sequelize.STRING },
    name_nick: { type: Sequelize.STRING },
    name_middle: { type: Sequelize.STRING },
    name_last: { type: Sequelize.STRING },
    year: { type: Sequelize.INTEGER },
    name_suffix: { type: Sequelize.STRING },
    name_maiden: { type: Sequelize.STRING },
    deceased: { type: Sequelize.BOOLEAN },
    gender: { type: Sequelize.STRING },
    ethnicity: { type: Sequelize.STRING },
    mentor: { type: Sequelize.BOOLEAN },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT },
    school_identifier: { type: Sequelize.STRING }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('custom_property_value', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    object_guid: { type: Sequelize.STRING },
    property_id: { type: Sequelize.INTEGER },
    value: { type: Sequelize.TEXT(65535) },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('education', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    object_guid: { type: Sequelize.STRING },
    school_name: { type: Sequelize.STRING },
    year: { type: Sequelize.INTEGER },
    degree_key: { type: Sequelize.STRING },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('education_major', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    education_id: { type: Sequelize.BIGINT },
    major: { type: Sequelize.STRING },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('email', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    object_guid: { type: Sequelize.STRING },
    email: { type: Sequelize.STRING },
    type: { type: Sequelize.STRING },
    primary: { type: Sequelize.BOOLEAN },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('employment', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    object_guid: { type: Sequelize.STRING },
    function: { type: Sequelize.STRING },
    title: { type: Sequelize.STRING },
    company: { type: Sequelize.STRING },
    industry: { type: Sequelize.STRING },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('extracurricular_activity', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    object_guid: { type: Sequelize.STRING },
    name: { type: Sequelize.TEXT(65535) },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('facebook', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    object_guid: { type: Sequelize.STRING },
    uid: { type: Sequelize.STRING },
    total_engagement_count: { type: Sequelize.INTEGER },
    like_count: { type: Sequelize.INTEGER },
    comment_count: { type: Sequelize.INTEGER },
    share_count: { type: Sequelize.INTEGER },
    last_engagement_date: { type: Sequelize.DATEONLY },
    warmed_at: { type: Sequelize.DATEONLY },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('giving', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    object_guid: { type: Sequelize.STRING },
    assignee: { type: Sequelize.STRING },
    assignee_remote_user_id: { type: Sequelize.STRING },
    assignee_user_id: { type: Sequelize.INTEGER },
    donor_score: { type: Sequelize.STRING },
    capacity_score: { type: Sequelize.STRING },
    engagement_score: { type: Sequelize.STRING },
    lifetime_amount: { type: Sequelize.DECIMAL(13, 2) },
    lifetime_hard_credit_amount: { type: Sequelize.DECIMAL(13, 2) },
    lifetime_soft_credit_amount: { type: Sequelize.DECIMAL(13, 2) },
    lifetime_hard_and_soft_credit_amount: { type: Sequelize.DECIMAL(13, 2) },
    lifetime_recognition_amount: { type: Sequelize.DECIMAL(13, 2) },
    lifetime_pledge_balance: { type: Sequelize.DECIMAL(13, 2) },
    largest_gift_amount: { type: Sequelize.DECIMAL(13, 2) },
    largest_gift_date: { type: Sequelize.DATEONLY },
    largest_gift_label: { type: Sequelize.TEXT(65535) },
    last_gift_amount: { type: Sequelize.DECIMAL(13, 2) },
    last_gift_date: { type: Sequelize.DATEONLY },
    last_gift_label: { type: Sequelize.TEXT(65535) },
    et_donor_index: { type: Sequelize.STRING },
    lybunt: { type: Sequelize.BOOLEAN },
    sybunt: { type: Sequelize.BOOLEAN },
    giving_velocity: { type: Sequelize.STRING },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT },
    total_pledge_balance: { type: Sequelize.DECIMAL(13, 2) },
    has_recurring_payments: { type: Sequelize.BOOLEAN },
    giving_pattern: { type: Sequelize.STRING }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('giving_annual_donation', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    object_guid: { type: Sequelize.STRING },
    fiscal_year: { type: Sequelize.INTEGER },
    amount: { type: Sequelize.DECIMAL(13, 2) },
    soft_credit_amount: { type: Sequelize.DECIMAL(13, 2) },
    pledge_amount: { type: Sequelize.DECIMAL(13, 2) },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('giving_categories', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    object_guid: { type: Sequelize.STRING },
    label: { type: Sequelize.STRING },
    lifetime_amount: { type: Sequelize.DECIMAL(13, 2) },
    largest_gift_amount: { type: Sequelize.DECIMAL(13, 2) },
    largest_gift_date: { type: Sequelize.DATEONLY },
    largest_gift_label: { type: Sequelize.STRING },
    last_gift_amount: { type: Sequelize.DECIMAL(13, 2) },
    last_gift_date: { type: Sequelize.DATEONLY },
    last_gift_label: { type: Sequelize.STRING },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT },
    fy_0_amount: { type: Sequelize.DECIMAL(13, 2) },
    fy_1_amount: { type: Sequelize.DECIMAL(13, 2) },
    fy_2_amount: { type: Sequelize.DECIMAL(13, 2) },
    fy_3_amount: { type: Sequelize.DECIMAL(13, 2) },
    fy_4_amount: { type: Sequelize.DECIMAL(13, 2) },
    fy_5_amount: { type: Sequelize.DECIMAL(13, 2) },
    total_pledge_balance: { type: Sequelize.DECIMAL(13, 2) },
    has_recurring_payments: { type: Sequelize.BOOLEAN }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('identity', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    value: { type: Sequelize.STRING },
    oid: { type: Sequelize.INTEGER },
    type: { type: Sequelize.INTEGER },
    contact_id: { type: Sequelize.BIGINT },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('identity_conversion', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    oid: { type: Sequelize.INTEGER },
    contact_id: { type: Sequelize.BIGINT },
    value_old: { type: Sequelize.STRING },
    value_new: { type: Sequelize.STRING },
    type: { type: Sequelize.INTEGER },
    migration_timestamp: { type: Sequelize.BIGINT },
    marked_for_deletion: { type: Sequelize.BOOLEAN },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('ignored_uid', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    object_guid: { type: Sequelize.STRING },
    uid: { type: Sequelize.STRING },
    type: { type: Sequelize.STRING },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('linkedin', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    object_guid: { type: Sequelize.STRING },
    uid: { type: Sequelize.STRING },
    location: { type: Sequelize.STRING },
    headline: { type: Sequelize.STRING },
    industry_code: { type: Sequelize.STRING },
    industry_group: { type: Sequelize.STRING },
    industry_description: { type: Sequelize.STRING },
    picture_url: { type: Sequelize.STRING },
    standard_profile_request_url: { type: Sequelize.STRING },
    standard_profile_request_headers: { type: Sequelize.STRING },
    public_profile_url: { type: Sequelize.STRING },
    rewarmed_at: { type: Sequelize.BIGINT },
    resolved_title: { type: Sequelize.STRING },
    warmed_at: { type: Sequelize.DATEONLY },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('linkedin_position', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    object_guid: { type: Sequelize.STRING },
    company_name: { type: Sequelize.STRING },
    company_id: { type: Sequelize.STRING },
    industry_code: { type: Sequelize.STRING },
    industry_group: { type: Sequelize.STRING },
    industry_description: { type: Sequelize.STRING },
    company_size: { type: Sequelize.STRING },
    company_type: { type: Sequelize.STRING },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('list', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    user_id: { type: Sequelize.INTEGER },
    oid: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT },
    app_key_id: { type: Sequelize.INTEGER },
    remote_id: { type: Sequelize.STRING },
    owner_group_id: { type: Sequelize.INTEGER },
    global: { type: Sequelize.BOOLEAN },
    type: { type: Sequelize.INTEGER },
    criteria: { type: Sequelize.TEXT(65535) },
    name: { type: Sequelize.STRING },
    slug: { type: Sequelize.STRING },
    hidden: { type: Sequelize.BOOLEAN },
    alias: { type: Sequelize.TEXT(65535) },
    filter: { type: Sequelize.TEXT(65535) },
    private: { type: Sequelize.BOOLEAN },
    deleted: { type: Sequelize.BOOLEAN }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('list_activity', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    list_id: { type: Sequelize.INTEGER },
    oid: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    user_id: { type: Sequelize.INTEGER },
    target: { type: Sequelize.INTEGER },
    type: { type: Sequelize.INTEGER },
    target_id: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('list_collaborator', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT },
    list_id: { type: Sequelize.INTEGER },
    oid: { type: Sequelize.INTEGER },
    collaborator_user_id: { type: Sequelize.INTEGER },
    added_by_user_id: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('list_contact', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    list_id: { type: Sequelize.INTEGER },
    constraint_group_id: { type: Sequelize.INTEGER },
    user_id: { type: Sequelize.INTEGER },
    oid: { type: Sequelize.INTEGER },
    contact_id: { type: Sequelize.BIGINT },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('list_filter', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    filter_id: { type: Sequelize.STRING },
    label: { type: Sequelize.TEXT(65535) },
    data_type: { type: Sequelize.STRING },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT },
    platforms: { type: Sequelize.STRING },
    version: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('list_group', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    oid: { type: Sequelize.INTEGER },
    name: { type: Sequelize.STRING },
    slug: { type: Sequelize.STRING },
    type: { type: Sequelize.INTEGER },
    owner_list_id: { type: Sequelize.INTEGER },
    unique_constraint_on_contact: { type: Sequelize.BOOLEAN },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('list_labels', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    object_type: { type: Sequelize.INTEGER },
    object_id: { type: Sequelize.INTEGER },
    key: { type: Sequelize.STRING },
    value: { type: Sequelize.STRING },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('mail_event', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    hashed_msg_id: { type: Sequelize.STRING },
    oid: { type: Sequelize.INTEGER },
    to_email: { type: Sequelize.STRING },
    state: { type: Sequelize.STRING },
    created_at: { type: Sequelize.BIGINT },
    provided_msg_id: { type: Sequelize.STRING },
    email_name: { type: Sequelize.STRING }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('phone', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    object_guid: { type: Sequelize.STRING },
    phone: { type: Sequelize.STRING },
    type: { type: Sequelize.STRING },
    primary: { type: Sequelize.BOOLEAN },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('profile_view', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    viewed_contact_id: { type: Sequelize.BIGINT },
    user_id: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    app_key: { type: Sequelize.INTEGER },
    viewer_contact_id: { type: Sequelize.BIGINT },
    oid: { type: Sequelize.INTEGER }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('property', {
    id: { type: Sequelize.INTEGER, primaryKey: true },
    name: { type: Sequelize.STRING },
    data_type: { type: Sequelize.STRING },
    description: { type: Sequelize.STRING },
    default_prop: { type: Sequelize.BOOLEAN },
    reserved: { type: Sequelize.BOOLEAN },
    visible: { type: Sequelize.BOOLEAN },
    filterable: { type: Sequelize.BOOLEAN },
    oid: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT },
    parent_id: { type: Sequelize.INTEGER },
    permissions: { type: Sequelize.TEXT(65535) },
    app_keys: { type: Sequelize.STRING },
    deleted: { type: Sequelize.BOOLEAN }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('prospect_status', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    object_guid: { type: Sequelize.STRING },
    status: { type: Sequelize.STRING },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('relationship', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    object_guid: { type: Sequelize.STRING },
    type: { type: Sequelize.STRING },
    name: { type: Sequelize.STRING },
    remote_id: { type: Sequelize.STRING },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('scores', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    object_guid: { type: Sequelize.STRING },
    professional: { type: Sequelize.INTEGER },
    demographics: { type: Sequelize.INTEGER },
    social: { type: Sequelize.INTEGER },
    giving: { type: Sequelize.INTEGER },
    score: { type: Sequelize.INTEGER },
    annual_fund: { type: Sequelize.INTEGER },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('share_search', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    hash_id: { type: Sequelize.STRING },
    criteria: { type: Sequelize.TEXT(65535) },
    filter: { type: Sequelize.TEXT(65535) },
    oid: { type: Sequelize.INTEGER },
    user_id: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('solicitation_code', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    object_guid: { type: Sequelize.STRING },
    code: { type: Sequelize.TEXT(65535) },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('sport', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    contact_id: { type: Sequelize.BIGINT },
    object_guid: { type: Sequelize.STRING },
    name: { type: Sequelize.STRING },
    update_source: { type: Sequelize.INTEGER },
    private: { type: Sequelize.BOOLEAN },
    privatized_source: { type: Sequelize.INTEGER },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  db.define('suggested_update', {
    id: { type: Sequelize.BIGINT, primaryKey: true },
    user_id: { type: Sequelize.INTEGER },
    oid: { type: Sequelize.INTEGER },
    target_contact_id: { type: Sequelize.BIGINT },
    suggestion: { type: Sequelize.TEXT(65535) },
    suggested_updates: { type: Sequelize.TEXT(65535) },
    created_at: { type: Sequelize.BIGINT },
    updated_at: { type: Sequelize.BIGINT }
  }, {
    timestamps: false,
    underscored: true,
    freezeTableName: true
  });

  return {
    Address: db.models.address,
    AddressCensusDatum: db.models.address_census_data,
    AddressGeoCoordinate: db.models.address_geo_coordinates,
    Assignee: db.models.assignee,
    Avatar: db.models.avatar,
    BlacklistSetting: db.models.blacklist_setting,
    Constituency: db.models.constituency,
    Contact: db.models.contact,
    ContactAttribute: db.models.contact_attributes,
    CustomPropertyValue: db.models.custom_property_value,
    Education: db.models.education,
    EducationMajor: db.models.education_major,
    Email: db.models.email,
    Employment: db.models.employment,
    ExtracurricularActivity: db.models.extracurricular_activity,
    Facebook: db.models.facebook,
    Giving: db.models.giving,
    GivingAnnualDonation: db.models.giving_annual_donation,
    GivingCategory: db.models.giving_categories,
    Identity: db.models.identity,
    IdentityConversion: db.models.identity_conversion,
    IgnoredUid: db.models.ignored_uid,
    Linkedin: db.models.linkedin,
    LinkedinPosition: db.models.linkedin_position,
    List: db.models.list,
    ListActivity: db.models.list_activity,
    ListCollaborator: db.models.list_collaborator,
    ListContact: db.models.list_contact,
    ListFilter: db.models.list_filter,
    ListGroup: db.models.list_group,
    ListLabel: db.models.list_labels,
    MailEvent: db.models.mail_event,
    Phone: db.models.phone,
    ProfileView: db.models.profile_view,
    Property: db.models.property,
    ProspectStatus: db.models.prospect_status,
    Relationship: db.models.relationship,
    Score: db.models.scores,
    ShareSearch: db.models.share_search,
    SolicitationCode: db.models.solicitation_code,
    Sport: db.models.sport,
    SuggestedUpdate: db.models.suggested_update
  };
};
