import { schemaComposer } from 'graphql-compose';

const AddressTC = schemaComposer.createObjectTC({
  name: 'Address',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    object_guid: 'String',
    type: 'String',
    address_1: 'String',
    address_2: 'String',
    address_3: 'String',
    city: 'String',
    state: 'String',
    country: 'String',
    zip_code: 'String',
    primary: 'Boolean',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const AddressCensusDatumTC = schemaComposer.createObjectTC({
  name: 'AddressCensusDatum',
  fields: {
    id: 'BigInt!',
    address_id: 'BigInt!',
    house_value_lq: 'Int',
    house_value_median: 'Int',
    house_value_uq: 'Int',
    income_median: 'Int',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const AddressGeoCoordinateTC = schemaComposer.createObjectTC({
  name: 'AddressGeoCoordinate',
  fields: {
    id: 'BigInt!',
    address_id: 'BigInt!',
    lat: 'Number',
    lng: 'Number',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const AssigneeTC = schemaComposer.createObjectTC({
  name: 'Assignee',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    object_guid: 'String',
    name: 'String',
    remote_user_id: 'String',
    user_id: 'Int',
    type: 'String',
    status: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const AvatarTC = schemaComposer.createObjectTC({
  name: 'Avatar',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    object_guid: 'String',
    url: 'String',
    service_id: 'String',
    service_type: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const BlacklistSettingTC = schemaComposer.createObjectTC({
  name: 'BlacklistSetting',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    object_guid: 'String!',
    type: 'String',
    blacklisted: 'Boolean',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const ConstituencyTC = schemaComposer.createObjectTC({
  name: 'Constituency',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    object_guid: 'String',
    status: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const ContactTC = schemaComposer.createObjectTC({
  name: 'Contact',
  fields: {
    id: 'BigInt!',
    oid: 'Int!',
    roles: 'String',
    roles_override: 'String',
    deleted: 'Boolean!',
    school_identifier: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const ContactAttributeTC = schemaComposer.createObjectTC({
  name: 'ContactAttribute',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    name_prefix: 'String',
    name_first: 'String',
    name_nick: 'String',
    name_middle: 'String',
    name_last: 'String',
    year: 'Int',
    name_suffix: 'String',
    name_maiden: 'String',
    deceased: 'Boolean',
    gender: 'String',
    ethnicity: 'String',
    mentor: 'Boolean',
    created_at: 'BigInt!',
    updated_at: 'BigInt!',
    school_identifier: 'String'
  }
});

const CustomPropertyValueTC = schemaComposer.createObjectTC({
  name: 'CustomPropertyValue',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    object_guid: 'String',
    property_id: 'Int!',
    value: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const EducationTC = schemaComposer.createObjectTC({
  name: 'Education',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    object_guid: 'String',
    school_name: 'String',
    year: 'Int',
    degree_key: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const EducationMajorTC = schemaComposer.createObjectTC({
  name: 'EducationMajor',
  fields: {
    id: 'BigInt!',
    education_id: 'BigInt!',
    major: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const EmailTC = schemaComposer.createObjectTC({
  name: 'Email',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    object_guid: 'String',
    email: 'String',
    type: 'String',
    primary: 'Boolean',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const EmploymentTC = schemaComposer.createObjectTC({
  name: 'Employment',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    object_guid: 'String',
    function: 'String',
    title: 'String',
    company: 'String',
    industry: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const ExtracurricularActivityTC = schemaComposer.createObjectTC({
  name: 'ExtracurricularActivity',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    object_guid: 'String',
    name: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const FacebookTC = schemaComposer.createObjectTC({
  name: 'Facebook',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    object_guid: 'String',
    uid: 'String',
    total_engagement_count: 'Int',
    like_count: 'Int',
    comment_count: 'Int',
    share_count: 'Int',
    last_engagement_date: 'Date',
    warmed_at: 'Date',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const GivingTC = schemaComposer.createObjectTC({
  name: 'Giving',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    object_guid: 'String',
    assignee: 'String',
    assignee_remote_user_id: 'String',
    assignee_user_id: 'Int',
    donor_score: 'String',
    capacity_score: 'String',
    engagement_score: 'String',
    lifetime_amount: 'Number',
    lifetime_hard_credit_amount: 'Number',
    lifetime_soft_credit_amount: 'Number',
    lifetime_hard_and_soft_credit_amount: 'Number',
    lifetime_recognition_amount: 'Number',
    lifetime_pledge_balance: 'Number',
    largest_gift_amount: 'Number',
    largest_gift_date: 'Date',
    largest_gift_label: 'String',
    last_gift_amount: 'Number',
    last_gift_date: 'Date',
    last_gift_label: 'String',
    et_donor_index: 'String',
    lybunt: 'Boolean',
    sybunt: 'Boolean',
    giving_velocity: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt!',
    total_pledge_balance: 'Number',
    has_recurring_payments: 'Boolean',
    giving_pattern: 'String'
  }
});

const GivingAnnualDonationTC = schemaComposer.createObjectTC({
  name: 'GivingAnnualDonation',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    object_guid: 'String',
    fiscal_year: 'Int',
    amount: 'Number',
    soft_credit_amount: 'Number',
    pledge_amount: 'Number',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const GivingCategoryTC = schemaComposer.createObjectTC({
  name: 'GivingCategory',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    object_guid: 'String',
    label: 'String',
    lifetime_amount: 'Number',
    largest_gift_amount: 'Number',
    largest_gift_date: 'Date',
    largest_gift_label: 'String',
    last_gift_amount: 'Number',
    last_gift_date: 'Date',
    last_gift_label: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt!',
    fy_0_amount: 'Number',
    fy_1_amount: 'Number',
    fy_2_amount: 'Number',
    fy_3_amount: 'Number',
    fy_4_amount: 'Number',
    fy_5_amount: 'Number',
    total_pledge_balance: 'Number',
    has_recurring_payments: 'Boolean'
  }
});

const IdentityTC = schemaComposer.createObjectTC({
  name: 'Identity',
  fields: {
    id: 'BigInt!',
    value: 'String!',
    oid: 'Int!',
    type: 'Int!',
    contact_id: 'BigInt!',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const IdentityConversionTC = schemaComposer.createObjectTC({
  name: 'IdentityConversion',
  fields: {
    id: 'BigInt!',
    oid: 'Int!',
    contact_id: 'BigInt!',
    value_old: 'String!',
    value_new: 'String',
    type: 'Int!',
    migration_timestamp: 'BigInt!',
    marked_for_deletion: 'Boolean!',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const IgnoredUidTC = schemaComposer.createObjectTC({
  name: 'IgnoredUid',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    object_guid: 'String',
    uid: 'String',
    type: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const LinkedinTC = schemaComposer.createObjectTC({
  name: 'Linkedin',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    object_guid: 'String',
    uid: 'String',
    location: 'String',
    headline: 'String',
    industry_code: 'String',
    industry_group: 'String',
    industry_description: 'String',
    picture_url: 'String',
    standard_profile_request_url: 'String',
    standard_profile_request_headers: 'String',
    public_profile_url: 'String',
    rewarmed_at: 'BigInt',
    resolved_title: 'String',
    warmed_at: 'Date',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const LinkedinPositionTC = schemaComposer.createObjectTC({
  name: 'LinkedinPosition',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    object_guid: 'String',
    company_name: 'String',
    company_id: 'String',
    industry_code: 'String',
    industry_group: 'String',
    industry_description: 'String',
    company_size: 'String',
    company_type: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const ListTC = schemaComposer.createObjectTC({
  name: 'List',
  fields: {
    id: 'Int!',
    user_id: 'Int!',
    oid: 'Int!',
    created_at: 'BigInt!',
    updated_at: 'BigInt!',
    app_key_id: 'Int',
    remote_id: 'String',
    owner_group_id: 'Int',
    global: 'Boolean',
    type: 'Int!',
    criteria: 'String',
    name: 'String!',
    slug: 'String',
    hidden: 'Boolean',
    alias: 'String',
    filter: 'String',
    deleted: 'Boolean!'
  }
});

const ListActivityTC = schemaComposer.createObjectTC({
  name: 'ListActivity',
  fields: {
    id: 'Int!',
    list_id: 'Int!',
    oid: 'Int!',
    created_at: 'BigInt!',
    user_id: 'Int!',
    target: 'Int!',
    type: 'Int!',
    target_id: 'BigInt!'
  }
});

const ListCollaboratorTC = schemaComposer.createObjectTC({
  name: 'ListCollaborator',
  fields: {
    id: 'Int!',
    created_at: 'BigInt!',
    updated_at: 'BigInt!',
    list_id: 'Int!',
    oid: 'Int!',
    collaborator_user_id: 'Int!',
    added_by_user_id: 'Int!'
  }
});

const ListContactTC = schemaComposer.createObjectTC({
  name: 'ListContact',
  fields: {
    id: 'Int!',
    list_id: 'Int!',
    constraint_group_id: 'Int',
    user_id: 'Int!',
    oid: 'Int!',
    contact_id: 'BigInt!',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const ListFilterTC = schemaComposer.createObjectTC({
  name: 'ListFilter',
  fields: {
    id: 'Int!',
    filter_id: 'String!',
    label: 'String!',
    data_type: 'String!',
    created_at: 'BigInt!',
    updated_at: 'BigInt!',
    platforms: 'String!',
    version: 'Int!'
  }
});

const ListGroupTC = schemaComposer.createObjectTC({
  name: 'ListGroup',
  fields: {
    id: 'Int!',
    oid: 'Int!',
    name: 'String!',
    slug: 'String',
    type: 'Int!',
    owner_list_id: 'Int',
    unique_constraint_on_contact: 'Boolean!',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const ListLabelTC = schemaComposer.createObjectTC({
  name: 'ListLabel',
  fields: {
    id: 'Int!',
    object_type: 'Int!',
    object_id: 'Int!',
    key: 'String!',
    value: 'String!',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const MailEventTC = schemaComposer.createObjectTC({
  name: 'MailEvent',
  fields: {
    id: 'BigInt!',
    hashed_msg_id: 'String!',
    oid: 'Int!',
    to_email: 'String!',
    state: 'String!',
    created_at: 'BigInt!',
    provided_msg_id: 'String!',
    email_name: 'String!'
  }
});

const PhoneTC = schemaComposer.createObjectTC({
  name: 'Phone',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    object_guid: 'String',
    phone: 'String',
    type: 'String',
    primary: 'Boolean',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const ProfileViewTC = schemaComposer.createObjectTC({
  name: 'ProfileView',
  fields: {
    id: 'BigInt!',
    viewed_contact_id: 'BigInt!',
    user_id: 'Int!',
    created_at: 'BigInt!',
    app_key: 'Int!',
    viewer_contact_id: 'BigInt',
    oid: 'Int!'
  }
});

const PropertyTC = schemaComposer.createObjectTC({
  name: 'Property',
  fields: {
    id: 'Int!',
    name: 'String!',
    data_type: 'String!',
    description: 'String',
    default_prop: 'Boolean!',
    reserved: 'Boolean!',
    visible: 'Boolean',
    filterable: 'Boolean',
    oid: 'Int!',
    created_at: 'BigInt!',
    updated_at: 'BigInt!',
    parent_id: 'Int',
    permissions: 'String',
    app_keys: 'String',
    deleted: 'Boolean!'
  }
});

const ProspectStatusTC = schemaComposer.createObjectTC({
  name: 'ProspectStatus',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    object_guid: 'String',
    status: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const RelationshipTC = schemaComposer.createObjectTC({
  name: 'Relationship',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    object_guid: 'String',
    type: 'String',
    name: 'String',
    remote_id: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const ScoreTC = schemaComposer.createObjectTC({
  name: 'Score',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    object_guid: 'String',
    professional: 'Int',
    demographics: 'Int',
    social: 'Int',
    giving: 'Int',
    score: 'Int',
    annual_fund: 'Int',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const ShareSearchTC = schemaComposer.createObjectTC({
  name: 'ShareSearch',
  fields: {
    id: 'BigInt!',
    hash_id: 'String!',
    criteria: 'String!',
    filter: 'String!',
    oid: 'Int!',
    user_id: 'Int!',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const SolicitationCodeTC = schemaComposer.createObjectTC({
  name: 'SolicitationCode',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    object_guid: 'String',
    code: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const SportTC = schemaComposer.createObjectTC({
  name: 'Sport',
  fields: {
    id: 'BigInt!',
    contact_id: 'BigInt!',
    object_guid: 'String',
    name: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

const SuggestedUpdateTC = schemaComposer.createObjectTC({
  name: 'SuggestedUpdate',
  fields: {
    id: 'BigInt!',
    user_id: 'Int!',
    oid: 'Int!',
    target_contact_id: 'BigInt!',
    suggestion: 'String',
    suggested_updates: 'String',
    created_at: 'BigInt!',
    updated_at: 'BigInt!'
  }
});

