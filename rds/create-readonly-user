#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:change_password] = false

  opts.on('-u', '--user [USERNAME]', 'Username') do |v|
    opts[:user] = v
  end

  opts.on('-p', '--password [PASSWORD]', 'Password') do |v|
    opts[:password] = v
  end

  opts.on('--change-password', 'Change Password') do
    opts[:change_password] = true
  end

  opts.require_option(:user)
  opts.require_option(:password)
end

require File.expand_path('../config/environment', __dir__)

ETOnePassword.new.config['rds'][ENV['RAILS_ENV']].each do |rds_name, rds_settings|
  client = MySQLMasterHelpers.create_rds_client(rds_name)

  if !SCRIPT_OPTIONS[:change_password] && MySQLMasterHelpers.get_user(client, SCRIPT_OPTIONS[:user])
    puts "updating user #{SCRIPT_OPTIONS[:user]} readonly privileges on #{rds_name}/#{rds_settings['host']}"
    MySQLMasterHelpers.update_read_only_user(client, SCRIPT_OPTIONS[:user])
  else
    puts "creating user #{SCRIPT_OPTIONS[:user]} on #{rds_name}/#{rds_settings['host']}"
    MySQLMasterHelpers.create_read_only_user(client, SCRIPT_OPTIONS[:user], SCRIPT_OPTIONS[:password])
  end
end
