#!/usr/bin/env ruby

# Purpose: to restore users to DBs that were restored using a mysql dump file.

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)

USERS = [
  'sf-updater',
  'doug',
  'josh',
  'george',
  'sarah',
  'alex',
  'dan',
  'adam',
  'davidanchin',
  'mikebrucek',
  'nickbor',
  'pj',
  'rday',
  'regan',
  'spencer'
]

MySQLMasterHelpers.et_one_password.config['rds']["old_#{ENV['RAILS_ENV']}"].each do |rds_name, settings|
  next if rds_name == 'prod_singularity_copy'

  old_rds_client = MySQLMasterHelpers.create_rds_client(rds_name)

  settings['databases'].each do |database|
    new_rds_client = MySQLMasterHelpers.create_client(database)

    USERS.each do |user|
      if MySQLMasterHelpers.get_user(new_rds_client, user)
        puts "user #{user} exists in #{database}"
        next
      end

      unless old_user_data = MySQLMasterHelpers.get_user(old_rds_client, user)
        puts "user #{user} not found in #{rds_name}/#{settings['host']}"
        next
      end

      puts "copying user #{user} from #{rds_name}/#{settings['host']} to #{database}"
      authentication_string = old_user_data['Password'] ||  old_user_data['authentication_string']
      if authentication_string.nil? || authentication_string.empty?
        raise("no authentication_string set for #{user}")
      end

      MySQLMasterHelpers.create_read_only_user(new_rds_client, user, 'M4Pass0Rd1!')
      MySQLMasterHelpers.update_user_authentication_string(new_rds_client, user, authentication_string)
    end
  end
end
