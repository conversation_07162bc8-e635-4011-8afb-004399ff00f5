{"DBInstances": [{"DBInstanceIdentifier": "assetmanager", "DBInstanceClass": "db.t2.micro", "Engine": "mysql", "DBInstanceStatus": "available", "MasterUsername": "assetmanager", "DBName": "assetmanager", "Endpoint": {"Address": "assetmanager.cg0lvth7azzh.us-east-1.rds.amazonaws.com", "Port": 3306, "HostedZoneId": "Z2R2ITUGPM61AM"}, "AllocatedStorage": 20, "InstanceCreateTime": "2018-08-29T13:47:12.822000+00:00", "PreferredBackupWindow": "06:39-07:09", "BackupRetentionPeriod": 30, "DBSecurityGroups": [], "VpcSecurityGroups": [{"VpcSecurityGroupId": "sg-dd9445b2", "Status": "active"}, {"VpcSecurityGroupId": "sg-97f7edf5", "Status": "active"}], "DBParameterGroups": [{"DBParameterGroupName": "contacts-prod-56", "ParameterApplyStatus": "in-sync"}], "AvailabilityZone": "us-east-1d", "DBSubnetGroup": {"DBSubnetGroupName": "prod-private", "DBSubnetGroupDescription": "VPC private subnet group", "VpcId": "vpc-9318d5f8", "SubnetGroupStatus": "Complete", "Subnets": [{"SubnetIdentifier": "subnet-5e1bd635", "SubnetAvailabilityZone": {"Name": "us-east-1d"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-601bd60b", "SubnetAvailabilityZone": {"Name": "us-east-1a"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-421bd629", "SubnetAvailabilityZone": {"Name": "us-east-1c"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-711bd61a", "SubnetAvailabilityZone": {"Name": "us-east-1b"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}]}, "PreferredMaintenanceWindow": "wed:07:41-wed:08:11", "PendingModifiedValues": {}, "LatestRestorableTime": "2021-04-07T14:35:00+00:00", "MultiAZ": true, "EngineVersion": "5.6.39", "AutoMinorVersionUpgrade": true, "ReadReplicaDBInstanceIdentifiers": [], "LicenseModel": "general-public-license", "OptionGroupMemberships": [{"OptionGroupName": "default:mysql-5-6", "Status": "in-sync"}], "SecondaryAvailabilityZone": "us-east-1b", "PubliclyAccessible": false, "StorageType": "gp2", "DbInstancePort": 0, "StorageEncrypted": false, "DbiResourceId": "db-RCUEQLDOUM7DJXWMZIIVJVLFCQ", "CACertificateIdentifier": "rds-ca-2019", "DomainMemberships": [], "CopyTagsToSnapshot": true, "MonitoringInterval": 60, "EnhancedMonitoringResourceArn": "arn:aws:logs:us-east-1:037590317780:log-group:RDSOSMetrics:log-stream:db-RCUEQLDOUM7DJXWMZIIVJVLFCQ", "MonitoringRoleArn": "arn:aws:iam::037590317780:role/rds-monitoring-role", "DBInstanceArn": "arn:aws:rds:us-east-1:037590317780:db:assetmanager", "IAMDatabaseAuthenticationEnabled": false, "PerformanceInsightsEnabled": false, "DeletionProtection": false, "AssociatedRoles": [], "TagList": [{"Key": "workload-type", "Value": "production"}], "CustomerOwnedIpEnabled": false}, {"DBInstanceIdentifier": "assignment", "DBInstanceClass": "db.m3.medium", "Engine": "mysql", "DBInstanceStatus": "available", "MasterUsername": "<PERSON><PERSON><PERSON>", "DBName": "assignment", "Endpoint": {"Address": "assignment.cg0lvth7azzh.us-east-1.rds.amazonaws.com", "Port": 3306, "HostedZoneId": "Z2R2ITUGPM61AM"}, "AllocatedStorage": 10, "InstanceCreateTime": "2017-09-05T15:04:20.831000+00:00", "PreferredBackupWindow": "10:28-10:58", "BackupRetentionPeriod": 35, "DBSecurityGroups": [], "VpcSecurityGroups": [{"VpcSecurityGroupId": "sg-97f7edf5", "Status": "active"}, {"VpcSecurityGroupId": "sg-0cc33263", "Status": "active"}], "DBParameterGroups": [{"DBParameterGroupName": "evertrue-mysql56", "ParameterApplyStatus": "in-sync"}], "AvailabilityZone": "us-east-1b", "DBSubnetGroup": {"DBSubnetGroupName": "prod-private", "DBSubnetGroupDescription": "VPC private subnet group", "VpcId": "vpc-9318d5f8", "SubnetGroupStatus": "Complete", "Subnets": [{"SubnetIdentifier": "subnet-5e1bd635", "SubnetAvailabilityZone": {"Name": "us-east-1d"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-601bd60b", "SubnetAvailabilityZone": {"Name": "us-east-1a"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-421bd629", "SubnetAvailabilityZone": {"Name": "us-east-1c"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-711bd61a", "SubnetAvailabilityZone": {"Name": "us-east-1b"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}]}, "PreferredMaintenanceWindow": "fri:08:56-fri:09:26", "PendingModifiedValues": {}, "LatestRestorableTime": "2021-04-07T14:35:00+00:00", "MultiAZ": true, "EngineVersion": "5.6.40", "AutoMinorVersionUpgrade": true, "ReadReplicaDBInstanceIdentifiers": [], "LicenseModel": "general-public-license", "OptionGroupMemberships": [{"OptionGroupName": "default:mysql-5-6", "Status": "in-sync"}], "SecondaryAvailabilityZone": "us-east-1c", "PubliclyAccessible": false, "StorageType": "gp2", "DbInstancePort": 0, "StorageEncrypted": true, "KmsKeyId": "arn:aws:kms:us-east-1:037590317780:key/6e31bfa5-e040-46ac-812a-c4125ad8a817", "DbiResourceId": "db-IDLHB4J3ZPSTRGFNJLDMV4XVU4", "CACertificateIdentifier": "rds-ca-2019", "DomainMemberships": [], "CopyTagsToSnapshot": false, "MonitoringInterval": 60, "EnhancedMonitoringResourceArn": "arn:aws:logs:us-east-1:037590317780:log-group:RDSOSMetrics:log-stream:db-IDLHB4J3ZPSTRGFNJLDMV4XVU4", "MonitoringRoleArn": "arn:aws:iam::037590317780:role/rds-monitoring-role", "DBInstanceArn": "arn:aws:rds:us-east-1:037590317780:db:assignment", "IAMDatabaseAuthenticationEnabled": false, "PerformanceInsightsEnabled": false, "DeletionProtection": false, "AssociatedRoles": [], "TagList": [{"Key": "workload-type", "Value": "production"}], "CustomerOwnedIpEnabled": false}, {"DBInstanceIdentifier": "auth-prod-copy", "DBInstanceClass": "db.m1.large", "Engine": "mysql", "DBInstanceStatus": "available", "MasterUsername": "auth", "DBName": "auth", "Endpoint": {"Address": "auth-prod-copy.cg0lvth7azzh.us-east-1.rds.amazonaws.com", "Port": 3306, "HostedZoneId": "Z2R2ITUGPM61AM"}, "AllocatedStorage": 100, "InstanceCreateTime": "2017-03-30T00:28:09.822000+00:00", "PreferredBackupWindow": "09:37-10:07", "BackupRetentionPeriod": 30, "DBSecurityGroups": [], "VpcSecurityGroups": [{"VpcSecurityGroupId": "sg-97f7edf5", "Status": "active"}, {"VpcSecurityGroupId": "sg-ae4c48cb", "Status": "active"}], "DBParameterGroups": [{"DBParameterGroupName": "evertrue-mysql56", "ParameterApplyStatus": "in-sync"}], "AvailabilityZone": "us-east-1d", "DBSubnetGroup": {"DBSubnetGroupName": "prod-private", "DBSubnetGroupDescription": "VPC private subnet group", "VpcId": "vpc-9318d5f8", "SubnetGroupStatus": "Complete", "Subnets": [{"SubnetIdentifier": "subnet-5e1bd635", "SubnetAvailabilityZone": {"Name": "us-east-1d"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-601bd60b", "SubnetAvailabilityZone": {"Name": "us-east-1a"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-421bd629", "SubnetAvailabilityZone": {"Name": "us-east-1c"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-711bd61a", "SubnetAvailabilityZone": {"Name": "us-east-1b"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}]}, "PreferredMaintenanceWindow": "tue:15:00-tue:16:00", "PendingModifiedValues": {}, "LatestRestorableTime": "2021-04-07T14:35:00+00:00", "MultiAZ": true, "EngineVersion": "5.6.34", "AutoMinorVersionUpgrade": true, "ReadReplicaDBInstanceIdentifiers": ["auth-prod-copy-read-replica"], "LicenseModel": "general-public-license", "OptionGroupMemberships": [{"OptionGroupName": "default:mysql-5-6", "Status": "in-sync"}], "SecondaryAvailabilityZone": "us-east-1c", "PubliclyAccessible": false, "StorageType": "standard", "DbInstancePort": 0, "StorageEncrypted": false, "DbiResourceId": "db-TSQFHIWVC3XGN3D4RRCPDT6K2Y", "CACertificateIdentifier": "rds-ca-2019", "DomainMemberships": [], "CopyTagsToSnapshot": true, "MonitoringInterval": 60, "EnhancedMonitoringResourceArn": "arn:aws:logs:us-east-1:037590317780:log-group:RDSOSMetrics:log-stream:db-TSQFHIWVC3XGN3D4RRCPDT6K2Y", "MonitoringRoleArn": "arn:aws:iam::037590317780:role/rds-monitoring-role", "DBInstanceArn": "arn:aws:rds:us-east-1:037590317780:db:auth-prod-copy", "IAMDatabaseAuthenticationEnabled": false, "PerformanceInsightsEnabled": false, "DeletionProtection": false, "AssociatedRoles": [], "TagList": [], "CustomerOwnedIpEnabled": false}, {"DBInstanceIdentifier": "auth-prod-copy-read-replica", "DBInstanceClass": "db.t2.large", "Engine": "mysql", "DBInstanceStatus": "available", "MasterUsername": "auth", "DBName": "auth", "Endpoint": {"Address": "auth-prod-copy-read-replica.cg0lvth7azzh.us-east-1.rds.amazonaws.com", "Port": 3306, "HostedZoneId": "Z2R2ITUGPM61AM"}, "AllocatedStorage": 100, "InstanceCreateTime": "2019-03-20T20:10:47.173000+00:00", "PreferredBackupWindow": "09:37-10:07", "BackupRetentionPeriod": 0, "DBSecurityGroups": [], "VpcSecurityGroups": [{"VpcSecurityGroupId": "sg-97f7edf5", "Status": "active"}, {"VpcSecurityGroupId": "sg-ae4c48cb", "Status": "active"}], "DBParameterGroups": [{"DBParameterGroupName": "evertrue-mysql56", "ParameterApplyStatus": "in-sync"}], "AvailabilityZone": "us-east-1c", "DBSubnetGroup": {"DBSubnetGroupName": "prod-private", "DBSubnetGroupDescription": "VPC private subnet group", "VpcId": "vpc-9318d5f8", "SubnetGroupStatus": "Complete", "Subnets": [{"SubnetIdentifier": "subnet-5e1bd635", "SubnetAvailabilityZone": {"Name": "us-east-1d"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-601bd60b", "SubnetAvailabilityZone": {"Name": "us-east-1a"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-421bd629", "SubnetAvailabilityZone": {"Name": "us-east-1c"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-711bd61a", "SubnetAvailabilityZone": {"Name": "us-east-1b"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}]}, "PreferredMaintenanceWindow": "tue:15:00-tue:16:00", "PendingModifiedValues": {}, "MultiAZ": false, "EngineVersion": "5.6.34", "AutoMinorVersionUpgrade": true, "ReadReplicaSourceDBInstanceIdentifier": "auth-prod-copy", "ReadReplicaDBInstanceIdentifiers": [], "LicenseModel": "general-public-license", "OptionGroupMemberships": [{"OptionGroupName": "default:mysql-5-6", "Status": "in-sync"}], "PubliclyAccessible": false, "StatusInfos": [{"StatusType": "read replication", "Normal": true, "Status": "replicating"}], "StorageType": "standard", "DbInstancePort": 0, "StorageEncrypted": false, "DbiResourceId": "db-GKO35OZOUNV5LD3HQH65YRX4IU", "CACertificateIdentifier": "rds-ca-2019", "DomainMemberships": [], "CopyTagsToSnapshot": false, "MonitoringInterval": 0, "DBInstanceArn": "arn:aws:rds:us-east-1:037590317780:db:auth-prod-copy-read-replica", "IAMDatabaseAuthenticationEnabled": false, "PerformanceInsightsEnabled": false, "DeletionProtection": false, "AssociatedRoles": [], "TagList": [], "CustomerOwnedIpEnabled": false}, {"DBInstanceIdentifier": "census", "DBInstanceClass": "db.t2.medium", "Engine": "mysql", "DBInstanceStatus": "available", "MasterUsername": "census", "DBName": "census", "Endpoint": {"Address": "census.cg0lvth7azzh.us-east-1.rds.amazonaws.com", "Port": 3306, "HostedZoneId": "Z2R2ITUGPM61AM"}, "AllocatedStorage": 100, "InstanceCreateTime": "2018-06-22T17:54:44.865000+00:00", "PreferredBackupWindow": "05:27-05:57", "BackupRetentionPeriod": 7, "DBSecurityGroups": [], "VpcSecurityGroups": [{"VpcSecurityGroupId": "sg-dd9445b2", "Status": "active"}, {"VpcSecurityGroupId": "sg-97f7edf5", "Status": "active"}], "DBParameterGroups": [{"DBParameterGroupName": "default.mysql5.7", "ParameterApplyStatus": "in-sync"}], "AvailabilityZone": "us-east-1b", "DBSubnetGroup": {"DBSubnetGroupName": "prod-private", "DBSubnetGroupDescription": "VPC private subnet group", "VpcId": "vpc-9318d5f8", "SubnetGroupStatus": "Complete", "Subnets": [{"SubnetIdentifier": "subnet-5e1bd635", "SubnetAvailabilityZone": {"Name": "us-east-1d"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-601bd60b", "SubnetAvailabilityZone": {"Name": "us-east-1a"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-421bd629", "SubnetAvailabilityZone": {"Name": "us-east-1c"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-711bd61a", "SubnetAvailabilityZone": {"Name": "us-east-1b"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}]}, "PreferredMaintenanceWindow": "tue:09:29-tue:09:59", "PendingModifiedValues": {}, "LatestRestorableTime": "2021-04-07T14:35:00+00:00", "MultiAZ": false, "EngineVersion": "5.7.22", "AutoMinorVersionUpgrade": true, "ReadReplicaDBInstanceIdentifiers": [], "LicenseModel": "general-public-license", "OptionGroupMemberships": [{"OptionGroupName": "default:mysql-5-7", "Status": "in-sync"}], "PubliclyAccessible": false, "StorageType": "gp2", "DbInstancePort": 0, "StorageEncrypted": false, "DbiResourceId": "db-7MVU2DO4CIXS3VO6VOAEGEEOQY", "CACertificateIdentifier": "rds-ca-2019", "DomainMemberships": [], "CopyTagsToSnapshot": true, "MonitoringInterval": 0, "EnhancedMonitoringResourceArn": "arn:aws:logs:us-east-1:037590317780:log-group:RDSOSMetrics:log-stream:db-7MVU2DO4CIXS3VO6VOAEGEEOQY", "MonitoringRoleArn": "arn:aws:iam::037590317780:role/rds-monitoring-role", "DBInstanceArn": "arn:aws:rds:us-east-1:037590317780:db:census", "IAMDatabaseAuthenticationEnabled": false, "PerformanceInsightsEnabled": false, "DeletionProtection": false, "AssociatedRoles": [], "TagList": [{"Key": "workload-type", "Value": "production"}], "CustomerOwnedIpEnabled": false}, {"DBInstanceIdentifier": "contacts-prod", "DBInstanceClass": "db.m3.large", "Engine": "mysql", "DBInstanceStatus": "available", "MasterUsername": "contacts", "DBName": "contacts", "Endpoint": {"Address": "contacts-prod.cg0lvth7azzh.us-east-1.rds.amazonaws.com", "Port": 3306, "HostedZoneId": "Z2R2ITUGPM61AM"}, "AllocatedStorage": 1750, "InstanceCreateTime": "2013-06-18T19:00:15.215000+00:00", "PreferredBackupWindow": "03:00-03:30", "BackupRetentionPeriod": 30, "DBSecurityGroups": [], "VpcSecurityGroups": [{"VpcSecurityGroupId": "sg-97f7edf5", "Status": "active"}, {"VpcSecurityGroupId": "sg-dd9445b2", "Status": "active"}], "DBParameterGroups": [{"DBParameterGroupName": "contacts-prod-56", "ParameterApplyStatus": "in-sync"}], "AvailabilityZone": "us-east-1b", "DBSubnetGroup": {"DBSubnetGroupName": "prod-private", "DBSubnetGroupDescription": "VPC private subnet group", "VpcId": "vpc-9318d5f8", "SubnetGroupStatus": "Complete", "Subnets": [{"SubnetIdentifier": "subnet-5e1bd635", "SubnetAvailabilityZone": {"Name": "us-east-1d"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-601bd60b", "SubnetAvailabilityZone": {"Name": "us-east-1a"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-421bd629", "SubnetAvailabilityZone": {"Name": "us-east-1c"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-711bd61a", "SubnetAvailabilityZone": {"Name": "us-east-1b"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}]}, "PreferredMaintenanceWindow": "sat:05:00-sat:06:00", "PendingModifiedValues": {}, "LatestRestorableTime": "2021-04-07T14:35:00+00:00", "MultiAZ": true, "EngineVersion": "5.6.40", "AutoMinorVersionUpgrade": true, "ReadReplicaDBInstanceIdentifiers": ["contacts-prod-replica"], "LicenseModel": "general-public-license", "OptionGroupMemberships": [{"OptionGroupName": "default:mysql-5-6", "Status": "in-sync"}], "SecondaryAvailabilityZone": "us-east-1c", "PubliclyAccessible": false, "StorageType": "gp2", "DbInstancePort": 0, "StorageEncrypted": false, "DbiResourceId": "db-NDPJFFOJDJNAF5OO6KDZIXYDSU", "CACertificateIdentifier": "rds-ca-2019", "DomainMemberships": [], "CopyTagsToSnapshot": true, "MonitoringInterval": 60, "EnhancedMonitoringResourceArn": "arn:aws:logs:us-east-1:037590317780:log-group:RDSOSMetrics:log-stream:db-NDPJFFOJDJNAF5OO6KDZIXYDSU", "MonitoringRoleArn": "arn:aws:iam::037590317780:role/rds-monitoring-role", "DBInstanceArn": "arn:aws:rds:us-east-1:037590317780:db:contacts-prod", "IAMDatabaseAuthenticationEnabled": false, "EnabledCloudwatchLogsExports": ["audit", "error", "general", "slowquery"], "DeletionProtection": false, "AssociatedRoles": [], "TagList": [{"Key": "Type", "Value": "data_storage"}], "CustomerOwnedIpEnabled": false}, {"DBInstanceIdentifier": "contacts-prod-encrypted", "DBInstanceClass": "db.r4.xlarge", "Engine": "aurora", "DBInstanceStatus": "available", "MasterUsername": "contacts", "DBName": "contacts", "Endpoint": {"Address": "contacts-prod-encrypted.cg0lvth7azzh.us-east-1.rds.amazonaws.com", "Port": 3306, "HostedZoneId": "Z2R2ITUGPM61AM"}, "AllocatedStorage": 1750, "InstanceCreateTime": "2018-01-10T22:12:12.557000+00:00", "PreferredBackupWindow": "03:00-03:30", "BackupRetentionPeriod": 30, "DBSecurityGroups": [], "VpcSecurityGroups": [{"VpcSecurityGroupId": "sg-dd9445b2", "Status": "active"}, {"VpcSecurityGroupId": "sg-97f7edf5", "Status": "active"}], "DBParameterGroups": [{"DBParameterGroupName": "default.aurora5.6", "ParameterApplyStatus": "in-sync"}], "AvailabilityZone": "us-east-1d", "DBSubnetGroup": {"DBSubnetGroupName": "prod-private", "DBSubnetGroupDescription": "VPC private subnet group", "VpcId": "vpc-9318d5f8", "SubnetGroupStatus": "Complete", "Subnets": [{"SubnetIdentifier": "subnet-5e1bd635", "SubnetAvailabilityZone": {"Name": "us-east-1d"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-601bd60b", "SubnetAvailabilityZone": {"Name": "us-east-1a"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-421bd629", "SubnetAvailabilityZone": {"Name": "us-east-1c"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-711bd61a", "SubnetAvailabilityZone": {"Name": "us-east-1b"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}]}, "PreferredMaintenanceWindow": "wed:07:41-wed:08:11", "PendingModifiedValues": {}, "MultiAZ": false, "EngineVersion": "5.6.mysql_aurora.1.22.2", "AutoMinorVersionUpgrade": true, "ReadReplicaDBInstanceIdentifiers": [], "LicenseModel": "general-public-license", "OptionGroupMemberships": [{"OptionGroupName": "default:aurora-5-6", "Status": "in-sync"}], "PubliclyAccessible": false, "StorageType": "aurora", "DbInstancePort": 0, "DBClusterIdentifier": "contacts-prod-encrypted-cluster", "StorageEncrypted": true, "KmsKeyId": "arn:aws:kms:us-east-1:037590317780:key/f0e789fb-243c-4d74-ac99-bb5de68971a3", "DbiResourceId": "db-TGSJ5N5M4SMASE7XYYZIQSD5LA", "CACertificateIdentifier": "rds-ca-2019", "DomainMemberships": [], "CopyTagsToSnapshot": false, "MonitoringInterval": 0, "PromotionTier": 1, "DBInstanceArn": "arn:aws:rds:us-east-1:037590317780:db:contacts-prod-encrypted", "IAMDatabaseAuthenticationEnabled": false, "PerformanceInsightsEnabled": false, "DeletionProtection": false, "AssociatedRoles": [], "TagList": [], "CustomerOwnedIpEnabled": false}, {"DBInstanceIdentifier": "contacts-prod-encrypted-replica", "DBInstanceClass": "db.r5.large", "Engine": "aurora", "DBInstanceStatus": "available", "MasterUsername": "contacts", "DBName": "contacts", "Endpoint": {"Address": "contacts-prod-encrypted-replica.cg0lvth7azzh.us-east-1.rds.amazonaws.com", "Port": 3306, "HostedZoneId": "Z2R2ITUGPM61AM"}, "AllocatedStorage": 1750, "InstanceCreateTime": "2019-07-10T18:52:24.192000+00:00", "PreferredBackupWindow": "03:00-03:30", "BackupRetentionPeriod": 30, "DBSecurityGroups": [], "VpcSecurityGroups": [{"VpcSecurityGroupId": "sg-dd9445b2", "Status": "active"}, {"VpcSecurityGroupId": "sg-97f7edf5", "Status": "active"}], "DBParameterGroups": [{"DBParameterGroupName": "default.aurora5.6", "ParameterApplyStatus": "in-sync"}], "AvailabilityZone": "us-east-1c", "DBSubnetGroup": {"DBSubnetGroupName": "prod-private", "DBSubnetGroupDescription": "VPC private subnet group", "VpcId": "vpc-9318d5f8", "SubnetGroupStatus": "Complete", "Subnets": [{"SubnetIdentifier": "subnet-5e1bd635", "SubnetAvailabilityZone": {"Name": "us-east-1d"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-601bd60b", "SubnetAvailabilityZone": {"Name": "us-east-1a"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-421bd629", "SubnetAvailabilityZone": {"Name": "us-east-1c"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-711bd61a", "SubnetAvailabilityZone": {"Name": "us-east-1b"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}]}, "PreferredMaintenanceWindow": "wed:07:41-wed:08:11", "PendingModifiedValues": {}, "MultiAZ": false, "EngineVersion": "5.6.mysql_aurora.1.22.2", "AutoMinorVersionUpgrade": true, "ReadReplicaDBInstanceIdentifiers": [], "LicenseModel": "general-public-license", "OptionGroupMemberships": [{"OptionGroupName": "default:aurora-5-6", "Status": "in-sync"}], "PubliclyAccessible": false, "StorageType": "aurora", "DbInstancePort": 0, "DBClusterIdentifier": "contacts-prod-encrypted-cluster", "StorageEncrypted": true, "KmsKeyId": "arn:aws:kms:us-east-1:037590317780:key/f0e789fb-243c-4d74-ac99-bb5de68971a3", "DbiResourceId": "db-BNT5BRIZPWVLJPMALRPZRINJ6E", "CACertificateIdentifier": "rds-ca-2019", "DomainMemberships": [], "CopyTagsToSnapshot": false, "MonitoringInterval": 0, "PromotionTier": 1, "DBInstanceArn": "arn:aws:rds:us-east-1:037590317780:db:contacts-prod-encrypted-replica", "IAMDatabaseAuthenticationEnabled": false, "PerformanceInsightsEnabled": false, "DeletionProtection": false, "AssociatedRoles": [], "TagList": [], "CustomerOwnedIpEnabled": false}, {"DBInstanceIdentifier": "contacts-prod-replica", "DBInstanceClass": "db.m3.large", "Engine": "mysql", "DBInstanceStatus": "available", "MasterUsername": "contacts", "DBName": "contacts", "Endpoint": {"Address": "contacts-prod-replica.cg0lvth7azzh.us-east-1.rds.amazonaws.com", "Port": 3306, "HostedZoneId": "Z2R2ITUGPM61AM"}, "AllocatedStorage": 1750, "InstanceCreateTime": "2020-08-10T21:43:04.583000+00:00", "PreferredBackupWindow": "03:00-03:30", "BackupRetentionPeriod": 0, "DBSecurityGroups": [], "VpcSecurityGroups": [{"VpcSecurityGroupId": "sg-dd9445b2", "Status": "active"}, {"VpcSecurityGroupId": "sg-97f7edf5", "Status": "active"}], "DBParameterGroups": [{"DBParameterGroupName": "contacts-prod-56", "ParameterApplyStatus": "in-sync"}], "AvailabilityZone": "us-east-1b", "DBSubnetGroup": {"DBSubnetGroupName": "prod-private", "DBSubnetGroupDescription": "VPC private subnet group", "VpcId": "vpc-9318d5f8", "SubnetGroupStatus": "Complete", "Subnets": [{"SubnetIdentifier": "subnet-5e1bd635", "SubnetAvailabilityZone": {"Name": "us-east-1d"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-601bd60b", "SubnetAvailabilityZone": {"Name": "us-east-1a"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-421bd629", "SubnetAvailabilityZone": {"Name": "us-east-1c"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-711bd61a", "SubnetAvailabilityZone": {"Name": "us-east-1b"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}]}, "PreferredMaintenanceWindow": "sat:05:00-sat:06:00", "PendingModifiedValues": {}, "MultiAZ": false, "EngineVersion": "5.6.40", "AutoMinorVersionUpgrade": true, "ReadReplicaSourceDBInstanceIdentifier": "contacts-prod", "ReadReplicaDBInstanceIdentifiers": [], "LicenseModel": "general-public-license", "OptionGroupMemberships": [{"OptionGroupName": "default:mysql-5-6", "Status": "in-sync"}], "PubliclyAccessible": false, "StatusInfos": [{"StatusType": "read replication", "Normal": true, "Status": "replicating"}], "StorageType": "gp2", "DbInstancePort": 0, "StorageEncrypted": false, "DbiResourceId": "db-RBJG4Z5TSTLJELXQTZI56MNMQQ", "CACertificateIdentifier": "rds-ca-2019", "DomainMemberships": [], "CopyTagsToSnapshot": false, "MonitoringInterval": 0, "DBInstanceArn": "arn:aws:rds:us-east-1:037590317780:db:contacts-prod-replica", "IAMDatabaseAuthenticationEnabled": false, "PerformanceInsightsEnabled": false, "EnabledCloudwatchLogsExports": ["audit", "error", "general", "slowquery"], "DeletionProtection": false, "AssociatedRoles": [], "TagList": [], "CustomerOwnedIpEnabled": false}, {"DBInstanceIdentifier": "emma-prod", "DBInstanceClass": "db.t2.micro", "Engine": "mysql", "DBInstanceStatus": "available", "MasterUsername": "dan", "DBName": "eds", "Endpoint": {"Address": "emma-prod.cg0lvth7azzh.us-east-1.rds.amazonaws.com", "Port": 3306, "HostedZoneId": "Z2R2ITUGPM61AM"}, "AllocatedStorage": 20, "InstanceCreateTime": "2018-08-06T20:25:45.651000+00:00", "PreferredBackupWindow": "09:09-09:39", "BackupRetentionPeriod": 30, "DBSecurityGroups": [], "VpcSecurityGroups": [{"VpcSecurityGroupId": "sg-97f7edf5", "Status": "active"}, {"VpcSecurityGroupId": "sg-dd9445b2", "Status": "active"}], "DBParameterGroups": [{"DBParameterGroupName": "contacts-prod-56", "ParameterApplyStatus": "in-sync"}], "AvailabilityZone": "us-east-1b", "DBSubnetGroup": {"DBSubnetGroupName": "prod-private", "DBSubnetGroupDescription": "VPC private subnet group", "VpcId": "vpc-9318d5f8", "SubnetGroupStatus": "Complete", "Subnets": [{"SubnetIdentifier": "subnet-5e1bd635", "SubnetAvailabilityZone": {"Name": "us-east-1d"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-601bd60b", "SubnetAvailabilityZone": {"Name": "us-east-1a"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-421bd629", "SubnetAvailabilityZone": {"Name": "us-east-1c"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-711bd61a", "SubnetAvailabilityZone": {"Name": "us-east-1b"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}]}, "PreferredMaintenanceWindow": "mon:08:33-mon:09:03", "PendingModifiedValues": {}, "LatestRestorableTime": "2021-04-07T14:35:00+00:00", "MultiAZ": true, "EngineVersion": "5.6.39", "AutoMinorVersionUpgrade": true, "ReadReplicaDBInstanceIdentifiers": [], "LicenseModel": "general-public-license", "OptionGroupMemberships": [{"OptionGroupName": "default:mysql-5-6", "Status": "in-sync"}], "SecondaryAvailabilityZone": "us-east-1c", "PubliclyAccessible": false, "StorageType": "gp2", "DbInstancePort": 0, "StorageEncrypted": false, "DbiResourceId": "db-PQPKYDEECHLZFDXAVZO6XVULBQ", "CACertificateIdentifier": "rds-ca-2019", "DomainMemberships": [], "CopyTagsToSnapshot": true, "MonitoringInterval": 60, "EnhancedMonitoringResourceArn": "arn:aws:logs:us-east-1:037590317780:log-group:RDSOSMetrics:log-stream:db-PQPKYDEECHLZFDXAVZO6XVULBQ", "MonitoringRoleArn": "arn:aws:iam::037590317780:role/rds-monitoring-role", "DBInstanceArn": "arn:aws:rds:us-east-1:037590317780:db:emma-prod", "IAMDatabaseAuthenticationEnabled": false, "PerformanceInsightsEnabled": false, "EnabledCloudwatchLogsExports": ["audit", "error", "general", "slowquery"], "DeletionProtection": false, "AssociatedRoles": [], "TagList": [{"Key": "workload-type", "Value": "production"}], "CustomerOwnedIpEnabled": false}, {"DBInstanceIdentifier": "exporter", "DBInstanceClass": "db.t2.micro", "Engine": "mysql", "DBInstanceStatus": "available", "MasterUsername": "exporter", "DBName": "exporter", "Endpoint": {"Address": "exporter.cg0lvth7azzh.us-east-1.rds.amazonaws.com", "Port": 3306, "HostedZoneId": "Z2R2ITUGPM61AM"}, "AllocatedStorage": 20, "InstanceCreateTime": "2018-07-10T15:43:10.185000+00:00", "PreferredBackupWindow": "10:22-10:52", "BackupRetentionPeriod": 30, "DBSecurityGroups": [], "VpcSecurityGroups": [{"VpcSecurityGroupId": "sg-dd9445b2", "Status": "active"}, {"VpcSecurityGroupId": "sg-97f7edf5", "Status": "active"}], "DBParameterGroups": [{"DBParameterGroupName": "contacts-prod-56", "ParameterApplyStatus": "in-sync"}], "AvailabilityZone": "us-east-1c", "DBSubnetGroup": {"DBSubnetGroupName": "prod-private", "DBSubnetGroupDescription": "VPC private subnet group", "VpcId": "vpc-9318d5f8", "SubnetGroupStatus": "Complete", "Subnets": [{"SubnetIdentifier": "subnet-5e1bd635", "SubnetAvailabilityZone": {"Name": "us-east-1d"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-601bd60b", "SubnetAvailabilityZone": {"Name": "us-east-1a"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-421bd629", "SubnetAvailabilityZone": {"Name": "us-east-1c"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-711bd61a", "SubnetAvailabilityZone": {"Name": "us-east-1b"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}]}, "PreferredMaintenanceWindow": "tue:03:08-tue:03:38", "PendingModifiedValues": {}, "LatestRestorableTime": "2021-04-07T14:35:00+00:00", "MultiAZ": true, "EngineVersion": "5.6.39", "AutoMinorVersionUpgrade": true, "ReadReplicaDBInstanceIdentifiers": [], "LicenseModel": "general-public-license", "OptionGroupMemberships": [{"OptionGroupName": "default:mysql-5-6", "Status": "in-sync"}], "SecondaryAvailabilityZone": "us-east-1b", "PubliclyAccessible": false, "StorageType": "gp2", "DbInstancePort": 0, "StorageEncrypted": false, "DbiResourceId": "db-EXZQEPT7GXZU2T337MQRL344UM", "CACertificateIdentifier": "rds-ca-2019", "DomainMemberships": [], "CopyTagsToSnapshot": true, "MonitoringInterval": 60, "EnhancedMonitoringResourceArn": "arn:aws:logs:us-east-1:037590317780:log-group:RDSOSMetrics:log-stream:db-EXZQEPT7GXZU2T337MQRL344UM", "MonitoringRoleArn": "arn:aws:iam::037590317780:role/rds-monitoring-role", "DBInstanceArn": "arn:aws:rds:us-east-1:037590317780:db:exporter", "IAMDatabaseAuthenticationEnabled": false, "PerformanceInsightsEnabled": false, "DeletionProtection": false, "AssociatedRoles": [], "TagList": [{"Key": "workload-type", "Value": "production"}], "CustomerOwnedIpEnabled": false}, {"DBInstanceIdentifier": "give-prod", "DBInstanceClass": "db.m3.medium", "Engine": "mysql", "DBInstanceStatus": "available", "MasterUsername": "<PERSON><PERSON><PERSON>", "Endpoint": {"Address": "give-prod.cg0lvth7azzh.us-east-1.rds.amazonaws.com", "Port": 3306, "HostedZoneId": "Z2R2ITUGPM61AM"}, "AllocatedStorage": 5, "InstanceCreateTime": "2016-07-15T19:18:57.082000+00:00", "PreferredBackupWindow": "06:41-07:11", "BackupRetentionPeriod": 35, "DBSecurityGroups": [], "VpcSecurityGroups": [{"VpcSecurityGroupId": "sg-97f7edf5", "Status": "active"}, {"VpcSecurityGroupId": "sg-0cc33263", "Status": "active"}], "DBParameterGroups": [{"DBParameterGroupName": "evertrue-mysql56", "ParameterApplyStatus": "in-sync"}], "AvailabilityZone": "us-east-1b", "DBSubnetGroup": {"DBSubnetGroupName": "prod-private", "DBSubnetGroupDescription": "VPC private subnet group", "VpcId": "vpc-9318d5f8", "SubnetGroupStatus": "Complete", "Subnets": [{"SubnetIdentifier": "subnet-5e1bd635", "SubnetAvailabilityZone": {"Name": "us-east-1d"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-601bd60b", "SubnetAvailabilityZone": {"Name": "us-east-1a"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-421bd629", "SubnetAvailabilityZone": {"Name": "us-east-1c"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-711bd61a", "SubnetAvailabilityZone": {"Name": "us-east-1b"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}]}, "PreferredMaintenanceWindow": "sat:04:20-sat:04:50", "PendingModifiedValues": {}, "LatestRestorableTime": "2021-04-07T14:35:00+00:00", "MultiAZ": true, "EngineVersion": "5.6.40", "AutoMinorVersionUpgrade": true, "ReadReplicaDBInstanceIdentifiers": [], "LicenseModel": "general-public-license", "OptionGroupMemberships": [{"OptionGroupName": "default:mysql-5-6", "Status": "in-sync"}], "SecondaryAvailabilityZone": "us-east-1c", "PubliclyAccessible": false, "StorageType": "gp2", "DbInstancePort": 0, "StorageEncrypted": true, "KmsKeyId": "arn:aws:kms:us-east-1:037590317780:key/e8d1c3c8-2b95-4965-bb0e-70d3195a02c5", "DbiResourceId": "db-LJK6MHIGKW3UOXAM4JAKB374TY", "CACertificateIdentifier": "rds-ca-2019", "DomainMemberships": [], "CopyTagsToSnapshot": true, "MonitoringInterval": 60, "EnhancedMonitoringResourceArn": "arn:aws:logs:us-east-1:037590317780:log-group:RDSOSMetrics:log-stream:db-LJK6MHIGKW3UOXAM4JAKB374TY", "MonitoringRoleArn": "arn:aws:iam::037590317780:role/rds-monitoring-role", "DBInstanceArn": "arn:aws:rds:us-east-1:037590317780:db:give-prod", "IAMDatabaseAuthenticationEnabled": false, "DeletionProtection": false, "AssociatedRoles": [], "TagList": [{"Key": "Env", "Value": "prod"}, {"Key": "Job", "Value": "api-db"}, {"Key": "workload-type", "Value": "production"}], "CustomerOwnedIpEnabled": false}, {"DBInstanceIdentifier": "prod-data-importer-aurora", "DBInstanceClass": "db.r3.xlarge", "Engine": "aurora", "DBInstanceStatus": "available", "MasterUsername": "importer", "Endpoint": {"Address": "prod-data-importer-aurora.cg0lvth7azzh.us-east-1.rds.amazonaws.com", "Port": 3306, "HostedZoneId": "Z2R2ITUGPM61AM"}, "AllocatedStorage": 1, "InstanceCreateTime": "2017-08-01T16:46:48.639000+00:00", "PreferredBackupWindow": "08:33-09:03", "BackupRetentionPeriod": 30, "DBSecurityGroups": [], "VpcSecurityGroups": [{"VpcSecurityGroupId": "sg-97f7edf5", "Status": "active"}, {"VpcSecurityGroupId": "sg-0cc33263", "Status": "active"}], "DBParameterGroups": [{"DBParameterGroupName": "default.aurora5.6", "ParameterApplyStatus": "in-sync"}], "AvailabilityZone": "us-east-1c", "DBSubnetGroup": {"DBSubnetGroupName": "prod-private", "DBSubnetGroupDescription": "VPC private subnet group", "VpcId": "vpc-9318d5f8", "SubnetGroupStatus": "Complete", "Subnets": [{"SubnetIdentifier": "subnet-5e1bd635", "SubnetAvailabilityZone": {"Name": "us-east-1d"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-601bd60b", "SubnetAvailabilityZone": {"Name": "us-east-1a"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-421bd629", "SubnetAvailabilityZone": {"Name": "us-east-1c"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-711bd61a", "SubnetAvailabilityZone": {"Name": "us-east-1b"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}]}, "PreferredMaintenanceWindow": "fri:06:21-fri:06:51", "PendingModifiedValues": {}, "MultiAZ": false, "EngineVersion": "5.6.mysql_aurora.1.22.2", "AutoMinorVersionUpgrade": true, "ReadReplicaDBInstanceIdentifiers": [], "LicenseModel": "general-public-license", "OptionGroupMemberships": [{"OptionGroupName": "default:aurora-5-6", "Status": "in-sync"}], "PubliclyAccessible": false, "StorageType": "aurora", "DbInstancePort": 0, "DBClusterIdentifier": "prod-data-importer-aurora-cluster", "StorageEncrypted": false, "DbiResourceId": "db-CYAO3LYR5AR6ZPLJC7NYNQEZQI", "CACertificateIdentifier": "rds-ca-2019", "DomainMemberships": [], "CopyTagsToSnapshot": false, "MonitoringInterval": 60, "EnhancedMonitoringResourceArn": "arn:aws:logs:us-east-1:037590317780:log-group:RDSOSMetrics:log-stream:db-CYAO3LYR5AR6ZPLJC7NYNQEZQI", "MonitoringRoleArn": "arn:aws:iam::037590317780:role/rds-monitoring-role", "PromotionTier": 15, "DBInstanceArn": "arn:aws:rds:us-east-1:037590317780:db:prod-data-importer-aurora", "IAMDatabaseAuthenticationEnabled": false, "PerformanceInsightsEnabled": false, "DeletionProtection": false, "AssociatedRoles": [], "TagList": [], "CustomerOwnedIpEnabled": false}, {"DBInstanceIdentifier": "prod-data-importer-copy", "DBInstanceClass": "db.m3.xlarge", "Engine": "mysql", "DBInstanceStatus": "available", "MasterUsername": "importer", "Endpoint": {"Address": "prod-data-importer-copy.cg0lvth7azzh.us-east-1.rds.amazonaws.com", "Port": 3306, "HostedZoneId": "Z2R2ITUGPM61AM"}, "AllocatedStorage": 200, "InstanceCreateTime": "2017-08-07T04:09:30.137000+00:00", "PreferredBackupWindow": "05:06-05:36", "BackupRetentionPeriod": 30, "DBSecurityGroups": [], "VpcSecurityGroups": [{"VpcSecurityGroupId": "sg-97f7edf5", "Status": "active"}, {"VpcSecurityGroupId": "sg-0cc33263", "Status": "active"}], "DBParameterGroups": [{"DBParameterGroupName": "evertrue-mysql56", "ParameterApplyStatus": "in-sync"}], "AvailabilityZone": "us-east-1b", "DBSubnetGroup": {"DBSubnetGroupName": "prod-private", "DBSubnetGroupDescription": "VPC private subnet group", "VpcId": "vpc-9318d5f8", "SubnetGroupStatus": "Complete", "Subnets": [{"SubnetIdentifier": "subnet-5e1bd635", "SubnetAvailabilityZone": {"Name": "us-east-1d"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-601bd60b", "SubnetAvailabilityZone": {"Name": "us-east-1a"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-421bd629", "SubnetAvailabilityZone": {"Name": "us-east-1c"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-711bd61a", "SubnetAvailabilityZone": {"Name": "us-east-1b"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}]}, "PreferredMaintenanceWindow": "fri:06:21-fri:06:51", "PendingModifiedValues": {}, "LatestRestorableTime": "2021-04-07T14:35:00+00:00", "MultiAZ": true, "EngineVersion": "5.6.34", "AutoMinorVersionUpgrade": true, "ReadReplicaDBInstanceIdentifiers": [], "LicenseModel": "general-public-license", "Iops": 2000, "OptionGroupMemberships": [{"OptionGroupName": "default:mysql-5-6", "Status": "in-sync"}], "SecondaryAvailabilityZone": "us-east-1d", "PubliclyAccessible": false, "StorageType": "io1", "DbInstancePort": 0, "StorageEncrypted": false, "DbiResourceId": "db-GQZZEP5BIXGIHCADCX3NSCOENQ", "CACertificateIdentifier": "rds-ca-2019", "DomainMemberships": [], "CopyTagsToSnapshot": false, "MonitoringInterval": 0, "DBInstanceArn": "arn:aws:rds:us-east-1:037590317780:db:prod-data-importer-copy", "IAMDatabaseAuthenticationEnabled": false, "PerformanceInsightsEnabled": false, "DeletionProtection": false, "AssociatedRoles": [], "TagList": [], "CustomerOwnedIpEnabled": false}, {"DBInstanceIdentifier": "prod-singularity-copy", "DBInstanceClass": "db.t2.micro", "Engine": "mysql", "DBInstanceStatus": "available", "MasterUsername": "master", "DBName": "singularity", "Endpoint": {"Address": "prod-singularity-copy.cg0lvth7azzh.us-east-1.rds.amazonaws.com", "Port": 3306, "HostedZoneId": "Z2R2ITUGPM61AM"}, "AllocatedStorage": 10, "InstanceCreateTime": "2017-03-30T00:31:27.546000+00:00", "PreferredBackupWindow": "08:49-09:19", "BackupRetentionPeriod": 7, "DBSecurityGroups": [], "VpcSecurityGroups": [{"VpcSecurityGroupId": "sg-6d288f0b", "Status": "active"}, {"VpcSecurityGroupId": "sg-97f7edf5", "Status": "active"}], "DBParameterGroups": [{"DBParameterGroupName": "singularity-mysql56", "ParameterApplyStatus": "in-sync"}], "AvailabilityZone": "us-east-1b", "DBSubnetGroup": {"DBSubnetGroupName": "prod-private", "DBSubnetGroupDescription": "VPC private subnet group", "VpcId": "vpc-9318d5f8", "SubnetGroupStatus": "Complete", "Subnets": [{"SubnetIdentifier": "subnet-5e1bd635", "SubnetAvailabilityZone": {"Name": "us-east-1d"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-601bd60b", "SubnetAvailabilityZone": {"Name": "us-east-1a"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-421bd629", "SubnetAvailabilityZone": {"Name": "us-east-1c"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-711bd61a", "SubnetAvailabilityZone": {"Name": "us-east-1b"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}]}, "PreferredMaintenanceWindow": "wed:06:02-wed:06:32", "PendingModifiedValues": {}, "LatestRestorableTime": "2021-04-07T14:35:00+00:00", "MultiAZ": true, "EngineVersion": "5.6.34", "AutoMinorVersionUpgrade": true, "ReadReplicaDBInstanceIdentifiers": [], "LicenseModel": "general-public-license", "OptionGroupMemberships": [{"OptionGroupName": "default:mysql-5-6", "Status": "in-sync"}], "SecondaryAvailabilityZone": "us-east-1c", "PubliclyAccessible": false, "StorageType": "standard", "DbInstancePort": 0, "StorageEncrypted": false, "DbiResourceId": "db-3IOR5DXHQOOLOQZHZRRJZVZIOI", "CACertificateIdentifier": "rds-ca-2019", "DomainMemberships": [], "CopyTagsToSnapshot": true, "MonitoringInterval": 60, "EnhancedMonitoringResourceArn": "arn:aws:logs:us-east-1:037590317780:log-group:RDSOSMetrics:log-stream:db-3IOR5DXHQOOLOQZHZRRJZVZIOI", "MonitoringRoleArn": "arn:aws:iam::037590317780:role/rds-monitoring-role", "DBInstanceArn": "arn:aws:rds:us-east-1:037590317780:db:prod-singularity-copy", "IAMDatabaseAuthenticationEnabled": false, "PerformanceInsightsEnabled": false, "DeletionProtection": false, "AssociatedRoles": [], "TagList": [], "CustomerOwnedIpEnabled": false}, {"DBInstanceIdentifier": "sodas-prod", "DBInstanceClass": "db.r5.xlarge", "Engine": "aurora", "DBInstanceStatus": "available", "MasterUsername": "importer", "Endpoint": {"Address": "sodas-prod.cg0lvth7azzh.us-east-1.rds.amazonaws.com", "Port": 3306, "HostedZoneId": "Z2R2ITUGPM61AM"}, "AllocatedStorage": 1, "InstanceCreateTime": "2019-04-05T19:02:06.443000+00:00", "PreferredBackupWindow": "05:19-05:49", "BackupRetentionPeriod": 30, "DBSecurityGroups": [], "VpcSecurityGroups": [{"VpcSecurityGroupId": "sg-97f7edf5", "Status": "active"}, {"VpcSecurityGroupId": "sg-0ffaa319007f0d5ad", "Status": "active"}], "DBParameterGroups": [{"DBParameterGroupName": "default.aurora5.6", "ParameterApplyStatus": "in-sync"}], "AvailabilityZone": "us-east-1d", "DBSubnetGroup": {"DBSubnetGroupName": "prod-private", "DBSubnetGroupDescription": "VPC private subnet group", "VpcId": "vpc-9318d5f8", "SubnetGroupStatus": "Complete", "Subnets": [{"SubnetIdentifier": "subnet-5e1bd635", "SubnetAvailabilityZone": {"Name": "us-east-1d"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-601bd60b", "SubnetAvailabilityZone": {"Name": "us-east-1a"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-421bd629", "SubnetAvailabilityZone": {"Name": "us-east-1c"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-711bd61a", "SubnetAvailabilityZone": {"Name": "us-east-1b"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}]}, "PreferredMaintenanceWindow": "wed:07:00-wed:07:30", "PendingModifiedValues": {}, "MultiAZ": false, "EngineVersion": "5.6.mysql_aurora.1.22.2", "AutoMinorVersionUpgrade": true, "ReadReplicaDBInstanceIdentifiers": [], "LicenseModel": "general-public-license", "OptionGroupMemberships": [{"OptionGroupName": "default:aurora-5-6", "Status": "in-sync"}], "PubliclyAccessible": false, "StorageType": "aurora", "DbInstancePort": 0, "DBClusterIdentifier": "sodas-prod-cluster", "StorageEncrypted": true, "KmsKeyId": "arn:aws:kms:us-east-1:037590317780:key/0618ac1a-6cac-4521-8995-c230bcdb5b41", "DbiResourceId": "db-J77L2ED7M5CCAGBSIVFGRSZJ7A", "CACertificateIdentifier": "rds-ca-2019", "DomainMemberships": [], "CopyTagsToSnapshot": false, "MonitoringInterval": 0, "PromotionTier": 1, "DBInstanceArn": "arn:aws:rds:us-east-1:037590317780:db:sodas-prod", "IAMDatabaseAuthenticationEnabled": false, "PerformanceInsightsEnabled": false, "DeletionProtection": false, "AssociatedRoles": [], "TagList": [], "CustomerOwnedIpEnabled": false}, {"DBInstanceIdentifier": "stage-etdb-2-copy", "DBInstanceClass": "db.m3.xlarge", "Engine": "mysql", "DBInstanceStatus": "available", "MasterUsername": "prodadmin", "Endpoint": {"Address": "stage-etdb-2-copy.cg0lvth7azzh.us-east-1.rds.amazonaws.com", "Port": 3306, "HostedZoneId": "Z2R2ITUGPM61AM"}, "AllocatedStorage": 100, "InstanceCreateTime": "2017-03-30T00:24:15.953000+00:00", "PreferredBackupWindow": "10:26-10:56", "BackupRetentionPeriod": 1, "DBSecurityGroups": [], "VpcSecurityGroups": [{"VpcSecurityGroupId": "sg-8ae55eef", "Status": "active"}, {"VpcSecurityGroupId": "sg-97e55ef2", "Status": "active"}], "DBParameterGroups": [{"DBParameterGroupName": "default.mysql5.7", "ParameterApplyStatus": "in-sync"}], "AvailabilityZone": "us-east-1b", "DBSubnetGroup": {"DBSubnetGroupName": "stage-private-newvpc", "DBSubnetGroupDescription": "Stage Private", "VpcId": "vpc-1e45b27b", "SubnetGroupStatus": "Complete", "Subnets": [{"SubnetIdentifier": "subnet-09b2b87d", "SubnetAvailabilityZone": {"Name": "us-east-1c"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-34e9de72", "SubnetAvailabilityZone": {"Name": "us-east-1d"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-ffcfda9d", "SubnetAvailabilityZone": {"Name": "us-east-1a"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-a792da8f", "SubnetAvailabilityZone": {"Name": "us-east-1b"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}]}, "PreferredMaintenanceWindow": "mon:05:41-mon:06:11", "PendingModifiedValues": {}, "LatestRestorableTime": "2021-04-07T14:35:00+00:00", "MultiAZ": false, "EngineVersion": "5.7.31", "AutoMinorVersionUpgrade": true, "ReadReplicaDBInstanceIdentifiers": [], "LicenseModel": "general-public-license", "OptionGroupMemberships": [{"OptionGroupName": "default:mysql-5-7", "Status": "in-sync"}], "PubliclyAccessible": false, "StorageType": "gp2", "DbInstancePort": 0, "StorageEncrypted": false, "DbiResourceId": "db-MQ4YYQOLKEC5WIF3643GO7SQ5U", "CACertificateIdentifier": "rds-ca-2019", "DomainMemberships": [], "CopyTagsToSnapshot": true, "MonitoringInterval": 60, "EnhancedMonitoringResourceArn": "arn:aws:logs:us-east-1:037590317780:log-group:RDSOSMetrics:log-stream:db-MQ4YYQOLKEC5WIF3643GO7SQ5U", "MonitoringRoleArn": "arn:aws:iam::037590317780:role/rds-monitoring-role", "DBInstanceArn": "arn:aws:rds:us-east-1:037590317780:db:stage-etdb-2-copy", "IAMDatabaseAuthenticationEnabled": false, "PerformanceInsightsEnabled": false, "DeletionProtection": false, "AssociatedRoles": [], "TagList": [], "CustomerOwnedIpEnabled": false}, {"DBInstanceIdentifier": "stage-singularity", "DBInstanceClass": "db.t2.micro", "Engine": "mysql", "DBInstanceStatus": "available", "MasterUsername": "master", "DBName": "singularity", "Endpoint": {"Address": "stage-singularity.cg0lvth7azzh.us-east-1.rds.amazonaws.com", "Port": 3306, "HostedZoneId": "Z2R2ITUGPM61AM"}, "AllocatedStorage": 10, "InstanceCreateTime": "2015-10-13T19:56:06.573000+00:00", "PreferredBackupWindow": "10:02-10:32", "BackupRetentionPeriod": 0, "DBSecurityGroups": [], "VpcSecurityGroups": [{"VpcSecurityGroupId": "sg-cc3bfcaa", "Status": "active"}, {"VpcSecurityGroupId": "sg-97e55ef2", "Status": "active"}], "DBParameterGroups": [{"DBParameterGroupName": "singularity-mysql56", "ParameterApplyStatus": "in-sync"}], "AvailabilityZone": "us-east-1b", "DBSubnetGroup": {"DBSubnetGroupName": "stage-private-newvpc", "DBSubnetGroupDescription": "Stage Private", "VpcId": "vpc-1e45b27b", "SubnetGroupStatus": "Complete", "Subnets": [{"SubnetIdentifier": "subnet-09b2b87d", "SubnetAvailabilityZone": {"Name": "us-east-1c"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-34e9de72", "SubnetAvailabilityZone": {"Name": "us-east-1d"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-ffcfda9d", "SubnetAvailabilityZone": {"Name": "us-east-1a"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-a792da8f", "SubnetAvailabilityZone": {"Name": "us-east-1b"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}]}, "PreferredMaintenanceWindow": "fri:03:31-fri:04:01", "PendingModifiedValues": {}, "MultiAZ": false, "EngineVersion": "5.6.34", "AutoMinorVersionUpgrade": true, "ReadReplicaDBInstanceIdentifiers": [], "LicenseModel": "general-public-license", "OptionGroupMemberships": [{"OptionGroupName": "default:mysql-5-6", "Status": "in-sync"}], "PubliclyAccessible": false, "StorageType": "standard", "DbInstancePort": 0, "StorageEncrypted": false, "DbiResourceId": "db-SWAHDMOCXIQW3FD3X3DMYWNUJY", "CACertificateIdentifier": "rds-ca-2019", "DomainMemberships": [], "CopyTagsToSnapshot": false, "MonitoringInterval": 0, "DBInstanceArn": "arn:aws:rds:us-east-1:037590317780:db:stage-singularity", "IAMDatabaseAuthenticationEnabled": false, "DeletionProtection": false, "AssociatedRoles": [], "TagList": [{"Key": "Env", "Value": "stage"}, {"Key": "Job", "Value": "singularity-db"}, {"Key": "workload-type", "Value": "other"}], "CustomerOwnedIpEnabled": false}, {"DBInstanceIdentifier": "suggestions", "DBInstanceClass": "db.t2.micro", "Engine": "mysql", "DBInstanceStatus": "available", "MasterUsername": "suggestions", "DBName": "suggestions", "Endpoint": {"Address": "suggestions.cg0lvth7azzh.us-east-1.rds.amazonaws.com", "Port": 3306, "HostedZoneId": "Z2R2ITUGPM61AM"}, "AllocatedStorage": 20, "InstanceCreateTime": "2018-07-31T20:35:07+00:00", "PreferredBackupWindow": "09:04-09:34", "BackupRetentionPeriod": 30, "DBSecurityGroups": [], "VpcSecurityGroups": [{"VpcSecurityGroupId": "sg-dd9445b2", "Status": "active"}, {"VpcSecurityGroupId": "sg-97f7edf5", "Status": "active"}], "DBParameterGroups": [{"DBParameterGroupName": "contacts-prod-56", "ParameterApplyStatus": "in-sync"}], "AvailabilityZone": "us-east-1d", "DBSubnetGroup": {"DBSubnetGroupName": "prod-private", "DBSubnetGroupDescription": "VPC private subnet group", "VpcId": "vpc-9318d5f8", "SubnetGroupStatus": "Complete", "Subnets": [{"SubnetIdentifier": "subnet-5e1bd635", "SubnetAvailabilityZone": {"Name": "us-east-1d"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-601bd60b", "SubnetAvailabilityZone": {"Name": "us-east-1a"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-421bd629", "SubnetAvailabilityZone": {"Name": "us-east-1c"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}, {"SubnetIdentifier": "subnet-711bd61a", "SubnetAvailabilityZone": {"Name": "us-east-1b"}, "SubnetOutpost": {}, "SubnetStatus": "Active"}]}, "PreferredMaintenanceWindow": "mon:05:43-mon:06:13", "PendingModifiedValues": {}, "LatestRestorableTime": "2021-04-07T14:35:00+00:00", "MultiAZ": true, "EngineVersion": "5.6.39", "AutoMinorVersionUpgrade": true, "ReadReplicaDBInstanceIdentifiers": [], "LicenseModel": "general-public-license", "OptionGroupMemberships": [{"OptionGroupName": "default:mysql-5-6", "Status": "in-sync"}], "SecondaryAvailabilityZone": "us-east-1b", "PubliclyAccessible": false, "StorageType": "gp2", "DbInstancePort": 0, "StorageEncrypted": false, "DbiResourceId": "db-LIZ4INDSCK7QTKJUS7WEKD64EA", "CACertificateIdentifier": "rds-ca-2019", "DomainMemberships": [], "CopyTagsToSnapshot": true, "MonitoringInterval": 60, "EnhancedMonitoringResourceArn": "arn:aws:logs:us-east-1:037590317780:log-group:RDSOSMetrics:log-stream:db-LIZ4INDSCK7QTKJUS7WEKD64EA", "MonitoringRoleArn": "arn:aws:iam::037590317780:role/rds-monitoring-role", "DBInstanceArn": "arn:aws:rds:us-east-1:037590317780:db:suggestions", "IAMDatabaseAuthenticationEnabled": false, "PerformanceInsightsEnabled": false, "DeletionProtection": false, "AssociatedRoles": [], "TagList": [{"Key": "workload-type", "Value": "production"}], "CustomerOwnedIpEnabled": false}]}