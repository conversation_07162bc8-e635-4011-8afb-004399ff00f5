#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)

CSV.open("evertrue-dbs.csv", 'wb') do |out|
  out << ['schema', 'table', 'column', 'type']

  DynamicActiveModelGenerator::DATABASES.each do |db_name|
    database = DynamicActiveModelGenerator.create_database_models(db_name)
    database.models.each do |model|
      model.columns.each do |column|
        out << [db_name, model.table_name, column.name, column.sql_type]
      end
    end
  end
end
