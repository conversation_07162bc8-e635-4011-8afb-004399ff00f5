#!/usr/bin/env ruby

# Purpose: to convert all DBs and tables to utf8mb4

=begin
Database facepalm manual changes

ALTER TABLE monthly_page_stats DROP FOREIGN KEY fk_monthly_page_stats_pages;
ALTER TABLE monthly_recent_posts DROP FOREIGN KEY fk_monthly_recent_posts_pages;
ALTER TABLE monthly_top_users DROP FOREIGN KEY fk_monthly_top_users_pages;
ALTER TABLE recent_posts DROP FOREIGN KEY fk_recent_posts_pages;
ALTER TABLE top_users DROP FOREIGN KEY fk_top_users_pages;
ALTER TABLE monthly_top_posts DROP FOREIGN KEY fk_monthly_top_posts_pages;
ALTER TABLE top_posts DROP FOREIGN KEY fk_top_posts_pages;

ALTER TABLE pages CONVERT TO CHARACTER SET utf8mb4;
ALTER TABLE monthly_page_stats CONVERT TO CHARACTER SET utf8mb4;
ALTER TABLE monthly_recent_posts CONVERT TO CHARACTER SET utf8mb4;
ALTER TABLE monthly_top_users CONVERT TO CHARACTER SET utf8mb4;
ALTER TABLE recent_posts CONVERT TO CHARACTER SET utf8mb4;
ALTER TABLE top_users CONVERT TO CHARACTER SET utf8mb4;
ALTER TABLE monthly_top_posts CONVERT TO CHARACTER SET utf8mb4;
ALTER TABLE top_posts CONVERT TO CHARACTER SET utf8mb4;

ALTER TABLE monthly_page_stats ADD FOREIGN KEY fk_monthly_page_stats_pages (page_id) REFERENCES pages (page_id);
ALTER TABLE monthly_recent_posts ADD FOREIGN KEY fk_monthly_recent_posts_pages (page_id) REFERENCES pages (page_id);
ALTER TABLE monthly_top_users ADD FOREIGN KEY fk_monthly_top_users_pages (page_id) REFERENCES pages (page_id);
ALTER TABLE recent_posts ADD FOREIGN KEY fk_recent_posts_pages (page_id) REFERENCES pages (page_id);
ALTER TABLE top_users ADD FOREIGN KEY fk_top_users_pages (page_id) REFERENCES pages (page_id);
ALTER TABLE monthly_top_posts ADD FOREIGN KEY fk_monthly_top_posts_pages (page_id) REFERENCES pages (page_id);
ALTER TABLE top_posts ADD FOREIGN KEY fk_top_posts_pages (page_id) REFERENCES pages (page_id);

=end

=begin
Database chronometer manual changes

ALTER TABLE QRTZ_BLOB_TRIGGERS DROP FOREIGN KEY QRTZ_BLOB_TRIGGERS_ibfk_1;
ALTER TABLE QRTZ_CRON_TRIGGERS DROP FOREIGN KEY QRTZ_CRON_TRIGGERS_ibfk_1;
ALTER TABLE QRTZ_SIMPLE_TRIGGERS DROP FOREIGN KEY QRTZ_SIMPLE_TRIGGERS_ibfk_1;
ALTER TABLE QRTZ_SIMPROP_TRIGGERS DROP FOREIGN KEY QRTZ_SIMPROP_TRIGGERS_ibfk_1;
ALTER TABLE QRTZ_TRIGGERS DROP FOREIGN KEY QRTZ_TRIGGERS_ibfk_1;

ALTER TABLE QRTZ_BLOB_TRIGGERS CONVERT TO CHARACTER SET utf8mb4;
ALTER TABLE QRTZ_CALENDARS CONVERT TO CHARACTER SET utf8mb4;
ALTER TABLE QRTZ_CRON_TRIGGERS CONVERT TO CHARACTER SET utf8mb4;
ALTER TABLE QRTZ_FIRED_TRIGGERS CONVERT TO CHARACTER SET utf8mb4;
ALTER TABLE QRTZ_JOB_DETAILS CONVERT TO CHARACTER SET utf8mb4;
ALTER TABLE QRTZ_LOCKS CONVERT TO CHARACTER SET utf8mb4;
ALTER TABLE QRTZ_PAUSED_TRIGGER_GRPS CONVERT TO CHARACTER SET utf8mb4;
ALTER TABLE QRTZ_SCHEDULER_STATE CONVERT TO CHARACTER SET utf8mb4;
ALTER TABLE QRTZ_SIMPLE_TRIGGERS CONVERT TO CHARACTER SET utf8mb4;
ALTER TABLE QRTZ_SIMPROP_TRIGGERS CONVERT TO CHARACTER SET utf8mb4;
ALTER TABLE QRTZ_TRIGGERS CONVERT TO CHARACTER SET utf8mb4;

ALTER TABLE QRTZ_BLOB_TRIGGERS ADD FOREIGN KEY QRTZ_BLOB_TRIGGERS_ibfk_1 (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `QRTZ_TRIGGERS` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`);
ALTER TABLE QRTZ_CRON_TRIGGERS ADD FOREIGN KEY QRTZ_CRON_TRIGGERS_ibfk_1 (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `QRTZ_TRIGGERS` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`);
ALTER TABLE QRTZ_SIMPLE_TRIGGERS ADD FOREIGN KEY QRTZ_SIMPLE_TRIGGERS_ibfk_1 (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `QRTZ_TRIGGERS` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`);
ALTER TABLE QRTZ_SIMPROP_TRIGGERS ADD FOREIGN KEY QRTZ_SIMPROP_TRIGGERS_ibfk_1 (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `QRTZ_TRIGGERS` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`);
ALTER TABLE QRTZ_TRIGGERS ADD FOREIGN KEY QRTZ_TRIGGERS_ibfk_1 (`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) REFERENCES `QRTZ_JOB_DETAILS` (`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`);

=end

=begin
Database ugc manual changes

DROP INDEX index_oid_data_type_type ON ugc_types;
CREATE UNIQUE INDEX index_oid_data_type_type ON ugc_types (oid, data_type, type(50));

=end

require_relative '../config/script_options'

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-r', '--rds RDS', 'RDS name') do |v|
    opts[:rds] = v
  end

  opts.on('-h', '--host HOST', 'Host override') do |v|
    opts[:host] = v
  end

  opts.require_option(:rds)
end

require_relative '../config/environment'
require 'active_support/core_ext/hash'

def get_character_set_name(client, database, table)
  sql = <<SQL
SELECT
  CCSA.character_set_name FROM information_schema.`TABLES` T
JOIN information_schema.`COLLATION_CHARACTER_SET_APPLICABILITY` CCSA
  ON CCSA.collation_name = T.table_collation
WHERE
  T.table_schema = "#{database}"
  AND T.table_name = "#{table}";
SQL

  results = client.query(sql)
  results && results.first ? (results.first['character_set_name'] || results.first['CHARACTER_SET_NAME']) : nil
end

def convert_character_set_to_utf8mb4(client, table)
  sql = "ALTER TABLE #{table} CONVERT TO CHARACTER SET utf8mb4"
  client.query(sql)
end

def convert_all_tables_to_utf8mb4(client, database)
  client.query('SHOW TABLES').each do |result|
    table = result["Tables_in_#{database}"]
    puts "checking #{table}"
    character_set = get_character_set_name(client, database, table)
    if character_set.nil?
      puts "skipping #{table} failed to lookup character set"
    elsif character_set == 'utf8mb4'
      puts "skipping #{table} already using utf8mb4"
    else
      puts "converting #{table} to utf8mb4"
      convert_character_set_to_utf8mb4(client, table)
    end
  end
end

def change_database_default_character_set_to_utf8mb4(client, database)
  sql = "ALTER DATABASE `#{database}` DEFAULT CHARACTER SET = 'utf8mb4'"
  client.query(sql)
end

IGNORE = [
  'information_schema',
  'mysql',
  'innodb',
  'performance_schema',
  'sys'
]


rds_config = ETOnePassword.new.rds_config(SCRIPT_OPTIONS[:rds])
rds_config['username'] = rds_config.delete('user')
rds_config['database'] = 'mysql'
rds_config['host'] = SCRIPT_OPTIONS[:host] if SCRIPT_OPTIONS[:host]
rds_config.symbolize_keys!

master_client = Mysql2::Client.new(rds_config)

master_client.query('SHOW DATABASES').each do |result|
  database = result['Database']
  next if IGNORE.include?(database)

  rds_config[:database] = database
  client = Mysql2::Client.new(rds_config)

  puts database
  change_database_default_character_set_to_utf8mb4(client, database)
  convert_all_tables_to_utf8mb4(client, database)
end
