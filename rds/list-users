#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)

users = []
ETOnePassword.new.config['rds'][ENV['RAILS_ENV']].each do |rds_name, rds_settings|
  client = MySQLMasterHelpers.create_rds_client(rds_name)
  users += MySQLMasterHelpers.all_users(client)
  
  users.sort!
  users.uniq!

  print rds_name + "\n" + "\n"
  print users.join("\n") + "\n" + "\n"

end

