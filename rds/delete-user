#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-u', '--user [USERNAME]', 'Username') do |v|
    opts[:user] = v
  end

  opts.require_option(:user)
end

require File.expand_path('../config/environment', __dir__)

ETOnePassword.new.config['rds'][ENV['RAILS_ENV']].each do |rds_name, rds_settings|
  client = MySQLMasterHelpers.create_rds_client(rds_name)

  if MySQLMasterHelpers.get_user(client, SCRIPT_OPTIONS[:user])
    puts "deleting user #{SCRIPT_OPTIONS[:user]} on #{rds_name}/#{rds_settings['host']}"
    MySQLMasterHelpers.delete_user(client, SCRIPT_OPTIONS[:user])
  end
end
