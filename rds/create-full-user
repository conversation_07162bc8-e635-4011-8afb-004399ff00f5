#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-u', '--user [USERNAME]', 'Username') do |v|
    opts[:user] = v
  end

  opts.on('-p', '--password [PASSWORD]', 'Password') do |v|
    opts[:password] = v
  end

  opts.on('--database DATABASE', 'Database') do |v|
    opts[:database] = v
  end

  opts.require_option(:user)
  opts.require_option(:password)
end

require File.expand_path('../config/environment', __dir__)

ETOnePassword.new.config['rds'][ENV['RAILS_ENV']].each do |rds_name, rds_settings|
  client = MySQLMasterHelpers.create_rds_client(rds_name)

  rds_settings['databases'].each do |database|
    next if SCRIPT_OPTIONS[:database] && SCRIPT_OPTIONS[:database] != database

    puts "creating a full user #{SCRIPT_OPTIONS[:user]} on #{rds_name}/#{rds_settings['host']} for #{database}"
    MySQLMasterHelpers.create_user(client, SCRIPT_OPTIONS[:user], SCRIPT_OPTIONS[:password])
    MySQLMasterHelpers.grant_all_to_user(client, SCRIPT_OPTIONS[:user], database)
  end
end
