# Adding a user to a single DB

Connect to the prod VPN

Signin to 1password on the command line
```
eval $(op signin evertrue)
```

Run the ruby console from the root of this repo as follows
```bash
./bin/console -e prod
```

Then set the client to the appropriate DB instance you want to work with and then execute the appropriate create user statement (create_read_only_user) in this example. To get the appropriate DB instance, look in `et_repair_shop/config/one_password.yml`
```ruby
client = MySQLMasterHelpers.create_rds_client("prod-integrations")
MySQLMasterHelpers.create_read_only_user(client, "josh", "12345")
```

See [here](https://github.com/evertrue/et_repair_shop/blob/master/lib/mysql_master_helpers.rb#L23-L37) for other `MySQLMasterHelpers` definitions (eg. update_read_only_user, created_full_user, etc).