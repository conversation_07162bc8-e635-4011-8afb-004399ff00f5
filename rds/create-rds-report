#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)

class RDSCSVRow
  include CSVUtils::CSVRow

  attr_reader :payload

  csv_column('Current Name') { payload['DBInstanceIdentifier'] }
  csv_column('Current Host') { host }
  csv_column('DB Type') { payload['Engine'] + ' ' + payload['EngineVersion'] }
  csv_column('Security Groups') { security_groups }
  csv_column('Instance Type') { payload['DBInstanceClass'] }

  def initialize(payload)
    @payload = payload
  end

  def host
    payload['Endpoint']['Address']
  end

  def replica?
    payload['ReadReplicaSourceDBInstanceIdentifier'] || host.include?('replica')
  end

  def security_groups
    payload['VpcSecurityGroups'].map { |info| info['VpcSecurityGroupId'] }.join(', ')
  end

  def stage?
    payload['DBSubnetGroup']['DBSubnetGroupName'] == 'stage-private-newvpc'
  end
end

output = `aws rds describe-db-instances`
File.open('rds/describe-db-instances.json', 'wb') { |f| f.write(output) }
data = JSON.parse(output)

CSVUtils::CSVReport.new('prod-rds.csv', RDSCSVRow) do |report|
  data['DBInstances'].each do |payload|
    rds = RDSCSVRow.new(payload)
    next if rds.replica?
    next if rds.stage?

    report << RDSCSVRow.new(payload)
  end
end

CSVUtils::CSVReport.new('stage-rds.csv', RDSCSVRow) do |report|
  data['DBInstances'].each do |payload|
    rds = RDSCSVRow.new(payload)
    next if rds.replica?
    next unless rds.stage?

    report << RDSCSVRow.new(payload)
  end
end
