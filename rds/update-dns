#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:zone_file] = "#{ENV['HOME']}/dev/server-chef/data_bags/dns/zones.json"

  opts.on('-z', '--zone-file [FILE]', 'Server Chef zones.json file') do |v|
    opts[:zone_file] = v
  end
end

raise("zones file not found in #{SCRIPT_OPTIONS[:zone_file]}") unless File.exist?(SCRIPT_OPTIONS[:zone_file])

require File.expand_path('../config/environment', __dir__)

MySQLMasterHelpers.verify_rds!

one_password = MySQLMasterHelpers.et_one_password

dns = {}
prefix = ENV['RAILS_ENV'] == 'production' ? 'prod' : 'stage'

one_password.config['rds'][ENV['RAILS_ENV']].each_value do |rds_setting|
  rds_setting['databases'].each do |database_name|
    database_name = database_name.gsub('_', '-')
    cname = prefix + '-' + database_name + '.rds.evertrue.com'
    dns[cname] = rds_setting['host'] + '.'

    if rds_setting['replica_host']
      cname = prefix + '-' + database_name + '-replica.rds.evertrue.com'
      dns[cname] = rds_setting['replica_host'] + '.'
    end
  end
end

puts "DNS Settings"
print JSON.pretty_generate(dns) + "\n"

zone_data = JSON.parse(File.read(SCRIPT_OPTIONS[:zone_file]))

changed_dns_entries = []
zone_data['evertrue.com']['data'].each do |dns_entry|
  dns_name = dns_entry['name']
  rds_host = dns[dns_name]
  next unless rds_host

  dns_entry['value'] = [rds_host]
  changed_dns_entries << dns_name
end

dns.each do |dns_name, rds_host|
  next if changed_dns_entries.include?(dns_name)

  dns_entry = {
    'value' => [rds_host],
    'name' => dns_name,
    'type' => 'CNAME',
    'ttl' => '300'
  }

  zone_data['evertrue.com']['data'] << dns_entry
end

File.open(SCRIPT_OPTIONS[:zone_file], 'wb') { |f| f.write(JSON.pretty_generate(zone_data) + "\n") }

print <<STR
Updated zones.json file

Next step is to verify and commit changes

cd ~/dev/server-chef
git diff # verify changes
git add data_bags/dns/zones.json
git commit -m 'updated RDS aliases'
git push origin master

Route53 is updated from the prod-worker-1c server using chef-client

ssh prod-worker-1c
sudo su -
chef-client
STR
