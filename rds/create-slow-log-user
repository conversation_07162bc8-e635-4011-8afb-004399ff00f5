#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)

ETOnePassword.new.config['rds'][ENV['RAILS_ENV']].each do |rds_name, rds_settings|
  client = MySQLMasterHelpers.create_rds_client(rds_name)

  puts "creating user #{SCRIPT_OPTIONS[:user]} on #{rds_name}/#{rds_settings['host']}"
  MySQLMasterHelpers.create_slow_log_user(client, MySQLSlowLogHelpers.mysql_user, MySQLSlowLogHelpers.mysql_password)
end
