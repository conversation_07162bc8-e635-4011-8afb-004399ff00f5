#!/usr/bin/env ruby

# Purpose: to list out EMR clusters and their current state

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)

=begin
{
    "DBInstances": [
        {
            "DBInstanceIdentifier": "prod-ai",
            "DBInstanceClass": "db.t3.small",
            "Engine": "mysql",
            "MultiAZ": false,
            "EngineVersion": "8.0.35",
            "StorageType": "gp3",
            "CACertificateIdentifier": "rds-ca-rsa4096-g1",

=end

exec("aws --profile #{EnvironmentLoader.instance.aws_profile} rds describe-db-clusters --query 'DBClusters[*].{DBClusterIdentifier:DBClusterIdentifier, Status:Status, EngineMode:EngineMode, Engine:Engine, EngineVersion:EngineVersion, MultiAZ:MultiAZ, EngineLifecycleSupport:EngineLifecycleSupport}' --output table")
