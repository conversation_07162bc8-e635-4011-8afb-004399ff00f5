#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts[:days_ago] = 1

  opts.on('-d', '--days-agon DAYS_AGO', Integer, 'Days ago') do |v|
    opts[:days_ago] = v
  end
end

require File.expand_path('../config/environment', __dir__)

@preferred_mx_domains = File.exist?('preferred_mx_domains.json') ? JSON.parse(File.read('preferred_mx_domains.json')) : {}

def preferred_mx_domain(domain)
  @preferred_mx_domains[domain] ||= Resolv::DNS.open do |dns|
    res = dns.getresources(domain, Resolv::DNS::Resource::IN::MX)
    mx = res.sort_by(&:preference).first
    parts = mx&.exchange.to_s.split('.')
    parts.size >= 2 ? parts[-2..-1].join('.') : "#{domain} (FAILED)"
  end
rescue => e
  puts "domain #{domain} lookup failed"
  File.open('preferred_mx_domains.json', 'wb') { |f| f.write(@preferred_mx_domains.to_json) }
  raise e
end

ThankviewDB::Email

days_ago = SCRIPT_OPTIONS[:days_ago].days.ago
now = Time.now

results = ThankviewDB::Email
            .select('contacts.business_id')
            .select("RIGHT(contacts.email, LENGTH(contacts.email)-INSTR(contacts.email, '@')) AS domain")
            .select('COUNT(*) AS cnt')
            .joins(video: [guest: :contact])
            .where(schedule_date: days_ago..now)
            .group('business_id, domain')
            .order('business_id, cnt DESC')
            .to_a

business_mx_records = {}

results.each do |record|
  next unless record.domain

  mx_domain = preferred_mx_domain(record.domain)
  next unless mx_domain

  (business_mx_records[record.business_id] ||= Hash.new(0))[mx_domain] += record.cnt
end

File.open('preferred_mx_domains.json', 'wb') { |f| f.write(@preferred_mx_domains.to_json) }

businesses = ThankviewDB::Business.all.index_by(&:id)

CSV.open('thankview-email-domains.csv', 'wb') do |csv|
  csv << ['Business ID', 'Portal', 'MX Domain', 'Count']

  business_mx_records.each do |business_id, mx_records|
    mx_records.sort_by { |r| -r.last }.each do |mx_domain, count|
      business = businesses[business_id]
      csv << [business_id, business&.name, mx_domain, count]
    end
  end
end
