#!/usr/bin/env ruby

# Purpose: create a report that is a list of all the emails that the user will need to make a new recording for

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse! do |opts|
  opts.on('-b', '--business-ids BUSINESS_IDs', 'Business IDs') do |v|
    opts[:business_id] = v.split(',').map(&:to_i)
  end

  opts.on('-v', '--video-ids VIDEO_IDS', 'Video IDs') do |v|
    opts[:video_ids] = v.split(',').map { |id| id.to_i }
  end
end

require File.expand_path('../config/environment', __dir__)

CLOUDFRONT_HOSTS = [
  'd310lx2axip3m3.cloudfront.net',
  'assets.thankview.com'
]

BACKED_UP_TIME = (Time.now - (4.5 * 365 * 86_400))

S3_BUCKET = 'thankviews'

# THIS ONLY WORKS LOCALLY!!!
def tv_s3_client()
  @tv_s3_client ||= Aws::S3::Client.new(
    profile: 'thankview',
    region: 'us-west-2'
  )
end

videos = ThankviewDB::Video
if SCRIPT_OPTIONS[:business_id]
  videos = videos.joins(
    guest: [
      :contact
    ]
  ).where(
    contact: {
      business_id: SCRIPT_OPTIONS[:business_id]
    }
  ).where(
    'videos.created_at <= ?', BACKED_UP_TIME
  )
end

if SCRIPT_OPTIONS[:video_ids]
  videos = videos.where(id: SCRIPT_OPTIONS[:video_ids])
end

videos = videos.order('videos.created_at').select(videos: [:id, :final_video, :created_at]).to_a

STATS.total = videos.size

videos.each do |video|
  STATS.inc_and_notify

  unless video.final_video
    STATS.update(:missing_final_video)
    next
  end

  unless CLOUDFRONT_HOSTS.any? { |h|  video.final_video.include?(h) }
    STATS.update(:not_on_cloudfront)
    next
  end

  unless video.created_at < BACKED_UP_TIME
    STATS.update(:skipping_not_backed)
    next
  end

  uri = URI(video.final_video)

  s3_key = uri.path[1..-1]
  res = tv_s3_client.head_object(bucket: S3_BUCKET, key: s3_key)
  unless res.storage_class == 'GLACIER'
    STATS.update(:not_in_glacier)
    next
  end

  if res.restore
    STATS.update(:already_restored)
    next
  end

  LOG.info("restoring #{s3_key} for video #{video.id}")

  begin
    tv_s3_client.restore_object(
      bucket: S3_BUCKET,
      key: s3_key,
      restore_request: {
        days: 999,
        glacier_job_parameters: {
          tier: "Expedited",
        }
      }
    )
  rescue Aws::S3::Errors::GlacierExpeditedRetrievalNotAvailable => e
    LOG.warn("waiting for restore resources, #{e}")
    sleep(1)
    retry
  end

  STATS.update(:restored_videos)
end

STATS.notify(true)
