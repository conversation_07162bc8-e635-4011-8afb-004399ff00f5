#!/usr/bin/env ruby

require File.expand_path('../config/script_options', __dir__)

SCRIPT_OPTIONS = ScriptOptions.parse!

require File.expand_path('../config/environment', __dir__)

class LibraryVideoCSVRow < ThankviewDB::LibraryVideo
  include CSVUtils::CSVRow

  csv_column :recorded_at, header: 'Recorded At'
  csv_column :title, header: 'Video Title'
  csv_column :user_email, header: 'User Email'
  csv_column :business_slug, header: 'Biz Slug'
  csv_column :business_name, header: 'Biz Name'
  csv_column :num_videos, header: 'Num Videos'

  def recorded_at
    created_at.strftime('%Y-%m-%d %H:%M')
  end

  def user_email
    user.email
  end

  def business_slug
    business.slug
  end

  def business_name
    business.name
  end

  def num_videos
    videos.size
  end
end

class LibraryVideoCaCSVRow < ThankviewCaDB::LibraryVideo
  include CSVUtils::CSVRow

  csv_column :recorded_at, header: 'Recorded At'
  csv_column :title, header: 'Video Title'
  csv_column :user_email, header: 'User Email'
  csv_column :business_slug, header: 'Biz Slug'
  csv_column :business_name, header: 'Biz Name'
  csv_column :num_videos, header: 'Num Videos'

  def recorded_at
    created_at.strftime('%Y-%m-%d %H:%M')
  end

  def user_email
    user.email
  end

  def business_slug
    business.slug
  end

  def business_name
    business.name
  end

  def num_videos
    videos.size
  end
end

CSVUtils::CSVReport.new('thankview-failed-videos.csv', LibraryVideoCSVRow) do |report|
  LibraryVideoCSVRow.includes(:user, :business, :videos).where(is_raw: 1, executing_raw: 1).where.not(deleted_at: nil).where('created_at > ?', 14.days.ago).order('created_at DESC').each do |library_vidoe|
    report << library_vidoe
  end
end

CSVUtils::CSVReport.new('thankview-ca-failed-videos.csv', LibraryVideoCSVRow) do |report|
  LibraryVideoCaCSVRow.includes(:user, :business, :videos).where(is_raw: 1, executing_raw: 1).where.not(deleted_at: nil).where('created_at > ?', 14.days.ago).order('created_at DESC').each do |library_vidoe|
    report << library_vidoe
  end
end
