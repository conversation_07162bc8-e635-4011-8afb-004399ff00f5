#!/bin/bash

# Check the cwd by checking for the correct Gem<PERSON>le, if it's not et_repair_shop/chef, then tell the user to go there
if [ ! -e <PERSON>em<PERSON><PERSON> ] || ! grep chef Gemfile; then
  echo "You must be in the et_repair_shop/chef folder, cd there then rerun this script."
  exit 1
fi

# Install ChefDK (Developer Kit) 1.5.0

# https://downloads.chef.io/chefdk/stable/1.5.0

if ! which chef; then
  echo "Downloading ChefDK 1.5.0 to the Downloads folder"
  if ! curl -s 'https://packages.chef.io/files/stable/chefdk/1.5.0/mac_os_x/10.12/chefdk-1.5.0-1.dmg' -o ~/Downloads/chefdk-1.5.0-1.dmg; then
      echo "Failed to download ChefDK"
      exit $?
  fi

  echo "Install ChefDK then re-run this script"
  open ~/Downloads/chefdk-1.5.0-1.dmg

  exit 1
fi

if [ ! -e $HOME/.chef ]; then
  echo "Creating .chef folder"
  mkdir $HOME/.chef
fi

# Setup fog file, fog is ruby gem
if [ ! -e $HOME/.fog ]; then
  echo "Setting up .fog file"
  printf "default:\n  path_style: true\n" > $HOME/.fog
fi

if [ ! -e $HOME/.chef/credentials ]; then
  echo "$HOME/.chef/credentials not found, configuring"
  printf "[default]\nclient_name     = 'evertrue-validator'\nclient_key      = '$HOME/.chef/evertrue-validator.pem'\nchef_server_url = 'https://api.opscode.com/organizations/evertrue'\n" > $HOME/.chef/credentials
fi

# Install required libraries for chef

chef exec bundle

if [ ! -e $HOME/.chef/encryption_token ]; then
  echo "$HOME/.chef/encryption_token not found, file can be found in 1Password Chef encrypted data bag secret"
fi

if [ ! -e $HOME/.chef/knife.rb ]; then
  echo "$HOME/.chef/knife.rb not found, create a symlink from you server-chef/chef-config/ folder"
  echo "cd $HOME/.chef; ln -s $HOME/dev/dev/server-chef/chef-config/knife.rb"
  if [ -e "$HOME/dev/dev/server-chef/chef-config/knife.rb" ]; then
    echo "knife.rb found creating symlink for you"
    cd "$HOME/.chef"; ln -s "$HOME/dev/dev/server-chef/chef-config/knife.rb"; cd -
  fi
fi

if [ ! -e $HOME/.chef/evertrue-validator.pem ]; then
  echo "$HOME/.chef/evertrue-validator.pem not found, file can be found in 1Password Chef validation key"
else
  pem_count=$(ls -1 $HOME/.chef/*.pem | grep -v evertrue-validator.pem | wc -l | awk '{print $1}')
  if [ "$pem_count" != "1" ]; then
    echo "Missing chef.io username.pem file"
    echo "Goto: https://chef.io/ and download you key"
    echo "Click on Login > Management Console"
    echo "If you do not have an account with chef.io, create one and then have an admin invite you to evertrue"
  fi
fi
