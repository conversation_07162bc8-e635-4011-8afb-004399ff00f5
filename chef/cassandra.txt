
How to replace a dead node.

https://docs.datastax.com/en/dse/5.1/dse-admin/datastax_enterprise/operations/opsReplaceNode.html#:~:text=In%20the%20cassandra.,list%20of%20the%20other%20nodes.

Basically leave the new server shutdown.  It probably won't be able to join the new cluster anyways.

sudo /etc/init.d/cassandra stop

Now you can remove the node while you are bring up a new server.  And it does take quite a while for the remove node command to finish

ssh into an existing cassandra server and do

nodetool -h localhost removenode <Host ID>

Once the server has been removed from

nodetool status

You can start Cassandra on the new server

/etc/init.d/cassandra start

The new server will show up as UJ (up joining) in nodetool status

