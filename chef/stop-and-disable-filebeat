#!/bin/bash

PROGRAM=filebeat

for server in  prod-app-landingpages-1d prod-archiva-1c prod-chef prod-ci-1c prod-consul-1b-1 prod-consul-1c-1 prod-consul-1d-1 prod-contacts-cass-1b-1 prod-contacts-cass-1b-2 prod-contacts-cass-1c-1 prod-contacts-cass-1c-2 prod-contacts-cass-1d-1 prod-contacts-cass-1d-2 prod-contacts-spark-1b-1 prod-contacts-spark-1b-2 prod-contacts-spark-1b-3 prod-contacts-spark-1c-1 prod-contacts-spark-1c-2 prod-contacts-spark-1c-3 prod-contacts-spark-1d-1 prod-contacts-spark-1d-2 prod-contacts-spark-1d-3 prod-dns-1c-1 prod-dns-1d-1 prod-ecs-1c-1 prod-internal-lb-1b prod-kafka-1b prod-kafka-1c prod-kafka-1d prod-lb-1c-1 prod-lb-1d-1 prod-mesos-agent-1b-1 prod-mesos-agent-1c-1 prod-mesos-agent-1d-1 prod-monitor-1b prod-search-v2-contacts-1b-1 prod-search-v2-contacts-1b-2 prod-search-v2-contacts-1b-3 prod-search-v2-contacts-1c-1 prod-search-v2-contacts-1c-2 prod-search-v2-contacts-1c-3 prod-search-v2-contacts-1d-1 prod-search-v2-contacts-1d-2 prod-search-v2-contacts-1d-3 prod-searchcoordinator-v2-contacts-1b prod-searchcoordinator-v2-contacts-1c prod-searchcoordinator-v2-contacts-1d prod-searchmaster-v2-contacts-1b prod-searchmaster-v2-contacts-1c prod-searchmaster-v2-contacts-1d prod-singularity-1b-1 prod-singularity-1c-1 prod-singularity-1d-1 prod-storm-nimbus-1b prod-storm-supervisor-1b prod-storm-supervisor-1d prod-upload-1b prod-vpn prod-vpn-tunnel prod-worker-1b prod-zookeeper-1b prod-zookeeper-1c prod-zookeeper-1d stage-consul-1b-1 stage-consul-1c-2 stage-consul-1d-2 stage-contacts-cass-1b-4 stage-contacts-cass-1c-2 stage-contacts-cass-1d-1 stage-dns-1b stage-dns-1c stage-ecs-1c-1 stage-ecs-1d-1 stage-internal-lb-g13 stage-kafka-3zq stage-kafka-4uj stage-kafka-yc6 stage-lb-1b stage-mesos-agent-1b-1 stage-mesos-agent-1c-1 stage-mesos-agent-1d-1 stage-monitor-1b stage-search-v2-contacts-1b stage-search-v2-contacts-1c stage-search-v2-contacts-1d stage-searchcoordinator-v2-contacts-1b stage-searchmaster-v2-contacts-1b stage-searchmaster-v2-contacts-1c stage-searchmaster-v2-contacts-1d stage-singularity-1b-1 stage-singularity-1c-1 stage-storm-nimbus-1b stage-storm-supervisor-1d stage-vpn stage-zookeeper-1b stage-zookeeper-1c stage-zookeeper-1c-1 stage-zookeeper-1d; do

  if host $server >& /dev/null; then
    echo "Stopping $PROGRAM on $server"
    ssh $server sudo /etc/init.d/$PROGRAM stop
    ssh $server sudo update-rc.d -f $PROGRAM remove
    ssh $server sudo systemctl disable $PROGRAM
  else
    echo "$server not found"
  fi
done

