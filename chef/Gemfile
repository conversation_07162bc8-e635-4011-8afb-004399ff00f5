# frozen_string_literal: true

source 'https://rubygems.org'

gem 'awsutils', '2.1.7'
gem 'chef-zero', '5.3.2'
gem 'dry-inflector', '0.1.2'
gem 'et-knife-ec2', source: 'https://gem.fury.io/evertrue/'
gem 'kitchen-ec2', '1.3.2'
gem 'kitchen-sync', '2.2.0'
gem 'knife-stalenodes', '1.2.0'
gem 'mime-types', '3.1'
gem 'mime-types-data', '3.2016.0521'
gem 'mixlib-archive', '0.4.1'
gem 'mixlib-cli', '1.7.0'
gem 'mixlib-config', '2.2.4'
gem 'ohai', '8.24.1'
gem 'rubyzip', '1.2.1'
gem 'stove', '5.2.0'
gem 'vagrant-cachier', '1.2.1'
