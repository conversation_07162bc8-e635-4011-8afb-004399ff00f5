#!/bin/bash

knife ec2 server create \
  --environment prod \
  --type infra \
  --subnet subnet-421bd629 \
  --node-name prod-worker-1c \
  --security-group-ids sg-e83bb98c,sg-e67db089,sg-97f7edf5 \
  --run-list "recipe[et_base],recipe[et_worker],recipe[et_chefgithook],recipe[et_worker::zonemanager],recipe[et_chef_stale_bot],role[relayhost]" \
  --availability-zone us-east-1c \
  --image ami-04ef7d8fec8ae87fc \
  --flavor t3a.medium \
  --ssh-user ubuntu \
  --ebs-volume-type gp2 \
  --ebs-size 10 \
  --identity-file $HOME/.ssh/aws_dev.pem

# When the knife command tells you the private IP, ssh directly to the server with the ubuntu user, eg.
# $ ssh -i ~/.ssh/aws_dev.pem ubuntu@************

# wait for initial chef run (kicked off by the knife command) to fail, then...

# 1. manually fix some gems:
/opt/chef/embedded/bin/gem install sinatra -q --no-rdoc --no-ri -v "2.1.0"
/opt/chef/embedded/bin/gem install dry-inflector -q --no-rdoc --no-ri -v "0.1.2"
/opt/chef/embedded/bin/gem install mini_mime -q --no-rdoc --no-ri -v "1.0.2"
/opt/chef/embedded/bin/gem install mail  --no-rdoc --no-ri -v "2.7.1"

# manually install the long list of gems at the bottom of this file.

# 2. Fix the out of date cacerts.pem that chef uses
sudo apt install ca-certificates
sudo rm /etc/ssl/certs/DST_Root_CA_X3.pem
sudo update-ca-certificates
sudo rm /opt/chef/embedded/ssl/certs/cacert.pem
sudo ln -s /etc/ssl/certs/ca-certificates.crt /opt/chef/embedded/ssl/certs/cacert.pem

# 3. Now fix consul
sudo su -
mkdir -p /opt/consul/0.7.1
cd /opt/consul/0.7.1
curl -s https://releases.hashicorp.com/consul/0.7.1/consul_0.7.1_linux_amd64.zip -o consul_0.7.1_linux_amd64.zip
unzip consul_0.7.1_linux_amd64.zip
rm consul_0.7.1_linux_amd64.zip

start consul

# 4. Now re-run chef-client, it will fail again
sudo chef-client


# 04:35:03 (0) root@prod-worker-1c: ~
# $ /opt/chef/embedded/bin/gem list

# *** LOCAL GEMS ***

# addressable (2.4.0)
# aliyun-sdk (0.8.0)
# appbundler (0.10.0)
# ast (2.4.0)
# awesome_print (1.8.0)
# aws-eventstream (1.1.0)
# aws-partitions (1.389.0)
# aws-sdk-cloudwatchlogs (1.38.0)
# aws-sdk-core (3.109.2)
# aws-sdk-ec2 (1.206.0)
# aws-sdk-elasticloadbalancing (1.29.0)
# aws-sdk-elasticloadbalancingv2 (1.54.0)
# aws-sdk-route53 (1.44.0)
# aws-sigv4 (1.2.2)
# awsutils (2.2.2)
# bigdecimal (default: 1.2.8)
# binding_of_caller (0.8.0)
# builder (3.2.3)
# bundler (1.12.5)
# byebug (10.0.0)
# CFPropertyList (2.3.6)
# chef (12.22.5)
# chef-api (0.10.10)
# chef-config (12.22.5)
# chef-sugar (5.1.12, 5.1.11)
# chef-zero (5.3.2)
# cheffish (5.0.1)
# chefstyle (0.5.0)
# coderay (1.1.2)
# colorize (0.8.1)
# debug_inspector (0.0.3)
# did_you_mean (1.0.0)
# diff-lcs (1.3)
# docile (1.1.5)
# domain_name (0.5.20190701)
# dry-inflector (0.1.2)
# erubis (2.7.0)
# excon (0.78.0)
# facets (2.9.3)
# faraday (1.0.1, 0.9.2)
# ffi (1.9.23)
# ffi-yajl (2.3.1)
# fission (0.5.0)
# fog (1.37.0, 1.27.0)
# fog-aliyun (0.3.19)
# fog-atmos (0.1.0)
# fog-aws (0.11.0)
# fog-brightbox (0.16.1)
# fog-core (1.45.0)
# fog-dynect (0.0.3)
# fog-ecloud (0.3.0)
# fog-google (0.1.0)
# fog-json (1.2.0)
# fog-local (0.6.0)
# fog-powerdns (0.2.0)
# fog-profitbricks (4.1.1)
# fog-radosgw (0.0.5)
# fog-riakcs (0.1.0)
# fog-sakuracloud (1.7.5)
# fog-serverlove (0.1.2)
# fog-softlayer (1.1.4)
# fog-storm_on_demand (0.1.1)
# fog-terremark (0.1.0)
# fog-vmfusion (0.1.0)
# fog-voxel (0.1.0)
# fog-vsphere (3.5.1, 3.4.0)
# fog-xenserver (1.0.0)
# fog-xml (0.1.3)
# formatador (0.2.5)
# fuzzyurl (0.9.0)
# hashie (3.5.7)
# highline (2.0.3, 1.7.10)
# http-accept (1.7.0)
# http-cookie (1.0.3)
# iniparse (1.4.4)
# io-console (default: 0.4.5)
# ipaddress (0.8.3)
# jmespath (1.4.0)
# json (2.1.0, default: *******)
# libyajl2 (1.2.0)
# mail (2.7.1)
# method_source (0.9.0)
# mime-types (3.3.1, 2.6.2)
# mime-types-data (3.2020.1104)
# mini_mime (1.0.2)
# mini_portile2 (2.1.0)
# minitest (5.8.5)
# mixlib-archive (0.4.1)
# mixlib-authentication (1.4.2)
# mixlib-cli (1.7.0)
# mixlib-config (2.2.5)
# mixlib-log (3.0.9, 1.7.1)
# mixlib-shellout (2.3.2)
# multi_json (1.13.1)
# multipart-post (2.1.1)
# mustermann (1.1.1)
# net-scp (1.2.1)
# net-sftp (2.1.2)
# net-ssh (4.2.0)
# net-ssh-gateway (1.3.0)
# net-ssh-multi (1.2.1)
# net-telnet (0.1.1)
# netrc (0.11.0)
# nokogiri (1.7.2)
# ohai (8.26.1)
# optimist (3.0.1)
# parser (*******)
# plist (3.4.0)
# pony (1.13.1)
# power_assert (0.2.6)
# powerpack (0.1.1)
# proxifier (1.0.3)
# pry (0.11.3)
# pry-byebug (3.6.0)
# pry-remote (0.1.8)
# pry-stack_explorer (*******)
# psych (default: *******)
# rack (2.2.3, 2.0.4)
# rack-protection (2.1.0)
# rainbow (2.2.2)
# rake (12.3.0, 10.4.2)
# rb-readline (0.5.5)
# rbvmomi (2.4.1)
# rdoc (default: 4.2.1)
# rest-client (2.1.0, 1.7.3)
# rspec (3.7.0)
# rspec-core (3.7.1)
# rspec-expectations (3.7.0)
# rspec-its (1.2.0)
# rspec-mocks (3.7.0)
# rspec-support (3.7.1)
# rspec_junit_formatter (0.2.3)
# rubocop (0.47.1)
# ruby-prof (0.17.0)
# ruby-progressbar (1.9.0)
# ruby-shadow (2.5.0)
# ruby2_keywords (0.0.2)
# rubygems-update (2.6.14)
# rubyzip (1.2.0)
# sentry-raven (0.9.4)
# serverspec (2.41.3)
# sfl (2.3)
# simplecov (0.15.1)
# simplecov-html (0.10.2)
# sinatra (2.1.0)
# slack-notifier (2.3.2)
# slop (3.6.0)
# specinfra (2.73.2)
# syslog-logger (1.6.8)
# systemu (2.6.5)
# test-unit (3.1.5)
# tilt (2.0.10)
# unf (0.1.4)
# unf_ext (*******)
# unicode-display_width (1.3.0)
# uuidtools (2.1.5)
# vault (0.15.0)
# webrick (1.7.0)
# wmi-lite (1.0.0)
# xml-simple (1.1.5)
# xmlrpc (0.3.0)

# 04:43:04 (0) root@prod-worker-1c: ~
# $ /usr/bin/gem list

# *** LOCAL GEMS ***

# aws-cleanup (1.0.1)
# aws-eventstream (1.1.0)
# aws-partitions (1.390.0)
# aws-sdk (3.0.1)
# aws-sdk-accessanalyzer (1.14.0)
# aws-sdk-acm (1.38.0)
# aws-sdk-acmpca (1.30.0)
# aws-sdk-alexaforbusiness (1.43.0)
# aws-sdk-amplify (1.26.0)
# aws-sdk-apigateway (1.56.0)
# aws-sdk-apigatewaymanagementapi (1.19.0)
# aws-sdk-apigatewayv2 (1.29.0)
# aws-sdk-appconfig (1.12.0)
# aws-sdk-appflow (1.3.0)
# aws-sdk-applicationautoscaling (1.48.0)
# aws-sdk-applicationdiscoveryservice (1.33.0)
# aws-sdk-applicationinsights (1.15.0)
# aws-sdk-appmesh (1.32.0)
# aws-sdk-appstream (1.48.0)
# aws-sdk-appsync (1.37.0)
# aws-sdk-athena (1.33.0)
# aws-sdk-augmentedairuntime (1.10.0)
# aws-sdk-autoscaling (1.48.0)
# aws-sdk-autoscalingplans (1.28.0)
# aws-sdk-backup (1.24.0)
# aws-sdk-batch (1.40.0)
# aws-sdk-braket (1.5.0)
# aws-sdk-budgets (1.36.0)
# aws-sdk-chime (1.37.0)
# aws-sdk-cloud9 (1.29.0)
# aws-sdk-clouddirectory (1.29.0)
# aws-sdk-cloudformation (1.44.0)
# aws-sdk-cloudfront (1.46.0)
# aws-sdk-cloudhsm (1.27.0)
# aws-sdk-cloudhsmv2 (1.30.0)
# aws-sdk-cloudsearch (1.26.0)
# aws-sdk-cloudsearchdomain (1.22.0)
# aws-sdk-cloudtrail (1.29.0)
# aws-sdk-cloudwatch (1.46.0)
# aws-sdk-cloudwatchevents (1.39.0)
# aws-sdk-cloudwatchlogs (1.38.0)
# aws-sdk-codeartifact (1.5.0)
# aws-sdk-codebuild (1.63.0)
# aws-sdk-codecommit (1.40.0)
# aws-sdk-codedeploy (1.37.0)
# aws-sdk-codeguruprofiler (1.12.0)
# aws-sdk-codegurureviewer (1.13.0)
# aws-sdk-codepipeline (1.37.0)
# aws-sdk-codestar (1.27.0)
# aws-sdk-codestarconnections (1.11.0)
# aws-sdk-codestarnotifications (1.8.0)
# aws-sdk-cognitoidentity (1.27.0)
# aws-sdk-cognitoidentityprovider (1.47.0)
# aws-sdk-cognitosync (1.24.0)
# aws-sdk-comprehend (1.41.0)
# aws-sdk-comprehendmedical (1.23.0)
# aws-sdk-computeoptimizer (1.9.0)
# aws-sdk-configservice (1.53.0)
# aws-sdk-connect (1.34.0)
# aws-sdk-connectparticipant (1.8.0)
# aws-sdk-core (3.109.2)
# aws-sdk-costandusagereportservice (1.28.0)
# aws-sdk-costexplorer (1.52.0)
# aws-sdk-databasemigrationservice (1.46.0)
# aws-sdk-dataexchange (1.10.0)
# aws-sdk-datapipeline (1.24.0)
# aws-sdk-datasync (1.27.0)
# aws-sdk-dax (1.27.0)
# aws-sdk-detective (1.11.0)
# aws-sdk-devicefarm (1.39.0)
# aws-sdk-directconnect (1.37.0)
# aws-sdk-directoryservice (1.34.0)
# aws-sdk-dlm (1.35.0)
# aws-sdk-docdb (1.26.0)
# aws-sdk-dynamodb (1.56.0)
# aws-sdk-dynamodbstreams (1.26.0)
# aws-sdk-ebs (1.11.0)
# aws-sdk-ec2 (1.207.0)
# aws-sdk-ec2instanceconnect (1.11.0)
# aws-sdk-ecr (1.39.0)
# aws-sdk-ecs (1.70.0)
# aws-sdk-efs (1.36.0)
# aws-sdk-eks (1.45.0)
# aws-sdk-elasticache (1.45.0)
# aws-sdk-elasticbeanstalk (1.39.0)
# aws-sdk-elasticinference (1.10.0)
# aws-sdk-elasticloadbalancing (1.29.0)
# aws-sdk-elasticloadbalancingv2 (1.54.0)
# aws-sdk-elasticsearchservice (1.45.0)
# aws-sdk-elastictranscoder (1.27.0)
# aws-sdk-emr (1.39.0)
# aws-sdk-eventbridge (1.17.0)
# aws-sdk-firehose (1.35.0)
# aws-sdk-fms (1.32.0)
# aws-sdk-forecastqueryservice (1.10.0)
# aws-sdk-forecastservice (1.11.0)
# aws-sdk-frauddetector (1.14.0)
# aws-sdk-fsx (1.31.0)
# aws-sdk-gamelift (1.38.0)
# aws-sdk-glacier (1.35.0)
# aws-sdk-globalaccelerator (1.25.0)
# aws-sdk-glue (1.78.0)
# aws-sdk-greengrass (1.37.0)
# aws-sdk-groundstation (1.15.0)
# aws-sdk-guardduty (1.42.0)
# aws-sdk-health (1.31.0)
# aws-sdk-honeycode (1.3.0)
# aws-sdk-iam (1.46.0)
# aws-sdk-identitystore (1.3.0)
# aws-sdk-imagebuilder (1.16.0)
# aws-sdk-importexport (1.24.0)
# aws-sdk-inspector (1.32.0)
# aws-sdk-iot (1.61.0)
# aws-sdk-iot1clickdevicesservice (1.26.0)
# aws-sdk-iot1clickprojects (1.26.0)
# aws-sdk-iotanalytics (1.34.0)
# aws-sdk-iotdataplane (1.26.0)
# aws-sdk-iotevents (1.20.0)
# aws-sdk-ioteventsdata (1.13.0)
# aws-sdk-iotjobsdataplane (1.25.0)
# aws-sdk-iotsecuretunneling (1.8.0)
# aws-sdk-iotsitewise (1.12.0)
# aws-sdk-iotthingsgraph (1.12.0)
# aws-sdk-ivs (1.5.0)
# aws-sdk-kafka (1.29.0)
# aws-sdk-kendra (1.18.0)
# aws-sdk-kinesis (1.30.0)
# aws-sdk-kinesisanalytics (1.29.0)
# aws-sdk-kinesisanalyticsv2 (1.23.0)
# aws-sdk-kinesisvideo (1.30.0)
# aws-sdk-kinesisvideoarchivedmedia (1.29.0)
# aws-sdk-kinesisvideomedia (1.26.0)
# aws-sdk-kinesisvideosignalingchannels (1.8.0)
# aws-sdk-kms (1.39.0)
# aws-sdk-lakeformation (1.11.0)
# aws-sdk-lambda (1.52.0)
# aws-sdk-lambdapreview (1.24.0)
# aws-sdk-lex (1.32.0)
# aws-sdk-lexmodelbuildingservice (1.39.0)
# aws-sdk-licensemanager (1.20.0)
# aws-sdk-lightsail (1.39.0)
# aws-sdk-machinelearning (1.25.0)
# aws-sdk-macie (1.25.0)
# aws-sdk-macie2 (1.15.0)
# aws-sdk-managedblockchain (1.17.0)
# aws-sdk-marketplacecatalog (1.9.0)
# aws-sdk-marketplacecommerceanalytics (1.30.0)
# aws-sdk-marketplaceentitlementservice (1.24.0)
# aws-sdk-marketplacemetering (1.32.0)
# aws-sdk-mediaconnect (1.28.0)
# aws-sdk-mediaconvert (1.58.0)
# aws-sdk-medialive (1.58.0)
# aws-sdk-mediapackage (1.36.0)
# aws-sdk-mediapackagevod (1.19.0)
# aws-sdk-mediastore (1.30.0)
# aws-sdk-mediastoredata (1.27.0)
# aws-sdk-mediatailor (1.33.0)
# aws-sdk-migrationhub (1.29.0)
# aws-sdk-migrationhubconfig (1.9.0)
# aws-sdk-mobile (1.24.0)
# aws-sdk-mq (1.34.0)
# aws-sdk-mturk (1.27.0)
# aws-sdk-neptune (1.31.0)
# aws-sdk-networkmanager (1.8.0)
# aws-sdk-opsworks (1.30.0)
# aws-sdk-opsworkscm (1.40.0)
# aws-sdk-organizations (1.55.0)
# aws-sdk-outposts (1.10.0)
# aws-sdk-personalize (1.19.0)
# aws-sdk-personalizeevents (1.14.0)
# aws-sdk-personalizeruntime (1.18.0)
# aws-sdk-pi (1.24.0)
# aws-sdk-pinpoint (1.47.0)
# aws-sdk-pinpointemail (1.24.0)
# aws-sdk-pinpointsmsvoice (1.21.0)
# aws-sdk-polly (1.37.0)
# aws-sdk-pricing (1.24.0)
# aws-sdk-qldb (1.11.0)
# aws-sdk-qldbsession (1.9.0)
# aws-sdk-quicksight (1.34.0)
# aws-sdk-ram (1.22.0)
# aws-sdk-rds (1.105.0)
# aws-sdk-rdsdataservice (1.23.0)
# aws-sdk-redshift (1.50.0)
# aws-sdk-redshiftdataapiservice (1.2.0)
# aws-sdk-rekognition (1.47.0)
# aws-sdk-resourcegroups (1.32.0)
# aws-sdk-resourcegroupstaggingapi (1.34.0)
# aws-sdk-resources (3.84.0)
# aws-sdk-robomaker (1.30.0)
# aws-sdk-route53 (1.44.0)
# aws-sdk-route53domains (1.28.0)
# aws-sdk-route53resolver (1.21.0)
# aws-sdk-s3 (1.83.1)
# aws-sdk-s3control (1.24.0)
# aws-sdk-s3outposts (1.0.0)
# aws-sdk-sagemaker (1.71.0)
# aws-sdk-sagemakerruntime (1.27.0)
# aws-sdk-savingsplans (1.12.0)
# aws-sdk-schemas (1.10.0)
# aws-sdk-secretsmanager (1.43.0)
# aws-sdk-securityhub (1.35.0)
# aws-sdk-serverlessapplicationrepository (1.32.0)
# aws-sdk-servicecatalog (1.53.0)
# aws-sdk-servicediscovery (1.31.0)
# aws-sdk-servicequotas (1.11.0)
# aws-sdk-ses (1.36.0)
# aws-sdk-sesv2 (1.14.0)
# aws-sdk-shield (1.32.0)
# aws-sdk-signer (1.26.0)
# aws-sdk-simpledb (1.24.0)
# aws-sdk-sms (1.27.0)
# aws-sdk-snowball (1.35.0)
# aws-sdk-sns (1.35.0)
# aws-sdk-sqs (1.34.0)
# aws-sdk-ssm (1.95.0)
# aws-sdk-ssoadmin (1.3.0)
# aws-sdk-ssooidc (1.8.0)
# aws-sdk-states (1.36.0)
# aws-sdk-storagegateway (1.51.0)
# aws-sdk-support (1.28.0)
# aws-sdk-swf (1.25.0)
# aws-sdk-synthetics (1.9.0)
# aws-sdk-textract (1.21.0)
# aws-sdk-timestreamquery (1.1.0)
# aws-sdk-timestreamwrite (1.1.0)
# aws-sdk-transcribeservice (1.50.0)
# aws-sdk-transcribestreamingservice (1.23.0)
# aws-sdk-transfer (1.28.0)
# aws-sdk-translate (1.28.0)
# aws-sdk-waf (1.36.0)
# aws-sdk-wafregional (1.37.0)
# aws-sdk-wafv2 (1.14.0)
# aws-sdk-workdocs (1.28.0)
# aws-sdk-worklink (1.21.0)
# aws-sdk-workmail (1.33.0)
# aws-sdk-workmailmessageflow (1.9.0)
# aws-sdk-workspaces (1.48.0)
# aws-sdk-xray (1.35.0)
# aws-sigv2 (1.0.1)
# aws-sigv4 (1.2.2)
# bigdecimal (1.2.6)
# bundler (1.13.7)
# chef-api (0.5.0)
# io-console (0.4.3)
# jmespath (1.4.0)
# json (1.8.1.1)
# livestatus-client (1.0.2)
# logify (0.2.0)
# mime-types (3.3.1, 2.6.1)
# mime-types-data (3.2020.1104)
# psych (2.0.8.1)
# rake (10.4.2)
# rdoc (4.2.0)
# slack-notifier (1.2.1)
# trollop (2.9.10)



# copy the below to gems.sh.  Then run it in a screen

#!/bin/bash
/usr/bin/gem install aws-eventstream -q --no-rdoc --no-ri -v "1.1.0"
/usr/bin/gem install aws-sigv4 -q --no-rdoc --no-ri -v "1.2.2"
/usr/bin/gem install aws-sdk-accessanalyzer -q --no-rdoc --no-ri -v "1.14.0"
/usr/bin/gem install aws-sdk-acm -q --no-rdoc --no-ri -v "1.38.0"
/usr/bin/gem install aws-sdk-acmpca -q --no-rdoc --no-ri -v "1.30.0"
/usr/bin/gem install aws-sdk-alexaforbusiness -q --no-rdoc --no-ri -v "1.43.0"
/usr/bin/gem install aws-sdk-amplify -q --no-rdoc --no-ri -v "1.26.0"
/usr/bin/gem install aws-sdk-apigateway -q --no-rdoc --no-ri -v "1.56.0"
/usr/bin/gem install aws-sdk-apigatewaymanagementapi -q --no-rdoc --no-ri -v "1.19.0"
/usr/bin/gem install aws-sdk-apigatewayv2 -q --no-rdoc --no-ri -v "1.29.0"
/usr/bin/gem install aws-sdk-appconfig -q --no-rdoc --no-ri -v "1.12.0"
/usr/bin/gem install aws-sdk-appflow -q --no-rdoc --no-ri -v "1.3.0"
/usr/bin/gem install aws-sdk-applicationautoscaling -q --no-rdoc --no-ri -v "1.48.0"
/usr/bin/gem install aws-sdk-applicationdiscoveryservice -q --no-rdoc --no-ri -v "1.33.0"
/usr/bin/gem install aws-sdk-applicationinsights -q --no-rdoc --no-ri -v "1.15.0"
/usr/bin/gem install aws-sdk-appmesh -q --no-rdoc --no-ri -v "1.32.0"
/usr/bin/gem install aws-sdk-appstream -q --no-rdoc --no-ri -v "1.48.0"
/usr/bin/gem install aws-sdk-appsync -q --no-rdoc --no-ri -v "1.37.0"
/usr/bin/gem install aws-sdk-athena -q --no-rdoc --no-ri -v "1.33.0"
/usr/bin/gem install aws-sdk-augmentedairuntime -q --no-rdoc --no-ri -v "1.10.0"
/usr/bin/gem install aws-sdk-autoscaling -q --no-rdoc --no-ri -v "1.48.0"
/usr/bin/gem install aws-sdk-autoscalingplans -q --no-rdoc --no-ri -v "1.28.0"
/usr/bin/gem install aws-sdk-backup -q --no-rdoc --no-ri -v "1.24.0"
/usr/bin/gem install aws-sdk-batch -q --no-rdoc --no-ri -v "1.40.0"
/usr/bin/gem install aws-sdk-braket -q --no-rdoc --no-ri -v "1.5.0"
/usr/bin/gem install aws-sdk-budgets -q --no-rdoc --no-ri -v "1.36.0"
/usr/bin/gem install aws-sdk-chime -q --no-rdoc --no-ri -v "1.37.0"
/usr/bin/gem install aws-sdk-cloud9 -q --no-rdoc --no-ri -v "1.29.0"
/usr/bin/gem install aws-sdk-clouddirectory -q --no-rdoc --no-ri -v "1.29.0"
/usr/bin/gem install aws-sdk-cloudformation -q --no-rdoc --no-ri -v "1.44.0"
/usr/bin/gem install aws-sdk-cloudfront -q --no-rdoc --no-ri -v "1.46.0"
/usr/bin/gem install aws-sdk-cloudhsm -q --no-rdoc --no-ri -v "1.27.0"
/usr/bin/gem install aws-sdk-cloudhsmv2 -q --no-rdoc --no-ri -v "1.30.0"
/usr/bin/gem install aws-sdk-cloudsearch -q --no-rdoc --no-ri -v "1.26.0"
/usr/bin/gem install aws-sdk-cloudsearchdomain -q --no-rdoc --no-ri -v "1.22.0"
/usr/bin/gem install aws-sdk-cloudtrail -q --no-rdoc --no-ri -v "1.29.0"
/usr/bin/gem install aws-sdk-cloudwatch -q --no-rdoc --no-ri -v "1.46.0"
/usr/bin/gem install aws-sdk-cloudwatchevents -q --no-rdoc --no-ri -v "1.39.0"
/usr/bin/gem install aws-sdk-cloudwatchlogs -q --no-rdoc --no-ri -v "1.38.0"
/usr/bin/gem install aws-sdk-codeartifact -q --no-rdoc --no-ri -v "1.5.0"
/usr/bin/gem install aws-sdk-codebuild -q --no-rdoc --no-ri -v "1.63.0"
/usr/bin/gem install aws-sdk-codecommit -q --no-rdoc --no-ri -v "1.40.0"
/usr/bin/gem install aws-sdk-codedeploy -q --no-rdoc --no-ri -v "1.37.0"
/usr/bin/gem install aws-sdk-codeguruprofiler -q --no-rdoc --no-ri -v "1.12.0"
/usr/bin/gem install aws-sdk-codegurureviewer -q --no-rdoc --no-ri -v "1.13.0"
/usr/bin/gem install aws-sdk-codepipeline -q --no-rdoc --no-ri -v "1.37.0"
/usr/bin/gem install aws-sdk-codestar -q --no-rdoc --no-ri -v "1.27.0"
/usr/bin/gem install aws-sdk-codestarconnections -q --no-rdoc --no-ri -v "1.11.0"
/usr/bin/gem install aws-sdk-codestarnotifications -q --no-rdoc --no-ri -v "1.8.0"
/usr/bin/gem install aws-sdk-cognitoidentity -q --no-rdoc --no-ri -v "1.27.0"
/usr/bin/gem install aws-sdk-cognitoidentityprovider -q --no-rdoc --no-ri -v "1.47.0"
/usr/bin/gem install aws-sdk-cognitosync -q --no-rdoc --no-ri -v "1.24.0"
/usr/bin/gem install aws-sdk-comprehend -q --no-rdoc --no-ri -v "1.41.0"
/usr/bin/gem install aws-sdk-comprehendmedical -q --no-rdoc --no-ri -v "1.23.0"
/usr/bin/gem install aws-sdk-computeoptimizer -q --no-rdoc --no-ri -v "1.9.0"
/usr/bin/gem install aws-sdk-configservice -q --no-rdoc --no-ri -v "1.53.0"
/usr/bin/gem install aws-sdk-connect -q --no-rdoc --no-ri -v "1.34.0"
/usr/bin/gem install aws-sdk-connectparticipant -q --no-rdoc --no-ri -v "1.8.0"
/usr/bin/gem install aws-sdk-costandusagereportservice -q --no-rdoc --no-ri -v "1.28.0"
/usr/bin/gem install aws-sdk-costexplorer -q --no-rdoc --no-ri -v "1.52.0"
/usr/bin/gem install aws-sdk-databasemigrationservice -q --no-rdoc --no-ri -v "1.46.0"
/usr/bin/gem install aws-sdk-dataexchange -q --no-rdoc --no-ri -v "1.10.0"
/usr/bin/gem install aws-sdk-datapipeline -q --no-rdoc --no-ri -v "1.24.0"
/usr/bin/gem install aws-sdk-datasync -q --no-rdoc --no-ri -v "1.27.0"
/usr/bin/gem install aws-sdk-dax -q --no-rdoc --no-ri -v "1.27.0"
/usr/bin/gem install aws-sdk-detective -q --no-rdoc --no-ri -v "1.11.0"
/usr/bin/gem install aws-sdk-devicefarm -q --no-rdoc --no-ri -v "1.39.0"
/usr/bin/gem install aws-sdk-directconnect -q --no-rdoc --no-ri -v "1.37.0"
/usr/bin/gem install aws-sdk-directoryservice -q --no-rdoc --no-ri -v "1.34.0"
/usr/bin/gem install aws-sdk-dlm -q --no-rdoc --no-ri -v "1.35.0"
/usr/bin/gem install aws-sdk-docdb -q --no-rdoc --no-ri -v "1.26.0"
/usr/bin/gem install aws-sdk-dynamodb -q --no-rdoc --no-ri -v "1.56.0"
/usr/bin/gem install aws-sdk-dynamodbstreams -q --no-rdoc --no-ri -v "1.26.0"
/usr/bin/gem install aws-sdk-ebs -q --no-rdoc --no-ri -v "1.11.0"
/usr/bin/gem install aws-sdk-ec2 -q --no-rdoc --no-ri -v "1.207.0"
/usr/bin/gem install aws-sdk-ec2instanceconnect -q --no-rdoc --no-ri -v "1.11.0"
/usr/bin/gem install aws-sdk-ecr -q --no-rdoc --no-ri -v "1.39.0"
/usr/bin/gem install aws-sdk-ecs -q --no-rdoc --no-ri -v "1.70.0"
/usr/bin/gem install aws-sdk-efs -q --no-rdoc --no-ri -v "1.36.0"
/usr/bin/gem install aws-sdk-eks -q --no-rdoc --no-ri -v "1.45.0"
/usr/bin/gem install aws-sdk-elasticache -q --no-rdoc --no-ri -v "1.45.0"
/usr/bin/gem install aws-sdk-elasticbeanstalk -q --no-rdoc --no-ri -v "1.39.0"
/usr/bin/gem install aws-sdk-elasticinference -q --no-rdoc --no-ri -v "1.10.0"
/usr/bin/gem install aws-sdk-elasticloadbalancing -q --no-rdoc --no-ri -v "1.29.0"
/usr/bin/gem install aws-sdk-elasticloadbalancingv2 -q --no-rdoc --no-ri -v "1.54.0"
/usr/bin/gem install aws-sdk-elasticsearchservice -q --no-rdoc --no-ri -v "1.45.0"
/usr/bin/gem install aws-sdk-elastictranscoder -q --no-rdoc --no-ri -v "1.27.0"
/usr/bin/gem install aws-sdk-emr -q --no-rdoc --no-ri -v "1.39.0"
/usr/bin/gem install aws-sdk-eventbridge -q --no-rdoc --no-ri -v "1.17.0"
/usr/bin/gem install aws-sdk-firehose -q --no-rdoc --no-ri -v "1.35.0"
/usr/bin/gem install aws-sdk-fms -q --no-rdoc --no-ri -v "1.32.0"
/usr/bin/gem install aws-sdk-forecastqueryservice -q --no-rdoc --no-ri -v "1.10.0"
/usr/bin/gem install aws-sdk-forecastservice -q --no-rdoc --no-ri -v "1.11.0"
/usr/bin/gem install aws-sdk-frauddetector -q --no-rdoc --no-ri -v "1.14.0"
/usr/bin/gem install aws-sdk-fsx -q --no-rdoc --no-ri -v "1.31.0"
/usr/bin/gem install aws-sdk-gamelift -q --no-rdoc --no-ri -v "1.38.0"
/usr/bin/gem install aws-sdk-glacier -q --no-rdoc --no-ri -v "1.35.0"
/usr/bin/gem install aws-sdk-globalaccelerator -q --no-rdoc --no-ri -v "1.25.0"
/usr/bin/gem install aws-sdk-glue -q --no-rdoc --no-ri -v "1.78.0"
/usr/bin/gem install aws-sdk-greengrass -q --no-rdoc --no-ri -v "1.37.0"
/usr/bin/gem install aws-sdk-groundstation -q --no-rdoc --no-ri -v "1.15.0"
/usr/bin/gem install aws-sdk-guardduty -q --no-rdoc --no-ri -v "1.42.0"
/usr/bin/gem install aws-sdk-health -q --no-rdoc --no-ri -v "1.31.0"
/usr/bin/gem install aws-sdk-honeycode -q --no-rdoc --no-ri -v "1.3.0"
/usr/bin/gem install aws-sdk-iam -q --no-rdoc --no-ri -v "1.46.0"
/usr/bin/gem install aws-sdk-identitystore -q --no-rdoc --no-ri -v "1.3.0"
/usr/bin/gem install aws-sdk-imagebuilder -q --no-rdoc --no-ri -v "1.16.0"
/usr/bin/gem install aws-sdk-importexport -q --no-rdoc --no-ri -v "1.24.0"
/usr/bin/gem install aws-sdk-inspector -q --no-rdoc --no-ri -v "1.32.0"
/usr/bin/gem install aws-sdk-iot -q --no-rdoc --no-ri -v "1.61.0"
/usr/bin/gem install aws-sdk-iot1clickdevicesservice -q --no-rdoc --no-ri -v "1.26.0"
/usr/bin/gem install aws-sdk-iot1clickprojects -q --no-rdoc --no-ri -v "1.26.0"
/usr/bin/gem install aws-sdk-iotanalytics -q --no-rdoc --no-ri -v "1.34.0"
/usr/bin/gem install aws-sdk-iotdataplane -q --no-rdoc --no-ri -v "1.26.0"
/usr/bin/gem install aws-sdk-iotevents -q --no-rdoc --no-ri -v "1.20.0"
/usr/bin/gem install aws-sdk-ioteventsdata -q --no-rdoc --no-ri -v "1.13.0"
/usr/bin/gem install aws-sdk-iotjobsdataplane -q --no-rdoc --no-ri -v "1.25.0"
/usr/bin/gem install aws-sdk-iotsecuretunneling -q --no-rdoc --no-ri -v "1.8.0"
/usr/bin/gem install aws-sdk-iotsitewise -q --no-rdoc --no-ri -v "1.12.0"
/usr/bin/gem install aws-sdk-iotthingsgraph -q --no-rdoc --no-ri -v "1.12.0"
/usr/bin/gem install aws-sdk-ivs -q --no-rdoc --no-ri -v "1.5.0"
/usr/bin/gem install aws-sdk-kafka -q --no-rdoc --no-ri -v "1.29.0"
/usr/bin/gem install aws-sdk-kendra -q --no-rdoc --no-ri -v "1.18.0"
/usr/bin/gem install aws-sdk-kinesis -q --no-rdoc --no-ri -v "1.30.0"
/usr/bin/gem install aws-sdk-kinesisanalytics -q --no-rdoc --no-ri -v "1.29.0"
/usr/bin/gem install aws-sdk-kinesisanalyticsv2 -q --no-rdoc --no-ri -v "1.23.0"
/usr/bin/gem install aws-sdk-kinesisvideo -q --no-rdoc --no-ri -v "1.30.0"
/usr/bin/gem install aws-sdk-kinesisvideoarchivedmedia -q --no-rdoc --no-ri -v "1.29.0"
/usr/bin/gem install aws-sdk-kinesisvideomedia -q --no-rdoc --no-ri -v "1.26.0"
/usr/bin/gem install aws-sdk-kinesisvideosignalingchannels -q --no-rdoc --no-ri -v "1.8.0"
/usr/bin/gem install aws-sdk-kms -q --no-rdoc --no-ri -v "1.39.0"
/usr/bin/gem install aws-sdk-lakeformation -q --no-rdoc --no-ri -v "1.11.0"
/usr/bin/gem install aws-sdk-lambda -q --no-rdoc --no-ri -v "1.52.0"
/usr/bin/gem install aws-sdk-lambdapreview -q --no-rdoc --no-ri -v "1.24.0"
/usr/bin/gem install aws-sdk-lex -q --no-rdoc --no-ri -v "1.32.0"
/usr/bin/gem install aws-sdk-lexmodelbuildingservice -q --no-rdoc --no-ri -v "1.39.0"
/usr/bin/gem install aws-sdk-licensemanager -q --no-rdoc --no-ri -v "1.20.0"
/usr/bin/gem install aws-sdk-lightsail -q --no-rdoc --no-ri -v "1.39.0"
/usr/bin/gem install aws-sdk-machinelearning -q --no-rdoc --no-ri -v "1.25.0"
/usr/bin/gem install aws-sdk-macie -q --no-rdoc --no-ri -v "1.25.0"
/usr/bin/gem install aws-sdk-macie2 -q --no-rdoc --no-ri -v "1.15.0"
/usr/bin/gem install aws-sdk-managedblockchain -q --no-rdoc --no-ri -v "1.17.0"
/usr/bin/gem install aws-sdk-marketplacecatalog -q --no-rdoc --no-ri -v "1.9.0"
/usr/bin/gem install aws-sdk-marketplacecommerceanalytics -q --no-rdoc --no-ri -v "1.30.0"
/usr/bin/gem install aws-sdk-marketplaceentitlementservice -q --no-rdoc --no-ri -v "1.24.0"
/usr/bin/gem install aws-sdk-marketplacemetering -q --no-rdoc --no-ri -v "1.32.0"
/usr/bin/gem install aws-sdk-mediaconnect -q --no-rdoc --no-ri -v "1.28.0"
/usr/bin/gem install aws-sdk-mediaconvert -q --no-rdoc --no-ri -v "1.58.0"
/usr/bin/gem install aws-sdk-medialive -q --no-rdoc --no-ri -v "1.58.0"
/usr/bin/gem install aws-sdk-mediapackage -q --no-rdoc --no-ri -v "1.36.0"
/usr/bin/gem install aws-sdk-mediapackagevod -q --no-rdoc --no-ri -v "1.19.0"
/usr/bin/gem install aws-sdk-mediastore -q --no-rdoc --no-ri -v "1.30.0"
/usr/bin/gem install aws-sdk-mediastoredata -q --no-rdoc --no-ri -v "1.27.0"
/usr/bin/gem install aws-sdk-mediatailor -q --no-rdoc --no-ri -v "1.33.0"
/usr/bin/gem install aws-sdk-migrationhub -q --no-rdoc --no-ri -v "1.29.0"
/usr/bin/gem install aws-sdk-migrationhubconfig -q --no-rdoc --no-ri -v "1.9.0"
/usr/bin/gem install aws-sdk-mobile -q --no-rdoc --no-ri -v "1.24.0"
/usr/bin/gem install aws-sdk-mq -q --no-rdoc --no-ri -v "1.34.0"
/usr/bin/gem install aws-sdk-mturk -q --no-rdoc --no-ri -v "1.27.0"
/usr/bin/gem install aws-sdk-neptune -q --no-rdoc --no-ri -v "1.31.0"
/usr/bin/gem install aws-sdk-networkmanager -q --no-rdoc --no-ri -v "1.8.0"
/usr/bin/gem install aws-sdk-opsworks -q --no-rdoc --no-ri -v "1.30.0"
/usr/bin/gem install aws-sdk-opsworkscm -q --no-rdoc --no-ri -v "1.40.0"
/usr/bin/gem install aws-sdk-organizations -q --no-rdoc --no-ri -v "1.55.0"
/usr/bin/gem install aws-sdk-outposts -q --no-rdoc --no-ri -v "1.10.0"
/usr/bin/gem install aws-sdk-personalize -q --no-rdoc --no-ri -v "1.19.0"
/usr/bin/gem install aws-sdk-personalizeevents -q --no-rdoc --no-ri -v "1.14.0"
/usr/bin/gem install aws-sdk-personalizeruntime -q --no-rdoc --no-ri -v "1.18.0"
/usr/bin/gem install aws-sdk-pi -q --no-rdoc --no-ri -v "1.24.0"
/usr/bin/gem install aws-sdk-pinpoint -q --no-rdoc --no-ri -v "1.47.0"
/usr/bin/gem install aws-sdk-pinpointemail -q --no-rdoc --no-ri -v "1.24.0"
/usr/bin/gem install aws-sdk-pinpointsmsvoice -q --no-rdoc --no-ri -v "1.21.0"
/usr/bin/gem install aws-sdk-polly -q --no-rdoc --no-ri -v "1.37.0"
/usr/bin/gem install aws-sdk-pricing -q --no-rdoc --no-ri -v "1.24.0"
/usr/bin/gem install aws-sdk-qldb -q --no-rdoc --no-ri -v "1.11.0"
/usr/bin/gem install aws-sdk-qldbsession -q --no-rdoc --no-ri -v "1.9.0"
/usr/bin/gem install aws-sdk-quicksight -q --no-rdoc --no-ri -v "1.34.0"
/usr/bin/gem install aws-sdk-ram -q --no-rdoc --no-ri -v "1.22.0"
/usr/bin/gem install aws-sdk-rds -q --no-rdoc --no-ri -v "1.105.0"
/usr/bin/gem install aws-sdk-rdsdataservice -q --no-rdoc --no-ri -v "1.23.0"
/usr/bin/gem install aws-sdk-redshift -q --no-rdoc --no-ri -v "1.50.0"
/usr/bin/gem install aws-sdk-redshiftdataapiservice -q --no-rdoc --no-ri -v "1.2.0"
/usr/bin/gem install aws-sdk-rekognition -q --no-rdoc --no-ri -v "1.47.0"
/usr/bin/gem install aws-sdk-resourcegroups -q --no-rdoc --no-ri -v "1.32.0"
/usr/bin/gem install aws-sdk-resourcegroupstaggingapi -q --no-rdoc --no-ri -v "1.34.0"
/usr/bin/gem install aws-sdk-robomaker -q --no-rdoc --no-ri -v "1.30.0"
/usr/bin/gem install aws-sdk-route53 -q --no-rdoc --no-ri -v "1.44.0"
/usr/bin/gem install aws-sdk-route53domains -q --no-rdoc --no-ri -v "1.28.0"
/usr/bin/gem install aws-sdk-route53resolver -q --no-rdoc --no-ri -v "1.21.0"
/usr/bin/gem install aws-sdk-s3 -q --no-rdoc --no-ri -v "1.83.1"
/usr/bin/gem install aws-sdk-s3control -q --no-rdoc --no-ri -v "1.24.0"
/usr/bin/gem install aws-sdk-s3outposts -q --no-rdoc --no-ri -v "1.0.0"
/usr/bin/gem install aws-sdk-sagemaker -q --no-rdoc --no-ri -v "1.71.0"
/usr/bin/gem install aws-sdk-sagemakerruntime -q --no-rdoc --no-ri -v "1.27.0"
/usr/bin/gem install aws-sdk-savingsplans -q --no-rdoc --no-ri -v "1.12.0"
/usr/bin/gem install aws-sdk-schemas -q --no-rdoc --no-ri -v "1.10.0"
/usr/bin/gem install aws-sdk-secretsmanager -q --no-rdoc --no-ri -v "1.43.0"
/usr/bin/gem install aws-sdk-securityhub -q --no-rdoc --no-ri -v "1.35.0"
/usr/bin/gem install aws-sdk-serverlessapplicationrepository -q --no-rdoc --no-ri -v "1.32.0"
/usr/bin/gem install aws-sdk-servicecatalog -q --no-rdoc --no-ri -v "1.53.0"
/usr/bin/gem install aws-sdk-servicediscovery -q --no-rdoc --no-ri -v "1.31.0"
/usr/bin/gem install aws-sdk-servicequotas -q --no-rdoc --no-ri -v "1.11.0"
/usr/bin/gem install aws-sdk-ses -q --no-rdoc --no-ri -v "1.36.0"
/usr/bin/gem install aws-sdk-sesv2 -q --no-rdoc --no-ri -v "1.14.0"
/usr/bin/gem install aws-sdk-shield -q --no-rdoc --no-ri -v "1.32.0"
/usr/bin/gem install aws-sdk-signer -q --no-rdoc --no-ri -v "1.26.0"
/usr/bin/gem install aws-sdk-simpledb -q --no-rdoc --no-ri -v "1.24.0"
/usr/bin/gem install aws-sdk-sms -q --no-rdoc --no-ri -v "1.27.0"
/usr/bin/gem install aws-sdk-snowball -q --no-rdoc --no-ri -v "1.35.0"
/usr/bin/gem install aws-sdk-sns -q --no-rdoc --no-ri -v "1.35.0"
/usr/bin/gem install aws-sdk-sqs -q --no-rdoc --no-ri -v "1.34.0"
/usr/bin/gem install aws-sdk-ssm -q --no-rdoc --no-ri -v "1.95.0"
/usr/bin/gem install aws-sdk-ssoadmin -q --no-rdoc --no-ri -v "1.3.0"
/usr/bin/gem install aws-sdk-ssooidc -q --no-rdoc --no-ri -v "1.8.0"
/usr/bin/gem install aws-sdk-states -q --no-rdoc --no-ri -v "1.36.0"
/usr/bin/gem install aws-sdk-storagegateway -q --no-rdoc --no-ri -v "1.51.0"
/usr/bin/gem install aws-sdk-support -q --no-rdoc --no-ri -v "1.28.0"
/usr/bin/gem install aws-sdk-swf -q --no-rdoc --no-ri -v "1.25.0"
/usr/bin/gem install aws-sdk-synthetics -q --no-rdoc --no-ri -v "1.9.0"
/usr/bin/gem install aws-sdk-textract -q --no-rdoc --no-ri -v "1.21.0"
/usr/bin/gem install aws-sdk-timestreamquery -q --no-rdoc --no-ri -v "1.1.0"
/usr/bin/gem install aws-sdk-timestreamwrite -q --no-rdoc --no-ri -v "1.1.0"
/usr/bin/gem install aws-sdk-transcribeservice -q --no-rdoc --no-ri -v "1.50.0"
/usr/bin/gem install aws-sdk-transcribestreamingservice -q --no-rdoc --no-ri -v "1.23.0"
/usr/bin/gem install aws-sdk-transfer -q --no-rdoc --no-ri -v "1.28.0"
/usr/bin/gem install aws-sdk-translate -q --no-rdoc --no-ri -v "1.28.0"
/usr/bin/gem install aws-sdk-waf -q --no-rdoc --no-ri -v "1.36.0"
/usr/bin/gem install aws-sdk-wafregional -q --no-rdoc --no-ri -v "1.37.0"
/usr/bin/gem install aws-sdk-wafv2 -q --no-rdoc --no-ri -v "1.14.0"
/usr/bin/gem install aws-sdk-workdocs -q --no-rdoc --no-ri -v "1.28.0"
/usr/bin/gem install aws-sdk-worklink -q --no-rdoc --no-ri -v "1.21.0"
/usr/bin/gem install aws-sdk-workmail -q --no-rdoc --no-ri -v "1.33.0"
/usr/bin/gem install aws-sdk-workmailmessageflow -q --no-rdoc --no-ri -v "1.9.0"
/usr/bin/gem install aws-sdk-workspaces -q --no-rdoc --no-ri -v "1.48.0"
/usr/bin/gem install aws-sdk-xray -q --no-rdoc --no-ri -v "1.35.0"
/usr/bin/gem install aws-sdk-resources -q --no-rdoc --no-ri -v "3.84.0"
/usr/bin/gem install aws-sdk -q --no-rdoc --no-ri -v "3.0.1"
/usr/bin/gem install bundler -q --no-rdoc --no-ri -v "1.12.5"
/usr/bin/gem install rspec -q --no-rdoc --no-ri -v "3.7.0"
/usr/bin/gem install aws-cleanup -q --no-rdoc --no-ri -v "1.0.1"



01:38:28 (0) root@prod-worker-1c: /usr/share/chef-bot on deploy
$ cat Gemfile.lock
GEM
  remote: https://rubygems.org/
  specs:
    chef-api (0.5.0)
      logify (~> 0.1)
      mime-types
    logify (0.2.0)
    mime-types (2.6.1)
    slack-notifier (1.2.1)

PLATFORMS
  ruby

DEPENDENCIES
  chef-api (~> 0.1)
  slack-notifier

BUNDLED WITH
   1.10.4

