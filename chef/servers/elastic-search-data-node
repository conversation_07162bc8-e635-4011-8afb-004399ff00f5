#!/bin/bash

###############################################################################
# If you need a new server follow the below
###############################################################################

if [ -e $HOME/.chef_config ]; then
  . $HOME/.chef_config
fi

# Testing with new server type

knife ec2 server create \
  --environment prod \
  --type infra \
  --subnet subnet-711bd61a \
  --node-name prod-search-v2-contacts-1b-1 \
  --security-group-ids sg-66e13b1d,sg-8bcd72f1 \
  --run-list "recipe[et_base],recipe[et_contacts_search]" \
  --availability-zone us-east-1b \
  --image ami-0b0ea68c435eb488d \
  --flavor r6i.xlarge \
  --ssh-user ubuntu \
  --ebs-volume-type gp2 \
  --identity-file $HOME/.ssh/aws_dev.pem

knife ec2 server create \
  --environment prod \
  --type infra \
  --subnet subnet-421bd629 \
  --node-name prod-search-v2-contacts-1c-1 \
  --security-group-ids sg-66e13b1d,sg-8bcd72f1 \
  --run-list "recipe[et_base],recipe[et_contacts_search]" \
  --availability-zone us-east-1c \
  --image ami-0b0ea68c435eb488d \
  --flavor r6i.xlarge \
  --ssh-user ubuntu \
  --ebs-volume-type gp2 \
  --identity-file $HOME/.ssh/aws_dev.pem

knife ec2 server create \
  --environment prod \
  --type infra \
  --subnet subnet-5e1bd635 \
  --node-name prod-search-v2-contacts-1d-1 \
  --security-group-ids sg-66e13b1d,sg-8bcd72f1 \
  --run-list "recipe[et_base],recipe[et_contacts_search]" \
  --availability-zone us-east-1d \
  --image ami-0b0ea68c435eb488d \
  --flavor r6i.xlarge \
  --ssh-user ubuntu \
  --ebs-volume-type gp2 \
  --identity-file $HOME/.ssh/aws_dev.pem

# requires creating a separate volume for ES and attaching it to the server

aws ec2 create-volume --availability-zone us-east-1b --volume-type gp3 --size 900

aws ec2 describe-volumes --volume-ids vol-0cebc6c450513abad

aws ec2 attach-volume --volume-id vol-0cebc6c450513abad --instance-id i-05f24449a85f2e9bf --device /dev/sdf

aws ec2 delete-volume --volume-id vol-0cebc6c450513abad

# some shortcuts

# rename the current instance to -old
./aws/rename-instance -n prod-search-v2-contacts-1b-1

# After running the knife command use this to create/attach volume

./aws/attach-volume -n prod-search-v2-contacts-1b-1 -s 900

# to terminate the old instance

aws ec2 terminate-instances --instance-ids $(./aws/get-instance-id -n prod-search-v2-contacts-1b-1-old)

# manually add datadog source
ssh -i .ssh/aws_dev.pem ubuntu@IP
sudo su -

echo 'deb      "http://apt.datadoghq.com" stable main' > /etc/apt/sources.list.d/datadog.list
chmod 644 /etc/apt/sources.list.d/datadog.list
mkdir -p /opt/consul/0.7.1
cp ~ubuntu/consul /opt/consul/0.7.1/
rm /etc/ssl/certs/DST_Root_CA_X3.pem; rm /opt/chef/embedded/ssl/certs/cacert.pem; ln -s /etc/ssl/certs/ca-certificates.crt /opt/chef/embedded/ssl/certs/cacert.pem

# format and mount drive
mkfs -t ext4 /dev/nvme1n1
mkdir /mnt/dev0
echo '/dev/nvme1n1 /mnt/dev0 ext4 defaults 0 2' >> /etc/fstab
mount /mnt/dev0

apt-get update
apt-get --allow-unauthenticated -q -y install datadog-agent=1:5.14.1-1

# start elasticsearch
sudo service elasticsearch start
