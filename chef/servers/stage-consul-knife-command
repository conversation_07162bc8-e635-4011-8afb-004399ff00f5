#!/bin/bash

if [ -e $HOME/.chef_config ]; then
  . $HOME/.chef_config
fi

knife ec2 server create  \
 --environment stage \
 --subnet subnet-a792da8f \
 --node-name stage-consul-1b-1 \
 --security-group-ids sg-97e55ef2,sg-4ba40933,sg-6ba92c10 \
 --run-list "recipe[et_base],recipe[et_consul::server],recipe[et_secrets],role[active_vault]" \
 --type infra \
 --availability-zone us-east-1b \
 --image ami-0b0ea68c435eb488d \
 --flavor t3a.medium \
 --ssh-user ubuntu \
 --ebs-volume-type gp2 \
 --identity-file $HOME/.ssh/aws_dev.pem

knife ec2 server create  \
 --environment stage \
 --subnet subnet-09b2b87d \
 --node-name stage-consul-1c-2 \
 --security-group-ids sg-97e55ef2,sg-4ba40933,sg-6ba92c10 \
 --run-list "recipe[et_base],recipe[et_consul::server],recipe[et_secrets],role[active_vault]" \
 --type infra \
 --availability-zone us-east-1c \
 --image ami-0b0ea68c435eb488d \
 --flavor t3a.medium \
 --ssh-user ubuntu \
 --ebs-volume-type gp2 \
 --identity-file $HOME/.ssh/aws_dev.pem

knife ec2 server create  \
 --environment stage \
 --subnet subnet-34e9de72 \
 --node-name stage-consul-1d-2 \
 --security-group-ids sg-97e55ef2,sg-4ba40933,sg-6ba92c10 \
 --run-list "recipe[et_base],recipe[et_consul::server],recipe[et_secrets],role[active_vault]" \
 --type infra \
 --availability-zone us-east-1d \
 --image ami-0b0ea68c435eb488d \
 --flavor t3a.medium \
 --ssh-user ubuntu \
 --ebs-volume-type gp2 \
 --identity-file $HOME/.ssh/aws_dev.pem


# When the knife command tells you the private IP, ssh directly to the server with the ubuntu user, eg.
# $ ssh -i ~/.ssh/aws_dev.pem ubuntu@************

# wait for initial chef run (kicked off by the knife command) to fail, then...

# 2. Fix the out of date cacerts.pem that chef uses
sudo apt install ca-certificates
sudo rm /etc/ssl/certs/DST_Root_CA_X3.pem
sudo update-ca-certificates
sudo rm /opt/chef/embedded/ssl/certs/cacert.pem
sudo ln -s /etc/ssl/certs/ca-certificates.crt /opt/chef/embedded/ssl/certs/cacert.pem

# 3. Now fix consul
sudo su -
mkdir -p /opt/consul/0.7.1
cd /opt/consul/0.7.1
curl -s https://releases.hashicorp.com/consul/0.7.1/consul_0.7.1_linux_amd64.zip -o consul_0.7.1_linux_amd64.zip
unzip consul_0.7.1_linux_amd64.zip
rm consul_0.7.1_linux_amd64.zip

start consul

# 4. Now re-run chef-client, it will fail again
sudo chef-client

# 5. Maually install datadog-agent
sudo apt-get --allow-unauthenticated -q -y install datadog-agent=1:5.14.1-1

# 6. Now re-run chef-client again
sudo chef-client

# ran without error now, almost hurray, still vault errors

# Fix issues with Consul no leader found

# you will need to stop chef-client and consul on all servers

/etc/init.d/chef-client stop
service consul stop

ps aux | grep consul # make sure it is not running

# Now on 1 of the servers that was previously running, create the peers.json file
# For info:  https://learn.hashicorp.com/tutorials/consul/recovery-outage#manual-recovery-using-peers-json

echo '["*************:8300","************:8300","*************:8300"]' > /var/lib/consul/raft/peers.json

service consul start

# Now you can start up chef-client and consul on the other servers

/etc/init.d/chef-client start
service consul start

# Now verify vault status (you may need to log out/in require environment variables that come from chef)

/opt/vault/0.6.0/vault status
