#!/bin/bash

knife ec2 server create \
  --environment prod \
  --type infra \
  --subnet subnet-421bd629 \
  --node-name prod-ci-1c \
  --security-group-ids sg-e9ce2886,sg-97f7edf5,sg-7108c71e \
  --run-list "recipe[et_base],recipe[et_jenkins]" \
  --availability-zone us-east-1c \
  --image ami-0b0ea68c435eb488d \
  --flavor m5a.2xlarge \
  --ssh-user ubuntu \
  --ebs-volume-type gp2 \
  --ebs-size 20 \
  --identity-file $HOME/.ssh/aws_dev.pem

# When the knife command tells you the private IP, ssh directly to the server with the ubuntu user, eg.
# $ ssh -i ~/.ssh/aws_dev.pem ubuntu@************

# wait for initial chef run (kicked off by the knife command) to fail, then...

# 1. manually fix some gems:
sudo /opt/chef/embedded/bin/gem install faraday -v "1.0.1"
sudo /opt/chef/embedded/bin/gem install public_suffix -v "4.0.7"
sudo /opt/chef/embedded/bin/gem uninstall fog-aliyun

# need to install some build tools for the next gem install
sudo apt install build-essential
sudo /opt/chef/embedded/bin/gem install rexml -v "3.2.5"
sudo /opt/chef/embedded/bin/gem install fog-aliyun -v "0.3.19"


# 2. Fix the out of date cacerts.pem that chef uses
sudo apt install ca-certificates unzip
sudo rm /etc/ssl/certs/DST_Root_CA_X3.pem
sudo rm /usr/share/ca-certificates/mozilla/DST_Root_CA_X3.crt
# remove reference to DST_Root_CA_X3.crt in /etc/ca-certificates.conf
sudo vi /etc/ca-certificates.conf
sudo update-ca-certificates
sudo rm /opt/chef/embedded/ssl/certs/cacert.pem
sudo ln -s /etc/ssl/certs/ca-certificates.crt /opt/chef/embedded/ssl/certs/cacert.pem

# 3. Now fix consul
sudo su -
mkdir -p /opt/consul/0.7.1
cd /opt/consul/0.7.1
curl -s https://releases.hashicorp.com/consul/0.7.1/consul_0.7.1_linux_amd64.zip -o consul_0.7.1_linux_amd64.zip
unzip consul_0.7.1_linux_amd64.zip
rm consul_0.7.1_linux_amd64.zip

start consul

# 4. Now re-run chef-client, it will fail again
sudo chef-client

# 5. Maually install datadog-agent
sudo apt-get --allow-unauthenticated -q -y install datadog-agent=1:5.14.1-1

# 6. Now re-run chef-client again
sudo chef-client

# need to change EBS volume vol-013b5a314b7646f3c over to the new server and add as /dev/xvde

# then mounted EBS volume with
mkdir /mnt/ebs0
mount /dev/xvdf /mnt/ebs0

# now you need to symlink the jenkins folder

rm -rf /var/lib/jenkins
ln -s /mnt/ebs0/jenkins /var/lib/jenkins

# downgrade pip

pip install --upgrade pip==8.0.2
pip install setuptools==36.4.0
pip install virtualenv==16.7.10

# run chef-client
sudo chef-client

# need to run chef-client on both load balancers

# prod-lb-1c-1, prod-lb-1d-1

# fix cacerts for Java
# copy the /etc/ssl/certs/java/cacerts from another server, I used

scp prod-mesos-agent-1b-1:/etc/ssl/certs/java/cacerts .
scp cacerts prod-ci-1c:
ssh prod-ci-1c
sudo cp cacerts /etc/ssl/certs/java/cacerts
sudo /etc/init.d/jenkins restart

# fix ruby install

/opt/rbenv/versions/2.2.2/bin/gem install bundler --no-rdoc --no-ri  -v "1.17.3"
/opt/rbenv/versions/2.2.2/bin/gem install aws-eventstream --no-rdoc --no-ri  -v '1.1.1'
/opt/rbenv/versions/2.2.2/bin/gem install aws-sigv4 --no-rdoc --no-ri  -v '1.2.4'
/opt/rbenv/versions/2.2.2/bin/gem install deb-s3 --no-rdoc --no-ri  -v "0.10.0"

apt install rake

# log into the AWS conole (old account)
# From EC2 running instances find the server
# select it and in actions goto Instance Settings => Attach/Replace IAM Role
# Now attach the jenkins role to the instance

# Docker CERTS need updating otherwise we'll get an X509 error when downloading images from registery.evertrue.com
# https://github.com/moby/moby/issues/8849#issuecomment-********

sudo update-ca-certificates
sudo service docker restart
