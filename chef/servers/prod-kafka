#!/bin/bash

if [ -e $HOME/.chef_config ]; then
  . $HOME/.chef_config
fi

# us-east-1b
knife ec2 server create  \
 --no-host-key-verify \
 --environment prod \
 --subnet subnet-711bd61a \
 --node-name prod-kafka-1b \
 --security-group-ids sg-b920d8de,sg-97f7edf5 \
 --availability-zone us-east-1b \
 --run-list "recipe[et_base],recipe[et_kafka]" \
 --image ami-04ef7d8fec8ae87fc \
 --type infra \
 --ssh-user ubuntu \
 --identity-file $HOME/.ssh/aws_dev.pem \
 --ebs-volume-type gp2 \
 --flavor t3a.xlarge

# us-east-1c
knife ec2 server create  \
 --no-host-key-verify \
 --environment prod \
 --subnet subnet-421bd629 \
 --node-name prod-kafka-1c \
 --security-group-ids sg-b920d8de,sg-97f7edf5 \
 --availability-zone us-east-1c \
 --run-list "recipe[et_base],recipe[et_kafka]" \
 --image ami-04ef7d8fec8ae87fc \
 --type infra \
 --ssh-user ubuntu \
 --identity-file $HOME/.ssh/aws_dev.pem \
 --ebs-volume-type gp2 \
 --flavor t3a.xlarge

# us-east-1d
knife ec2 server create  \
 --no-host-key-verify \
 --environment prod \
 --subnet subnet-5e1bd635 \
 --node-name prod-kafka-1d \
 --security-group-ids sg-b920d8de,sg-97f7edf5 \
 --availability-zone us-east-1d \
 --run-list "recipe[et_base],recipe[et_kafka]" \
 --image ami-04ef7d8fec8ae87fc \
 --type infra \
 --ssh-user ubuntu \
 --identity-file $HOME/.ssh/aws_dev.pem \
 --ebs-volume-type gp2 \
 --flavor t3a.xlarge

# When the knife command tells you the private IP, ssh directly to the server with the ubuntu user, eg.
# $ ssh -i ~/.ssh/aws_dev.pem ubuntu@************

# wait for initial chef run (kicked off by the knife command) to fail, then...

echo 'deb      "http://apt.datadoghq.com" stable main' > /etc/apt/sources.list.d/datadog.list
chmod 644 /etc/apt/sources.list.d/datadog.list
mkdir -p /opt/consul/0.7.1
cp ~ubuntu/consul /opt/consul/0.7.1/
rm /etc/ssl/certs/DST_Root_CA_X3.pem; rm /opt/chef/embedded/ssl/certs/cacert.pem; ln -s /etc/ssl/certs/ca-certificates.crt /opt/chef/embedded/ssl/certs/cacert.pem
cd /opt/; tar zxf ~ubuntu/java_jce.tgz

# format and mount drive
mkfs -t ext4 /dev/nvme1n1
mkdir /mnt/dev0
echo '/dev/nvme1n1 /mnt/dev0 ext4 defaults 0 2' >> /etc/fstab
mount /mnt/dev0

apt-get update
apt-get --allow-unauthenticated -q -y install datadog-agent=1:5.10.1-1

# fix checksum error with jce.zip
# log into another prod kafka server
# cd /opt
# tar zcf ~/java_jce.tgz java_jce/
# exit and scp locally
# and scp to new server
# now
# sudo su -
# cd /opt
# tar zxf ~youruser/java_jce.tgz
# run chef-client

# when replacing a kafka server, you need to assign it the old broker id
# service kafka stop
# vi /mnt/dev0/kafka/meta.properties
# change the broker.id and save wq!
# service start kafka

# Broker IDs

prod-kafka-1b: 1006
prod-kafka-1c: 1004
prod-kafka-1d: 1008
prod-kafka-1b-2: 1011
prod-kafka-1c-2: 1010
prod-kafka-1d-2: 1012
