#!/bin/bash

now goto the box you are removing, and do

# in a screen do, command waits for cassandra to stop
# nodetool -h prod-contacts-cass-1b-4 decommission

# monitor the progress with

# nodetool status
# nodetool netstats | grep -v '100%'

# you'll see the node with a UL (up leaving) status

# once node is not in the list it is safe to launch the new one
# rename the old one to "-old"



if [ -e $HOME/.chef_config ]; then
  . $HOME/.chef_config
fi

knife ec2 server create  \
 --environment prod \
 --subnet subnet-711bd61a \
 --node-name prod-contacts-cass-1b-1 \
 --security-group-ids sg-f2951a96,sg-97f7edf5,sg-e7a92683 \
 --run-list "recipe[et_base],recipe[et_contacts_cassandra],recipe[et_contacts_mcrouter]" \
 --availability-zone us-east-1b \
 --image ami-04ef7d8fec8ae87fc \
 --type infra \
 --ssh-user ubuntu \
 --identity-file $HOME/.ssh/aws_dev.pem \
  --ebs-volume-type gp2 \
 --flavor r6i.xlarge

knife ec2 server create  \
 --environment prod \
 --subnet subnet-421bd629 \
 --node-name prod-contacts-cass-1c-1 \
 --security-group-ids sg-f2951a96,sg-97f7edf5,sg-e7a92683 \
 --run-list "recipe[et_base],recipe[et_contacts_cassandra],recipe[et_contacts_mcrouter]" \
 --availability-zone us-east-1c \
 --image ami-04ef7d8fec8ae87fc \
 --type infra \
 --ssh-user ubuntu \
 --identity-file $HOME/.ssh/aws_dev.pem \
  --ebs-volume-type gp2 \
 --flavor r6i.xlarge

knife ec2 server create  \
 --environment prod \
 --subnet subnet-5e1bd635 \
 --node-name prod-contacts-cass-1d-1 \
 --security-group-ids sg-f2951a96,sg-97f7edf5,sg-e7a92683 \
 --run-list "recipe[et_base],recipe[et_contacts_cassandra],recipe[et_contacts_mcrouter]" \
 --availability-zone us-east-1d \
 --image ami-04ef7d8fec8ae87fc \
 --type infra \
 --ssh-user ubuntu \
 --identity-file $HOME/.ssh/aws_dev.pem \
  --ebs-volume-type gp2 \
 --flavor r6i.xlarge

# When the knife command tells you the private IP, ssh directly to the server with the ubuntu user, eg.
# $ ssh -i ~/.ssh/aws_dev.pem ubuntu@************

sudo su -

echo 'deb      "http://apt.datadoghq.com" stable main' > /etc/apt/sources.list.d/datadog.list
chmod 644 /etc/apt/sources.list.d/datadog.list
mkdir -p /opt/consul/0.7.1
cp ~ubuntu/consul /opt/consul/0.7.1/
rm /etc/ssl/certs/DST_Root_CA_X3.pem; rm /opt/chef/embedded/ssl/certs/cacert.pem; ln -s /etc/ssl/certs/ca-certificates.crt /opt/chef/embedded/ssl/certs/cacert.pem
cd /opt/; tar zxf ~ubuntu/java_jce.tgz

# format and mount drive
mkfs -t ext4 /dev/nvme1n1
mkdir /mnt/dev0
echo '/dev/nvme1n1 /mnt/dev0 ext4 defaults 0 2' >> /etc/fstab
mount /mnt/dev0

apt-get update
apt-get --allow-unauthenticated -q -y install datadog-agent=1:5.10.1-1

# /etc/init.d/cassandra restart

# Cassandra should be running and new node should be UP (up normal) in

# nodetool status
# nodetool netstats | grep -v '100%'

# now start repairs

# in a screen run

# /usr/local/bin/cassandra-repair


# for future reference a list of python packages that work together

# 02:57:01 (1) root@stage-contacts-cass-1b-4: ~
# $ pip list
# appdirs (1.4.3)
# apt-xapian-index (0.45)
# cassandra-pylib (0.0.0)
# chardet (2.0.1)
# Cheetah (2.4.4)
# cloud-init (0.7.5)
# configobj (4.7.2)
# configparser (4.0.2)
# contextlib2 (0.6.0.post1)
# distlib (0.3.0)
# filelock (3.0.12)
# importlib-metadata (1.5.0)
# importlib-resources (1.0.2)
# iotop (0.6)
# jsonpatch (1.3)
# jsonpointer (1.0)
# Landscape-Client (14.12)
# Mako (0.9.1)
# MarkupSafe (0.18)
# oauth (1.0.1)
# PAM (0.4.2)
# pathlib2 (2.3.5)
# pip (8.0.2)
# prettytable (0.7.2)
# pycrypto (2.6.1)
# pycurl (7.19.3)
# pyOpenSSL (0.13)
# pyserial (2.6)
# python-apt (0.9.3.5ubuntu2)
# python-dateutil (2.8.1)
# python-debian (0.1.21-nmu2ubuntu2)
# python-magic (0.4.15)
# PyYAML (3.10)
# requests (2.2.1)
# s3cmd (2.0.2)
# scandir (1.10.0)
# setuptools (36.4.0)
# six (1.14.0)
# ssh-import-id (3.21)
# Twisted-Core (13.2.0)
# Twisted-Names (13.2.0)
# Twisted-Web (13.2.0)
# typing (*******)
# urllib3 (1.7.1)
# virtualenv (20.0.3)
# wheel (0.34.2)
# zipp (0.0.0)
# zope.interface (4.0.5)
