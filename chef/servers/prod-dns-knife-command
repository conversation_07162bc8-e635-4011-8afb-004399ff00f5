#!/bin/bash

if [ -e $HOME/.chef_config ]; then
  . $HOME/.chef_config
fi

knife ec2 server create \
 --environment prod \
 --subnet subnet-421bd629 \
 --node-name prod-dns-1c-1 \
 --security-group-ids sg-d6f82ab9,sg-97f7edf5 \
 --run-list "recipe[et_base],recipe[et_ec2dnsserver]" \
 --image ami-0b0ea68c435eb488d \
 --type infra \
 --ssh-user ubuntu \
 --identity-file $HOME/.ssh/aws_dev.pem \
 --flavor t3.medium

knife ec2 server create \
 --environment prod \
 --subnet subnet-5e1bd635 \
 --node-name prod-dns-1d-1 \
 --security-group-ids sg-d6f82ab9,sg-97f7edf5 \
 --run-list "recipe[et_base],recipe[et_ec2dnsserver]" \
 --image ami-0b0ea68c435eb488d \
 --type infra \
 --ssh-user ubuntu \
 --identity-file $HOME/.ssh/aws_dev.pem \
 --flavor t3.medium


# Add the DNS iam role to the new instance in the AWS console

# When the knife command tells you the private IP, ssh directly to the server with the ubuntu user, eg.
# $ ssh -i ~/.ssh/aws_dev.pem ubuntu@************

# wait for initial chef run (kicked off by the knife command) to fail, then...

# 2. Fix the out of date cacerts.pem that chef uses
sudo apt install ca-certificates
sudo rm /etc/ssl/certs/DST_Root_CA_X3.pem
sudo update-ca-certificates
sudo rm /opt/chef/embedded/ssl/certs/cacert.pem
sudo ln -s /etc/ssl/certs/ca-certificates.crt /opt/chef/embedded/ssl/certs/cacert.pem

# 3. Now fix consul
sudo su -
mkdir -p /opt/consul/0.7.1
cd /opt/consul/0.7.1
curl -s https://releases.hashicorp.com/consul/0.7.1/consul_0.7.1_linux_amd64.zip -o consul_0.7.1_linux_amd64.zip
unzip consul_0.7.1_linux_amd64.zip
rm consul_0.7.1_linux_amd64.zip

start consul

# 4. Now re-run chef-client, it will fail again
sudo chef-client

# 5. Maually install datadog-agent
sudo apt-get --allow-unauthenticated -q -y install datadog-agent=1:5.14.1-1

# 6. Now re-run chef-client again
sudo chef-client
