#!/bin/bash

if [ -e $HOME/.chef_config ]; then
  . $HOME/.chef_config
fi

knife ec2 server create  \
 --environment stage \
<<<<<<< HEAD:chef/servers/stage-cassandra-knife-command
 --subnet subnet-a792da8f \
 --node-name stage-contacts-cass-1b-4 \
 --security-group-ids sg-97e55ef2,sg-a8438fcc,sg-c96da1ad,sg-b59619d1 \
 --run-list "recipe[et_base],recipe[et_contacts_cassandra],recipe[et_contacts_spark],recipe[et_contacts_mcrouter],role[cassandra_seed],role[cassandra_opscenter],role[spark_master],role[cassandra_contacts_search]" \
 --image ami-04ef7d8fec8ae87fc \
 --availability-zone us-east-1b \
 --type infra \
 --ssh-user ubuntu \
 --identity-file $HOME/.ssh/aws_dev.pem \
 --flavor r6i.large \
 --ebs-volume-type gp2

knife ec2 server create  \
 --environment stage \
 --subnet subnet-09b2b87d \
 --node-name stage-contacts-cass-1c-2 \
 --security-group-ids sg-97e55ef2,sg-a8438fcc,sg-c96da1ad,sg-b59619d1 \
 --run-list "recipe[et_base],recipe[et_contacts_cassandra],recipe[et_contacts_spark],recipe[et_contacts_mcrouter],role[cassandra_seed],role[cassandra_opscenter],role[spark_master],role[cassandra_contacts_search]" \
 --image ami-04ef7d8fec8ae87fc \
 --availability-zone us-east-1c \
 --type infra \
 --ssh-user ubuntu \
 --identity-file $HOME/.ssh/aws_dev.pem \
 --flavor r6i.large \
 --ebs-volume-type gp2

knife ec2 server create  \
 --environment stage \
 --subnet subnet-34e9de72 \
 --node-name stage-contacts-cass-1d-1 \
 --security-group-ids sg-97e55ef2,sg-a8438fcc,sg-c96da1ad,sg-b59619d1 \
 --run-list "recipe[et_base],recipe[et_contacts_cassandra],recipe[et_contacts_spark],recipe[et_contacts_mcrouter],role[cassandra_seed],role[cassandra_opscenter],role[spark_master],role[cassandra_contacts_search]" \
 --image ami-04ef7d8fec8ae87fc \
 --availability-zone us-east-1d \
 --type infra \
 --ssh-user ubuntu \
 --identity-file $HOME/.ssh/aws_dev.pem \
 --flavor r6i.large \
 --ebs-volume-type gp2

# When the knife command tells you the private IP, ssh directly to the server with the ubuntu user, eg.
# $ ssh -i ~/.ssh/aws_dev.pem ubuntu@************

# wait for initial chef run (kicked off by the knife command) to fail, then...

# 1. manually fix some gems:
sudo /opt/chef/embedded/bin/gem install faraday -v "1.0.1"
sudo /opt/chef/embedded/bin/gem install public_suffix -v "4.0.7"
sudo /opt/chef/embedded/bin/gem uninstall fog-aliyun

# need to install some build tools for the next gem install
sudo apt install build-essential unzip
sudo /opt/chef/embedded/bin/gem install fog-aliyun -v "0.3.19"

# 2. Fix the out of date cacerts.pem that chef uses
sudo apt install ca-certificates

sudo rm /etc/ssl/certs/DST_Root_CA_X3.pem

sudo update-ca-certificates

sudo rm /opt/chef/embedded/ssl/certs/cacert.pem
sudo ln -s /etc/ssl/certs/ca-certificates.crt /opt/chef/embedded/ssl/certs/cacert.pem

# 3. Now fix consul
sudo su -
mkdir -p /opt/consul/0.7.1
cd /opt/consul/0.7.1
curl -s https://releases.hashicorp.com/consul/0.7.1/consul_0.7.1_linux_amd64.zip -o consul_0.7.1_linux_amd64.zip
unzip consul_0.7.1_linux_amd64.zip
rm consul_0.7.1_linux_amd64.zip

start consul

# 4. Now re-run chef-client, it will fail again
sudo chef-client

# 4.1 Potenitally do the fix for "stack level too deep"
# https://github.com/evertrue/et_repair_shop/blob/master/chef/chef-issues.txt

# 4.2 Potentially fix fog-aliyun again
sudo /opt/chef/embedded/bin/gem uninstall fog-aliyun
sudo /opt/chef/embedded/bin/gem install fog-aliyun -v "0.3.19"

# 5. Maually install datadog-agent
sudo apt-get --allow-unauthenticated -q -y install datadog-agent=1:5.10.1-1

# 6. Now re-run chef-client again
sudo chef-client

# 7. fix checksum error with jce.zip
# log into another stage cassandra server
cd /opt
tar -czvf ~/java_jce.tgz java_jce/
# create base64 text of it to copy to the new server
base64 ~/java_jce.tgz
# on the new server create the file from the base64 output
echo 'blah blah blah' | base64 -d > ~/java_jce.tgz
cd /opt
rm -rf java_jce
tar zxf ~youruser/java_jce.tgz -C .

# 6. Now re-run chef-client, it will fail again
sudo chef-client

# 6.1 potenitally check the `ps awwwfx` listing for a gcc make process, this will take a fair while to finish like 5-10 min

# 7 Spark shit

# You might need to hand edit the /etc/init.d/spark-slave file.  In here is a list of spark masters and 1 of them might be ,:7077.
# This is because the new server you are starting up is in the list and chef doesn't know the FQDN yet; removed this ,:7077 from the list.

# Run /etc/init.d/spark-slave restart

# 8. Now re-run chef-client
sudo chef-client

# Cassandra should be running and new node should be UN (up normal) in
sudo nodetool status

# in a screen do, command waits for cassandra to stop
sudo nodetool -h stage-contacts-<server> decommission

# monitor the progress with nodetool, make note of the local IP and wait for it to leave the list
watch -n 5 sudo nodetool status # you'll see the node with a UL (up leaving) status until it is gone

# once node is not in the list it is safe to shutdown
sudo /etc/init.d/cassandra stop

# now start repairs; in a screen run
nodetool repair

# for future reference a list of python packages that work together

# 02:57:01 (1) root@stage-contacts-cass-1b-4: ~
# $ pip list
# appdirs (1.4.3)
# apt-xapian-index (0.45)
# cassandra-pylib (0.0.0)
# chardet (2.0.1)
# Cheetah (2.4.4)
# cloud-init (0.7.5)
# configobj (4.7.2)
# configparser (4.0.2)
# contextlib2 (0.6.0.post1)
# distlib (0.3.0)
# filelock (3.0.12)
# importlib-metadata (1.5.0)
# importlib-resources (1.0.2)
# iotop (0.6)
# jsonpatch (1.3)
# jsonpointer (1.0)
# Landscape-Client (14.12)
# Mako (0.9.1)
# MarkupSafe (0.18)
# oauth (1.0.1)
# PAM (0.4.2)
# pathlib2 (2.3.5)
# pip (8.0.2)
# prettytable (0.7.2)
# pycrypto (2.6.1)
# pycurl (7.19.3)
# pyOpenSSL (0.13)
# pyserial (2.6)
# python-apt (0.9.3.5ubuntu2)
# python-dateutil (2.8.1)
# python-debian (0.1.21-nmu2ubuntu2)
# python-magic (0.4.15)
# PyYAML (3.10)
# requests (2.2.1)
# s3cmd (2.0.2)
# scandir (1.10.0)
# setuptools (36.4.0)
# six (1.14.0)
# ssh-import-id (3.21)
# Twisted-Core (13.2.0)
# Twisted-Names (13.2.0)
# Twisted-Web (13.2.0)
# typing (*******)
# urllib3 (1.7.1)
# virtualenv (20.0.3)
# wheel (0.34.2)
# zipp (0.0.0)
# zope.interface (4.0.5)
