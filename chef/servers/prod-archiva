#!/bin/bash

# s3://et-sftp-prod/backups/archiva_backup.tar.gz

*********** Chef Client finished, 889/1383 resources updated in 24 minutes 26 seconds
*********** [2022-07-05T03:59:23+00:00] INFO: Sending resource update report (run-id: 3c9a94cd-816f-4585-a841-1d183282e9f3)

Instance ID: i-08f1055062c1fc4ea
Flavor: m1.large
Image: ami-d4e0a8c3
Region: us-east-1
Availability Zone: us-east-1c
Security Group Ids: sg-97f7edf5, sg-b767f6d3, sg-37495355
Tags: Type: infra, Name: prod-archiva-1c
SSH Key: aws_dev
Root Device Type: instance-store
Subnet ID: subnet-421bd629
Tenancy: default
Private IP Address: ***********
Environment: prod
Run List: recipe[et_base], recipe[et_archiva]

m1.large
ami-d4e0a8c3

archiva -> /mnt/dev0/archiva-2.1.1


knife ec2 server create \
  --environment prod \
  --type infra \
  --subnet subnet-711bd61a \
  --node-name prod-archiva-1b \
  --security-group-ids sg-97f7edf5,sg-b767f6d3,sg-37495355 \
  --run-list "recipe[et_base],recipe[et_archiva]" \
  --availability-zone us-east-1b \
  --image ami-04ef7d8fec8ae87fc \
  --flavor t3a.large \
  --ebs-volume-type gp2 \
  --ssh-user ubuntu \
  --identity-file $HOME/.ssh/aws_dev.pem

# When the knife command tells you the private IP, ssh directly to the server with the ubuntu user, eg.
# $ ssh -i ~/.ssh/aws_dev.pem ubuntu@************

# wait for initial chef run (kicked off by the knife command) to fail, then...

# 1. manually fix some gems:
sudo /opt/chef/embedded/bin/gem install faraday -v "1.0.1"
sudo /opt/chef/embedded/bin/gem install public_suffix -v "4.0.7"
sudo /opt/chef/embedded/bin/gem uninstall fog-aliyun

# need to install some build tools for the next gem install
sudo apt install build-essential
sudo /opt/chef/embedded/bin/gem install fog-aliyun -v "0.3.19"


# 2. Fix the out of date cacerts.pem that chef uses
sudo apt install ca-certificates
sudo rm /etc/ssl/certs/DST_Root_CA_X3.pem
sudo update-ca-certificates
sudo rm /opt/chef/embedded/ssl/certs/cacert.pem
sudo ln -s /etc/ssl/certs/ca-certificates.crt /opt/chef/embedded/ssl/certs/cacert.pem

# 3. Now fix consul
sudo su -
mkdir -p /opt/consul/0.7.1
cd /opt/consul/0.7.1
curl -s https://releases.hashicorp.com/consul/0.7.1/consul_0.7.1_linux_amd64.zip -o consul_0.7.1_linux_amd64.zip
unzip consul_0.7.1_linux_amd64.zip
rm consul_0.7.1_linux_amd64.zip

start consul

# 4. Now re-run chef-client, it will fail again
sudo chef-client

# 5. Maually install datadog-agent
sudo apt-get --allow-unauthenticated -q -y install datadog-agent=1:5.14.1-1

# 6. Now re-run chef-client again
sudo chef-client

# the DNS entry for prod-archiva-1c is in the evertruetools account

# using 1Pass create the admin, ops and jenkins users

# Create the evertrue repository

# Id: evertrue
# Nmae: Evertrue
# Directory: ./repositories/evertrue
# Index Directory: ./repositories/evertrue/.indexer
# Days Older: 0
# Retention Count: 0
# Snapshots: check box
# Scanned: leave checked

# Go back to Manager Users.  Select jenkins user and grant them Manage Repository for the evertrue repo at the bottom of the page.

Chef Manage
Nodes
Reports
Policy
Administration
FeedbackOrganizationevertrueSigned in asDoug Youch00
Nodes
Delete
Manage Tags
Reset Key
Edit Run List
Edit Attributes
Showing 1 node
prod-archiva-1c

Node NamePlatformFQDNIP AddressUptimeLast Check-InEnvironmentActions
prod-archiva-1cubuntuprod-archiva-1c.priv.evertrue.com************23 days17 hours agoprod
Node: prod-archiva-1c
Details
Attributes
Permissions
Attributes
Expand AllCollapse AllEdit
apt
cacher_dir: /var/cache/apt-cacher-ng
cacher_interface: 
cacher_port: 3142
compiletime: false
compile_time_update: true
key_proxy: 
periodic_update_min_delay: 86400
launchpad_api_version: 1.0
unattended_upgrades
enable: false
update_package_lists: true
allowed_origins
0: Ubuntu precise
origins_patterns: 
package_blacklist: 
auto_fix_interrupted_dpkg: false
minimal_steps: false
install_on_shutdown: false
mail: 
mail_only_on_error: true
remove_unused_dependencies: false
automatic_reboot: false
automatic_reboot_time: now
dl_limit: 
random_sleep: 
cacher_client
cacher_server: 
confd
force_confask: false
force_confdef: false
force_confmiss: false
force_confnew: false
force_confold: false
install_recommends: true
install_suggests: false
unattended-upgrades
mail_only_on_error: true
remove_unused_dependencies: true
seven_zip
url: https://www.7-zip.org/a/7z1805-x64.msi
checksum: 898c1ca0015183fe2ba7d55cacf0a1dea35e873bf3f8090f362a6288c6ef08d7
package_name: 7-Zip 18.05 (x64 edition)
default_extract_timeout: 600
msys2
url: http://downloads.sourceforge.net/project/msys2/Base/x86_64/msys2-base-x86_64-20160205.tar.xz
checksum: 7e97e2af042e1b6f62cf0298fe84839014ef3d4a3e7825cffc6931c66cc0fc20
build-essential
compile_time: true
msys2
path: \msys2
cron
package_name
0: cron
service_name: cron
logrotate
package
name: logrotate
source: 
version: 
provider: 
action: upgrade
directory: /etc/logrotate.d
cron
install: false
name: logrotate
command: /usr/sbin/logrotate /etc/logrotate.conf
minute: 35
hour: 2
global
weekly: true
rotate: 4
create: 
/var/log/wtmp
missingok: true
monthly: true
create: 0664 root utmp
rotate: 1
/var/log/btmp
missingok: true
monthly: true
create: 0660 root utmp
rotate: 1
chef_client
config
chef_server_url: https://api.opscode.com/organizations/evertrue
validation_client_name: evertrue-validator
node_name: prod-archiva-1c
verify_api_cert: true
client_fork: true
log_location: /var/log/chef/client.log
ssl_verify_mode: :verify_peer
log_file: client.log
interval: 1800
splay: 300
conf_dir: /etc/chef
bin: /usr/bin/chef-client
log_dir: /var/log/chef
log_perm: 640
cron
minute: 0
hour: 0,4,8,12,16,20
weekday: *
path: 
environment_variables: 
log_file: /dev/null
append_log: false
use_cron_d: false
mailto: 
task
frequency: minute
frequency_modifier: 30
user: SYSTEM
password: 
load_gems: 
reload_config: true
daemon_options: 
logrotate
rotate: 12
frequency: weekly
init_style: init
run_path: /var/run/chef
cache_path: /var/cache/chef
backup_path: /var/lib/chef
log_rotation
options
0: copytruncate
1: compress
prerotate: 
postrotate: 
ohai
disabled_plugins: 
plugin_path: 
plugins
reboot_coordinator: ohai_plugins
chef_handler
handler_path: /var/chef/handlers
sentry
dsn: https://9da60ffa43bc406f9bdd67eeaf3a2d73:<EMAIL>/30440
enabled: true
verify_ssl: true
target_domain: priv.evertrue.com
target_fqdn: prod-archiva-1c.priv.evertrue.com
packagecloud
base_repo_path: /install/repositories/
gpg_key_path: /gpgkey
hostname_override: 
proxy_host: 
proxy_port: 
default_type: deb
yum-epel
repos
0: epel
1: epel-debuginfo
2: epel-source
3: epel-testing
4: epel-testing-debuginfo
5: epel-testing-source
yum
epel-debuginfo
repositoryid: epel-debuginfo
description: Extra Packages for 12 - $basearch - Debug
mirrorlist: http://mirrors.fedoraproject.org/mirrorlist?repo=epel-debug-12&arch=$basearch
gpgkey: https://download.fedoraproject.org/pub/epel/RPM-GPG-KEY-EPEL-12
failovermethod: priority
gpgcheck: true
enabled: false
managed: false
make_cache: true
epel-source
repositoryid: epel-source
description: Extra Packages for 12 - $basearch - Source
mirrorlist: http://mirrors.fedoraproject.org/mirrorlist?repo=epel-source-12&arch=$basearch
gpgkey: https://download.fedoraproject.org/pub/epel/RPM-GPG-KEY-EPEL-12
failovermethod: priority
gpgcheck: true
enabled: false
managed: false
make_cache: true
epel-testing-debuginfo
repositoryid: epel-testing-debuginfo
description: Extra Packages for 12 - $basearch - Testing Debug
mirrorlist: http://mirrors.fedoraproject.org/mirrorlist?repo=testing-debug-epel12&arch=$basearch
gpgkey: https://download.fedoraproject.org/pub/epel/RPM-GPG-KEY-EPEL-12
failovermethod: priority
gpgcheck: true
enabled: false
managed: false
make_cache: true
epel-testing-source
repositoryid: epel-testing-source
description: Extra Packages for 12 - $basearch - Testing Source
mirrorlist: http://mirrors.fedoraproject.org/mirrorlist?repo=testing-source-epel12&arch=$basearch
gpgkey: https://download.fedoraproject.org/pub/epel/RPM-GPG-KEY-EPEL-12
failovermethod: priority
gpgcheck: true
enabled: false
managed: false
make_cache: true
epel-testing
repositoryid: epel-testing
description: Extra Packages for 12 - $basearch - Testing
mirrorlist: http://mirrors.fedoraproject.org/mirrorlist?repo=testing-epel12&arch=$basearch
gpgkey: https://download.fedoraproject.org/pub/epel/RPM-GPG-KEY-EPEL-12
failovermethod: priority
gpgcheck: true
enabled: false
managed: false
make_cache: true
epel
repositoryid: epel
description: Extra Packages for 12 - $basearch
mirrorlist: http://mirrors.fedoraproject.org/mirrorlist?repo=epel-12&arch=$basearch
gpgkey: https://download.fedoraproject.org/pub/epel/RPM-GPG-KEY-EPEL-12
failovermethod: priority
gpgcheck: true
enabled: true
managed: true
make_cache: true
main
cachedir: /var/cache/yum/$basearch/$releasever
distroverpkg: ubuntu-release
releasever: 
alwaysprompt: 
assumeyes: 
bandwidth: 
bugtracker_url: 
clean_requirements_on_remove: 
color: 
color_list_available_downgrade: 
color_list_available_install: 
color_list_available_reinstall: 
color_list_available_upgrade: 
color_list_installed_extra: 
color_list_installed_newer: 
color_list_installed_older: 
color_list_installed_reinstall: 
color_search_match: 
color_update_installed: 
color_update_local: 
color_update_remote: 
commands: 
deltarpm: 
debuglevel: 
diskspacecheck: 
enable_group_conditionals: 
errorlevel: 
exactarch: 
exclude: 
gpgcheck: true
group_package_types: 
groupremove_leaf_only: 
history_list_view: 
history_record: 
history_record_packages: 
http_caching: 
installonly_limit: 
installonlypkgs: 
installroot: 
keepalive: 
keepcache: false
kernelpkgnames: 
localpkg_gpgcheck: false
logfile: /var/log/yum.log
max_retries: 
mdpolicy: 
metadata_expire: 
mirrorlist_expire: 
multilib_policy: 
obsoletes: 
overwrite_groups: 
password: 
path: /etc/yum.conf
persistdir: 
pluginconfpath: 
pluginpath: 
plugins: 
protected_multilib: 
protected_packages: 
proxy: 
proxy_password: 
proxy_username: 
recent: 
repo_gpgcheck: 
reposdir: 
reset_nice: 
rpmverbosity: 
showdupesfromrepos: 
skip_broken: 
ssl_check_cert_permissions: 
sslcacert: 
sslclientcert: 
sslclientkey: 
sslverify: 
syslog_device: 
syslog_facility: 
syslog_ident: 
throttle: 
timeout: 
tolerant: false
tsflags: 
username: 
runit
sv_bin: /usr/bin/sv
chpst_bin: /usr/bin/chpst
service_dir: /etc/service
sv_dir: /etc/sv
lsb_init_dir: /etc/init.d
executable: /sbin/runit
yum-plugin-versionlock
enabled: 1
follow_obsoletes: 0
locklist: /etc/yum/pluginconf.d/versionlock.list
filebeat
version: 5.2.2
release: 1
disable_service: false
package_url: auto
package_force_overwrite: true
ignore_version: false
notify_restart: true
windows
base_dir: C:/opt/filebeat
solaris
base_dir: /opt
manifest_directory: /var/svc/manifest/application
service
init_style: init
retries: 0
retry_delay: 2
arch: x86_64
prospectors
chef-client
filebeat
prospectors
0
paths
0: /var/log/chef/client.log
input_type: log
document_type: chef-client
syslog
filebeat
prospectors
0
paths
0: /var/log/auth.log
1: /var/log/syslog
input_type: log
document_type: syslog
config
filebeat
prospectors: 
output
logstash
enabled: true
loadbalance: false
ssl
certificate_authorities
0: /etc/pki/tls/certs/filebeat/ca.pem
logging
to_syslog: false
to_files: true
level: info
files
path: /var/log/filebeat
name: filebeat.log
ignore_older: 6h
yum
description: Elastic Beats Repository
gpgcheck: true
enabled: true
metadata_expire: 3h
action: create
apt
description: Elastic Beats Repository
components
0: stable
1: main
distribution: 
options: -o Dpkg::Options::='--force-confnew'
action: add
openssl
restart_services: 
rsyslog
local_host_name: 
default_log_dir: /var/log
log_dir: /srv/rsyslog
working_dir: /var/spool/rsyslog
server: false
use_relp: false
relp_port: 20514
protocol: tcp
bind: *
port: 514
server_ip: 
server_search: role:loghost AND chef_environment:prod
remote_logs: false
per_host_dir: %$YEAR%/%$MONTH%/%$DAY%/%HOSTNAME%
max_message_size: 64k
preserve_fqdn: off
high_precision_timestamps: false
repeated_msg_reduction: on
logs_to_forward: *.*
enable_imklog: true
config_prefix: /etc
default_file_template: RSYSLOG_SyslogProtocol23Format
default_remote_template: RSYSLOG_SyslogProtocol23Format
rate_limit_interval: 
rate_limit_burst: 
enable_tls: false
action_queue_max_disk_space: 1G
tls_ca_file: 
tls_certificate_file: 
tls_key_file: 
tls_auth_mode: anon
tls_permitted_peer: 
use_local_ipv4: false
allow_non_local: false
custom_remote: 
additional_directives: 
templates: 
package_name: rsyslog
service_name: rsyslog
user: syslog
group: adm
priv_seperation: true
priv_user: 
priv_group: syslog
modules
0: imuxsock
1: imklog
file_create_mode: 0644
dir_create_mode: 0755
umask: 0022
dir_owner: root
dir_group: adm
config_files
owner: root
group: root
mode: 0644
default_facility_logs
auth,authpriv.*: /var/log/auth.log
*.*;auth,authpriv.none: -/var/log/syslog
daemon.*: -/var/log/daemon.log
kern.*: -/var/log/kern.log
mail.*: -/var/log/mail.log
user.*: -/var/log/user.log
mail.info: -/var/log/mail.info
mail.warn: -/var/log/mail.warn
mail.err: /var/log/mail.err
news.crit: /var/log/news/news.crit
news.err: /var/log/news/news.err
news.notice: -/var/log/news/news.notice
*.=debug;auth,authpriv.none;news.none;mail.none: -/var/log/debug
*.=info;*.=notice;*.=warn;auth,authpriv.none;cron,daemon.none;mail,news.none: -/var/log/messages
*.emerg: :omusrmsg:*
logspam_filters
0
phrase: **WARNING** packet truncated
app: ntop
et_logger
logstash_search_str: recipes:logserver\:\:default AND chef_environment:prod
networking
packages
0: lsof
1: iptables
2: jwhois
3: whois
4: curl
5: wget
6: rsync
7: jnettop
8: nmap
9: traceroute
10: ethtool
11: iproute
12: iputils-ping
13: netcat-openbsd
14: tcptraceroute
15: tcputils
16: tcpdump
17: elinks
18: lynx
19: ngrep
resolvconf
options
0: timeout:1
1: attempts:1
2: rotate
tools
packages
0: iotop
1: iptraf
2: mc
3: strace
4: tree
5: dnsutils
6: git-core
7: fortune-mod
8: fortunes-off
9: htop
10: hping3
11: iftop
12: links
13: locate
14: mailutils
15: nload
16: ntop
17: sysstat
18: zip
git
prefix: /usr/local
version: 2.8.1
url: https://nodeload.github.com/git/git/tar.gz/v%{version}
checksum: e08503ecaf5d3ac10c40f22871c996a392256c8d038d16f52ebf974cba29ae42
use_pcre: false
server
base_path: /srv/git
export_all: true
motd
color: true
modules
default
modules
0: lp
1: rtc
packages
0: module-init-tools
network_interfaces
replace_orig: true
order
0: eth0
omnibus_updater
version: 12.17.44
force_latest: false
cache_dir: /var/chef/cache/omnibus_updater
cache_omnibus_installer: false
prerelease: false
disabled: false
kill_chef_on_upgrade: true
kill_chef_on_upgrade_exit_code: 213
always_download: false
prevent_downgrade: true
restart_chef_service: true
checksum: 
addlocal: ChefClientFeature,ChefServiceFeature
full_url: https://packages.chef.io/files/stable/chef/12.17.44/ubuntu/12.04/chef_12.17.44-1_amd64.deb
direct_url: https://packages.chef.io/files/stable/chef/12.17.44/ubuntu/12.04/chef_12.17.44-1_amd64.deb
postfix
mail_type: client
relayhost_role: relayhost
multi_environment_relay: false
use_procmail: false
use_alias_maps: true
use_transport_maps: false
use_access_maps: false
use_virtual_aliases: false
use_virtual_aliases_domains: false
use_relay_restrictions_maps: false
transports: 
access: 
virtual_aliases: 
virtual_aliases_domains: 
main_template_source: postfix
master_template_source: postfix
sender_canonical_map_entries: 
smtp_generic_map_entries: 
access_db_type: hash
aliases_db_type: hash
transport_db_type: hash
virtual_alias_db_type: hash
virtual_alias_domains_db_type: hash
conf_dir: /etc/postfix
aliases_db: /etc/aliases
transport_db: /etc/postfix/transport
access_db: /etc/postfix/access
virtual_alias_db: /etc/postfix/virtual
virtual_alias_domains_db: /etc/postfix/virtual_domains
relay_restrictions_db: /etc/postfix/relay_restrictions
main
biff: no
append_dot_mydomain: no
myhostname: prod-archiva-1c.priv.evertrue.com
mydomain: priv.evertrue.com
myorigin: $myhostname
mydestination
0: prod-archiva-1c.priv.evertrue.com
1: prod-archiva-1c
2: localhost.localdomain
3: localhost
smtpd_use_tls: yes
smtp_use_tls: yes
smtp_sasl_auth_enable: no
mailbox_size_limit: 0
mynetworks: 
inet_interfaces: loopback-only
smtpd_tls_cert_file: /etc/ssl/certs/ssl-cert-snakeoil.pem
smtpd_tls_key_file: /etc/ssl/private/ssl-cert-snakeoil.key
smtpd_tls_CAfile: /etc/ssl/certs/ca-certificates.crt
smtpd_tls_session_cache_database: btree:${data_directory}/smtpd_scache
smtp_tls_CAfile: /etc/ssl/certs/ca-certificates.crt
smtp_tls_session_cache_database: btree:${data_directory}/smtp_scache
alias_maps
0: hash:/etc/aliases
cafile: /etc/ssl/certs/ca-certificates.crt
master
submission: false
aliases
root: <EMAIL>
reboot_coordinator
reboot_permitted: false
acceptable_reboot_times: 12..16
reboot_delay: 5
reboot_interval: 300
zk_hosts
0: localhost:2181
pre_reboot_commands
suspend_monitoring: /usr/local/bin/suspend-monitoring 300
pre_reboot_resources: 
convergences_since_creation: 64421
ssh_known_hosts
file: /etc/ssh/ssh_known_hosts
key_type: rsa,dsa
cacher
data_bag: server_data
data_bag_item: known_hosts
use_data_bag_cache: true
authorization
sudo
groups
0: sysadmin
users
0: ec2-user
1: vagrant
passwordless: true
setenv: false
include_sudoers_d: true
sudoers_d_mode: 0755
agent_forwarding: false
sudoers_defaults
0: !lecture,tty_tickets,!fqdn
command_aliases: 
env_keep_add: 
env_keep_subtract: 
custom_commands
users: 
groups: 
unattended-upgrades
admin_email: root@localhost
package_blacklist: 
autofix_dpkg: true
minimal_steps: false
install_on_shutdown: false
mail_only_on_error: true
remove_unused_dependencies: true
automatic_reboot: false
download_limit: 
allowed_origins
security: true
updates: false
proposed: false
backports: false
apt_recipe: default
update_package_lists_interval: 1
upgrade_interval: 1
download_upgradeable_interval: 
autoclean_interval: 
vim
extra_packages: 
install_method: package
source
version: 7.4
checksum: d0f5a6d2c439f02d97fa21bd9121f4c5abb1f6cd8b5a79d3ca82867495734ade
prefix: /usr/local
configuration: --without-x --enable-pythoninterp --enable-rubyinterp --enable-tclinterp --enable-luainterp --enable-perlinterp --enable-cscope --with-features=huge --prefix=/usr/local
dependencies
0: exuberant-ctags
1: gcc
2: libncurses5-dev
3: libperl-dev
4: lua5.1
5: make
6: python-dev
7: ruby-dev
8: tcl-dev
base
users_recipes
0: sysadmins
1: evertrue
ntp_cluster
public_servers
0: 0.amazon.pool.ntp.org iburst
1: 1.amazon.pool.ntp.org iburst
2: 2.amazon.pool.ntp.org iburst
3: 3.amazon.pool.ntp.org iburst
server_role: ntp_server
master: prod-zookeeper-1b.priv.evertrue.com
standbys
0: prod-zookeeper-1c.priv.evertrue.com
1: prod-zookeeper-1d.priv.evertrue.com
monitor
enabled: true
begin: curl -s https://cronitor.link/uSRE/run -m 10
success: curl -s https://cronitor.link/uSRE/complete -m 10
fail: curl -s https://cronitor.link/uSRE/fail -m 10
et_base
rc_local: 
firewall
allow_ssh: false
allow_winrm: false
allow_mosh: false
allow_loopback: false
allow_icmp: false
firewalld
permanent: false
iptables
defaults
policy
input: DROP
forward: DROP
output: ACCEPT
ruleset
*filter: 1
:INPUT DROP: 2
:FORWARD DROP: 3
:OUTPUT ACCEPT: 4
COMMIT_FILTER: 100
ubuntu_iptables: false
redhat7_iptables: false
allow_established: true
ipv6_enabled: true
ufw
defaults
ipv6: yes
manage_builtins: no
ipt_sysctl: /etc/ufw/sysctl.conf
ipt_modules: nf_conntrack_ftp nf_nat_ftp nf_conntrack_netbios_ns
policy
input: DROP
output: ACCEPT
forward: DROP
application: SKIP
windows
defaults
policy
input: blockinbound
output: allowoutbound
allow_consul: false
go
version: 1.5
platform: amd64
filename: go1.5.linux-amd64.tar.gz
from_source: false
url: http://golang.org/dl/go1.5.linux-amd64.tar.gz
install_dir: /usr/local
gopath: /opt/go
gobin: /opt/go/bin
scm: true
packages: 
owner: root
group: root
mode: 493
nssm
src: https://nssm.cc/ci/nssm-2.24-94-g9c88bc1.zip
sha256: 0bbe25025b69ebd8ab263ec4b443513d28a0d072e5fdd9b5cdb327359a27f96e
install_location: %WINDIR%
install_nssm: true
poise-archive
seven_zip
version: 16.04
url: http://www.7-zip.org/a/7z%{version_tag}%{arch_tag}.exe
poise-service
provider: auto
options: 
consul
service_name: consul
service_user: consul
service_group: consul
create_service_user: true
config
owner: consul
group: consul
path: /etc/consul/consul.json
data_dir: /var/lib/consul
ca_file: /etc/consul/ssl/CA/ca.crt
cert_file: /etc/consul/ssl/certs/consul.crt
key_file: /etc/consul/ssl/private/consul.key
client_addr: 0.0.0.0
ports
dns: 8600
http: 8500
rpc: 8400
serf_lan: 8301
serf_wan: 8302
server: 8300
datacenter: prod-us-east-1
retry_join
0: prod-consul-1b-1.priv.evertrue.com
1: prod-consul-1d-1.priv.evertrue.com
2: prod-consul-1c-1.priv.evertrue.com
enable_syslog: true
advertise_addr: ************
server: false
diplomat_version: 
service
config_dir: /etc/consul/conf.d
nssm_params
AppDirectory: /var/lib/consul
AppStdout: /etc/consul/stdout.log
AppStderr: /etc/consul/error.log
AppRotateFiles: 1
AppRotateOnline: 1
AppRotateBytes: 20000000
version: 0.7.1
et_consul
client
definitions: 
mocking: false
backup_work_dir: /mnt
datadog
api_key: 
application_key: 
agent6: false
agent6_version: 
agent6_package_action: install
agent6_aptrepo: http://apt.datadoghq.com
agent6_aptrepo_dist: beta
agent6_yumrepo: https://yum.datadoghq.com/beta/x86_64/
agent6_config_dir: /etc/datadog-agent
use_v2_api: 
extra_endpoints
prod
enabled: 
api_key: 
application_key: 
url: 
tag_prefix: tag:
url: https://app.datadoghq.com
tags: 
create_dd_check_tags: 
collect_ec2_tags: 
tags_blacklist_regex: 
send_policy_tags: false
autorestart: false
developer_mode: false
installrepo: true
aptrepo: http://apt.datadoghq.com
aptrepo_dist: stable
yumrepo: https://yum.datadoghq.com/rpm/x86_64/
yumrepo_gpgkey: https://yum.datadoghq.com/DATADOG_RPM_KEY.public
yumrepo_proxy: 
yumrepo_proxy_username: 
yumrepo_proxy_password: 
windows_agent_url: https://s3.amazonaws.com/ddagent-windows-stable/
yumrepo_gpgkey_new: https://yum.datadoghq.com/DATADOG_RPM_KEY_E09422B3.public
windows_agent_checksum: 
windows_agent_use_exe: false
config_dir: /etc/dd-agent
agent_name: datadog-agent
install_base: false
agent_version: 1:5.10.1-1
agent_package_action: install
agent_package_retries: 
agent_package_retry_delay: 
agent_allow_downgrade: false
chef_handler_version: 
chef_handler_enable: true
log_level: INFO
non_local_traffic: false
bind_host: localhost
check_freq: 15
hostname: prod-archiva-1c
use_ec2_instance_id: false
use_mount: false
agent_port: 17123
agent_enable: true
agent_start: true
graphite: false
graphite_port: 17124
dogstreams: 
custom_emitters: 
syslog
active: false
udp: false
host: 
port: 
log_file_directory: /var/log/datadog
web_proxy
host: 
port: 
user: 
password: 
skip_ssl_validation: 
dogstatsd: true
dogstatsd_port: 8125
dogstatsd_interval: 10
dogstatsd_normalize: yes
dogstatsd_target: http://localhost:17123
statsd_forward_host: 
statsd_forward_port: 8125
statsd_metric_namespace: 
histogram_aggregates: max, median, avg, count
histogram_percentiles: 0.95
extra_config
forwarder_timeout: 
extra_packages: 
legacy_integrations
nagios
enabled: false
description: Nagios integration
config
nagios_log: /var/log/nagios3/nagios.log
sd_backend_host: 127.0.0.1
sd_backend_port: 4001
sd_config_backend: etcd
sd_template_dir: /datadog/check_configs
service_discovery_backend: 
enable_trace_agent: 
trace_env: 
extra_sample_rate: 
max_traces_per_second: 
receiver_port: 
connection_limit: 
ddtrace_python_version: 
ddtrace_gem_version: 
enable_process_agent: 
process_agent
blacklist: 
container_blacklist: 
container_whitelist: 
log_file: 
process_interval: 
rtprocess_interval: 
container_interval: 
rtcontainer_interval: 
enable_logs_agent: 
gem_server: false
cassandra
version: 1
go-metro
libcap_package
package_name: libcap
init_config
snaplen: 512
idle_ttl: 300
exp_ttl: 60
statsd_ip: 127.0.0.1
statsd_port: 8125
log_to_file: true
log_level: info
kafka
version: 1
network
instances
0
collect_connection_state: false
excluded_interfaces
0: lo
1: lo0
et_fog
version: 1.37.0
dry_inflector_version: 0.1.2
dependencies
0: libxslt-dev
1: libxml2-dev
2: libghc-zlib-dev
curl
libcurl_packages
0: libcurl3
1: libcurl4-openssl-dev
poise-python
provider: auto
options: 
install_python2: true
install_python3: false
install_pypy: false
newrelic
license: 6757eb84682982ea6b4041026232883d1e610fe3
server_monitoring
license: 6757eb84682982ea6b4041026232883d1e610fe3
proxy: 
logfile: 
loglevel: 
ssl: 
ssl_ca_bundle: 
ssl_ca_path: 
hostname: 
pidfile: 
collector_host: 
timeout: 
other_options: 
application_monitoring
license: 
daemon
proxy: 
logfile: /var/log/newrelic/newrelic-daemon.log
loglevel: 
port: 
max_threads: 
ssl: 
ssl_ca_path: 
ssl_ca_bundle: 
pidfile: 
location: 
collector_host: 
dont_launch: 
enabled: 
logfile: 
logfile_path: 
loglevel: 
app_name: 
high_security: 
capture_params: 
cross_application_tracer
enable: 
thread_profiler
enable: 
labels: 
ignored_params: 
error_collector
enable: 
ignore_errors: 
ignore_status_codes: 
record_database_errors: 
prioritize_api_errors: 
browser_monitoring
auto_instrument: 
transaction_tracer
enable: 
threshold: 
detail: 
slow_sql: 
stack_trace_threshold: 
explain_threshold: 
record_sql: 
custom: 
framework: 
webtransaction
name
remove_trailing_path: 
functions: 
files: 
api_key: 
proxy: 
dotnet_agent
https_download: https://download.newrelic.com/dot_net_agent/release/x64
install_level: 1
agent_action: install
java_agent
version: latest
install_dir: /opt/newrelic/java
app_user: newrelic
app_group: newrelic
audit_mode: false
log_file_count: 1
log_limit_in_kbytes: 0
log_daily: true
agent_action: install
execute_agent_action: true
enable_custom_tracing: false
class_transformer_config: 
app_location: /opt/newrelic/java
template
cookbook: newrelic
source: agent/newrelic.yml.erb
nodejs_agent
agent_action: install
apps: 
template
cookbook: newrelic
source: agent/nodejs/newrelic.js.erb
default_app_log_level: info
php_agent
agent_action: 
install_silently: 
startup_mode: 
web_server
service_name: 
service_action: 
config_file: 
config_file_to_be_deleted: 
execute_php5enmod: 
enable_module: 
template
cookbook_ini: 
source_ini: 
cookbook: 
source: 
additional_config: 
python_agent
agent_action: install
python_version: 
python_venv: 
config_file: /etc/newrelic/newrelic.ini
template
cookbook: newrelic
source: agent/python/newrelic.ini.erb
feature_flag: 
repository
key: http://download.newrelic.com/548C16BF.gpg
ssl_verify: true
uri: http://download.newrelic.com/debian/
distribution: newrelic
components
0: non-free
infrastructure
key: https://download.newrelic.com/infrastructure_agent/gpg/newrelic-infra.gpg
ssl_verify: true
uri: https://download.newrelic.com/infrastructure_agent/linux/apt
components
0: main
ruby_agent
agent_action: install
version: latest
install_dir: 
app_user: newrelic
app_group: newrelic
audit_mode: false
log_file_count: 1
log_limit_in_kbytes: 0
log_daily: true
template
cookbook: newrelic
source: agent/newrelic.yml.erb
server_monitor_agent
agent_action: 
service_notify_action: 
service_actions: 
config_file_user: 
windows_version: 
windows64_checksum: 
windows32_checksum: 
template
cookbook: 
source: 
snmp
packages
0: snmp
1: snmpd
service: snmpd
community: 9FbkrzfKf7PgAJXqWbJgSvN7zTBm8TNC
sources
0: default
sources6
0: default
syslocationVirtual: Virtual Server
syslocationPhysical: Server Room
syscontact: Root <root@localhost>
full_systemview: true
trapcommunity: public
trapsinks: 
process_monitoring
proc: 
procfix: 
disman_events
enable: false
user: disman_events
password: disman_password
linkUpDownNotifications: yes
defaultMonitors: yes
monitors: 
include_all_disks: true
all_disk_min: 100
disks: 
load_average: 
extend_scripts: 
snmpd
mibdirs: /usr/share/snmp/mibs
mibs: 
snmpd_run: yes
snmpd_opts: -Lsd -Lf /dev/null -u snmp -g snmp -I -smux -p /var/run/snmpd.pid
trapd_run: no
trapd_opts: -Lsd -p /var/run/snmptrapd.pid
snmpd_compat: yes
rndc_stats_script: snmp_rndc_stats_v97.pl
snmptrapd
service: snmpd
traphandle: default /usr/sbin/snmptthandler
disableAuthorization: yes
donotlogtraps: yes
default_community: true
threatstack
version: 
pkg_action: install
rulesets: 
hostname: 
ignore_failure: true
deploy_key: 
data_bag_name: threatstack
data_bag_item: api_keys
feature_plan: 
configure_agent: true
agent_extra_args: 
agent_config_args: 
cloudsight_service_action
0: enable
1: start
cloudsight_service_timer: immediately
repo_enable: true
repo
dist: precise
url: https://pkg.threatstack.com/Ubuntu
key: https://app.threatstack.com/APT-GPG-KEY-THREATSTACK
et_monitoring
mock: false
threatstack_enabled: false
datadog: 
cloudwatch_alarms
Status Check Failed (any)
alarm_actions
0: OpsPagerDuty
1: arn:aws:swf:us-east-1:037590317780:action/actions/AWS_EC2.InstanceId.Reboot/1.0
ok_actions
0: OpsPagerDuty
statistic: Maximum
threshold: 1
period: 60
comparison_operator: GreaterThanOrEqualToThreshold
metric_name: StatusCheckFailed
ntp
servers
0: ************
1: ************
2: ************
peers: 
pools: 
restrictions: 
tinker
panic: 0
allan: 1500
dispersion: 15
step: 0.128
stepout: 900
restrict_default: kod notrap nomodify nopeer noquery
packages
0: ntp
service: ntp
varlibdir: /var/lib/ntp
driftfile: /var/lib/ntp/ntp.drift
logfile: 
conffile: /etc/ntp.conf
statsdir: /var/log/ntpstats/
conf_owner: root
conf_group: root
var_owner: ntp
var_group: ntp
leapfile: /etc/ntp.leapseconds
sync_clock: false
sync_hw_clock: false
listen: 
listen_network: 
ignore: 
apparmor_enabled: true
monitor: false
statistics: true
conf_restart_immediate: true
keys: 
trustedkey: 
requestkey: 
disable_tinker_panic_on_virtualization_guest: true
peer
key: 
use_iburst: true
use_burst: false
minpoll: 6
maxpoll: 10
server
prefer: ************
use_iburst: true
use_burst: false
minpoll: 6
maxpoll: 10
orphan
enabled: false
stratum: 5
localhost
noquery: false
use_cmos: false
et_ntp_client
verify
retries: 24
retry_delay: 5
ark
apache_mirror: http://apache.mirrors.tds.net
prefix_root: /usr/local
prefix_bin: /usr/local/bin
prefix_home: /usr/local
tar: 
sevenzip_binary: 
package_dependencies
0: libtool
1: autoconf
2: make
3: unzip
4: rsync
5: gcc
6: autogen
7: shtool
8: pkg-config
zypper
smt_host: 
nginx
version: 1.1.19
package_name: nginx
port: 80
dir: /etc/nginx
script_dir: /usr/sbin
log_dir: /var/log/nginx
log_dir_perm: 0750
binary: /usr/sbin/nginx
default_root: /var/www/nginx-default
ulimit: 1024
repo_source: nginx
install_method: package
user: www-data
upstart
runlevels: 2345
respawn_limit: 
foreground: true
group: www-data
gzip: on
gzip_static: off
gzip_http_version: 1.0
gzip_comp_level: 2
gzip_proxied: any
gzip_vary: off
gzip_buffers: 
gzip_types
0: text/plain
1: text/css
2: application/x-javascript
3: text/xml
4: application/xml
5: application/rss+xml
6: application/atom+xml
7: text/javascript
8: application/javascript
9: application/json
10: text/mathml
gzip_min_length: 1000
gzip_disable: MSIE [1-6]\.
keepalive: on
keepalive_requests: 100
keepalive_timeout: 65
worker_processes: 2
worker_connections: 1024
worker_rlimit_nofile: 
multi_accept: false
event: 
accept_mutex_delay: 
server_tokens: 
server_names_hash_bucket_size: 64
variables_hash_max_size: 1024
variables_hash_bucket_size: 64
sendfile: on
underscores_in_headers: 
tcp_nodelay: on
tcp_nopush: on
access_log_options: 
error_log_options: 
disable_access_log: false
log_formats: 
default_site_enabled: false
types_hash_max_size: 2048
types_hash_bucket_size: 64
proxy_read_timeout: 
client_body_buffer_size: 
client_max_body_size: 200M
large_client_header_buffers: 
default
modules: 
extra_configs: 
load_modules: 
auth_request
url: http://mdounin.ru/hg/ngx_http_auth_request_module/archive/662785733552.tar.gz
checksum: 2057bdefd2137a5000d9dbdbfca049d1ba7832ad2b9f8855a88ea5dfa70bd8c1
devel
version: 0.3.0
url: https://github.com/simpl/ngx_devel_kit/archive/v0.3.0.tar.gz
checksum: 88e05a99a8a7419066f5ae75966fb1efc409bad4522d14986da074554ae61619
echo
version: 0.59
url: https://github.com/openresty/echo-nginx-module/archive/v0.59.tar.gz
checksum: 9b319ad7836202883128d2b9c24ed818082541df57ef7f2065b7557085c603cd
geoip
path: /srv/geoip
enable_city: true
country_dat_url: http://geolite.maxmind.com/download/geoip/database/GeoLiteCountry/GeoIP.dat.gz
country_dat_checksum: 
city_dat_url: http://geolite.maxmind.com/download/geoip/database/GeoLiteCity.dat.gz
city_dat_checksum: 
lib_version: 1.6.9
lib_url: https://github.com/maxmind/geoip-api-c/releases/download/v1.6.9/GeoIP-1.6.9.tar.gz
lib_checksum: 4b446491843de67c1af9b887da17a3e5939e0aeed4826923a5f4bf09d845096f
headers_more
version: 0.30
source_url: https://github.com/openresty/headers-more-nginx-module/archive/v0.30.tar.gz
source_checksum: 2aad309a9313c21c7c06ee4e71a39c99d4d829e31c8b3e7d76f8c964ea8047f5
lua
version: 0.10.3
url: https://github.com/chaoslawful/lua-nginx-module/archive/v0.10.3.tar.gz
checksum: a69504c25de67bce968242d331d2e433c021405a6dba7bca0306e6e0b040bb50
luajit
version: 2.0.4
url: http://luajit.org/download/LuaJIT-2.0.4.tar.gz
checksum: 620fa4eb12375021bef6e4f237cbd2dd5d49e56beb414bee052c746beef1807d
naxsi
version: 0.54
url: https://github.com/nbs-system/naxsi/archive/0.54.tar.gz
checksum: 9cc2c09405bc71f78ef26a8b6d70afcea3fccbe8125df70cb0cfc480133daba5
openssl_source
version: 1.0.2j
url: http://www.openssl.org/source/openssl-1.0.2j.tar.gz
pagespeed
version: *********
url: https://github.com/pagespeed/ngx_pagespeed/archive/release-*********-beta.tar.gz
packages
rhel
0: pcre-devel
1: zlib-devel
debian
0: zlib1g-dev
1: libpcre3
2: libpcre3-dev
psol
url: https://dl.google.com/dl/page-speed/psol/*********.tar.gz
passenger
version: 4.0.57
root: /var/lib/gems/2.2.0/gems/passenger-4.0.57
ruby: /usr/bin/ruby2.2
packages
rhel
0: ruby-devel
1: curl-devel
fedora
0: ruby-devel
1: libcurl-devel
debian
0: ruby-dev
1: libcurl4-gnutls-dev
install_rake: true
spawn_method: smart-lv2
buffer_response: on
max_pool_size: 6
min_instances: 1
max_instances_per_app: 0
pool_idle_time: 300
max_requests: 0
gem_binary: 
show_version_in_header: on
passenger_log_file: 
nodejs: 
enable_rate_limiting: false
rate_limiting_zone_name: default
rate_limiting_backoff: 10m
rate_limit: 1r/s
upstream_repository: http://nginx.org/packages/ubuntu
set_misc
version: 0.30
url: https://github.com/agentzh/set-misc-nginx-module/archive/v0.30.tar.gz
checksum: 59920dd3f92c2be32627121605751b52eae32b5884be09f2e4c53fb2fae8aabc
socketproxy
root: /usr/share/nginx/apps
app_owner: root
logname: socketproxy
log_level: error
init_style: upstart
source
version: 1.10.1
prefix: /opt/nginx-1.10.1
conf_path: /etc/nginx/nginx.conf
sbin_path: /opt/nginx-1.10.1/sbin/nginx
default_configure_flags
0: --prefix=/opt/nginx-1.10.1
1: --conf-path=/etc/nginx/nginx.conf
2: --sbin-path=/opt/nginx-1.10.1/sbin/nginx
url: http://nginx.org/download/nginx-1.10.1.tar.gz
checksum: 1fd35846566485e03c0e318989561c135c598323ff349c503a6c14826487a801
modules
0: chef_nginx::http_ssl_module
1: chef_nginx::http_gzip_static_module
use_existing_user: false
configure_flags: 
status
port: 8090
syslog
git_repo: https://github.com/yaoweibin/nginx_syslog_patch.git
git_revision: master
upload_progress
url: https://github.com/masterzen/nginx-upload-progress-module/tarball/v0.9.0
checksum: 3fb903dab595cf6656fa0fc5743a48daffbba2f6b5c554836be630800eaad4e2
javascript_output: true
zone_name: proxied
zone_size: 1m
configure_arguments
0: --prefix=/etc/nginx
1: --conf-path=/etc/nginx/nginx.conf
2: --error-log-path=/var/log/nginx/error.log
3: --http-client-body-temp-path=/var/lib/nginx/body
4: --http-fastcgi-temp-path=/var/lib/nginx/fastcgi
5: --http-log-path=/var/log/nginx/access.log
6: --http-proxy-temp-path=/var/lib/nginx/proxy
7: --http-scgi-temp-path=/var/lib/nginx/scgi
8: --http-uwsgi-temp-path=/var/lib/nginx/uwsgi
9: --lock-path=/var/lock/nginx.lock
10: --pid-path=/var/run/nginx.pid
11: --with-debug
12: --with-http_addition_module
13: --with-http_dav_module
14: --with-http_geoip_module
15: --with-http_gzip_static_module
16: --with-http_image_filter_module
17: --with-http_realip_module
18: --with-http_stub_status_module
19: --with-http_ssl_module
20: --with-http_sub_module
21: --with-http_xslt_module
22: --with-ipv6
23: --with-sha1=/usr/include/openssl
24: --with-md5=/usr/include/openssl
25: --with-mail
26: --with-mail_ssl_module
27: --add-module=/build/buildd/nginx-1.1.19/debian/modules/nginx-auth-pam
28: --add-module=/build/buildd/nginx-1.1.19/debian/modules/nginx-echo
29: --add-module=/build/buildd/nginx-1.1.19/debian/modules/nginx-upstream-fair
30: --add-module=/build/buildd/nginx-1.1.19/debian/modules/nginx-dav-ext-module
prefix: /etc/nginx
conf_path: /etc/nginx/nginx.conf
homebrew
owner: 
auto-update: true
casks: 
formulas: 
taps: 
installer
url: https://raw.githubusercontent.com/Homebrew/install/master/install
checksum: 
enable-analytics: true
java
jdk_version: 7
arch: x86_64
openjdk_packages
0: openjdk-7-jdk
1: openjdk-7-jre-headless
openjdk_version: 
accept_license_agreement: false
set_default: true
alternatives_priority: 1062
set_etc_environment: false
use_alt_suffix: true
reset_alternatives: true
ark_retries: 0
ark_retry_delay: 2
ark_timeout: 600
ark_download_timeout: 600
install_flavor: openjdk
oracle
accept_oracle_download_terms: false
jce
6
url: https://edelivery.oracle.com/otn-pub/java/jce_policy/6/jce_policy-6.zip
checksum: d0c2258c3364120b4dbf7dd1655c967eee7057ac6ae6334b5ea8ceb8bafb9262
7
url: https://edelivery.oracle.com/otn-pub/java/jce/7/UnlimitedJCEPolicyJDK7.zip
checksum: 7a8d790e7bd9c2f82a83baddfae765797a4a56ea603c9150c87b7cdb7800194d
8
url: https://edelivery.oracle.com/otn-pub/java/jce/8/jce_policy-8.zip
checksum: f3020a3922efd6626c2fff45695d527f34a8020e938a49292561f18ad1320b59
enabled: false
home: /opt/java_jce
jdk
6
bin_cmds
0: appletviewer
1: apt
2: ControlPanel
3: extcheck
4: HtmlConverter
5: idlj
6: jar
7: jarsigner
8: java
9: javac
10: javadoc
11: javah
12: javap
13: javaws
14: jconsole
15: jcontrol
16: jdb
17: jhat
18: jinfo
19: jmap
20: jps
21: jrunscript
22: jsadebugd
23: jstack
24: jstat
25: jstatd
26: jvisualvm
27: keytool
28: native2ascii
29: orbd
30: pack200
31: policytool
32: rmic
33: rmid
34: rmiregistry
35: schemagen
36: serialver
37: servertool
38: tnameserv
39: unpack200
40: wsgen
41: wsimport
42: xjc
x86_64
url: http://download.oracle.com/otn/java/jdk/6u45-b06/jdk-6u45-linux-x64.bin
checksum: 6b493aeab16c940cae9e3d07ad2a5c5684fb49cf06c5d44c400c7993db0d12e8
i586
url: http://download.oracle.com/otn/java/jdk/6u45-b06/jdk-6u45-linux-i586.bin
checksum: d53b5a2518d80e1d95565f0adda54eee229dc5f4a1d1a3c2f7bf5045b168a357
7
bin_cmds
0: appletviewer
1: apt
2: ControlPanel
3: extcheck
4: idlj
5: jar
6: jarsigner
7: java
8: javac
9: javadoc
10: javafxpackager
11: javah
12: javap
13: javaws
14: jcmd
15: jconsole
16: jcontrol
17: jdb
18: jhat
19: jinfo
20: jmap
21: jps
22: jrunscript
23: jsadebugd
24: jstack
25: jstat
26: jstatd
27: jvisualvm
28: keytool
29: native2ascii
30: orbd
31: pack200
32: policytool
33: rmic
34: rmid
35: rmiregistry
36: schemagen
37: serialver
38: servertool
39: tnameserv
40: unpack200
41: wsgen
42: wsimport
43: xjc
x86_64
url: http://download.oracle.com/otn/java/jdk/7u75-b13/jdk-7u75-linux-x64.tar.gz
checksum: 6f1f81030a34f7a9c987f8b68a24d139
i586
url: http://download.oracle.com/otn/java/jdk/7u75-b13/jdk-7u75-linux-i586.tar.gz
checksum: e4371a4fddc049eca3bfef293d812b8e
8
bin_cmds
0: appletviewer
1: apt
2: ControlPanel
3: extcheck
4: idlj
5: jar
6: jarsigner
7: java
8: javac
9: javadoc
10: javafxpackager
11: javah
12: javap
13: javaws
14: jcmd
15: jconsole
16: jcontrol
17: jdb
18: jdeps
19: jhat
20: jinfo
21: jjs
22: jmap
23: jmc
24: jps
25: jrunscript
26: jsadebugd
27: jstack
28: jstat
29: jstatd
30: jvisualvm
31: keytool
32: native2ascii
33: orbd
34: pack200
35: policytool
36: rmic
37: rmid
38: rmiregistry
39: schemagen
40: serialver
41: servertool
42: tnameserv
43: unpack200
44: wsgen
45: wsimport
46: xjc
x86_64
url: https://edelivery.oracle.com/otn-pub/java/jdk/8u131-b11/d54c1d3a095b4ff2b6607d096fa80163/jdk-8u131-linux-x64.tar.gz
checksum: 62b215bdfb48bace523723cdbb2157c665e6a25429c73828a32f00e587301236
i586
url: https://edelivery.oracle.com/otn-pub/java/jdk/8u131-b11/d54c1d3a095b4ff2b6607d096fa80163/jdk-8u131-linux-i586.tar.gz
checksum: 2012d1c82f74bf830a80dfb5462f555b22271f74e4fc4a5779c7f459dcd0cabf
java_home: /usr/lib/jvm/java-7-openjdk-amd64
java_exec: /usr/lib/jvm/java-7-openjdk-amd64/bin/java
archiva
mirror: http://archive.apache.org/dist/archiva/
version: 2.1.1
checksum: e1c3245b73f8f6aceae05527331cd1902dd5edb647056e2f83269fc0e2e8ace7
install_path: /mnt/dev0
home: /mnt/dev0/archiva
user_owner: archiva
web_host: 127.0.0.1
web_port: 8080
web_domain: archiva.evertrue.com
web_template: default
nginx: default
nginx_uri: /
nginx_port: 80
aws
aws_sdk_version: ~> 2.2
databag_name: 
databag_entry: 
mfa_code: 
storage
aws_api_user: Storage
ephemeral_mounts
0: /mnt/dev0
filesystem
by_mountpoint: 
by_device: 
by_pair: 
/dev/xvda1
kb_size: 10321208
kb_used: 3888520
kb_available: 5908412
percent_used: 40%
mount: /
total_inodes: 655360
inodes_used: 174362
inodes_available: 480998
inodes_percent_used: 27%
fs_type: ext4
mount_options
0: rw
uuid: 92c362de-e9aa-4406-ba27-2e48e7408872
label: cloudimg-rootfs
udev
kb_size: 789624
kb_used: 8
kb_available: 789616
percent_used: 1%
mount: /dev
total_inodes: 197406
inodes_used: 385
inodes_available: 197021
inodes_percent_used: 1%
fs_type: devtmpfs
mount_options
0: rw
1: mode=0755
tmpfs
kb_size: 159576
kb_used: 192
kb_available: 159384
percent_used: 1%
mount: /run
total_inodes: 199467
inodes_used: 272
inodes_available: 199195
inodes_percent_used: 1%
fs_type: tmpfs
mount_options
0: rw
1: noexec
2: nosuid
3: size=10%
4: mode=0755
none
kb_size: 797868
kb_used: 0
kb_available: 797868
percent_used: 0%
mount: /run/shm
total_inodes: 199467
inodes_used: 1
inodes_available: 199466
inodes_percent_used: 1%
fs_type: tmpfs
mount_options
0: rw
1: nosuid
2: nodev
/dev/xvda2
kb_size: 350891748
kb_used: 66766572
kb_available: 266300896
percent_used: 21%
mount: /mnt/dev0
total_inodes: 22282240
inodes_used: 1303817
inodes_available: 20978423
inodes_percent_used: 6%
fs_type: ext3
mount_options
0: rw
uuid: d71ea955-0025-4361-80e3-af39de4c3cef
proc
mount: /proc
fs_type: proc
mount_options
0: rw
1: noexec
2: nosuid
3: nodev
sysfs
mount: /sys
fs_type: sysfs
mount_options
0: rw
1: noexec
2: nosuid
3: nodev
devpts
mount: /dev/pts
fs_type: devpts
mount_options
0: rw
1: noexec
2: nosuid
3: gid=5
4: mode=0620
/dev/xvda3
fs_type: swap
uuid: 84169cd0-93f9-4363-a93e-3da9fe3006ee
rootfs
mount: /
fs_type: rootfs
mount_options
0: rw
/dev/disk/by-label/cloudimg-rootfs
mount: /
fs_type: ext4
mount_options
0: rw
1: relatime
2: user_xattr
3: barrier=1
4: data=ordered
sentitrace
mount: /opt/sentinelone/mount
fs_type: debugfs
mount_options
0: rw
1: nodev
2: relatime
tags: 
newrelic-ng
license_key: 6757eb84682982ea6b4041026232883d1e610fe3
elasticsearch: 
kibana
file
config_template_cookbook: elk_kibana
url: https://download.elasticsearch.org/kibana/kibana/kibana-4.1.1-linux-x64.tar.gz
version: 4.1.1-linux-x64
elk_forwarder
config
network
servers
0: prod-logs-1b-1.priv.evertrue.com:5043
1: prod-logs-1d-2.priv.evertrue.com:5043
2: prod-logs-1c-1.priv.evertrue.com:5043
ssl ca: /etc/pki/tls/certs/logstash-forwarder/ca.pem
timeout: 15
files
syslog
paths
0: /var/log/auth.log
1: /var/log/syslog
fields
type: syslog
certs
ca_data_bag: certificates
ca_data_bag_item: logstash
ca_data_bag_item_key: ca certificate
etc_environment
PATH: /usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games
RACK_ENV: deployment
CHEF_ENV: production
ZK_HOSTS: prod-zookeeper-1d.priv.evertrue.com:2181,prod-zookeeper-1b.priv.evertrue.com:2181,prod-zookeeper-1c.priv.evertrue.com:2181
VAULT_HOSTS: http://prod-zookeeper-1b.priv.evertrue.com:8200,http://prod-zookeeper-1c.priv.evertrue.com:8200,http://prod-zookeeper-1d.priv.evertrue.com:8200
LC_ALL: en_US.utf8
PASSENGER_APP_ENV: production
VAULT_ADDR: http://vault.service.prod-us-east-1.consul:8200
VAULT_TOKEN: 20352df1-f9da-7a6a-92b2-842af7558d85
NEW_RELIC_LICENSE_KEY: 6757eb84682982ea6b4041026232883d1e610fe3
AWS_REGION: us-east-1
elk_logstash
plugins
0: logstash-filter-alter
hashicorp-vault
version: 0.5.2
config
address: 0.0.0.0:8200
tls_disable: true
backend_type: consul
default_lease_ttl: 720h
max_lease_ttl: 8760h
manage_certificate: false
domain: priv.evertrue.com
chef_env_long_name: production
stale-node-checker
ignore
0: firewall.corp.evertrue.com
probymonitor
task_id: 1f363070f6f30130c7e54040217846b4
ec2dnsserver
zones
priv.evertrue.com
slaves
0: **********
static_records: 
vpcs
0: vpc-1e45b27b
1: vpc-9318d5f8
10.in-addr.arpa
ptr_zone: true
suffix: priv.evertrue.com
ns_zone: priv.evertrue.com
vpcs
0: vpc-1e45b27b
1: vpc-9318d5f8
prod-us-east-1.consul
type: forward
forward: only
forwarders: { 127.0.0.1 port 8600; }
et_api_lb
servers
api
return_404_on_root: true
server_name: api.evertrue.com api-hb.evertrue.com
forbid_http: true
static_conf
proxies
exporter-api-prod-ecs
protocol: https
uri: /exporter
upstream: ecs-prod
cors_regexp: ^http(s)?://.*\.evertrue\.com(:\d{1,5})?($|/.*)
sparkapi-prod-ecs
protocol: https
uri: /spark
upstream: ecs-prod
cors_regexp: ^http(s)?://.*\.evertrue\.com(:\d{1,5})?($|/.*)
suggestions-api-prod-ecs
protocol: https
uri: /suggestions
upstream: ecs-prod
cors_regexp: ^http(s)?://.*\.evertrue\.com(:\d{1,5})?($|/.*)
assetmanager-prod-ecs
protocol: https
uri: /assets
upstream: ecs-prod
cors_regexp: ^http(s)?://.*\.evertrue\.com(:\d{1,5})?($|/.*)
voyager-api-prod-ecs
protocol: https
uri: /voyager
upstream: ecs-prod
cors_regexp: ^http(s)?://.*\.evertrue\.com(:\d{1,5})?($|/.*)
upstreams
ecs-prod
servers
0: prod-public-1740390187.us-east-1.elb.amazonaws.com
port: 443
new-ecs-prod
servers
0: shared-infra-prod-640392329.us-east-1.elb.amazonaws.com
port: 443
baragon
port: 8882
cors_regexp: ^http(s)?://.*\.evertrue\.com(:\d{1,5})?($|/.*)
custom_response_headers
Strict-Transport-Security: max-age=31536000; includeSubDomains;
Pragma: no-cache
Cache-Control: no-store
X-Frame-Options: DENY
chefhook
server_name: chefhook.evertrue.com
force_https: true
static_conf
proxies
chefhook
uri: /
upstream: chefhook
upstreams
chefhook
check
params
type: http
timeout: 3000
http_send: OPTIONS /status HTTP/1.1\r\nHost:chefhook.evertrue.com\r\n\r\n
servers_recipe: et_chefgithook
port: 6969
ci
server_name: ci.evertrue.com
force_https: true
static_conf
proxies
ci
uri: /
upstream: ci
upstreams
ci
check
params
type: http
timeout: 3000
http_send: GET /login HTTP/1.1\r\nHost:ci.evertrue.com\r\n\r\n
servers_recipe: et_jenkins
port: 8080
console
server_name: console.evertrue.com
redirect
code: 301
destination: https://web.evertrue.com
dock
server_name: dock.evertrue.com
force_https: true
baragon
port: 8884
everface
server_name: everface.evertrue.com
force_https: true
redirect
code: 301
destination: https://api.evertrue.com/everface
baragon
port: 8885
partners
server_name: partners.evertrue.com
baragon
port: 8888
give_origin
server_name: give-origin.evertrue.com
baragon
port: 8889
pricetag-public
server_name: pricetag-origin.evertrue.com
baragon
port: 8890
et_internal_lb
simple_apps
singularity
port: 7092
recipe: et_singularity
monitor
port: 7767
recipe: et_monitor_server
servers
burrow
server_name: burrow.evertrue.com
baragon
port: 8884
capillary
server_name: capillary.evertrue.com
baragon
port: 8882
consumer
server_name: consumer.evertrue.com
baragon
port: 8885
dashboards
server_name: dashboards.evertrue.com
baragon
port: 8886
kafka-manager
server_name: kafka-manager.evertrue.com
baragon
port: 8883
harbormaster
server_name: harbormaster.evertrue.com
baragon
port: 8887
devsetup
server_name: devsetup.evertrue.com
baragon
port: 8888
et_mesos_slave
vault_search_str: (recipes:et_secrets\:\:default NOT role:old_vault)
et_zookeeper
storage
ebs_volumes
vol1
device: /dev/xvde
size: 180
delete_on_termination: true
volume_type: gp2
logserver
storage
ebs_volumes
vol1
device: /dev/xvde
size: 2867
delete_on_termination: true
volume_type: gp2
route53
aws_user: Route53
zones
0: evertrue.com
shinken
hosts
0
host_name: contacts-search
address: contacts-search.service.prod-us-east-1.consul
event_handler_enabled: 0
active_checks_enabled: 0
passive_checks_enabled: 0
flap_detection_enabled: 0
hostgroups
cassandra-nodes
search_str: recipes:et_contacts_cassandra\:\:default
conf
alias: Cassandra Nodes
contacts-elasticsearch-nodes
search_str: recipes:et_contacts_search\:\:default
conf
alias: Cassandra Nodes
dns-servers
search_str: recipes:ec2dnsserver\:\:default
conf
alias: DNS Servers
kafka-nodes
search_str: recipes:et_kafka\:\:default
conf
alias: Kafka Nodes
mesos-slaves
search_str: recipes:et_mesos_slave\:\:default
conf
alias: Mesos Slaves
mesos-masters
search_str: recipes:et_mesos\:\:master
conf
alias: Mesos Masters
spark-masters
search_str: roles:spark_master
conf
alias: Spark Masters
storm-nimbus
search_str: recipes:et_storm\:\:nimbus
conf
alias: Storm Nimbus
zookeeper-nodes
search_str: recipes:et_zookeeper\:\:default
conf
alias: Zookeeper Nodes
services
cassandra
hostgroup_name: cassandra-nodes
service_description: Cassandra Node Health Check
check_command: check_cassandra
event_handler: notify_pagerduty_for_host_service
check_interval: 1
contacts-search
host_name: contacts-search
service_description: Contacts Search Cluster Prod
check_command: check_elasticsearch
event_handler: notify_slack_for_service!#platform_alerts!http://contacts-search.service.prod-us-east-1.consul:9200/_plugin/head/
check_interval: 1
contacts-search-node
hostgroup_name: contacts-elasticsearch-nodes
service_description: Contacts Elasticsearch Node
check_command: check_http_content!9200!/!You Know, for Search
event_handler: notify_slack_for_host_service!#platform_alerts
check_interval: 1
kafka-node
hostgroup_name: kafka-nodes
service_description: Kafka Node
check_command: check_tcp!9092
event_handler: notify_slack_for_host_service!#platform_alerts
check_interval: 1
prod-dns
hostgroup_name: dns-servers
service_description: DNS Service Check
check_command: check_dig!prod-dns-1c-1.priv.evertrue.com
contact_groups: admins
mesos-slave
hostgroup_name: mesos-slaves
service_description: Mesos Slave Service
check_command: check_http_nostatus!5051
mesos-cpus
hostgroup_name: mesos-masters
service_description: Mesos consumed CPU resources above 80 percent
check_command: check_mesos_cpus!0.80
mesos-mem
hostgroup_name: mesos-masters
service_description: Mesos consumed memory resources above 80 percent
check_command: check_mesos_mem!0.80
spark-master-process
hostgroup_name: spark-masters
service_description: Spark Master Process pgrep
check_command: check_remote_process!org\.apache\.spark\.deploy\.master\.Master
event_handler: notify_slack_for_host_service!#platform_alerts
check_interval: 1
storm-contact-change-topo
hostgroup_name: storm-nimbus
service_description: Storm Contact-Change Topology
check_command: check_http_content!8080!/api/v1/topology/summary!'"name":"contact-change","status":"ACTIVE"'
storm-contact-geocode-topo
hostgroup_name: storm-nimbus
service_description: Storm Contact-Geocode Topology
check_command: check_http_content!8080!/api/v1/topology/summary!'"name":"contact-geocode","status":"ACTIVE"'
storm-user-change-topo
hostgroup_name: storm-nimbus
service_description: Storm User-Change Topology
check_command: check_http_content!8080!/api/v1/topology/summary!'"name":"user-change","status":"ACTIVE"'
zookeeper-tcp-check
hostgroup_name: zookeeper-nodes
service_description: Zookeeper TCP Check
check_command: check_tcp!2181
event_handler: notify_slack_for_host_service!#ops-dev
check_interval: 1
singularity
base_url: https://singularity.evertrue.com
et_apache_kafka
conf
server
entries
inter.broker.protocol.version: 0.8.2
log.message.format.version: 0.8.2
aws_ecs
cluster_name: prod
data_dir: /data
log_file: /log/ecs-agent.log
languages
c
gcc
version: 4.6.3
description: gcc version 4.6.3 (Ubuntu/Linaro 4.6.3-1ubuntu5)
haskell
ghc
version: 7.4.1
description: The Glorious Glasgow Haskell Compilation System, version 7.4.1
ghci
version: 7.4.1
description: The Glorious Glasgow Haskell Compilation System, version 7.4.1
python
version: 2.7.3
builddate: Oct 6 2020, 15:47:39
perl
version: 5.14.2
archname: x86_64-linux-gnu-thread-multi
java
version: 1.7.0_79
hotspot
name: OpenJDK 64-Bit Server VM
build: 24.79-b02, mixed mode
ruby
platform: x86_64-linux-gnu
version: 2.2.6
release_date: 2016-11-15
target: x86_64-pc-linux-gnu
target_cpu: x86_64
target_vendor: pc
target_os: linux-gnu
host: x86_64-pc-linux-gnu
host_cpu: x86_64
host_os: linux-gnu
host_vendor: pc
bin_dir: /usr/bin
ruby_bin: /usr/bin/ruby2.2
gems_dir: /var/lib/gems/2.2.0
gem_bin: /usr/bin/gem2.2
network
interfaces
lo
mtu: 16436
flags
0: LOOPBACK
1: UP
2: LOWER_UP
encapsulation: Loopback
addresses
127.0.0.1
family: inet
prefixlen: 8
netmask: *********
scope: Node
::1
family: inet6
prefixlen: 128
scope: Node
tags: 
state: unknown
eth0
type: eth
number: 0
mtu: 1500
flags
0: BROADCAST
1: MULTICAST
2: UP
3: LOWER_UP
encapsulation: Ethernet
addresses
0A:6C:E3:12:0B:10
family: lladdr
************
family: inet
prefixlen: 24
netmask: *************
broadcast: ************
scope: Global
fe80::86c:e3ff:fe12:b10
family: inet6
prefixlen: 64
scope: Link
tags: 
state: up
arp
************: 0a:58:a1:f1:41:35
**********74: 0a:1d:c8:21:be:7b
************: 0a:82:c4:63:e6:77
***********: 0a:a0:76:3b:74:cc
**********: 0a:5c:40:c0:00:09
**********: 0a:dd:61:58:a6:be
***********: 0a:74:68:1a:d3:79
***********: 0a:e3:e1:41:b6:13
***********: 0a:8e:60:33:ce:4f
**********55: 0a:57:6b:b7:09:13
************: 0a:cd:d3:39:49:84
**********04: 0a:a4:d2:12:f4:2b
**********71: 0a:84:98:f7:e8:50
**********20: 0a:81:5a:c0:de:23
**********72: 0a:36:bf:14:cb:41
**********8: 0a:b8:57:a0:34:7b
**********25: 0a:2e:97:a3:25:1f
**********37: 0a:98:c3:29:0f:9b
routes
0
destination: default
family: inet
via: **********
metric: 100
1
destination: **********/24
family: inet
scope: link
proto: kernel
src: ************
2
destination: fe80::/64
family: inet6
metric: 256
proto: kernel
ring_params: 
default_interface: eth0
default_gateway: **********
counters
network
interfaces
lo
rx
bytes: 234963618
packets: 1652073
errors: 0
drop: 0
overrun: 0
tx
bytes: 234963618
packets: 1652073
errors: 0
drop: 0
carrier: 0
collisions: 0
eth0
tx
queuelen: 1000
bytes: 2032775976
packets: 9973366
errors: 0
drop: 0
carrier: 0
collisions: 0
rx
bytes: 3911241545
packets: 10622808
errors: 0
drop: 0
overrun: 0
ipaddress: ************
macaddress: 0A:6C:E3:12:0B:10
memory
swap
cached: 87988kB
total: 917500kB
free: 657816kB
hugepages: 
total: 1595740kB
free: 159584kB
buffers: 92476kB
cached: 153448kB
active: 821984kB
inactive: 360980kB
dirty: 2400kB
writeback: 0kB
anon_pages: 871420kB
mapped: 58040kB
slab: 174740kB
slab_reclaimable: 163072kB
slab_unreclaim: 11668kB
page_tables: 9236kB
nfs_unstable: 0kB
bounce: 0kB
commit_limit: 1715368kB
committed_as: 1798260kB
vmalloc_total: 34359738367kB
vmalloc_used: 11272kB
vmalloc_chunk: 34359724940kB
cpu
0
vendor_id: GenuineIntel
family: 6
model: 62
model_name: Intel(R) Xeon(R) CPU E5-2651 v2 @ 1.80GHz
stepping: 4
mhz: 1800.042
cache_size: 30720 KB
physical_id: 0
core_id: 0
cores: 1
flags
0: fpu
1: de
2: tsc
3: msr
4: pae
5: cx8
6: sep
7: cmov
8: pat
9: clflush
10: mmx
11: fxsr
12: sse
13: sse2
14: ht
15: syscall
16: nx
17: lm
18: constant_tsc
19: rep_good
20: nopl
21: pni
22: pclmulqdq
23: ssse3
24: cx16
25: sse4_1
26: sse4_2
27: x2apic
28: popcnt
29: aes
30: rdrand
31: hypervisor
32: lahf_lm
33: fsgsbase
34: erms
1
vendor_id: GenuineIntel
family: 6
model: 62
model_name: Intel(R) Xeon(R) CPU E5-2651 v2 @ 1.80GHz
stepping: 4
mhz: 1800.042
cache_size: 30720 KB
physical_id: 0
core_id: 0
cores: 1
flags
0: fpu
1: de
2: tsc
3: msr
4: pae
5: cx8
6: sep
7: cmov
8: pat
9: clflush
10: mmx
11: fxsr
12: sse
13: sse2
14: ht
15: syscall
16: nx
17: lm
18: constant_tsc
19: rep_good
20: nopl
21: pni
22: pclmulqdq
23: ssse3
24: cx16
25: sse4_1
26: sse4_2
27: x2apic
28: popcnt
29: aes
30: rdrand
31: hypervisor
32: lahf_lm
33: fsgsbase
34: erms
total: 2
real: 1
cores: 1
kernel
name: Linux
release: 3.2.0-150-virtual
version: #197-Ubuntu SMP Mon Apr 5 23:03:53 UTC 2021
machine: x86_64
processor: x86_64
os: GNU/Linux
modules
isofs
size: 40259
refcount: 0
os: linux
os_version: 3.2.0-150-virtual
ip6address: fe80::86c:e3ff:fe12:b10
lsb
id: Ubuntu
description: Ubuntu 12.04.5 LTS
release: 12.04
codename: precise
platform: ubuntu
platform_version: 12.04
platform_family: debian
time
timezone: UTC
dmi
dmidecode_version: 2.11
ec2
ami_id: ami-e002a788
ami_launch_index: 0
ami_manifest_path: ubuntu-us-east-1/images/ubuntu-precise-12.04-amd64-server-20140829.2.manifest.xml
block_device_mapping_ami: sda1
block_device_mapping_ephemeral0: sda2
block_device_mapping_root: /dev/sda1
block_device_mapping_swap: sda3
hostname: ip-10-0-112-115.priv.evertrue.com
instance_action: none
instance_id: i-37e843dc
instance_type: c1.medium
kernel_id: aki-919dcaf8
local_hostname: ip-10-0-112-115.priv.evertrue.com
local_ipv4: ************
mac: 0a:6c:e3:12:0b:10
metrics_vhostmd: <?xml version="1.0" encoding="UTF-8"?>
network_interfaces_macs
0a:6c:e3:12:0b:10
device_number: 0
interface_id: eni-21b60557
local_hostname: ip-10-0-112-115.priv.evertrue.com
local_ipv4s: ************
mac: 0a:6c:e3:12:0b:10
owner_id: 037590317780
security_group_ids: sg-97f7edf5 sg-b767f6d3 sg-37495355
security_groups: prod-default archiva prod-web-services
subnet_id: subnet-421bd629
subnet_ipv4_cidr_block: **********/24
vpc_id: vpc-9318d5f8
vpc_ipv4_cidr_block: 10.0.0.0/16
placement_availability_zone: us-east-1c
profile: default-paravirtual
public_keys_0_openssh_key: ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCY2kUIxzAXnMyBW7vQV+h/aa08Zdt/gL+EuZD/QUDGbiAe4XmyZ6c+xOTKoE18zsKLUlInx6pp8QN5zffWMVk6AyS5kitfh2lRv3ZI/rq6bAFIH/hFobJay3wLDuwo7Bw3GWfnaAQVTSvbwn8Nz9issln4OlOxqg60s7qgfEm7NB3oPHuTI17vvAUMMwxqiWPl3ob81Cokyp22QlQ7kA62pGwIijiztSLClzoSwzczeS5eO3ua5+yyubP41XJNTloHpW/y3nqvvpRjE7GSZQXAvwy2bTfnIWC2IqdSVAaYyK25YTxIftMPopph8YfHkk0mtnXmCuMd844/bdvTg9cp aws_dev
reservation_id: r-7559345e
security_groups
0: prod-default
1: archiva
2: prod-web-services
services_domain: amazonaws.com
userdata: 
etc
passwd
root
dir: /root
gid: 0
uid: 0
shell: /bin/bash
gecos: root
daemon
dir: /usr/sbin
gid: 1
uid: 1
shell: /bin/sh
gecos: daemon
bin
dir: /bin
gid: 2
uid: 2
shell: /bin/sh
gecos: bin
sys
dir: /dev
gid: 3
uid: 3
shell: /bin/sh
gecos: sys
sync
dir: /bin
gid: 65534
uid: 4
shell: /bin/sync
gecos: sync
games
dir: /usr/games
gid: 60
uid: 5
shell: /bin/sh
gecos: games
man
dir: /var/cache/man
gid: 12
uid: 6
shell: /bin/sh
gecos: man
lp
dir: /var/spool/lpd
gid: 7
uid: 7
shell: /bin/sh
gecos: lp
mail
dir: /var/mail
gid: 8
uid: 8
shell: /bin/sh
gecos: mail
news
dir: /var/spool/news
gid: 9
uid: 9
shell: /bin/sh
gecos: news
uucp
dir: /var/spool/uucp
gid: 10
uid: 10
shell: /bin/sh
gecos: uucp
proxy
dir: /bin
gid: 13
uid: 13
shell: /bin/sh
gecos: proxy
www-data
dir: /var/www
gid: 33
uid: 33
shell: /bin/sh
gecos: www-data
backup
dir: /var/backups
gid: 34
uid: 34
shell: /bin/sh
gecos: backup
list
dir: /var/list
gid: 38
uid: 38
shell: /bin/sh
gecos: Mailing List Manager
irc
dir: /var/run/ircd
gid: 39
uid: 39
shell: /bin/sh
gecos: ircd
gnats
dir: /var/lib/gnats
gid: 41
uid: 41
shell: /bin/sh
gecos: Gnats Bug-Reporting System (admin)
nobody
dir: /nonexistent
gid: 65534
uid: 65534
shell: /bin/sh
gecos: nobody
libuuid
dir: /var/lib/libuuid
gid: 101
uid: 100
shell: /bin/sh
gecos: 
syslog
dir: /home/<USER>
gid: 103
uid: 101
shell: /bin/false
gecos: 
messagebus
dir: /var/run/dbus
gid: 105
uid: 102
shell: /bin/false
gecos: 
whoopsie
dir: /nonexistent
gid: 106
uid: 103
shell: /bin/false
gecos: 
landscape
dir: /var/lib/landscape
gid: 109
uid: 104
shell: /bin/false
gecos: 
sshd
dir: /var/run/sshd
gid: 65534
uid: 105
shell: /usr/sbin/nologin
gecos: 
ubuntu
dir: /home/<USER>
gid: 1000
uid: 1000
shell: /bin/bash
gecos: Ubuntu
postfix
dir: /var/spool/postfix
gid: 113
uid: 106
shell: /bin/false
gecos: 
mark
dir: /home/<USER>
gid: 5003
uid: 5003
shell: /bin/bash
gecos: Mark Greene, set up by Chef
hai
dir: /home/<USER>
gid: 6005
uid: 6005
shell: /bin/bash
gecos: Hai Zhou, set up by Chef
alex
dir: /home/<USER>
gid: 6023
uid: 6023
shell: /bin/bash
gecos: Alex Tironati, set up by Chef
deploy
dir: /home/<USER>
gid: 1011
uid: 1011
shell: /bin/bash
gecos: Deploy worker, set up by Chef
worker
dir: /home/<USER>
gid: 1007
uid: 1007
shell: /bin/bash
gecos: Default worker, set up by Chef
pgray
dir: /home/<USER>
gid: 7002
uid: 7002
shell: /bin/bash
gecos: PJ Gray, set up by Chef
shinkenagent
dir: /home/<USER>
gid: 6027
uid: 6027
shell: /bin/bash
gecos: Shinken Agent User
glenn
dir: /home/<USER>
gid: 6026
uid: 6026
shell: /bin/bash
gecos: Glenn Primmer, generated by Chef
ntop
dir: /var/lib/ntop
gid: 116
uid: 107
shell: /bin/false
gecos: 
newrelic
dir: /opt/newrelic
gid: 117
uid: 108
shell: /bin/false
gecos: 
ntp
dir: /home/<USER>
gid: 118
uid: 109
shell: /bin/false
gecos: 
archiva
dir: /home/<USER>
gid: 999
uid: 999
shell: /bin/false
gecos: Archiva service user
maxkohl
dir: /home/<USER>
gid: 6029
uid: 6029
shell: /bin/bash
gecos: Max Kohl
mauricesmith
dir: /home/<USER>
gid: 6018
uid: 6018
shell: /bin/bash
gecos: Maurice Smith, created by Chef
consul
dir: /home/<USER>
gid: 7003
uid: 7003
shell: /bin/false
gecos: Service user for consul
edasque
dir: /home/<USER>
gid: 6056
uid: 6056
shell: /bin/bash
gecos: Erik Dasque
eben
dir: /home/<USER>
gid: 6061
uid: 6061
shell: /bin/bash
gecos: Eben Pingree
dan
dir: /home/<USER>
gid: 6062
uid: 6062
shell: /bin/bash
gecos: Daniel McGuane
lance
dir: /home/<USER>
gid: 6065
uid: 6065
shell: /bin/bash
gecos: Lance Laughlin
george
dir: /home/<USER>
gid: 6066
uid: 6066
shell: /bin/bash
gecos: Geroge Rose
sarah
dir: /home/<USER>
gid: 6067
uid: 6067
shell: /bin/bash
gecos: Sarah Morgan
jenkins
dir: /var/lib/jenkins
gid: 6071
uid: 6071
shell: /bin/bash
gecos: JENKINS
spencer
dir: /home/<USER>
gid: 6074
uid: 6074
shell: /bin/bash
gecos: Spencer Paschal
amy
dir: /home/<USER>
gid: 6076
uid: 6076
shell: /bin/bash
gecos: Amy Lashley
doug
dir: /home/<USER>
gid: 6078
uid: 6078
shell: /bin/bash
gecos: Doug Youch
josh
dir: /home/<USER>
gid: 6079
uid: 6079
shell: /bin/bash
gecos: Joshua Foster
patty
dir: /home/<USER>
gid: 6081
uid: 6081
shell: /bin/bash
gecos: Patty Boyer
rob
dir: /home/<USER>
gid: 6082
uid: 6082
shell: /bin/bash
gecos: Rob Kaminsky
regan
dir: /home/<USER>
gid: 6083
uid: 6083
shell: /bin/bash
gecos: Regan Holt
evertrue-upload
dir: /home/<USER>
gid: 8064
uid: 8064
shell: /bin/bash
gecos: Evertrue Upload
mikebrucek
dir: /home/<USER>
gid: 6084
uid: 6084
shell: /bin/bash
gecos: Mike Brucek
gabe
dir: /home/<USER>
gid: 6085
uid: 6085
shell: /bin/bash
gecos: Gabe Grayum
joshwallace
dir: /home/<USER>
gid: 6087
uid: 6087
shell: /bin/bash
gecos: Josh Wallace
kim
dir: /home/<USER>
gid: 6088
uid: 6088
shell: /bin/bash
gecos: Kim Selwitz
laura
dir: /home/<USER>
gid: 6089
uid: 6089
shell: /bin/bash
gecos: Laura Austin
davidanchin
dir: /home/<USER>
gid: 6090
uid: 6090
shell: /bin/bash
gecos: David Anchin
kevin
dir: /home/<USER>
gid: 6091
uid: 6091
shell: /bin/bash
gecos: Kevin Massimino
adam
dir: /home/<USER>
gid: 6092
uid: 6092
shell: /bin/bash
gecos: Adam Zielinski
rday
dir: /home/<USER>
gid: 6093
uid: 6093
shell: /bin/bash
gecos: Roderick Day
nickbor
dir: /home/<USER>
gid: 6094
uid: 6094
shell: /bin/bash
gecos: Nick Borunov
shelley
dir: /home/<USER>
gid: 6095
uid: 6095
shell: /bin/bash
gecos: Shelley Talbot
jsementilli
dir: /home/<USER>
gid: 6096
uid: 6096
shell: /bin/bash
gecos: Julia Sementilli
snmp
dir: /var/lib/snmp
gid: 119
uid: 110
shell: /bin/false
gecos: 
mschottanes
dir: /home/<USER>
gid: 6097
uid: 6097
shell: /bin/bash
gecos: Megan Schottanes
ttatro
dir: /home/<USER>
gid: 6098
uid: 6098
shell: /bin/bash
gecos: Travis Tatro
sentinelone
dir: /opt/sentinelone/home
gid: 998
uid: 998
shell: /usr/sbin/nologin
gecos: 
ian
dir: /home/<USER>
gid: 6099
uid: 6099
shell: /bin/bash
gecos: Ian Malcom
bseeger
dir: /home/<USER>
gid: 6100
uid: 6100
shell: /bin/bash
gecos: Bethany Seeger
urayoan
dir: /home/<USER>
gid: 6101
uid: 6101
shell: /bin/bash
gecos: Urayoan Irizarry
group
root
gid: 0
members: 
daemon
gid: 1
members: 
bin
gid: 2
members: 
sys
gid: 3
members: 
adm
gid: 4
members
0: ubuntu
tty
gid: 5
members: 
disk
gid: 6
members: 
lp
gid: 7
members: 
mail
gid: 8
members: 
news
gid: 9
members: 
uucp
gid: 10
members: 
man
gid: 12
members: 
proxy
gid: 13
members: 
kmem
gid: 15
members: 
dialout
gid: 20
members
0: ubuntu
fax
gid: 21
members: 
voice
gid: 22
members: 
cdrom
gid: 24
members
0: ubuntu
floppy
gid: 25
members
0: ubuntu
tape
gid: 26
members: 
sudo
gid: 27
members: 
audio
gid: 29
members
0: ubuntu
dip
gid: 30
members
0: ubuntu
www-data
gid: 33
members: 
backup
gid: 34
members: 
operator
gid: 37
members: 
list
gid: 38
members: 
irc
gid: 39
members: 
src
gid: 40
members: 
gnats
gid: 41
members: 
shadow
gid: 42
members: 
utmp
gid: 43
members: 
video
gid: 44
members
0: ubuntu
sasl
gid: 45
members: 
plugdev
gid: 46
members
0: ubuntu
staff
gid: 50
members: 
games
gid: 60
members: 
users
gid: 100
members: 
nogroup
gid: 65534
members: 
libuuid
gid: 101
members: 
crontab
gid: 102
members: 
syslog
gid: 103
members: 
fuse
gid: 104
members: 
messagebus
gid: 105
members: 
whoopsie
gid: 106
members: 
mlocate
gid: 107
members: 
ssh
gid: 108
members: 
landscape
gid: 109
members: 
netdev
gid: 110
members
0: ubuntu
admin
gid: 111
members
0: ubuntu
ubuntu
gid: 1000
members: 
ssl-cert
gid: 112
members: 
postfix
gid: 113
members: 
postdrop
gid: 114
members: 
mark
gid: 5003
members: 
hai
gid: 6005
members: 
alex
gid: 6023
members: 
sysadmin
gid: 2300
members
0: hai
1: dan
2: mschottanes
3: urayoan
4: adam
5: rday
6: doug
7: shelley
8: amy
9: ian
10: edasque
11: davidanchin
12: george
13: bseeger
14: josh
15: alex
deploy
gid: 1011
members: 
worker
gid: 1007
members: 
pgray
gid: 7002
members: 
shinkenagent
gid: 6027
members: 
glenn
gid: 6026
members: 
evertrue
gid: 2004
members
0: worker
1: pgray
2: hai
3: dan
4: nickbor
5: ttatro
6: mschottanes
7: jsementilli
8: urayoan
9: rob
10: adam
11: mikebrucek
12: patty
13: rday
14: joshwallace
15: doug
16: spencer
17: shinkenagent
18: shelley
19: kim
20: deploy
21: amy
22: ian
23: lance
24: edasque
25: gabe
26: sarah
27: jenkins
28: davidanchin
29: george
30: eben
31: laura
32: bseeger
33: regan
34: kevin
35: josh
36: alex
37: evertrue-upload
jwhois
gid: 115
members: 
ntop
gid: 116
members: 
newrelic
gid: 117
members: 
ntp
gid: 118
members: 
archiva
gid: 999
members: 
maxkohl
gid: 6029
members: 
mauricesmith
gid: 6018
members: 
consul
gid: 7003
members: 
edasque
gid: 6056
members: 
eben
gid: 6061
members: 
dan
gid: 6062
members: 
lance
gid: 6065
members: 
george
gid: 6066
members: 
sarah
gid: 6067
members: 
jenkins
gid: 6071
members: 
spencer
gid: 6074
members: 
amy
gid: 6076
members: 
doug
gid: 6078
members: 
josh
gid: 6079
members: 
patty
gid: 6081
members: 
rob
gid: 6082
members: 
regan
gid: 6083
members: 
evertrue-upload
gid: 8064
members: 
mikebrucek
gid: 6084
members: 
gabe
gid: 6085
members: 
joshwallace
gid: 6087
members: 
kim
gid: 6088
members: 
laura
gid: 6089
members: 
davidanchin
gid: 6090
members: 
kevin
gid: 6091
members: 
adam
gid: 6092
members: 
rday
gid: 6093
members: 
nickbor
gid: 6094
members: 
shelley
gid: 6095
members: 
jsementilli
gid: 6096
members: 
snmp
gid: 119
members: 
mschottanes
gid: 6097
members: 
ttatro
gid: 6098
members: 
sentinelone
gid: 998
members: 
ian
gid: 6099
members: 
bseeger
gid: 6100
members: 
urayoan
gid: 6101
members: 
current_user: root
cloud
public_ips
0: 
private_ips
0: ************
public_ipv4: 
public_hostname: 
local_ipv4: ************
local_hostname: ip-10-0-112-115.priv.evertrue.com
provider: ec2
fips
kernel
enabled: false
block_device
ram0
size: 131072
removable: 0
rotational: 1
physical_block_size: 512
logical_block_size: 512
ram1
size: 131072
removable: 0
rotational: 1
physical_block_size: 512
logical_block_size: 512
ram2
size: 131072
removable: 0
rotational: 1
physical_block_size: 512
logical_block_size: 512
ram3
size: 131072
removable: 0
rotational: 1
physical_block_size: 512
logical_block_size: 512
ram4
size: 131072
removable: 0
rotational: 1
physical_block_size: 512
logical_block_size: 512
ram5
size: 131072
removable: 0
rotational: 1
physical_block_size: 512
logical_block_size: 512
ram6
size: 131072
removable: 0
rotational: 1
physical_block_size: 512
logical_block_size: 512
ram7
size: 131072
removable: 0
rotational: 1
physical_block_size: 512
logical_block_size: 512
ram8
size: 131072
removable: 0
rotational: 1
physical_block_size: 512
logical_block_size: 512
ram9
size: 131072
removable: 0
rotational: 1
physical_block_size: 512
logical_block_size: 512
ram10
size: 131072
removable: 0
rotational: 1
physical_block_size: 512
logical_block_size: 512
ram11
size: 131072
removable: 0
rotational: 1
physical_block_size: 512
logical_block_size: 512
ram12
size: 131072
removable: 0
rotational: 1
physical_block_size: 512
logical_block_size: 512
ram13
size: 131072
removable: 0
rotational: 1
physical_block_size: 512
logical_block_size: 512
ram14
size: 131072
removable: 0
rotational: 1
physical_block_size: 512
logical_block_size: 512
ram15
size: 131072
removable: 0
rotational: 1
physical_block_size: 512
logical_block_size: 512
loop0
size: 0
removable: 0
rotational: 1
physical_block_size: 0
logical_block_size: 512
loop1
size: 0
removable: 0
rotational: 1
physical_block_size: 0
logical_block_size: 512
loop2
size: 0
removable: 0
rotational: 1
physical_block_size: 0
logical_block_size: 512
loop3
size: 0
removable: 0
rotational: 1
physical_block_size: 0
logical_block_size: 512
loop4
size: 0
removable: 0
rotational: 1
physical_block_size: 0
logical_block_size: 512
loop5
size: 0
removable: 0
rotational: 1
physical_block_size: 0
logical_block_size: 512
loop6
size: 0
removable: 0
rotational: 1
physical_block_size: 0
logical_block_size: 512
loop7
size: 0
removable: 0
rotational: 1
physical_block_size: 0
logical_block_size: 512
xvda1
size: 20971520
removable: 0
rotational: 0
physical_block_size: 512
logical_block_size: 512
xvda2
size: 712971264
removable: 0
rotational: 0
physical_block_size: 512
logical_block_size: 512
xvda3
size: 1835008
removable: 0
rotational: 0
physical_block_size: 512
logical_block_size: 512
filesystem2
by_device
/dev/xvda1
kb_size: 10321208
kb_used: 3888520
kb_available: 5908412
percent_used: 40%
total_inodes: 655360
inodes_used: 174362
inodes_available: 480998
inodes_percent_used: 27%
fs_type: ext4
mount_options
0: rw
uuid: 92c362de-e9aa-4406-ba27-2e48e7408872
label: cloudimg-rootfs
mounts
0: /
udev
kb_size: 789624
kb_used: 8
kb_available: 789616
percent_used: 1%
total_inodes: 197406
inodes_used: 385
inodes_available: 197021
inodes_percent_used: 1%
fs_type: devtmpfs
mount_options
0: rw
1: mode=0755
mounts
0: /dev
tmpfs
kb_size: 159576
kb_used: 192
kb_available: 159384
percent_used: 1%
total_inodes: 199467
inodes_used: 272
inodes_available: 199195
inodes_percent_used: 1%
fs_type: tmpfs
mount_options
0: rw
1: noexec
2: nosuid
3: size=10%
4: mode=0755
mounts
0: /run
none
kb_size: 797868
kb_used: 0
kb_available: 797868
percent_used: 0%
total_inodes: 199467
inodes_used: 1
inodes_available: 199466
inodes_percent_used: 1%
fs_type: securityfs
mount_options
0: rw
mounts
0: /run/lock
1: /run/shm
2: /sys/fs/fuse/connections
3: /sys/kernel/debug
4: /sys/kernel/security
/dev/xvda2
kb_size: 350891748
kb_used: 66766572
kb_available: 266300896
percent_used: 21%
total_inodes: 22282240
inodes_used: 1303817
inodes_available: 20978423
inodes_percent_used: 6%
fs_type: ext3
mount_options
0: rw
uuid: d71ea955-0025-4361-80e3-af39de4c3cef
mounts
0: /mnt/dev0
proc
fs_type: proc
mount_options
0: rw
1: noexec
2: nosuid
3: nodev
mounts
0: /proc
sysfs
fs_type: sysfs
mount_options
0: rw
1: noexec
2: nosuid
3: nodev
mounts
0: /sys
devpts
fs_type: devpts
mount_options
0: rw
1: noexec
2: nosuid
3: gid=5
4: mode=0620
mounts
0: /dev/pts
/dev/xvda3
fs_type: swap
uuid: 84169cd0-93f9-4363-a93e-3da9fe3006ee
rootfs
fs_type: rootfs
mount_options
0: rw
mounts
0: /
/dev/disk/by-label/cloudimg-rootfs
fs_type: ext4
mount_options
0: rw
1: relatime
2: user_xattr
3: barrier=1
4: data=ordered
mounts
0: /
sentitrace
fs_type: debugfs
mount_options
0: rw
1: nodev
2: relatime
mounts
0: /opt/sentinelone/mount
by_mountpoint
/
kb_size: 10321208
kb_used: 3888520
kb_available: 5908412
percent_used: 40%
total_inodes: 655360
inodes_used: 174362
inodes_available: 480998
inodes_percent_used: 27%
fs_type: ext4
mount_options
0: rw
1: relatime
2: user_xattr
3: barrier=1
4: data=ordered
uuid: 92c362de-e9aa-4406-ba27-2e48e7408872
label: cloudimg-rootfs
devices
0: /dev/xvda1
1: rootfs
2: /dev/disk/by-label/cloudimg-rootfs
/dev
kb_size: 789624
kb_used: 8
kb_available: 789616
percent_used: 1%
total_inodes: 197406
inodes_used: 385
inodes_available: 197021
inodes_percent_used: 1%
fs_type: devtmpfs
mount_options
0: rw
1: mode=0755
devices
0: udev
/run
kb_size: 159576
kb_used: 192
kb_available: 159384
percent_used: 1%
total_inodes: 199467
inodes_used: 272
inodes_available: 199195
inodes_percent_used: 1%
fs_type: tmpfs
mount_options
0: rw
1: noexec
2: nosuid
3: size=10%
4: mode=0755
devices
0: tmpfs
/run/lock
kb_size: 5120
kb_used: 0
kb_available: 5120
percent_used: 0%
total_inodes: 199467
inodes_used: 3
inodes_available: 199464
inodes_percent_used: 1%
fs_type: tmpfs
mount_options
0: rw
1: noexec
2: nosuid
3: nodev
4: size=5242880
devices
0: none
/run/shm
kb_size: 797868
kb_used: 0
kb_available: 797868
percent_used: 0%
total_inodes: 199467
inodes_used: 1
inodes_available: 199466
inodes_percent_used: 1%
fs_type: tmpfs
mount_options
0: rw
1: nosuid
2: nodev
devices
0: none
/mnt/dev0
kb_size: 350891748
kb_used: 66766572
kb_available: 266300896
percent_used: 21%
total_inodes: 22282240
inodes_used: 1303817
inodes_available: 20978423
inodes_percent_used: 6%
fs_type: ext3
mount_options
0: rw
uuid: d71ea955-0025-4361-80e3-af39de4c3cef
devices
0: /dev/xvda2
/proc
fs_type: proc
mount_options
0: rw
1: noexec
2: nosuid
3: nodev
devices
0: proc
/sys
fs_type: sysfs
mount_options
0: rw
1: noexec
2: nosuid
3: nodev
devices
0: sysfs
/sys/fs/fuse/connections
fs_type: fusectl
mount_options
0: rw
devices
0: none
/sys/kernel/debug
fs_type: debugfs
mount_options
0: rw
devices
0: none
/sys/kernel/security
fs_type: securityfs
mount_options
0: rw
devices
0: none
/dev/pts
fs_type: devpts
mount_options
0: rw
1: noexec
2: nosuid
3: gid=5
4: mode=0620
devices
0: devpts
/opt/sentinelone/mount
fs_type: debugfs
mount_options
0: rw
1: nodev
2: relatime
devices
0: sentitrace
by_pair
/dev/xvda1,/
device: /dev/xvda1
kb_size: 10321208
kb_used: 3888520
kb_available: 5908412
percent_used: 40%
mount: /
total_inodes: 655360
inodes_used: 174362
inodes_available: 480998
inodes_percent_used: 27%
fs_type: ext4
mount_options
0: rw
uuid: 92c362de-e9aa-4406-ba27-2e48e7408872
label: cloudimg-rootfs
udev,/dev
device: udev
kb_size: 789624
kb_used: 8
kb_available: 789616
percent_used: 1%
mount: /dev
total_inodes: 197406
inodes_used: 385
inodes_available: 197021
inodes_percent_used: 1%
fs_type: devtmpfs
mount_options
0: rw
1: mode=0755
tmpfs,/run
device: tmpfs
kb_size: 159576
kb_used: 192
kb_available: 159384
percent_used: 1%
mount: /run
total_inodes: 199467
inodes_used: 272
inodes_available: 199195
inodes_percent_used: 1%
fs_type: tmpfs
mount_options
0: rw
1: noexec
2: nosuid
3: size=10%
4: mode=0755
none,/run/lock
device: none
kb_size: 5120
kb_used: 0
kb_available: 5120
percent_used: 0%
mount: /run/lock
total_inodes: 199467
inodes_used: 3
inodes_available: 199464
inodes_percent_used: 1%
fs_type: tmpfs
mount_options
0: rw
1: noexec
2: nosuid
3: nodev
4: size=5242880
none,/run/shm
device: none
kb_size: 797868
kb_used: 0
kb_available: 797868
percent_used: 0%
mount: /run/shm
total_inodes: 199467
inodes_used: 1
inodes_available: 199466
inodes_percent_used: 1%
fs_type: tmpfs
mount_options
0: rw
1: nosuid
2: nodev
/dev/xvda2,/mnt/dev0
device: /dev/xvda2
kb_size: 350891748
kb_used: 66766572
kb_available: 266300896
percent_used: 21%
mount: /mnt/dev0
total_inodes: 22282240
inodes_used: 1303817
inodes_available: 20978423
inodes_percent_used: 6%
fs_type: ext3
mount_options
0: rw
uuid: d71ea955-0025-4361-80e3-af39de4c3cef
proc,/proc
device: proc
mount: /proc
fs_type: proc
mount_options
0: rw
1: noexec
2: nosuid
3: nodev
sysfs,/sys
device: sysfs
mount: /sys
fs_type: sysfs
mount_options
0: rw
1: noexec
2: nosuid
3: nodev
none,/sys/fs/fuse/connections
device: none
mount: /sys/fs/fuse/connections
fs_type: fusectl
mount_options
0: rw
none,/sys/kernel/debug
device: none
mount: /sys/kernel/debug
fs_type: debugfs
mount_options
0: rw
none,/sys/kernel/security
device: none
mount: /sys/kernel/security
fs_type: securityfs
mount_options
0: rw
devpts,/dev/pts
device: devpts
mount: /dev/pts
fs_type: devpts
mount_options
0: rw
1: noexec
2: nosuid
3: gid=5
4: mode=0620
/dev/xvda3,
device: /dev/xvda3
fs_type: swap
uuid: 84169cd0-93f9-4363-a93e-3da9fe3006ee
rootfs,/
device: rootfs
mount: /
fs_type: rootfs
mount_options
0: rw
/dev/disk/by-label/cloudimg-rootfs,/
device: /dev/disk/by-label/cloudimg-rootfs
mount: /
fs_type: ext4
mount_options
0: rw
1: relatime
2: user_xattr
3: barrier=1
4: data=ordered
sentitrace,/opt/sentinelone/mount
device: sentitrace
mount: /opt/sentinelone/mount
fs_type: debugfs
mount_options
0: rw
1: nodev
2: relatime
machine_id: 6803088dafa4e281b95ec54d00000108
hostnamectl: 
virtualization
systems
xen: guest
system: xen
role: guest
hostname: prod-archiva-1c
machinename: prod-archiva-1c.priv.evertrue.com
fqdn: prod-archiva-1c.priv.evertrue.com
init_package: init
uptime_seconds: 1943571
uptime: 22 days 11 hours 52 minutes 51 seconds
idletime_seconds: 1769062
idletime: 20 days 11 hours 24 minutes 22 seconds
chef_packages
ohai
version: 8.22.0
ohai_root: /opt/chef/embedded/lib/ruby/gems/2.3.0/gems/ohai-8.22.0/lib/ohai
chef
version: 12.17.44
chef_root: /opt/chef/embedded/lib/ruby/gems/2.3.0/gems/chef-12.17.44/lib
command
ps: ps -ef
keys
ssh
host_rsa_public: AAAAB3NzaC1yc2EAAAADAQABAAABAQCd84Y5X4GPAsgs7GeyZAMwbetD9/Gtwam0ULXgxy2g+e/030B69lsQhka8pH1+i9h9GIbm7L5DVC4bhk4ny4sEq/r2DfXxP+RKY/SGBa2EF8vg/kvA580isKsQXolrZEVgM8zMmYIbJpOrwlUcSiH1xEBZi9uILqzKOXaa+UyG6UFhXWYvSuV8iGjgE4Z3tp9DhCdxDDKAqtmGPdbdc34QDF/gBbzgaBFVGP2n5l5oyHs4Gs9sdcEHo1v8kl6Y+Eiym8vrY+Odk5T0/HZ8S8+ry/rctpGGDbz7OSYTa8B52dYF0zl98SNEt9qnMudOKWKjX4KZPaoTjqDGJKumrGDr
host_dsa_public: AAAAB3NzaC1kc3MAAACBALnfjhtVZoCggbSbJ9gZ/8ngXTu6co9eoNeP2kIhzGHKGltfnDVy5+WCTpKAjQrXPFNTaj5uQjAtXacrrrO4n2aJ11Wo+XoJO474XskC/1ePpSA95fBQYfJqsvK8ODunC9mvfteukEvQCfFip4Jpjzg7Ks1GaXRISh3WVJXUHp+VAAAAFQDEs5h8JHWARJxmscH1P/rMMObHbwAAAIEAhJPyBHKGPdPGtZZf/uX5F9ZRm4OOPquXHM8BytYVr/DAGjXRdT03M3xyLf9cHgUveCLR9J9RC0B8k0179BrArEZSRrl3Uu4gU+gaIF3IiN/RGuHvdMwiccyWPwRQOKK5IZg1O/2mNKgeA4rGzKi3tG4RHzBE5o1m024JNZa5/h4AAACAIsH5IbC0fifoR56go7Rhv393B1Y29iBf0+5myGn3uhIt1fIT/Z3zyOs/nPlLz+iTDGB4zO18YD2E3DUCLvvkASRNx69lhRJNg2DEyOXFGs8Cnuo7tZ2bVVA9zdHxnPDI25wKvhsILJfD1Krra3u18h+F/WzXQjlY06hvmDfxcPg=
host_ecdsa_public: AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBLK0VMvUWgAjk8e0rXzKWtoLd9GjTpQdfHuT2+NCa7JSWpqh+hgwv/31AF0EbrHl0i59bvVq1DVRw5mmqHrL7vE=
host_ecdsa_type: ecdsa-sha2-nistp256
packages
accountsservice
version: 0.6.15-2ubuntu9.7
arch: amd64
acpid
version: 1:2.0.10-1ubuntu3
arch: amd64
adduser
version: 3.113ubuntu2
arch: all
apparmor
version: 2.7.102-0ubuntu3.11
arch: amd64
apport
version: 2.0.1-0ubuntu17.16
arch: all
apport-symptoms
version: 0.16.1
arch: all
apt
version: 0.8.16~exp12ubuntu10.29
arch: amd64
apt-transport-https
version: 0.8.16~exp12ubuntu10.29
arch: amd64
apt-utils
version: 0.8.16~exp12ubuntu10.29
arch: amd64
apt-xapian-index
version: 0.44ubuntu5.1
arch: all
aptitude
version: 0.6.6-1ubuntu1.2
arch: amd64
at
version: 3.1.13-1ubuntu1
arch: amd64
autoconf
version: 2.68-1ubuntu2
arch: all
autogen
version: 1:5.12-0.1ubuntu1
arch: amd64
automake
version: 1:1.11.3-1ubuntu2
arch: all
autotools-dev
version: ********.1ubuntu1
arch: all
base-files
version: 6.5ubuntu6.8
arch: amd64
base-passwd
version: 3.5.24
arch: amd64
bash
version: 4.2-2ubuntu2.9
arch: amd64
bash-completion
version: 1:1.3-1ubuntu8.1
arch: all
bc
version: 1.06.95-2ubuntu1
arch: amd64
bind9-host
version: 1:9.8.1.dfsg.P1-4ubuntu0.32
arch: amd64
binutils
version: 2.22-6ubuntu1.4
arch: amd64
binutils-doc
version: 2.22-6ubuntu1.4
arch: all
bison
version: 1:2.5.dfsg-2.1
arch: amd64
bsdmainutils
version: 8.2.3ubuntu1
arch: amd64
bsdutils
version: 1:2.20.1-1ubuntu3.1
arch: amd64
build-essential
version: 11.5ubuntu2.1
arch: amd64
busybox-initramfs
version: 1:1.18.5-1ubuntu4.1
arch: amd64
busybox-static
version: 1:1.18.5-1ubuntu4.1
arch: amd64
byobu
version: 5.17-0ubuntu1
arch: all
bzip2
version: 1.0.6-1ubuntu0.2
arch: amd64
bzr
version: 2.5.1-0ubuntu2.1
arch: all
ca-certificates
version: 20190110~12.04.1
arch: all
ca-certificates-java
version: 20110912ubuntu6
arch: all
chef
version: 12.17.44-1
arch: amd64
cloud-init
version: 0.6.3-0ubuntu1.15
arch: all
cloud-initramfs-growroot
version: 0.4ubuntu1
arch: all
cloud-initramfs-rescuevol
version: 0.4ubuntu1
arch: all
cloud-utils
version: 0.25-0ubuntu5.1
arch: all
command-not-found
version: 0.2.46ubuntu6
arch: all
command-not-found-data
version: 0.2.46ubuntu6
arch: amd64
console-setup
version: 1.70ubuntu5
arch: all
consolekit
version: 0.4.5-2ubuntu0.1
arch: amd64
coreutils
version: 8.13-3ubuntu3.3
arch: amd64
cpio
version: 2.11-7ubuntu3.3
arch: amd64
cpp
version: 4:4.6.3-1ubuntu5
arch: amd64
cpp-4.6
version: 4.6.3-1ubuntu5
arch: amd64
crda
version: 1.1.2-1ubuntu1
arch: amd64
cron
version: 3.0pl1-120ubuntu4
arch: amd64
cryptsetup-bin
version: 2:1.4.1-2ubuntu4
arch: amd64
curl
version: 7.22.0-3ubuntu4.29
arch: amd64
dash
version: 0.5.7-2ubuntu2
arch: amd64
dbus
version: 1.4.18-1ubuntu1.10
arch: amd64
dbus-x11
version: 1.4.18-1ubuntu1.10
arch: amd64
dconf-gsettings-backend
version: 0.12.0-0ubuntu1.1
arch: amd64
dconf-service
version: 0.12.0-0ubuntu1.1
arch: amd64
debconf
version: 1.5.42ubuntu1
arch: all
debconf-i18n
version: 1.5.42ubuntu1
arch: all
debianutils
version: 4.2.1ubuntu2
arch: amd64
diffutils
version: 1:3.2-1ubuntu1
arch: amd64
dmidecode
version: 2.11-4ubuntu0.1
arch: amd64
dmsetup
version: 2:1.02.48-4ubuntu7.4
arch: amd64
dnsutils
version: 1:9.8.1.dfsg.P1-4ubuntu0.32
arch: amd64
dosfstools
version: 3.0.12-1ubuntu1.3
arch: amd64
dpkg
version: 1.16.1.2ubuntu7.9
arch: amd64
dpkg-dev
version: 1.16.1.2ubuntu7.9
arch: all
e2fslibs
version: 1.42-1ubuntu2.5
arch: amd64
e2fsprogs
version: 1.42-1ubuntu2.5
arch: amd64
ed
version: 1.5-3
arch: amd64
eject
version: 2.1.5+deb1+cvs20081104-9ubuntu0.1
arch: amd64
elinks
version: 0.12~pre5-7ubuntu1
arch: amd64
elinks-data
version: 0.12~pre5-7ubuntu1
arch: all
ethtool
version: 1:3.1-1
arch: amd64
euca2ools
version: 2.0.0~bzr516-0ubuntu3.1
arch: all
fakeroot
version: 1.18.2-1
arch: amd64
file
version: 5.09-2ubuntu0.8
arch: amd64
filebeat
version: 5.2.2
arch: amd64
findutils
version: 4.4.2-4ubuntu1
arch: amd64
flex
version: 2.5.35-10ubuntu3
arch: amd64
fontconfig
version: 2.8.0-3ubuntu9.2
arch: amd64
fontconfig-config
version: 2.8.0-3ubuntu9.2
arch: all
fonts-ubuntu-font-family-console
version: 0.80-0ubuntu2
arch: all
fortune-mod
version: 1:1.99.1-4
arch: amd64
fortunes
version: 1:1.99.1-4
arch: all
fortunes-min
version: 1:1.99.1-4
arch: all
fortunes-off
version: 1:1.99.1-4
arch: all
friendly-recovery
version: 0.2.25
arch: all
ftp
version: 0.17-25
arch: amd64
fuse
version: 2.8.6-2ubuntu2.1
arch: amd64
g++
version: 4:4.6.3-1ubuntu5
arch: amd64
g++-4.6
version: 4.6.3-1ubuntu5
arch: amd64
gcc
version: 4:4.6.3-1ubuntu5
arch: amd64
gcc-4.6
version: 4.6.3-1ubuntu5
arch: amd64
gcc-4.6-base
version: 4.6.3-1ubuntu5
arch: amd64
gconf-service
version: 3.2.5-0ubuntu2
arch: amd64
gconf-service-backend
version: 3.2.5-0ubuntu2
arch: amd64
gconf2
version: 3.2.5-0ubuntu2
arch: amd64
gconf2-common
version: 3.2.5-0ubuntu2
arch: all
geoip-database
version: 20111220-1
arch: all
gettext
version: ********-5ubuntu3.1
arch: amd64
gettext-base
version: ********-5ubuntu3.1
arch: amd64
ghc
version: 7.4.1-1ubuntu2
arch: amd64
gir1.2-glib-2.0
version: 1.32.0-1
arch: amd64
git
version: 1:*******-1ubuntu0.3
arch: amd64
git-core
version: 1:*******-1ubuntu0.3
arch: all
git-man
version: 1:*******-1ubuntu0.3
arch: all
gnupg
version: 1.4.11-3ubuntu2.12
arch: amd64
gpgv
version: 1.4.11-3ubuntu2.12
arch: amd64
grep
version: 2.10-1
arch: amd64
groff-base
version: 1.21-7
arch: amd64
grub-common
version: 1.99-21ubuntu3.19
arch: amd64
grub-gfxpayload-lists
version: 0.6
arch: amd64
grub-legacy-ec2
version: 0.6.3-0ubuntu1.15
arch: all
grub-pc
version: 1.99-21ubuntu3.19
arch: amd64
grub-pc-bin
version: 1.99-21ubuntu3.19
arch: amd64
grub2-common
version: 1.99-21ubuntu3.19
arch: amd64
guile-1.8-libs
version: 1.8.8+1-6ubuntu2
arch: amd64
gvfs
version: 1.12.1-0ubuntu1.2
arch: amd64
gvfs-common
version: 1.12.1-0ubuntu1.2
arch: all
gvfs-daemons
version: 1.12.1-0ubuntu1.2
arch: amd64
gvfs-libs
version: 1.12.1-0ubuntu1.2
arch: amd64
gzip
version: 1.4-1ubuntu2
arch: amd64
hdparm
version: 9.37-0ubuntu3.1
arch: amd64
hicolor-icon-theme
version: 0.12-1ubuntu2
arch: all
hostname
version: 3.06ubuntu1
arch: amd64
hping3
version: 3.a2.ds2-6
arch: amd64
htop
version: 1.0.1-1
arch: amd64
icedtea-7-jre-jamvm
version: 7u79-2.5.6-0ubuntu1.12.04.1
arch: amd64
iftop
version: 1.0~pre2-1
arch: amd64
ifupdown
version: 0.7~beta2ubuntu11.1
arch: amd64
info
version: 4.13a.dfsg.1-8ubuntu2
arch: amd64
initramfs-tools
version: 0.99ubuntu13.5
arch: all
initramfs-tools-bin
version: 0.99ubuntu13.5
arch: amd64
initscripts
version: 2.88dsf-13.10ubuntu11.1
arch: amd64
insserv
version: 1.14.0-2.1ubuntu2
arch: amd64
install-info
version: 4.13a.dfsg.1-8ubuntu2
arch: amd64
iotop
version: 0.4.4-4
arch: amd64
iproute
version: 20111117-1ubuntu2.3
arch: amd64
iptables
version: 1.4.12-1ubuntu5
arch: amd64
iptraf
version: 3.0.0-8
arch: amd64
iputils-ping
version: 3:20101006-1ubuntu1
arch: amd64
iputils-tracepath
version: 3:20101006-1ubuntu1
arch: amd64
irqbalance
version: 0.56-1ubuntu4
arch: amd64
isc-dhcp-client
version: 4.1.ESV-R4-0ubuntu5.13
arch: amd64
isc-dhcp-common
version: 4.1.ESV-R4-0ubuntu5.13
arch: amd64
iso-codes
version: 3.31-1
arch: all
iw
version: 3.2-1
arch: amd64
java-common
version: 0.43ubuntu2
arch: all
javascript-common
version: 8
arch: all
jnettop
version: 0.13.0-1ubuntu1
arch: amd64
jq
version: 1.2-8~ubuntu12.04.1
arch: amd64
jwhois
version: 4.0-2
arch: amd64
kbd
version: 1.15.2-3ubuntu4
arch: amd64
keyboard-configuration
version: 1.70ubuntu5
arch: all
klibc-utils
version: 1.5.25-1ubuntu2
arch: amd64
kmod
version: 15-0ubuntu6
arch: amd64
krb5-locales
version: 1.10+dfsg~beta1-2ubuntu0.7
arch: all
landscape-client
version: 13.07.3-0ubuntu0.12.04
arch: amd64
landscape-common
version: 13.07.3-0ubuntu0.12.04
arch: amd64
language-selector-common
version: 0.79.4
arch: all
laptop-detect
version: 0.13.7ubuntu2
arch: amd64
less
version: 444-1ubuntu1
arch: amd64
lftp
version: 4.4.13-1
arch: amd64
libaccountsservice0
version: 0.6.15-2ubuntu9.7
arch: amd64
libacl1
version: 2.2.52-1
arch: amd64
libalgorithm-diff-perl
version: 1.19.02-2
arch: all
libalgorithm-diff-xs-perl
version: 0.04-2build2
arch: amd64
libalgorithm-merge-perl
version: 0.08-2
arch: all
libapt-inst1.4
version: 0.8.16~exp12ubuntu10.29
arch: amd64
libapt-pkg4.12
version: 0.8.16~exp12ubuntu10.29
arch: amd64
libarchive-dev
version: 3.0.3-6ubuntu1.4
arch: amd64
libarchive12
version: 3.0.3-6ubuntu1.4
arch: amd64
libasn1-8-heimdal
version: 1.6~git20120311.dfsg.1-2ubuntu0.2
arch: amd64
libasound2
version: 1.0.25-1ubuntu10.2
arch: amd64
libasyncns0
version: 0.8-4
arch: amd64
libatasmart4
version: 0.18-3
arch: amd64
libatk-wrapper-java
version: 0.30.4-0ubuntu2
arch: all
libatk-wrapper-java-jni
version: 0.30.4-0ubuntu2
arch: amd64
libatk1.0-0
version: 2.4.0-0ubuntu1
arch: amd64
libatk1.0-data
version: 2.4.0-0ubuntu1
arch: all
libattr1
version: 1:2.4.47-1ubuntu1
arch: amd64
libavahi-client3
version: 0.6.30-5ubuntu2.3
arch: amd64
libavahi-common-data
version: 0.6.30-5ubuntu2.3
arch: amd64
libavahi-common3
version: 0.6.30-5ubuntu2.3
arch: amd64
libavahi-glib1
version: 0.6.30-5ubuntu2.3
arch: amd64
libbind9-80
version: 1:9.8.1.dfsg.P1-4ubuntu0.32
arch: amd64
libbison-dev
version: 1:2.5.dfsg-2.1
arch: amd64
libblkid1
version: 2.20.1-1ubuntu3.1
arch: amd64
libbonobo2-0
version: 2.32.1-0ubuntu1.1
arch: amd64
libbonobo2-common
version: 2.32.1-0ubuntu1.1
arch: all
libboost-iostreams1.46.1
version: 1.46.1-7ubuntu3
arch: amd64
libbsd-dev
version: 0.3.0-2ubuntu0.1
arch: amd64
libbsd0
version: 0.3.0-2ubuntu0.1
arch: amd64
libbz2-1.0
version: 1.0.6-1ubuntu0.2
arch: amd64
libc-bin
version: 2.15-0ubuntu10.23
arch: amd64
libc-dev-bin
version: 2.19-0ubuntu6
arch: amd64
libc6
version: 2.19-0ubuntu6
arch: amd64
libc6-dev
version: 2.19-0ubuntu6
arch: amd64
libcairo-gobject2
version: 1.10.2-6.1ubuntu3
arch: amd64
libcairo2
version: 1.10.2-6.1ubuntu3
arch: amd64
libcanberra0
version: 0.28-3ubuntu3
arch: amd64
libcap-ng0
version: 0.6.6-1ubuntu1
arch: amd64
libcap2
version: 1:2.22-1ubuntu3
arch: amd64
libck-connector0
version: 0.4.5-2ubuntu0.1
arch: amd64
libclass-accessor-perl
version: 0.34-1
arch: all
libclass-isa-perl
version: 0.36-3
arch: all
libcomerr2
version: 1.42-1ubuntu2.5
arch: amd64
libcroco3
version: 0.6.5-1ubuntu0.1
arch: amd64
libcryptsetup4
version: 2:1.4.1-2ubuntu4
arch: amd64
libcups2
version: 1.5.3-0ubuntu8.7
arch: amd64
libcurl3
version: 7.22.0-3ubuntu4.29
arch: amd64
libcurl3-gnutls
version: 7.22.0-3ubuntu4.29
arch: amd64
libcwidget3
version: 0.5.16-3.1ubuntu1
arch: amd64
libdatrie1
version: 0.2.5-3
arch: amd64
libdb5.1
version: 5.1.25-11ubuntu0.1
arch: amd64
libdbi1
version: 0.8.4-5.1
arch: amd64
libdbus-1-3
version: 1.4.18-1ubuntu1.10
arch: amd64
libdbus-glib-1-2
version: 0.98-1ubuntu1.1
arch: amd64
libdconf0
version: 0.12.0-0ubuntu1.1
arch: amd64
libdevmapper-event1.02.1
version: 2:1.02.48-4ubuntu7.4
arch: amd64
libdevmapper1.02.1
version: 2:1.02.48-4ubuntu7.4
arch: amd64
libdns81
version: 1:9.8.1.dfsg.P1-4ubuntu0.32
arch: amd64
libdpkg-perl
version: 1.16.1.2ubuntu7.9
arch: all
libdrm-intel1
version: 2.4.52-1~precise2
arch: amd64
libdrm-nouveau1a
version: 2.4.52-1~precise2
arch: amd64
libdrm-radeon1
version: 2.4.52-1~precise2
arch: amd64
libdrm2
version: 2.4.52-1~precise2
arch: amd64
libedit2
version: 2.11-20080614-3ubuntu2
arch: amd64
libelf1
version: 0.152-1ubuntu3.1
arch: amd64
libept1.4.12
version: 1.0.6~exp1ubuntu1
arch: amd64
liberror-perl
version: 0.17-1
arch: all
libevent-2.0-5
version: 2.0.16-stable-1ubuntu0.2
arch: amd64
libexpat1
version: 2.0.1-7.2ubuntu1.7
arch: amd64
libffi-dev
version: 3.0.11~rc1-5ubuntu0.1
arch: amd64
libffi6
version: 3.0.11~rc1-5ubuntu0.1
arch: amd64
libfl-dev
version: 2.5.35-10ubuntu3
arch: amd64
libflac8
version: 1.2.1-6ubuntu0.1
arch: amd64
libfontconfig1
version: 2.8.0-3ubuntu9.2
arch: amd64
libfontenc1
version: 1:1.1.0-1
arch: amd64
libfreetype6
version: 2.4.8-1ubuntu2.7
arch: amd64
libfribidi0
version: 0.19.2-1
arch: amd64
libfsplib0
version: 0.11-2
arch: amd64
libfuse2
version: 2.8.6-2ubuntu2.1
arch: amd64
libgc1c2
version: 1:7.1-8ubuntu0.12.04.3
arch: amd64
libgcc1
version: 1:4.6.3-1ubuntu5
arch: amd64
libgconf-2-4
version: 3.2.5-0ubuntu2
arch: amd64
libgconf2-4
version: 3.2.5-0ubuntu2
arch: amd64
libgcrypt11
version: 1.5.3-2ubuntu4
arch: amd64
libgd2-noxpm
version: 2.0.36~rc1~dfsg-6ubuntu2.6
arch: amd64
libgdbm3
version: 1.8.3-10
arch: amd64
libgdk-pixbuf2.0-0
version: 2.26.1-1ubuntu1.5
arch: amd64
libgdk-pixbuf2.0-common
version: 2.26.1-1ubuntu1.5
arch: all
libgdu0
version: 3.0.2-2ubuntu7
arch: amd64
libgeoip1
version: 1.4.8+dfsg-2
arch: amd64
libgettextpo0
version: ********-5ubuntu3.1
arch: amd64
libghc-zlib-dev
version: *******-1
arch: amd64
libgif4
version: 4.1.6-9ubuntu1
arch: amd64
libgirepository-1.0-1
version: 1.32.0-1
arch: amd64
libgl1-mesa-dri
version: 8.0.4-0ubuntu0.7
arch: amd64
libgl1-mesa-glx
version: 8.0.4-0ubuntu0.7
arch: amd64
libglapi-mesa
version: 8.0.4-0ubuntu0.7
arch: amd64
libglib2.0-0
version: 2.32.4-0ubuntu1.4
arch: amd64
libgmp-dev
version: 2:5.0.2+dfsg-2ubuntu1
arch: amd64
libgmp10
version: 2:5.0.2+dfsg-2ubuntu1
arch: amd64
libgmpxx4ldbl
version: 2:5.0.2+dfsg-2ubuntu1
arch: amd64
libgnome-keyring-common
version: 3.2.2-2
arch: all
libgnome-keyring0
version: 3.2.2-2
arch: amd64
libgnome2-0
version: 2.32.1-2ubuntu1.1
arch: amd64
libgnome2-bin
version: 2.32.1-2ubuntu1.1
arch: amd64
libgnome2-common
version: 2.32.1-2ubuntu1.1
arch: all
libgnomevfs2-0
version: 1:2.24.4-1ubuntu2.1
arch: amd64
libgnomevfs2-common
version: 1:2.24.4-1ubuntu2.1
arch: amd64
libgnutls26
version: 2.12.23-12ubuntu2
arch: amd64
libgomp1
version: 4.6.3-1ubuntu5
arch: amd64
libgpg-error0
version: 1.10-2ubuntu1
arch: amd64
libgpm2
version: 1.20.4-4
arch: amd64
libgsasl7
version: 1.6.1-1
arch: amd64
libgssapi-krb5-2
version: 1.10+dfsg~beta1-2ubuntu0.7
arch: amd64
libgssapi3-heimdal
version: 1.6~git20120311.dfsg.1-2ubuntu0.2
arch: amd64
libgtk-3-0
version: 3.4.2-0ubuntu0.9
arch: amd64
libgtk-3-bin
version: 3.4.2-0ubuntu0.9
arch: amd64
libgtk-3-common
version: 3.4.2-0ubuntu0.9
arch: all
libgtk2.0-0
version: 2.24.10-0ubuntu6.3
arch: amd64
libgtk2.0-bin
version: 2.24.10-0ubuntu6.3
arch: amd64
libgtk2.0-common
version: 2.24.10-0ubuntu6.3
arch: all
libgudev-1.0-0
version: 1:175-0ubuntu9.8
arch: amd64
libhcrypto4-heimdal
version: 1.6~git20120311.dfsg.1-2ubuntu0.2
arch: amd64
libheimbase1-heimdal
version: 1.6~git20120311.dfsg.1-2ubuntu0.2
arch: amd64
libheimntlm0-heimdal
version: 1.6~git20120311.dfsg.1-2ubuntu0.2
arch: amd64
libhx509-5-heimdal
version: 1.6~git20120311.dfsg.1-2ubuntu0.2
arch: amd64
libice-dev
version: 2:1.0.7-2build1
arch: amd64
libice6
version: 2:1.0.7-2build1
arch: amd64
libidl-common
version: 0.8.14-0.2ubuntu2
arch: all
libidl0
version: 0.8.14-0.2ubuntu2
arch: amd64
libidn11
version: 1.23-2ubuntu0.2
arch: amd64
libio-string-perl
version: 1.08-2
arch: all
libisc83
version: 1:9.8.1.dfsg.P1-4ubuntu0.32
arch: amd64
libisccc80
version: 1:9.8.1.dfsg.P1-4ubuntu0.32
arch: amd64
libisccfg82
version: 1:9.8.1.dfsg.P1-4ubuntu0.32
arch: amd64
libiw30
version: 30~pre9-5ubuntu2
arch: amd64
libjasper1
version: 1.900.1-13ubuntu0.3
arch: amd64
libjpeg-turbo8
version: 1.1.90+svn733-0ubuntu4.6
arch: amd64
libjpeg8
version: 8c-2ubuntu7
arch: amd64
libjs-jquery
version: 1.7.1-1ubuntu1
arch: all
libjs-mochikit
version: 1.4.2-3fakesync1
arch: all
libjson0
version: 0.9-1ubuntu1.4
arch: amd64
libk5crypto3
version: 1.10+dfsg~beta1-2ubuntu0.7
arch: amd64
libkeyutils1
version: 1.5.2-2
arch: amd64
libklibc
version: 1.5.25-1ubuntu2
arch: amd64
libkmod2
version: 15-0ubuntu6
arch: amd64
libkrb5-26-heimdal
version: 1.6~git20120311.dfsg.1-2ubuntu0.2
arch: amd64
libkrb5-3
version: 1.10+dfsg~beta1-2ubuntu0.7
arch: amd64
libkrb5support0
version: 1.10+dfsg~beta1-2ubuntu0.7
arch: amd64
liblcms2-2
version: 2.2+git20110628-2ubuntu3.3
arch: amd64
libldap-2.4-2
version: 2.4.28-1.1ubuntu4.12
arch: amd64
libllvm3.0
version: 3.0-4ubuntu1
arch: amd64
liblocale-gettext-perl
version: 1.05-7build1
arch: amd64
liblockfile-bin
version: 1.09-3ubuntu0.1
arch: amd64
liblockfile1
version: 1.09-3ubuntu0.1
arch: amd64
libltdl-dev
version: 2.4.2-1ubuntu1
arch: amd64
libltdl7
version: 2.4.2-1ubuntu1
arch: amd64
liblua5.1-0
version: 5.1.4-12ubuntu1.1
arch: amd64
liblua50
version: 5.0.3-6
arch: amd64
liblualib50
version: 5.0.3-6
arch: amd64
liblvm2app2.2
version: 2.02.66-4ubuntu7.4
arch: amd64
liblwres80
version: 1:9.8.1.dfsg.P1-4ubuntu0.32
arch: amd64
liblzma5
version: 5.1.1alpha+20110809-3
arch: amd64
libmagic1
version: 5.09-2ubuntu0.8
arch: amd64
libmailutils2
version: 1:2.2+dfsg1-5
arch: amd64
libmount1
version: 2.20.1-1ubuntu3.1
arch: amd64
libmpc2
version: 0.9-4
arch: amd64
libmpfr4
version: 3.1.0-3ubuntu2
arch: amd64
libmysqlclient18
version: 5.5.62-0ubuntu0.12.04.1
arch: amd64
libncurses5
version: 5.9-4
arch: amd64
libncurses5-dev
version: 5.9-4
arch: amd64
libncursesw5
version: 5.9-4
arch: amd64
libnet1
version: 1.1.4-2.1
arch: amd64
libnettle4
version: 2.4-1ubuntu0.1
arch: amd64
libnewt0.52
version: 0.52.11-2ubuntu10
arch: amd64
libnfnetlink0
version: 1.0.0-1
arch: amd64
libnih-dbus1
version: 1.0.3-4ubuntu25
arch: amd64
libnih1
version: 1.0.3-4ubuntu25
arch: amd64
libnl-3-200
version: 3.2.3-2ubuntu2.1
arch: amd64
libnl-genl-3-200
version: 3.2.3-2ubuntu2.1
arch: amd64
libnspr4
version: 4.13.1-0ubuntu0.12.04.1
arch: amd64
libnss3
version: 2:3.28.4-0ubuntu0.12.04.11
arch: amd64
libnss3-1d
version: 2:3.28.4-0ubuntu0.12.04.11
arch: amd64
libntlm0
version: 1.2-1
arch: amd64
libogg0
version: 1.2.2~dfsg-1ubuntu1
arch: amd64
libopts25
version: 1:5.12-0.1ubuntu1
arch: amd64
libopts25-dev
version: 1:5.12-0.1ubuntu1
arch: amd64
liborbit2
version: 1:2.14.19-0.1ubuntu1
arch: amd64
libp11-kit0
version: 0.12-2ubuntu1
arch: amd64
libpam-ck-connector
version: 0.4.5-2ubuntu0.1
arch: amd64
libpam-modules
version: 1.1.3-7ubuntu2.3
arch: amd64
libpam-modules-bin
version: 1.1.3-7ubuntu2.3
arch: amd64
libpam-runtime
version: 1.1.3-7ubuntu2.3
arch: all
libpam0g
version: 1.1.3-7ubuntu2.3
arch: amd64
libpango1.0-0
version: 1.30.0-0ubuntu3.1
arch: amd64
libparse-debianchangelog-perl
version: 1.2.0-1ubuntu1
arch: all
libparted0debian1
version: 2.3-8ubuntu5.2
arch: amd64
libpcap0.8
version: 1.1.1-10ubuntu0.1
arch: amd64
libpci3
version: 1:3.1.8-2ubuntu6
arch: amd64
libpciaccess0
version: 0.12.902-1ubuntu0.2
arch: amd64
libpcre3
version: 8.12-4ubuntu0.2
arch: amd64
libpcsclite1
version: 1.7.4-2ubuntu2.1
arch: amd64
libperl5.14
version: 5.14.2-6ubuntu2.11
arch: amd64
libpipeline1
version: 1.2.1-1
arch: amd64
libpixman-1-0
version: 0.30.2-1ubuntu0.*******
arch: amd64
libplymouth2
version: 0.8.2-2ubuntu31.1
arch: amd64
libpng12-0
version: 1.2.46-3ubuntu4.3
arch: amd64
libpolkit-agent-1-0
version: 0.104-1ubuntu1.5
arch: amd64
libpolkit-backend-1-0
version: 0.104-1ubuntu1.5
arch: amd64
libpolkit-gobject-1-0
version: 0.104-1ubuntu1.5
arch: amd64
libpopt0
version: 1.16-3ubuntu1
arch: amd64
libpthread-stubs0
version: 0.3-3
arch: amd64
libpthread-stubs0-dev
version: 0.3-3
arch: amd64
libpulse0
version: 1:1.1-0ubuntu15.4
arch: amd64
libpython2.7
version: 2.7.3-0ubuntu3.19
arch: amd64
libquadmath0
version: 4.6.3-1ubuntu5
arch: amd64
libreadline5
version: 5.2-11
arch: amd64
libreadline6
version: 6.2-8
arch: amd64
librecode0
version: 3.6-18
arch: amd64
libroken18-heimdal
version: 1.6~git20120311.dfsg.1-2ubuntu0.2
arch: amd64
librrd4
version: 1.4.7-1
arch: amd64
librtmp0
version: 2.4~20110711.gitc28f1bab-1ubuntu0.1
arch: amd64
libruby1.8
version: 1.8.7.352-2ubuntu1.6
arch: amd64
libruby2.2
version: 2.2.6-1bbox1~precise1
arch: amd64
libsasl2-2
version: 2.1.25.dfsg1-3ubuntu0.2
arch: amd64
libsasl2-modules
version: 2.1.25.dfsg1-3ubuntu0.2
arch: amd64
libselinux1
version: 2.1.0-4.1ubuntu1
arch: amd64
libsensors4
version: 1:3.3.1-2ubuntu1
arch: amd64
libsgutils2-2
version: 1.33-1
arch: amd64
libsigc++-2.0-0c2a
version: 2.2.10-0ubuntu2
arch: amd64
libslang2
version: 2.2.4-3ubuntu1
arch: amd64
libsm-dev
version: 2:1.2.0-2build1
arch: amd64
libsm6
version: 2:1.2.0-2build1
arch: amd64
libsndfile1
version: 1.0.25-4ubuntu0.1
arch: amd64
libsnmp-base
version: 5.4.3~dfsg-2.4ubuntu1.6
arch: all
libsnmp15
version: 5.4.3~dfsg-2.4ubuntu1.6
arch: amd64
libsqlite3-0
version: 3.7.9-2ubuntu1.4
arch: amd64
libss2
version: 1.42-1ubuntu2.5
arch: amd64
libssl1.0.0
version: 1.0.1-4ubuntu5.45
arch: amd64
libstdc++6
version: 4.6.3-1ubuntu5
arch: amd64
libstdc++6-4.6-dev
version: 4.6.3-1ubuntu5
arch: amd64
libsub-name-perl
version: 0.05-1build2
arch: amd64
libswitch-perl
version: 2.16-2
arch: all
libtasn1-3
version: 2.10-1ubuntu1.6
arch: amd64
libtasn1-6
version: 3.4-3
arch: amd64
libtdb1
version: 1.2.9-4
arch: amd64
libtext-charwidth-perl
version: 0.04-7build1
arch: amd64
libtext-iconv-perl
version: 1.7-5
arch: amd64
libtext-wrapi18n-perl
version: 0.06-7
arch: all
libthai-data
version: 0.1.16-3
arch: all
libthai0
version: 0.1.16-3
arch: amd64
libtiff4
version: 3.9.5-2ubuntu1.12
arch: amd64
libtimedate-perl
version: 1.2000-1
arch: all
libtinfo-dev
version: 5.9-4
arch: amd64
libtinfo5
version: 5.9-4
arch: amd64
libtool
version: 2.4.2-1ubuntu1
arch: amd64
libtre5
version: 0.8.0-2
arch: amd64
libudev0
version: 175-0ubuntu9.8
arch: amd64
libunistring0
version: 0.9.3-5
arch: amd64
libusb-0.1-4
version: 2:0.1.12-20
arch: amd64
libusb-1.0-0
version: 2:1.0.9~rc3-2ubuntu1
arch: amd64
libuuid1
version: 2.20.1-1ubuntu3.1
arch: amd64
libvorbis0a
version: 1.3.2-1ubuntu3
arch: amd64
libvorbisenc2
version: 1.3.2-1ubuntu3
arch: amd64
libvorbisfile3
version: 1.3.2-1ubuntu3
arch: amd64
libwind0-heimdal
version: 1.6~git20120311.dfsg.1-2ubuntu0.2
arch: amd64
libwrap0
version: 7.6.q-21
arch: amd64
libx11-6
version: 2:1.4.99.1-0ubuntu2.5
arch: amd64
libx11-data
version: 2:1.4.99.1-0ubuntu2.5
arch: all
libx11-dev
version: 2:1.4.99.1-0ubuntu2.5
arch: amd64
libx11-doc
version: 2:1.4.99.1-0ubuntu2.5
arch: all
libx11-xcb1
version: 2:1.4.99.1-0ubuntu2.5
arch: amd64
libxapian22
version: 1.2.8-1
arch: amd64
libxau-dev
version: 1:1.0.6-4
arch: amd64
libxau6
version: 1:1.0.6-4
arch: amd64
libxaw7
version: 2:1.0.9-3ubuntu1
arch: amd64
libxcb-glx0
version: 1.8.1-1ubuntu0.2
arch: amd64
libxcb-render0
version: 1.8.1-1ubuntu0.2
arch: amd64
libxcb-shape0
version: 1.8.1-1ubuntu0.2
arch: amd64
libxcb-shm0
version: 1.8.1-1ubuntu0.2
arch: amd64
libxcb1
version: 1.8.1-1ubuntu0.2
arch: amd64
libxcb1-dev
version: 1.8.1-1ubuntu0.2
arch: amd64
libxcomposite1
version: 1:0.4.3-2build1
arch: amd64
libxcursor1
version: 1:1.1.12-1ubuntu0.1
arch: amd64
libxdamage1
version: 1:1.1.3-2build1
arch: amd64
libxdmcp-dev
version: 1:1.1.0-4
arch: amd64
libxdmcp6
version: 1:1.1.0-4
arch: amd64
libxext6
version: 2:1.3.0-3ubuntu0.2
arch: amd64
libxfixes3
version: 1:5.0-4ubuntu4.4
arch: amd64
libxft2
version: 2.2.0-3ubuntu2
arch: amd64
libxi6
version: 2:1.7.1.901-1ubuntu1~precise3
arch: amd64
libxinerama1
version: 2:1.1.1-3ubuntu0.1
arch: amd64
libxml2
version: 2.7.8.dfsg-5.1ubuntu4.22
arch: amd64
libxml2-dev
version: 2.7.8.dfsg-5.1ubuntu4.22
arch: amd64
libxmu6
version: 2:1.1.0-3
arch: amd64
libxmuu1
version: 2:1.1.0-3
arch: amd64
libxpm4
version: 1:3.5.9-4ubuntu0.1
arch: amd64
libxrandr2
version: 2:1.3.2-2ubuntu0.3
arch: amd64
libxrender1
version: 1:0.9.6-2ubuntu0.2
arch: amd64
libxslt1-dev
version: 1.1.26-8ubuntu1.6
arch: amd64
libxslt1.1
version: 1.1.26-8ubuntu1.6
arch: amd64
libxt-dev
version: 1:1.1.1-2ubuntu0.1
arch: amd64
libxt6
version: 1:1.1.1-2ubuntu0.1
arch: amd64
libxtst6
version: 2:1.2.0-4ubuntu0.1
arch: amd64
libxv1
version: 2:1.0.6-2ubuntu0.2
arch: amd64
libxxf86dga1
version: 2:1.1.2-1ubuntu0.1
arch: amd64
libxxf86vm1
version: 1:1.1.1-2ubuntu0.1
arch: amd64
libyaml-0-2
version: 0.1.4-2ubuntu0.12.04.4
arch: amd64
links
version: 2.6-1
arch: amd64
linux-firmware
version: 1.79.18
arch: all
linux-headers-3.2.0-126
version: 3.2.0-126.169
arch: all
linux-headers-3.2.0-126-virtual
version: 3.2.0-126.169
arch: amd64
linux-headers-3.2.0-150
version: 3.2.0-150.197
arch: all
linux-headers-3.2.0-150-virtual
version: 3.2.0-150.197
arch: amd64
linux-headers-3.2.0-94
version: 3.2.0-94.134
arch: all
linux-headers-3.2.0-94-virtual
version: 3.2.0-94.134
arch: amd64
linux-headers-virtual
version: 3.2.0.150.164
arch: amd64
linux-image-3.2.0-101-virtual
version: 3.2.0-101.141
arch: amd64
linux-image-3.2.0-102-virtual
version: 3.2.0-102.142
arch: amd64
linux-image-3.2.0-104-virtual
version: 3.2.0-104.145
arch: amd64
linux-image-3.2.0-106-virtual
version: 3.2.0-106.147
arch: amd64
linux-image-3.2.0-107-virtual
version: 3.2.0-107.148
arch: amd64
linux-image-3.2.0-109-virtual
version: 3.2.0-109.150
arch: amd64
linux-image-3.2.0-110-virtual
version: 3.2.0-110.151
arch: amd64
linux-image-3.2.0-111-virtual
version: 3.2.0-111.153
arch: amd64
linux-image-3.2.0-113-virtual
version: 3.2.0-113.155
arch: amd64
linux-image-3.2.0-115-virtual
version: 3.2.0-115.157
arch: amd64
linux-image-3.2.0-116-virtual
version: 3.2.0-116.158
arch: amd64
linux-image-3.2.0-118-virtual
version: 3.2.0-118.161
arch: amd64
linux-image-3.2.0-119-virtual
version: 3.2.0-119.162
arch: amd64
linux-image-3.2.0-120-virtual
version: 3.2.0-120.163
arch: amd64
linux-image-3.2.0-121-virtual
version: 3.2.0-121.164
arch: amd64
linux-image-3.2.0-122-virtual
version: 3.2.0-122.165
arch: amd64
linux-image-3.2.0-123-virtual
version: 3.2.0-123.166
arch: amd64
linux-image-3.2.0-124-virtual
version: 3.2.0-124.167
arch: amd64
linux-image-3.2.0-125-virtual
version: 3.2.0-125.168
arch: amd64
linux-image-3.2.0-126-virtual
version: 3.2.0-126.169
arch: amd64
linux-image-3.2.0-150-virtual
version: 3.2.0-150.197
arch: amd64
linux-image-3.2.0-68-virtual
version: 3.2.0-68.102
arch: amd64
linux-image-3.2.0-69-virtual
version: 3.2.0-69.103
arch: amd64
linux-image-3.2.0-70-virtual
version: 3.2.0-70.105
arch: amd64
linux-image-3.2.0-72-virtual
version: 3.2.0-72.107
arch: amd64
linux-image-3.2.0-73-virtual
version: 3.2.0-73.108
arch: amd64
linux-image-3.2.0-74-virtual
version: 3.2.0-74.109
arch: amd64
linux-image-3.2.0-75-virtual
version: 3.2.0-75.110
arch: amd64
linux-image-3.2.0-76-virtual
version: 3.2.0-76.111
arch: amd64
linux-image-3.2.0-77-virtual
version: 3.2.0-77.114
arch: amd64
linux-image-3.2.0-79-virtual
version: 3.2.0-79.115
arch: amd64
linux-image-3.2.0-80-virtual
version: 3.2.0-80.116
arch: amd64
linux-image-3.2.0-82-virtual
version: 3.2.0-82.119
arch: amd64
linux-image-3.2.0-83-virtual
version: 3.2.0-83.120
arch: amd64
linux-image-3.2.0-84-virtual
version: 3.2.0-84.121
arch: amd64
linux-image-3.2.0-85-virtual
version: 3.2.0-85.122
arch: amd64
linux-image-3.2.0-86-virtual
version: 3.2.0-86.124
arch: amd64
linux-image-3.2.0-87-virtual
version: 3.2.0-87.125
arch: amd64
linux-image-3.2.0-88-virtual
version: 3.2.0-88.126
arch: amd64
linux-image-3.2.0-89-virtual
version: 3.2.0-89.127
arch: amd64
linux-image-3.2.0-90-virtual
version: 3.2.0-90.128
arch: amd64
linux-image-3.2.0-91-virtual
version: 3.2.0-91.129
arch: amd64
linux-image-3.2.0-92-virtual
version: 3.2.0-92.131
arch: amd64
linux-image-3.2.0-93-virtual
version: 3.2.0-93.133
arch: amd64
linux-image-3.2.0-94-virtual
version: 3.2.0-94.134
arch: amd64
linux-image-3.2.0-95-virtual
version: 3.2.0-95.135
arch: amd64
linux-image-3.2.0-96-virtual
version: 3.2.0-96.136
arch: amd64
linux-image-3.2.0-97-virtual
version: 3.2.0-97.137
arch: amd64
linux-image-3.2.0-98-virtual
version: 3.2.0-98.138
arch: amd64
linux-image-3.2.0-99-virtual
version: 3.2.0-99.139
arch: amd64
linux-image-virtual
version: 3.2.0.150.164
arch: amd64
linux-libc-dev
version: 3.2.0-150.197
arch: amd64
linux-virtual
version: 3.2.0.150.164
arch: amd64
locales
version: 2.13+git20120306-3
arch: all
locate
version: 4.4.2-4ubuntu1
arch: amd64
lockfile-progs
version: 0.1.16
arch: amd64
login
version: 1:4.1.4.2+svn3283-3ubuntu5.2
arch: amd64
logrotate
version: 3.8.7-1ubuntu1
arch: amd64
lsb-base
version: 4.0-0ubuntu20.3
arch: all
lsb-release
version: 4.0-0ubuntu20.3
arch: all
lshw
version: 02.15-2
arch: amd64
lsof
version: 4.81.dfsg.1-1build1
arch: amd64
ltrace
version: 0.5.3-2.1ubuntu2
arch: amd64
lynx
version: 2.8.8dev.9-2ubuntu0.12.04.1
arch: all
lynx-cur
version: 2.8.8dev.9-2ubuntu0.12.04.1
arch: amd64
m4
version: 1.4.16-2ubuntu1
arch: amd64
mailutils
version: 1:2.2+dfsg1-5
arch: amd64
make
version: 3.81-8.1ubuntu1.1
arch: amd64
makedev
version: 2.3.1-89ubuntu2
arch: all
man-db
version: 2.6.1-2ubuntu2
arch: amd64
manpages
version: 3.35-0.1ubuntu1
arch: all
manpages-dev
version: 3.35-0.1ubuntu1
arch: all
mawk
version: 1.3.3-17
arch: amd64
mc
version: 3:4.8.1-2ubuntu1
arch: amd64
mc-data
version: 3:4.8.1-2ubuntu1
arch: all
memtest86+
version: 4.20-1.1ubuntu1
arch: amd64
mercurial
version: 2.0.2-1ubuntu1.2
arch: amd64
mercurial-common
version: 2.0.2-1ubuntu1.2
arch: all
mime-support
version: 3.51-1ubuntu1.1
arch: all
mlocate
version: 0.23.1-1ubuntu2
arch: amd64
module-init-tools
version: 15-0ubuntu6
arch: all
mount
version: 2.20.1-1ubuntu3.1
arch: amd64
mountall
version: 2.36.4
arch: amd64
mtools
version: 4.0.12-1ubuntu0.12.04.1
arch: amd64
mtr-tiny
version: 0.80-1ubuntu1
arch: amd64
multiarch-support
version: 2.15-0ubuntu10.23
arch: amd64
mysql-common
version: 5.5.62-0ubuntu0.12.04.1
arch: all
nano
version: 2.2.6-1
arch: amd64
ncurses-base
version: 5.9-4
arch: all
ncurses-bin
version: 5.9-4
arch: amd64
net-tools
version: 1.60-24.1ubuntu2
arch: amd64
netbase
version: 4.47ubuntu1
arch: all
netcat-openbsd
version: 1.89-4ubuntu1
arch: amd64
newrelic-sysmond
version: 2.2.0.125
arch: amd64
nginx
version: 1.1.19-1ubuntu0.7
arch: all
nginx-common
version: 1.1.19-1ubuntu0.7
arch: all
nginx-full
version: 1.1.19-1ubuntu0.7
arch: amd64
ngrep
version: 1.45.ds2-11
arch: amd64
nload
version: 0.7.3-1
arch: amd64
nmap
version: 5.21-1.1ubuntu1
arch: amd64
ntfs-3g
version: 1:2012.1.15AR.1-1ubuntu1.2
arch: amd64
ntop
version: 3:4.1.0+dfsg1-1
arch: amd64
ntop-data
version: 3:4.1.0+dfsg1-1
arch: all
ntp
version: 1:4.2.6.p3+dfsg-1ubuntu3.13
arch: amd64
ntpdate
version: 1:4.2.6.p3+dfsg-1ubuntu3.13
arch: amd64
openjdk-7-jdk
version: 7u79-2.5.6-0ubuntu1.12.04.1
arch: amd64
openjdk-7-jre
version: 7u79-2.5.6-0ubuntu1.12.04.1
arch: amd64
openjdk-7-jre-headless
version: 7u79-2.5.6-0ubuntu1.12.04.1
arch: amd64
openssh-client
version: 1:5.9p1-5ubuntu1.10
arch: amd64
openssh-server
version: 1:5.9p1-5ubuntu1.10
arch: amd64
openssl
version: 1.0.1f-1ubuntu2
arch: amd64
os-prober
version: 1.51ubuntu3
arch: amd64
parted
version: 2.3-8ubuntu5.2
arch: amd64
passwd
version: 1:4.1.4.2+svn3283-3ubuntu5.2
arch: amd64
patch
version: 2.6.1-3ubuntu0.2
arch: amd64
pciutils
version: 1:3.1.8-2ubuntu6
arch: amd64
perl
version: 5.14.2-6ubuntu2.11
arch: amd64
perl-base
version: 5.14.2-6ubuntu2.11
arch: amd64
perl-modules
version: 5.14.2-6ubuntu2.11
arch: all
pkg-config
version: 0.26-1ubuntu1
arch: amd64
plymouth
version: 0.8.2-2ubuntu31.1
arch: amd64
plymouth-theme-ubuntu-text
version: 0.8.2-2ubuntu31.1
arch: amd64
policykit-1
version: 0.104-1ubuntu1.5
arch: amd64
policykit-1-gnome
version: 0.105-1ubuntu3.1
arch: amd64
popularity-contest
version: 1.53ubuntu1
arch: all
postfix
version: 2.9.6-1~12.04.2
arch: amd64
powermgmt-base
version: 1.31
arch: amd64
ppp
version: 2.4.5-5ubuntu1.4
arch: amd64
pppconfig
version: 2.3.18+nmu3ubuntu1
arch: all
pppoeconf
version: 1.20ubuntu1
arch: all
procps
version: 1:3.2.8-11ubuntu6.3
arch: amd64
psmisc
version: 22.15-2ubuntu1.1
arch: amd64
python
version: 2.7.3-0ubuntu2.2
arch: amd64
python-apport
version: 2.0.1-0ubuntu17.16
arch: all
python-apt
version: 0.8.3ubuntu7.5
arch: amd64
python-apt-common
version: 0.8.3ubuntu7.5
arch: all
python-boto
version: 2.2.2-0ubuntu3
arch: all
python-bzrlib
version: 2.5.1-0ubuntu2.1
arch: amd64
python-chardet
version: 2.0.1-2build1
arch: all
python-cheetah
version: 2.4.4-2ubuntu3
arch: amd64
python-configobj
version: 4.7.2+ds-3build1
arch: all
python-crypto
version: 2.4.1-1ubuntu0.3
arch: amd64
python-dbus
version: 1.0.0-1ubuntu1
arch: amd64
python-dbus-dev
version: 1.0.0-1ubuntu1
arch: all
python-debian
version: 0.1.21ubuntu1
arch: all
python-gdbm
version: 2.7.3-1ubuntu1
arch: amd64
python-gi
version: 3.2.2-1~precise
arch: amd64
python-gnupginterface
version: 0.3.2-9.1ubuntu3
arch: all
python-httplib2
version: 0.7.2-1ubuntu2.1
arch: all
python-keyring
version: 0.9.2-0ubuntu0.12.04.2
arch: all
python-launchpadlib
version: 1.9.12-1
arch: all
python-lazr.restfulclient
version: 0.12.0-1ubuntu1.2
arch: all
python-lazr.uri
version: 1.0.3-1
arch: all
python-m2crypto
version: 0.21.1-2ubuntu2
arch: amd64
python-mako
version: 0.5.0-1
arch: all
python-markupsafe
version: 0.15-1
arch: amd64
python-minimal
version: 2.7.3-0ubuntu2.2
arch: amd64
python-newt
version: 0.52.11-2ubuntu10
arch: amd64
python-oauth
version: 1.0.1-3build1
arch: all
python-openssl
version: 0.12-1ubuntu2.1
arch: amd64
python-pam
version: 0.4.2-12.2ubuntu4
arch: amd64
python-paramiko
version: *******-2ubuntu1.2
arch: all
python-pip
version: 1.0-1build1
arch: all
python-pkg-resources
version: 0.6.24-1ubuntu1
arch: all
python-problem-report
version: 2.0.1-0ubuntu17.16
arch: all
python-pycurl
version: 7.19.0-4ubuntu3
arch: amd64
python-serial
version: 2.5-2.1build1
arch: all
python-setuptools
version: 0.6.24-1ubuntu1
arch: all
python-simplejson
version: 2.3.2-1
arch: amd64
python-software-properties
version: ********
arch: all
python-twisted-bin
version: 11.1.0-1ubuntu2
arch: amd64
python-twisted-core
version: 11.1.0-1ubuntu2
arch: all
python-twisted-names
version: 11.1.0-1
arch: all
python-twisted-web
version: 11.1.0-1
arch: all
python-wadllib
version: 1.3.0-2
arch: all
python-xapian
version: 1.2.8-1
arch: amd64
python-yaml
version: 3.10-2ubuntu0.1
arch: amd64
python-zope.interface
version: 3.6.1-1ubuntu3
arch: amd64
python2.7
version: 2.7.3-0ubuntu3.19
arch: amd64
python2.7-minimal
version: 2.7.3-0ubuntu3.19
arch: amd64
readline-common
version: 6.2-8
arch: all
resolvconf
version: 1.63ubuntu16
arch: all
rsync
version: 3.0.9-1ubuntu1.3
arch: amd64
rsyslog
version: 5.8.6-1ubuntu8.9
arch: amd64
ruby2.2
version: 2.2.6-1bbox1~precise1
arch: amd64
ruby2.2-dev
version: 2.2.6-1bbox1~precise1
arch: amd64
rubygems-integration
version: 1.8-1bbox1~precise1
arch: all
screen
version: 4.0.3-14ubuntu8.1
arch: amd64
sed
version: 4.2.1-9
arch: amd64
sensible-utils
version: 0.0.6ubuntu2
arch: all
sentinelagent
version: 21.10.3.3
arch: amd64
sgml-base
version: 1.26+nmu1ubuntu1
arch: all
shared-mime-info
version: 1.0-0ubuntu4.1
arch: amd64
shtool
version: 2.0.8-6
arch: all
snmp
version: 5.4.3~dfsg-2.4ubuntu1.6
arch: amd64
snmpd
version: 5.4.3~dfsg-2.4ubuntu1.6
arch: amd64
sound-theme-freedesktop
version: 0.7.pristine-2
arch: all
ssh-import-id
version: 2.10-0ubuntu1
arch: all
ssl-cert
version: 1.0.28ubuntu0.1
arch: all
strace
version: 4.5.20-2.3ubuntu1
arch: amd64
sudo
version: 1.8.3p1-1ubuntu3.10
arch: amd64
sysstat
version: 10.0.3-1
arch: amd64
sysv-rc
version: 2.88dsf-41ubuntu6
arch: all
sysvinit-utils
version: 2.88dsf-13.10ubuntu11.1
arch: amd64
tar
version: 1.26-4ubuntu1.2
arch: amd64
tasksel
version: 2.88ubuntu9
arch: all
tasksel-data
version: 2.88ubuntu9
arch: all
tcl8.4
version: 8.4.19-4ubuntu3
arch: amd64
tcpd
version: 7.6.q-21
arch: amd64
tcpdump
version: 4.9.3-0ubuntu0.12.04.1
arch: amd64
tcptraceroute
version: 1.5beta7+debian-4
arch: amd64
tcputils
version: 0.6.2-9
arch: amd64
telnet
version: 0.17-36build1
arch: amd64
time
version: 1.7-23.1
arch: amd64
tmux
version: 1.6-1ubuntu1
arch: amd64
traceroute
version: 1:2.0.18-1
arch: amd64
tree
version: 1.5.3-2
arch: amd64
ttf-dejavu
version: 2.33-2ubuntu1
arch: all
ttf-dejavu-core
version: 2.33-2ubuntu1
arch: all
ttf-dejavu-extra
version: 2.33-2ubuntu1
arch: all
tzdata
version: 2021a-0ubuntu0.12.04
arch: all
tzdata-java
version: 2021a-0ubuntu0.12.04
arch: all
ubuntu-keyring
version: 2011.11.21.1
arch: all
ubuntu-minimal
version: 1.267.1
arch: amd64
ubuntu-standard
version: 1.267.1
arch: amd64
ucf
version: 3.0025+nmu2ubuntu1
arch: all
udev
version: 175-0ubuntu9.8
arch: amd64
udisks
version: 1.0.4-5ubuntu2.2
arch: amd64
ufw
version: 0.31.1-1
arch: all
unattended-upgrades
version: 0.76ubuntu1.1
arch: all
unzip
version: 6.0-4ubuntu2.6
arch: amd64
update-manager-core
version: 1:***********
arch: amd64
update-notifier-common
version: 0.119ubuntu8.7
arch: all
upstart
version: 1.5-0ubuntu7.2
arch: amd64
ureadahead
version: 0.100.0-12
arch: amd64
usbutils
version: 1:005-1
arch: amd64
util-linux
version: 2.20.1-1ubuntu3.1
arch: amd64
uuid-runtime
version: 2.20.1-1ubuntu3.1
arch: amd64
vim
version: 2:7.3.429-2ubuntu2.3
arch: amd64
vim-common
version: 2:7.3.429-2ubuntu2.3
arch: amd64
vim-runtime
version: 2:7.3.429-2ubuntu2.3
arch: all
vim-tiny
version: 2:7.3.429-2ubuntu2.3
arch: amd64
w3m
version: 0.5.3-5ubuntu1.3
arch: amd64
wget
version: 1.13.4-2ubuntu1.7
arch: amd64
whiptail
version: 0.52.11-2ubuntu10
arch: amd64
whois
version: 5.0.15ubuntu2
arch: amd64
whoopsie
version: 0.1.34
arch: amd64
wireless-regdb
version: 2011.04.28-1ubuntu3
arch: all
wireless-tools
version: 30~pre9-5ubuntu2
arch: amd64
wpasupplicant
version: 0.7.3-6ubuntu2.5
arch: amd64
wwwconfig-common
version: 0.2.2
arch: all
x11-common
version: 1:7.6+12ubuntu2
arch: all
x11-utils
version: 7.6+4ubuntu0.1
arch: amd64
x11proto-core-dev
version: 7.0.22-1ubuntu0.2
arch: all
x11proto-input-dev
version: 2.3-1~precise2
arch: all
x11proto-kb-dev
version: 1.0.5-2
arch: all
xauth
version: 1:1.0.6-1
arch: amd64
xkb-data
version: 2.5-1ubuntu1.5
arch: all
xml-core
version: 0.13
arch: all
xorg-sgml-doctools
version: 1:1.10-1
arch: all
xtrans-dev
version: 1.2.6-2
arch: all
xz-lzma
version: 5.1.1alpha+20110809-3
arch: all
xz-utils
version: 5.1.1alpha+20110809-3
arch: amd64
zip
version: 3.0-4
arch: amd64
zlib1g
version: 1:*******.dfsg-3ubuntu4
arch: amd64
zlib1g-dev
version: 1:*******.dfsg-3ubuntu4
arch: amd64
ohai_time: **********.2719893
root_group: root
shells
0: /bin/sh
1: /bin/dash
2: /bin/bash
3: /bin/rbash
4: /usr/bin/tmux
5: /usr/bin/screen
cloud_v2
local_ipv4_addrs
0: ************
provider: ec2
local_hostname: ip-10-0-112-115.priv.evertrue.com
local_ipv4: ************
recipes
0: et_base
1: et_base::default
2: et_archiva
3: et_archiva::default
4: et_base_image::default
5: cookbook_versions_handler::default
6: chef_handler::default
7: et_base_image::openssl
8: apt::default
9: et_users::root
10: et_logger::default
11: rsyslog::default
12: et_logger::logrotate
13: et_hostname::default
14: et_base_image::handlers
15: chef-sentry-handler::default
16: et_users::sysadmins
17: et_users::reloadohai
18: et_users::evertrue
19: yum::default
20: sudo::default
21: git::default
22: git::package
23: chef-client::default
24: chef-client::service
25: chef-client::init_service
26: omnibus_updater::default
27: omnibus_updater::downloader
28: omnibus_updater::old_package_cleaner
29: omnibus_updater::installer
30: chef-client::config
31: build-essential::default
32: vim::default
33: vim::package
34: logrotate::default
35: networking_basic::default
36: et_tools::default
37: et_tools::vim
38: ssh_known_hosts::default
39: postfix::default
40: postfix::_common
41: postfix::_attributes
42: postfix::aliases
43: et_base_image::env
44: motd::knife_status
45: unattended-upgrades::default
46: et_base_image::autoremove
47: reboot_coordinator::default
48: reboot_coordinator::ohai
49: reboot_coordinator::legacy
50: et_base_image::rc_local
51: et_monitoring::agent
52: newrelic::default
53: newrelic::server_monitor_agent
54: et_monitoring::snmpd
55: snmp::default
56: et_monitoring::_livestatus
57: et_monitoring::aws
58: et_consul::client
59: et_consul::default
60: consul::default
61: chef-sugar::default
62: et_base::env
63: et_ntp_client::default
64: ntp::default
65: ntp::apparmor
66: storage::default
67: et_fog::default
68: storage::udev-fix
69: archiva::nginx_proxy
70: archiva::default
71: ark::default
72: java::default
73: java::set_attributes_from_version
74: java::openjdk
75: java::notify
76: java::default_java_symlink
77: java::set_java_home
78: chef_nginx::default
79: chef_nginx::package
80: chef_nginx::ohai_plugin
81: chef_nginx::repo
82: chef_nginx::commons
83: chef_nginx::commons_dir
84: chef_nginx::commons_script
85: chef_nginx::commons_conf
expanded_run_list
0: et_base::default
1: et_archiva::default
roles: 
cookbooks
et_base
version: 17.0.4
et_base_image
version: 1.0.4
chef_handler
version: 3.0.3
yum
version: 5.1.0
sudo
version: 5.4.0
build-essential
version: 8.2.1
seven_zip
version: 3.0.0
windows
version: 5.1.1
mingw
version: 2.1.0
vim
version: 2.0.2
logrotate
version: 2.2.0
apt
version: 6.1.4
motd
version: 0.6.4
postfix
version: 3.8.0
et_users
version: 2.0.1
et_hostname
version: 1.1.0
git
version: 4.6.0
dmg
version: 4.1.1
yum-epel
version: 3.1.0
chef-client
version: 7.1.0
cron
version: 6.1.1
et_networking_basic
version: 1.0.4
networking_basic
version: 0.0.7
et_tools
version: 1.1.2
et_logger
version: 9.1.0
rsyslog
version: 6.0.4
filebeat
version: 0.4.9
yum-plugin-versionlock
version: 0.2.1
runit
version: 4.1.1
packagecloud
version: 1.0.0
openssl
version: 8.1.2
network_interfaces
version: 1.0.0
modules
version: 0.2.0
cookbook_versions_handler
version: 1.0.0
unattended-upgrades
version: 0.1.2
ssh_known_hosts
version: 2.1.0
partial_search
version: 1.0.9
chef-sentry-handler
version: 1.0.0
omnibus_updater
version: 3.0.2
reboot_coordinator
version: 2.0.8
ohai
version: 5.2.5
magic
version: 1.5.0
et_ntp_client
version: 1.0.2
ntp
version: 3.5.1
et_monitoring
version: 3.1.4
newrelic
version: 2.31.1
poise-python
version: 1.7.0
poise
version: 2.8.1
poise-languages
version: 2.1.1
poise-archive
version: 1.5.0
curl
version: 2.0.3
et_cloudwatch
version: 1.0.5
et_fog
version: 4.0.2
threatstack
version: 1.7.3
snmp
version: 3.0.1
datadog
version: 2.13.0
et_consul
version: 4.2.0
consul
version: 2.3.0
nssm
version: 4.0.0
golang
version: 1.7.0
firewall
version: 2.6.2
chef-sugar
version: 4.0.0
poise-service
version: 1.5.2
et_archiva
version: 3.0.0
archiva
version: 2.0.1
ark
version: 3.1.0
java
version: 1.50.0
homebrew
version: 5.0.4
chef_nginx
version: 5.1.1
compat_resource
version: 12.19.1
zypper
version: 0.4.0
storage
version: 6.1.2
aws
version: 4.2.2
pending_reboot: false
▲
Copyright © 2012 – 2022 Chef Software, Inc.FeedbackWhat's New?About Chef ManageNeed help? If you have questions or you're stuck, we're here to help.
My Profile
My Account
Sign Out
evertrue
Manage Organizations

