#!/bin/bash

if [ -e $HOME/.chef_config ]; then
  . $HOME/.chef_config
fi

knife ec2 server create \
 --environment prod \
 --type infra \
 --subnet subnet-711bd61a \
 --node-name prod-consul-1b-1 \
 --security-group-ids sg-6faab617,sg-97f7edf5 \
 --run-list "recipe[et_base],recipe[et_consul::server],recipe[et_secrets],role[active_vault]" \
 --availability-zone us-east-1b \
 --image ami-0b0ea68c435eb488d \
 --flavor t3a.medium \
 --ssh-user ubuntu \
 --ebs-volume-type gp2 \
 --identity-file $HOME/.ssh/aws_dev.pem

knife ec2 server create \
 --environment prod \
 --type infra \
 --subnet subnet-421bd629 \
 --node-name prod-consul-1c-1 \
 --security-group-ids sg-6faab617,sg-97f7edf5 \
 --run-list "recipe[et_base],recipe[et_consul::server],recipe[et_secrets],role[active_vault]" \
 --availability-zone us-east-1c \
 --image ami-0b0ea68c435eb488d \
 --flavor t3a.medium \
 --ssh-user ubuntu \
 --ebs-volume-type gp2 \
 --identity-file $HOME/.ssh/aws_dev.pem

knife ec2 server create \
 --environment prod \
 --type infra \
 --subnet subnet-5e1bd635 \
 --node-name prod-consul-1d-1 \
 --security-group-ids sg-6faab617,sg-97f7edf5 \
 --run-list "recipe[et_base],recipe[et_consul::server],recipe[et_secrets],role[active_vault]" \
 --availability-zone us-east-1d \
 --image ami-0b0ea68c435eb488d \
 --flavor t3a.medium \
 --ssh-user ubuntu \
 --ebs-volume-type gp2 \
 --identity-file $HOME/.ssh/aws_dev.pem

# When the knife command tells you the private IP, ssh directly to the server with the ubuntu user, eg.
# $ ssh -i ~/.ssh/aws_dev.pem ubuntu@************

# wait for initial chef run (kicked off by the knife command) to fail, then...

echo 'deb      "http://apt.datadoghq.com" stable main' > /etc/apt/sources.list.d/datadog.list
chmod 644 /etc/apt/sources.list.d/datadog.list
mkdir -p /opt/consul/0.7.1
cp ~ubuntu/consul /opt/consul/0.7.1/
rm /etc/ssl/certs/DST_Root_CA_X3.pem; rm /opt/chef/embedded/ssl/certs/cacert.pem; ln -s /etc/ssl/certs/ca-certificates.crt /opt/chef/embedded/ssl/certs/cacert.pem

apt-get update
apt-get --allow-unauthenticated -q -y install datadog-agent=1:5.14.1-1

sudo chef-client

# ran without error now, almost hurray, still vault errors

# Fix issues with Consul no leader found

# you will need to stop chef-client and consul on all servers

/etc/init.d/chef-client stop
service consul stop

ps aux | grep consul # make sure it is not running

# Now on 1 of the servers that was previously running, create the peers.json file
# For info:  https://learn.hashicorp.com/tutorials/consul/recovery-outage#manual-recovery-using-peers-json

echo '["10.0.113.158:8300","10.0.111.39:8300","10.0.112.78:8300"]' > /var/lib/consul/raft/peers.json

service consul start

# Now you can start up chef-client and consul on the other servers

/etc/init.d/chef-client start
service consul start

# Now verify vault status (you may need to log out/in require environment variables that come from chef)

/opt/vault/0.6.0/vault status
