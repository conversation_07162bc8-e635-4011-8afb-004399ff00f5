#!/bin/bash

if [ -e $HOME/.chef_config ]; then
  . $HOME/.chef_config
fi

knife ec2 server create \
  --environment prod \
  --type infra \
  --subnet subnet-711bd61a \
  --node-name prod-storm-nimbus-1b \
  --security-group-ids sg-d6d1cab4,sg-97f7edf5 \
  --run-list "recipe[et_base],recipe[et_storm::nimbus],role[storm_nimbus]" \
  --availability-zone us-east-1b \
  --image ami-04ef7d8fec8ae87fc \
  --flavor t3a.medium \
  --ebs-volume-type gp2 \
  --ssh-user ubuntu \
  --identity-file $HOME/.ssh/aws_dev.pem

# When the knife command tells you the private IP, ssh directly to the server with the ubuntu user, eg.
# $ ssh -i ~/.ssh/aws_dev.pem ubuntu@************

# wait for initial chef run (kicked off by the knife command) to fail, then...

# 2. Fix the out of date cacerts.pem that chef uses
sudo apt install ca-certificates
sudo rm /etc/ssl/certs/DST_Root_CA_X3.pem
sudo update-ca-certificates
sudo rm /opt/chef/embedded/ssl/certs/cacert.pem
sudo ln -s /etc/ssl/certs/ca-certificates.crt /opt/chef/embedded/ssl/certs/cacert.pem

# 3. Now fix consul
sudo su -
mkdir -p /opt/consul/0.7.1
cd /opt/consul/0.7.1
curl -s https://releases.hashicorp.com/consul/0.7.1/consul_0.7.1_linux_amd64.zip -o consul_0.7.1_linux_amd64.zip
unzip consul_0.7.1_linux_amd64.zip
rm consul_0.7.1_linux_amd64.zip

start consul

# 4. Now re-run chef-client, it will fail again
sudo chef-client

# 5. Maually install datadog-agent
sudo apt-get --allow-unauthenticated -q -y install datadog-agent=1:5.14.1-1

# 6. Now re-run chef-client again
sudo chef-client

# deploy storm topologies

cd dev/contacts_storm
cap production deploy
# then again and specify contacts-geocode
cap production deploy

cd dev/users_storm
cap production deploy
