#!/bin/bash

knife ec2 server create \
  --environment prod \
  --type infra \
  --subnet subnet-7a1bd611 \
  --node-name prod-upload-1b \
  --security-group-ids sg-97f7edf5,sg-2f9ff148 \
  --run-list "recipe[et_base],recipe[et_upload]" \
  --availability-zone us-east-1b \
  --image ami-bbef47d0 \
  --flavor m1.small \
  --ssh-user ubuntu \
  --identity-file $HOME/.ssh/aws_dev.pem

# afterwards ssh into each server and run

# sudo chef-client

# consul was not properly installed need todo manually

# sudo su -
# cd /opt/consul/0.7.1
# curl -s https://releases.hashicorp.com/consul/0.7.1/consul_0.7.1_linux_amd64.zip -o consul_0.7.1_linux_amd64.zip
# unzip consul_0.7.1_linux_amd64.zip
# rm consul_0.7.1_linux_amd64.zip

# kill chef on the box

# /etc/init.d/chef-client stop

# kill all remaining chef processes

# ps aux | grep chef

# kill -9 <chef pid>

# there is was a bash script running to trying to start consul

# stop consul

# ps aux | grep consul

# you'll see

# consul   28329  0.0  0.0   4448   664 ?        Ss   Jan17   0:16 /bin/sh -e -c while ! /opt/consul/0.7.1/consul info ; do sleep 1; done /bin/sh

# Goto: https://www.youtube.com/watch?v=IuGjtlsKo4s and then
# kill -9 <pid>

# now start consul, by running the following command as root

# start consul

# manually fix sentry-raven failure due to faraday

# /usr/bin/gem2.2 install faraday -q --no-rdoc --no-ri -v "0.17.3"
# /usr/bin/gem2.2 install sentry-raven -q --no-rdoc --no-ri -v "2.13.0"
# /usr/bin/gem2.2 install pony -q --no-rdoc --no-ri -v "1.13.1"
# /usr/bin/gem2.2 install trollop -q --no-rdoc --no-ri -v "2.1.3"
# /usr/bin/gem2.2 install nokogiri -q --no-rdoc --no-ri -v "1.9.1"
# /usr/bin/gem2.2 install aws-sdk -q --no-rdoc --no-ri -v "1.67.0"
# /usr/bin/gem2.2 install rubyzip -q --no-rdoc --no-ri -v "1.3.0"


# /usr/bin/gem2.0 install faraday -q --no-rdoc --no-ri -v "0.17.3"
# /usr/bin/gem2.0 install sentry-raven -q --no-rdoc --no-ri -v "2.13.0"
# /usr/bin/gem2.0 install pony -q --no-rdoc --no-ri -v "1.13.1"
# /usr/bin/gem2.0 install trollop -q --no-rdoc --no-ri -v "2.1.3"
# /usr/bin/gem2.0 install nokogiri -q --no-rdoc --no-ri -v "1.6.8"
# /usr/bin/gem2.0 install aws-sdk -q --no-rdoc --no-ri -v "1.67.0"
# /usr/bin/gem2.0 install rubyzip -q --no-rdoc --no-ri -v "1.3.0"

