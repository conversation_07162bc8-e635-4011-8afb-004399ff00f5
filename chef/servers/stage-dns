#!/bin/bash

if [ -e $HOME/.chef_config ]; then
  . $HOME/.chef_config
fi

knife ec2 server create  \
 --environment stage \
 --subnet subnet-34e9de72 \
 --node-name stage-dns-1d \
 --security-group-ids sg-78e45f1d,sg-97e55ef2 \
 --run-list "recipe[et_base],recipe[et_ec2dnsserver]" \
 --image ami-04ef7d8fec8ae87fc \
 --availability-zone us-east-1d \
 --type infra \
 --ssh-user ubuntu \
 --identity-file $HOME/.ssh/aws_dev.pem \
 --ebs-volume-type gp2 \
 --flavor t3a.medium

# Add the DNS iam role to the new instance in the AWS console

# When the knife command tells you the private IP, ssh directly to the server with the ubuntu user, eg.
# $ ssh -i ~/.ssh/aws_dev.pem ubuntu@************

# 2. Fix the out of date cacerts.pem that chef uses
sudo apt install ca-certificates
sudo rm /etc/ssl/certs/DST_Root_CA_X3.pem
sudo update-ca-certificates
sudo rm /opt/chef/embedded/ssl/certs/cacert.pem
sudo ln -s /etc/ssl/certs/ca-certificates.crt /opt/chef/embedded/ssl/certs/cacert.pem

# 3. Now fix consul
sudo su -
mkdir -p /opt/consul/0.7.1
cd /opt/consul/0.7.1
curl -s https://releases.hashicorp.com/consul/0.7.1/consul_0.7.1_linux_amd64.zip -o consul_0.7.1_linux_amd64.zip
unzip consul_0.7.1_linux_amd64.zip
rm consul_0.7.1_linux_amd64.zip

start consul

# 4. Now re-run chef-client, it will fail again
sudo chef-client
