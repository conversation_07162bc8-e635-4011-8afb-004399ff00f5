#!/bin/bash

knife ec2 server create \
  --environment prod \
  --type infra \
  --subnet subnet-711bd61a \
  --node-name prod-searchmaster-v2-contacts-1b \
  --security-group-ids sg-66e13b1d,sg-8bcd72f1 \
  --run-list "recipe[et_base],recipe[et_contacts_search],role[es_master]" \
  --availability-zone us-east-1b \
  --image ami-0b0ea68c435eb488d \
  --flavor t3a.large \
  --ssh-user ubuntu \
  --ebs-volume-type gp2 \
  --identity-file $HOME/.ssh/aws_dev.pem

knife ec2 server create \
  --environment prod \
  --type infra \
  --subnet subnet-421bd629 \
  --node-name prod-searchmaster-v2-contacts-1c \
  --security-group-ids sg-66e13b1d,sg-8bcd72f1 \
  --run-list "recipe[et_base],recipe[et_contacts_search],role[es_master]" \
  --availability-zone us-east-1c \
  --image ami-0b0ea68c435eb488d \
  --flavor t3a.large \
  --ssh-user ubuntu \
  --ebs-volume-type gp2 \
  --identity-file $HOME/.ssh/aws_dev.pem

knife ec2 server create \
  --environment prod \
  --type infra \
  --subnet subnet-5e1bd635 \
  --node-name prod-searchmaster-v2-contacts-1d \
  --security-group-ids sg-66e13b1d,sg-8bcd72f1 \
  --run-list "recipe[et_base],recipe[et_contacts_search],role[es_master]" \
  --availability-zone us-east-1d \
  --image ami-0b0ea68c435eb488d \
  --flavor t3a.large \
  --ssh-user ubuntu \
  --ebs-volume-type gp2 \
  --identity-file $HOME/.ssh/aws_dev.pem

# When the knife command tells you the private IP, ssh directly to the server with the ubuntu user, eg.
# $ ssh -i ~/.ssh/aws_dev.pem ubuntu@************

# wait for initial chef run (kicked off by the knife command) to fail, then...

# 2. Fix the out of date cacerts.pem that chef uses
sudo apt install ca-certificates
sudo rm /etc/ssl/certs/DST_Root_CA_X3.pem
sudo update-ca-certificates
sudo rm /opt/chef/embedded/ssl/certs/cacert.pem
sudo ln -s /etc/ssl/certs/ca-certificates.crt /opt/chef/embedded/ssl/certs/cacert.pem

# 3. Now fix consul
sudo su -
mkdir -p /opt/consul/0.7.1
cd /opt/consul/0.7.1
curl -s https://releases.hashicorp.com/consul/0.7.1/consul_0.7.1_linux_amd64.zip -o consul_0.7.1_linux_amd64.zip
unzip consul_0.7.1_linux_amd64.zip
rm consul_0.7.1_linux_amd64.zip

start consul

# 4. Now re-run chef-client, it will fail again
sudo chef-client

# 5. Maually install datadog-agent
sudo apt-get --allow-unauthenticated -q -y install datadog-agent=1:5.14.1-1

# 6. Now re-run chef-client again
sudo chef-client

sudo service elasticsearch start

# update DNS
# update priv.evertrue.com in new account structure in evertrue-stage
# update A record for replacement server

# For reference from a working searchmaster

# addressable (2.4.0)
# appbundler (0.10.0)
# ast (2.3.0)
# bigdecimal (default: 1.2.8)
# binding_of_caller (0.7.2)
# builder (3.2.2)
# bundler (1.12.5)
# byebug (9.0.6)
# CFPropertyList (2.3.5)
# chef (12.17.44)
# chef-config (12.17.44)
# chef-handler-datadog (0.11.0)
# chef-sugar (5.1.12, 5.1.11, 5.1.9, 5.1.8, 5.0.1, 5.0.0, 4.2.1, 4.2.0, 4.1.0, 4.0.1, 4.0.0, 3.6.0, 3.4.0)
# chef-zero (5.1.0)
# cheffish (4.0.0)
# chefstyle (0.4.0)
# coderay (1.1.1)
# debug_inspector (0.0.2)
# did_you_mean (1.0.0)
# diff-lcs (1.2.5)
# docile (1.1.5)
# dogapi (1.27.0)
# domain_name (0.5.********)
# dry-inflector (0.1.2)
# erubis (2.7.0)
# excon (0.55.0)
# faraday (********)
# ffi (1.9.14)
# ffi-yajl (2.3.0)
# fission (0.5.0)
# fog (1.37.0)
# fog-aliyun (0.1.0)
# fog-atmos (0.1.0)
# fog-aws (1.3.0)
# fog-brightbox (0.11.0)
# fog-core (1.44.1)
# fog-dynect (0.0.3)
# fog-ecloud (0.3.0)
# fog-google (0.1.0)
# fog-json (1.0.2)
# fog-local (0.3.1)
# fog-powerdns (0.1.1)
# fog-profitbricks (3.0.0)
# fog-radosgw (0.0.5)
# fog-riakcs (0.1.0)
# fog-sakuracloud (1.7.5)
# fog-serverlove (0.1.2)
# fog-softlayer (1.1.4)
# fog-storm_on_demand (0.1.1)
# fog-terremark (0.1.0)
# fog-vmfusion (0.1.0)
# fog-voxel (0.1.0)
# fog-vsphere (1.9.1)
# fog-xenserver (0.3.0)
# fog-xml (0.1.3)
# formatador (0.2.5)
# fuzzyurl (0.9.0)
# hashie (3.4.6)
# highline (1.7.8)
# http-cookie (1.0.3)
# inflecto (0.0.2)
# iniparse (1.4.2)
# io-console (default: 0.4.5)
# ipaddress (0.8.3)
# json (default: 1.8.3)
# libyajl2 (1.2.0)
# method_source (0.8.2)
# mime-types (3.1, 2.6.2)
# mime-types-data (3.2016.0521)
# mini_portile2 (2.1.0)
# minitest (5.8.3)
# mixlib-archive (0.2.0)
# mixlib-authentication (1.4.1)
# mixlib-cli (1.7.0)
# mixlib-config (2.2.4)
# mixlib-log (1.7.1)
# mixlib-shellout (2.2.7)
# multi_json (1.12.1)
# multipart-post (2.0.0)
# net-scp (1.2.1)
# net-sftp (2.1.2)
# net-ssh (3.2.0)
# net-ssh-gateway (1.2.0)
# net-ssh-multi (1.2.1)
# net-telnet (0.1.1)
# netrc (0.11.0)
# nokogiri (*******)
# ohai (8.22.0)
# parser (*******)f
# plist (3.2.0)
# power_assert (0.2.6)
# powerpack (0.1.1)
# proxifier (1.0.3)
# pry (0.10.4)
# pry-byebug (3.4.1)
# pry-remote (0.1.8)
# pry-stack_explorer (*******)
# psych (default: 2.0.17)
# rack (2.0.1)
# rainbow (2.1.0)
# rake (11.3.0, 10.4.2)
# rb-readline (0.5.3)
# rbvmomi (1.11.2)
# rdoc (default: 4.2.1)
# rest-client (2.0.2, 1.7.3)
# rspec (3.5.0)
# rspec-core (3.5.4)
# rspec-expectations (3.5.0)
# rspec-its (1.2.0)
# rspec-mocks (3.5.0)
# rspec-support (3.5.0)
# rspec_junit_formatter (0.2.3)
# rubocop (0.45.0)
# ruby-prof (0.16.2)
# ruby-progressbar (1.8.1)
# ruby-shadow (2.5.0)
# rubygems-update (2.6.8)
# rubyzip (1.2.1)
# sentry-raven (0.9.4)
# serverspec (2.37.2)
# sfl (2.3)
# simplecov (0.12.0)
# simplecov-html (0.10.0)
# slop (3.6.0)
# specinfra (2.66.0)
# syslog-logger (1.6.8)
# systemu (2.6.5)
# test-unit (3.1.5)
# trollop (2.1.2)
# unf (0.1.4)
# unf_ext (*******)
# unicode-display_width (1.1.1)
# uuidtools (2.1.5)
# wmi-lite (1.0.0)
# xml-simple (1.1.5)
