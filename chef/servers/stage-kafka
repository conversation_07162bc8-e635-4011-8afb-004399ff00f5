#!/bin/bash

if [ -e $HOME/.chef_config ]; then
  . $HOME/.chef_config
fi

# us-east-1b
knife ec2 server create  \
 --environment stage \
 --subnet subnet-a792da8f \
 --node-name stage-kafka-3zq \
 --security-group-ids sg-97e55ef2,sg-a920d8ce \
 --run-list "recipe[et_base],recipe[et_kafka]" \
 --availability-zone us-east-1b \
 --image ami-04ef7d8fec8ae87fc \
 --type infra \
 --ssh-user ubuntu \
 --identity-file $HOME/.ssh/aws_dev.pem \
 --ebs-volume-type gp2 \
 --flavor t3a.medium

# us-east-1c
knife ec2 server create  \
 --environment stage \
 --subnet subnet-09b2b87d \
 --node-name stage-kafka-4uj \
 --security-group-ids sg-97e55ef2,sg-a920d8ce \
 --run-list "recipe[et_base],recipe[et_kafka]" \
 --availability-zone us-east-1c \
 --image ami-04ef7d8fec8ae87fc \
 --type infra \
 --ssh-user ubuntu \
 --identity-file $HOME/.ssh/aws_dev.pem \
 --ebs-volume-type gp2 \
 --flavor t3a.medium

# us-east-1d
knife ec2 server create  \
 --environment stage \
 --subnet subnet-34e9de72 \
 --node-name stage-kafka-yc6 \
 --security-group-ids sg-97e55ef2,sg-a920d8ce \
 --run-list "recipe[et_base],recipe[et_kafka]" \
 --availability-zone us-east-1d \
 --image ami-04ef7d8fec8ae87fc \
 --type infra \
 --ssh-user ubuntu \
 --identity-file $HOME/.ssh/aws_dev.pem \
 --ebs-volume-type gp2 \
 --flavor t3a.medium

# When the knife command tells you the private IP, ssh directly to the server with the ubuntu user, eg.
# $ ssh -i ~/.ssh/aws_dev.pem ubuntu@************

# wait for initial chef run (kicked off by the knife command) to fail, then...

# 2. Fix the out of date cacerts.pem that chef uses
su -
rm /etc/ssl/certs/DST_Root_CA_X3.pem; rm /opt/chef/embedded/ssl/certs/cacert.pem; ln -s /etc/ssl/certs/ca-certificates.crt /opt/chef/embedded/ssl/certs/cacert.pem

# 3. Now fix consul
sudo su -
mkdir -p /opt/consul/0.7.1
cd /opt/consul/0.7.1
curl -s https://releases.hashicorp.com/consul/0.7.1/consul_0.7.1_linux_amd64.zip -o consul_0.7.1_linux_amd64.zip
unzip consul_0.7.1_linux_amd64.zip
rm consul_0.7.1_linux_amd64.zip

# attach a EBS volume to server for Kafka
./aws/attach-volume -n stage-kafka-4uj -s 20

# fix mounted FS
mkfs -t ext4 /dev/nvme1n1

# 6. Now re-run chef-client again
sudo chef-client

# fix checksum error with jce.zip
# log into another stage kafka server
# cd /opt
# tar zcf ~/java_jce.tgz java_jce/
# exit and scp locally
# and scp to new server
# now
# sudo su -
# cd /opt
# tar zxf ~youruser/java_jce.tgz
# run chef-client

# when replacing a kafka server, you need to assign it the old broker id
# service stop kafka
# vi /mnt/dev0/kafka/meta.properties
# change the broker.id and save wq!
# service start kafka

# update DNS
# update priv.evertrue.com in new account structure in evertrue-stage
# update A record for replacement server

Broker IDs

stage-kafka-3zq 5
stage-kafka-4uj 3
stage-kafka-yc6 4

