#!/bin/bash

if [ -e $HOME/.chef_config ]; then
  . $HOME/.chef_config
fi

knife ec2 server create \
  --environment stage \
  --type infra \
  --subnet subnet-711bd61a \
  --node-name prod-monitor-1b \
  --security-group-ids sg-97f7edf5,sg-d9bec8bd \
  --run-list "recipe[et_base],recipe[et_monitor_server]" \
  --availability-zone us-east-1b \
  --image ami-0b0ea68c435eb488d \
  --flavor t3a.medium \
  --ssh-user ubuntu \
  --identity-file $HOME/.ssh/aws_dev.pem

# When the knife command tells you the private IP, ssh directly to the server with the ubuntu user, eg.
# $ ssh -i ~/.ssh/aws_dev.pem ubuntu@************

# wait for initial chef run (kicked off by the knife command) to fail, then...

# 1. manually fix some gems:
sudo /opt/chef/embedded/bin/gem install faraday -v "1.0.1"
sudo /opt/chef/embedded/bin/gem install public_suffix -v "4.0.7"
sudo /opt/chef/embedded/bin/gem uninstall fog-aliyun

# need to install some build tools for the next gem install
sudo apt install build-essential
sudo /opt/chef/embedded/bin/gem install fog-aliyun -v "0.3.19"


# 2. Fix the out of date cacerts.pem that chef uses
sudo apt install ca-certificates
sudo rm /etc/ssl/certs/DST_Root_CA_X3.pem
sudo update-ca-certificates
sudo rm /opt/chef/embedded/ssl/certs/cacert.pem
sudo ln -s /etc/ssl/certs/ca-certificates.crt /opt/chef/embedded/ssl/certs/cacert.pem

# 3. Now fix consul
sudo su -
mkdir -p /opt/consul/0.7.1
cd /opt/consul/0.7.1
curl -s https://releases.hashicorp.com/consul/0.7.1/consul_0.7.1_linux_amd64.zip -o consul_0.7.1_linux_amd64.zip
unzip consul_0.7.1_linux_amd64.zip
rm consul_0.7.1_linux_amd64.zip

start consul

# 4. Now re-run chef-client, it will fail again
sudo chef-client

# 5. Maually install datadog-agent
sudo apt-get --allow-unauthenticated -q -y install datadog-agent=1:5.14.1-1

# 6. Now re-run chef-client again
sudo chef-client


# this may no longer be true with ami-0b0ea68c435eb488d as that is 16.04 with does have ruby 2.3.0
# Now you will need to install v3.0.0 of the pagerduty gem as v4.x requires Ruby 2.3, which we dont have
sudo /usr/bin/gem install pagerduty -v 3.0.0