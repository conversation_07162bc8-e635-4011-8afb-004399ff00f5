#!/bin/bash

if [ -e $HOME/.chef_config ]; then
  . $HOME/.chef_config
fi

knife ec2 server create \
  --environment stage \
  --type infra \
  --subnet subnet-a792da8f \
  --node-name stage-searchmaster-v2-contacts-1b \
  --security-group-ids sg-0d579f77,sg-13201e6b \
  --run-list "recipe[et_base],recipe[et_contacts_search],role[es_master]" \
  --availability-zone us-east-1b \
  --image ami-0b0ea68c435eb488d \
  --flavor t3a.small \
  --ebs-volume-type gp2 \
  --ssh-user ubuntu \
  --identity-file $HOME/.ssh/aws_dev.pem

knife ec2 server create \
  --environment stage \
  --type infra \
  --subnet subnet-09b2b87d \
  --node-name stage-searchmaster-v2-contacts-1c \
  --security-group-ids sg-0d579f77,sg-13201e6b \
  --run-list "recipe[et_base],recipe[et_contacts_search],role[es_master]" \
  --availability-zone us-east-1c \
  --image ami-0b0ea68c435eb488d \
  --flavor t3a.small \
  --ebs-volume-type gp2 \
  --ssh-user ubuntu \
  --identity-file $HOME/.ssh/aws_dev.pem

knife ec2 server create \
  --environment stage \
  --type infra \
  --subnet subnet-34e9de72 \
  --node-name stage-searchmaster-v2-contacts-1d \
  --security-group-ids sg-0d579f77,sg-13201e6b \
  --run-list "recipe[et_base],recipe[et_contacts_search],role[es_master]" \
  --availability-zone us-east-1d \
  --image ami-0b0ea68c435eb488d \
  --flavor t3a.small \
  --ebs-volume-type gp2 \
  --ssh-user ubuntu \
  --identity-file $HOME/.ssh/aws_dev.pem

# When the knife command tells you the private IP, ssh directly to the server with the ubuntu user, eg.
# $ ssh -i ~/.ssh/aws_dev.pem ubuntu@************

# wait for initial chef run (kicked off by the knife command) to fail, then...

# 1. manually fix some gems:
sudo /opt/chef/embedded/bin/gem install faraday -v "1.0.1"
sudo /opt/chef/embedded/bin/gem install public_suffix -v "4.0.7"
sudo /opt/chef/embedded/bin/gem uninstall fog-aliyun

# need to install some build tools for the next gem install
sudo apt install build-essential
sudo /opt/chef/embedded/bin/gem install fog-aliyun -v "0.3.19"


# 2. Fix the out of date cacerts.pem that chef uses
sudo apt install ca-certificates
sudo rm /etc/ssl/certs/DST_Root_CA_X3.pem
sudo update-ca-certificates
sudo rm /opt/chef/embedded/ssl/certs/cacert.pem
sudo ln -s /etc/ssl/certs/ca-certificates.crt /opt/chef/embedded/ssl/certs/cacert.pem

# 3. Now fix consul
sudo su -
mkdir -p /opt/consul/0.7.1
cd /opt/consul/0.7.1
curl -s https://releases.hashicorp.com/consul/0.7.1/consul_0.7.1_linux_amd64.zip -o consul_0.7.1_linux_amd64.zip
unzip consul_0.7.1_linux_amd64.zip
rm consul_0.7.1_linux_amd64.zip

start consul

# 4. Now re-run chef-client, it will fail again
sudo chef-client

# 5. Maually install datadog-agent
sudo apt-get --allow-unauthenticated -q -y install datadog-agent=1:5.14.1-1

# 6. Now re-run chef-client again
sudo chef-client

# manually install gems for elasticsearch_s3_backup
/usr/bin/gem install minitest -q --no-rdoc --no-ri -v "5.10.1"
/usr/bin/gem install i18n -q --no-rdoc --no-ri -v "0.8.1"
/usr/bin/gem install unirest -q --no-rdoc --no-ri -v 1.1.2
/usr/bin/gem install trollop -q --no-rdoc --no-ri -v 2.1.2
/usr/bin/gem install tzinfo -q --no-rdoc --no-ri -v 1.2.3
/usr/bin/gem install multipart-post -q --no-rdoc --no-ri -v 2.0.0
/usr/bin/gem install faraday -q --no-rdoc --no-ri -v ********
/usr/bin/gem install elasticsearch -q --no-rdoc --no-ri -v 5.0.3
/usr/bin/gem install concurrent-ruby -q --no-rdoc --no-ri -v 1.0.5
/usr/bin/gem install activesupport -q --no-rdoc --no-ri -v 5.0.2
/usr/bin/gem install faker -q --no-rdoc --no-ri -v 1.7.3
/usr/bin/gem install multi_json -q --no-rdoc --no-ri -v 1.12.1
/usr/bin/gem install sentry-raven -q --no-rdoc --no-ri -v 2.4.0
/usr/bin/gem install pagerduty -q --no-rdoc --no-ri -v 2.1.0

# adjust based on latest gems installed, use /usr/bin/gem list
/usr/bin/gem uninstall tzinfo -v 2.0.4
/usr/bin/gem uninstall trollop -v 2.9.10
/usr/bin/gem uninstall concurrent-ruby -v 1.1.10
/usr/bin/gem uninstall multi_json -v 1.15.0

/usr/bin/gem install elasticsearch_s3_backup -q --no-rdoc --no-ri -v "2.0.7"

sudo chef-client
sudo service elasticsearch start

# update DNS
# update priv.evertrue.com in new account structure in evertrue-stage
# update A record for replacement server

