#!/bin/bash

if [ -e $HOME/.chef_config ]; then
  . $HOME/.chef_config
fi

knife ec2 server create  \
 --environment prod \
 --subnet subnet-711bd61a \
 --node-name prod-zookeeper-1b \
 --security-group-ids sg-3d6e1e59,sg-97f7edf5,sg-9d4e54ff \
 --run-list "recipe[et_base],recipe[ntp_cluster],role[ntp_server],recipe[et_zookeeper],role[zookeeper]" \
 --image ami-04ef7d8fec8ae87fc \
 --availability-zone us-east-1b \
 --type infra \
 --ssh-user ubuntu \
 --ebs-volume-type gp2 \
 --identity-file $HOME/.ssh/aws_dev.pem \
 --flavor t3a.large

knife ec2 server create  \
 --environment prod \
 --subnet subnet-421bd629 \
 --node-name prod-zookeeper-1c \
 --security-group-ids sg-3d6e1e59,sg-97f7edf5,sg-9d4e54ff \
 --run-list "recipe[et_base],recipe[ntp_cluster],role[ntp_server],recipe[et_zookeeper],role[zookeeper]" \
 --image ami-04ef7d8fec8ae87fc \
 --availability-zone us-east-1c \
 --type infra \
 --ssh-user ubuntu \
 --ebs-volume-type gp2 \
 --identity-file $HOME/.ssh/aws_dev.pem \
 --flavor t3a.large

knife ec2 server create  \
 --environment prod \
 --subnet subnet-5e1bd635 \
 --node-name prod-zookeeper-1d \
 --security-group-ids sg-3d6e1e59,sg-97f7edf5,sg-9d4e54ff \
 --run-list "recipe[et_base],recipe[ntp_cluster],role[ntp_server],recipe[et_zookeeper],role[zookeeper]" \
 --image ami-04ef7d8fec8ae87fc \
 --availability-zone us-east-1d \
 --type infra \
 --ssh-user ubuntu \
 --ebs-volume-type gp2 \
 --identity-file $HOME/.ssh/aws_dev.pem \
 --flavor t3a.large

# When the knife command tells you the private IP, ssh directly to the server with the ubuntu user, eg.
# $ ssh -i ~/.ssh/aws_dev.pem ubuntu@************

sudo su -

echo 'deb      "http://apt.datadoghq.com" stable main' > /etc/apt/sources.list.d/datadog.list
chmod 644 /etc/apt/sources.list.d/datadog.list
mkdir -p /opt/consul/0.7.1
cp ~ubuntu/consul /opt/consul/0.7.1/
rm /etc/ssl/certs/DST_Root_CA_X3.pem; rm /opt/chef/embedded/ssl/certs/cacert.pem; ln -s /etc/ssl/certs/ca-certificates.crt /opt/chef/embedded/ssl/certs/cacert.pem

# format and mount drive
mkfs -t ext4 /dev/nvme1n1
mkdir /mnt/dev0
echo '/dev/nvme1n1 /mnt/dev0 ext4 defaults 0 2' >> /etc/fstab
mount /mnt/dev0

apt-get update
apt-get --allow-unauthenticated -q -y install datadog-agent=1:5.10.1-1
