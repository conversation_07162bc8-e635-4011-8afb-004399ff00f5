#!/bin/bash

if [ -e $HOME/.chef_config ]; then
  . $HOME/.chef_config
fi

# us-east-1b
knife ec2 server create  \
 --environment prod \
 --subnet subnet-711bd61a \
 --node-name prod-contacts-spark-1b-1 \
 --security-group-ids sg-04991660,sg-97f7edf5,sg-9e047fe0 \
 --run-list "recipe[et_base],recipe[et_contacts_spark],role[spark_master]" \
 --availability-zone us-east-1b \
 --image ami-0b0ea68c435eb488d \
 --type infra \
 --ssh-user ubuntu \
 --identity-file $HOME/.ssh/aws_dev.pem \
 --ebs-volume-type gp2 \
 --flavor r6i.xlarge

# us-east-1c
knife ec2 server create  \
 --environment prod \
 --subnet subnet-421bd629 \
 --node-name prod-contacts-spark-1c-1 \
 --security-group-ids sg-04991660,sg-97f7edf5,sg-9e047fe0 \
 --run-list "recipe[et_base],recipe[et_contacts_spark],role[spark_master]" \
 --availability-zone us-east-1c \
 --image ami-0b0ea68c435eb488d \
 --type infra \
 --ssh-user ubuntu \
 --identity-file $HOME/.ssh/aws_dev.pem \
 --ebs-volume-type gp2 \
 --flavor r6i.xlarge

# us-east-1d
knife ec2 server create  \
 --environment prod \
 --subnet subnet-5e1bd635 \
 --node-name prod-contacts-spark-1d-1 \
 --security-group-ids sg-04991660,sg-97f7edf5,sg-9e047fe0 \
 --run-list "recipe[et_base],recipe[et_contacts_spark],role[spark_master]" \
 --availability-zone us-east-1d \
 --image ami-0b0ea68c435eb488d \
 --type infra \
 --ssh-user ubuntu \
 --identity-file $HOME/.ssh/aws_dev.pem \
 --ebs-volume-type gp2 \
 --flavor r6i.xlarge

# When the knife command tells you the private IP, ssh directly to the server with the ubuntu user, eg.
# $ ssh -i ~/.ssh/aws_dev.pem ubuntu@************

# wait for initial chef run (kicked off by the knife command) to fail, then...

# fix checksum error with jce.zip
# log into another prod spark server
# cd /opt
# tar zcf ~/java_jce.tgz java_jce/
# exit and scp locally
# and scp to new server
# now
# sudo su -
# cd /opt
# tar zxf ~youruser/java_jce.tgz
# run chef-client

# Special instructions

# When you finally get to the end.  You will need to hand edit the /etc/init.d/spark-slave file.  In here is a list of spark masters.
# 1 of them will be ,:7077.  This is because the new server you are starting up is in the list.  But chef doesn't know the FQDN yet.

# Simple, removed this ,:7077 from the list.

# Run /etc/init.d/spark-slave start

# and kick off

chef-client

# Created deploy folder

mkdir /mnt/dev0/deploy
chown deploy:deploy /mnt/dev0/deploy/

# If this is a totally new server (not a replacement) do the following:

# Update cap and add the new server to config/deploy/production.rb. This is in the Github repo contacts_spark (https://github.com/evertrue/contacts_spark/blob/master/config/deploy/production.rb)

# Deploy contacts_spark
# from your local machine, do

cd contacts_spark
cap production deploy


echo 'deb      "http://apt.datadoghq.com" stable main' > /etc/apt/sources.list.d/datadog.list
chmod 644 /etc/apt/sources.list.d/datadog.list
mkdir -p /opt/consul/0.7.1
cp ~ubuntu/consul /opt/consul/0.7.1/
rm /etc/ssl/certs/DST_Root_CA_X3.pem; rm /opt/chef/embedded/ssl/certs/cacert.pem; ln -s /etc/ssl/certs/ca-certificates.crt /opt/chef/embedded/ssl/certs/cacert.pem
cd /opt/; tar zxf ~ubuntu/java_jce.tgz

# format and mount drive
mkfs -t ext4 /dev/nvme1n1
mkdir /mnt/dev0
echo '/dev/nvme1n1 /mnt/dev0 ext4 defaults 0 2' >> /etc/fstab
mount /mnt/dev0

apt-get update
apt-get --allow-unauthenticated -q -y install datadog-agent=1:5.14.1-1
