GEM
  remote: https://rubygems.org/
  remote: https://gem.fury.io/evertrue/
  specs:
    CFPropertyList (2.3.6)
    addressable (2.7.0)
      public_suffix (>= 2.0.2, < 5.0)
    aws-eventstream (1.0.3)
    aws-sdk (2.11.431)
      aws-sdk-resources (= 2.11.431)
    aws-sdk-core (2.11.431)
      aws-sigv4 (~> 1.0)
      jmespath (~> 1.0)
    aws-sdk-resources (2.11.431)
      aws-sdk-core (= 2.11.431)
    aws-sigv4 (1.1.0)
      aws-eventstream (~> 1.0, >= 1.0.2)
    awsutils (2.1.7)
      aws-sdk
      facets (~> 2.9)
      fog-aws (~> 0.11.0)
      rainbow (~> 2.0)
      trollop
    builder (3.2.4)
    chef (12.22.5)
      addressable
      bundler (>= 1.10)
      chef-config (= 12.22.5)
      chef-zero (>= 4.8, < 13)
      diff-lcs (~> 1.2, >= 1.2.4)
      erubis (~> 2.7)
      ffi-yajl (~> 2.2)
      highline (~> 1.6, >= 1.6.9)
      iniparse (~> 1.4)
      mixlib-archive (~> 0.4)
      mixlib-authentication (~> 1.4)
      mixlib-cli (~> 1.7)
      mixlib-log (~> 1.3)
      mixlib-shellout (~> 2.0)
      net-sftp (~> 2.1, >= 2.1.2)
      net-ssh (>= 2.9, < 5.0)
      net-ssh-multi (~> 1.2, >= 1.2.1)
      ohai (>= 8.6.0.alpha.1, < 13)
      plist (~> 3.2)
      proxifier (~> 1.0)
      rspec-core (~> 3.5)
      rspec-expectations (~> 3.5)
      rspec-mocks (~> 3.5)
      rspec_junit_formatter (~> 0.2.0)
      serverspec (~> 2.7)
      specinfra (~> 2.10)
      syslog-logger (~> 1.6)
      uuidtools (~> 2.1.5)
    chef-api (0.10.2)
      logify (~> 0.1)
      mime-types
    chef-config (12.22.5)
      addressable
      fuzzyurl
      mixlib-config (~> 2.0)
      mixlib-shellout (~> 2.0)
    chef-zero (5.3.2)
      ffi-yajl (~> 2.2)
      hashie (>= 2.0, < 4.0)
      mixlib-log (~> 1.3)
      rack (~> 2.0)
      uuidtools (~> 2.1)
    diff-lcs (1.3)
    dry-inflector (0.1.2)
    equatable (0.6.1)
    erubi (1.9.0)
    erubis (2.7.0)
    et-knife-ec2 (1.7.1)
      fog (~> 1.29.0)
      knife-windows (~> 1.0)
    excon (0.71.1)
    facets (2.9.3)
    ffi (1.12.1)
    ffi-yajl (2.3.3)
      libyajl2 (~> 1.2)
    fission (0.5.0)
      CFPropertyList (~> 2.2)
    fog (1.29.0)
      fog-atmos
      fog-aws (~> 0.0)
      fog-brightbox (~> 0.4)
      fog-core (~> 1.27, >= 1.27.4)
      fog-ecloud
      fog-json
      fog-local
      fog-powerdns (>= 0.1.1)
      fog-profitbricks
      fog-radosgw (>= 0.0.2)
      fog-riakcs
      fog-sakuracloud (>= 0.0.4)
      fog-serverlove
      fog-softlayer
      fog-storm_on_demand
      fog-terremark
      fog-vmfusion
      fog-voxel
      fog-xml (~> 0.1.1)
      ipaddress (~> 0.5)
      nokogiri (~> 1.5, >= 1.5.11)
    fog-atmos (0.1.0)
      fog-core
      fog-xml
    fog-aws (0.11.0)
      fog-core (~> 1.38)
      fog-json (~> 1.0)
      fog-xml (~> 0.1)
      ipaddress (~> 0.8)
    fog-brightbox (0.16.1)
      dry-inflector
      fog-core
      fog-json
      mime-types
    fog-core (1.45.0)
      builder
      excon (~> 0.58)
      formatador (~> 0.2)
    fog-ecloud (0.3.0)
      fog-core
      fog-xml
    fog-json (1.2.0)
      fog-core
      multi_json (~> 1.10)
    fog-local (0.6.0)
      fog-core (>= 1.27, < 3.0)
    fog-powerdns (0.2.0)
      fog-core
      fog-json
      fog-xml
    fog-profitbricks (4.1.1)
      fog-core (~> 1.42)
      fog-json (~> 1.0)
    fog-radosgw (0.0.5)
      fog-core (>= 1.21.0)
      fog-json
      fog-xml (>= 0.0.1)
    fog-riakcs (0.1.0)
      fog-core
      fog-json
      fog-xml
    fog-sakuracloud (1.7.5)
      fog-core
      fog-json
    fog-serverlove (0.1.2)
      fog-core
      fog-json
    fog-softlayer (1.1.4)
      fog-core
      fog-json
    fog-storm_on_demand (0.1.1)
      fog-core
      fog-json
    fog-terremark (0.1.0)
      fog-core
      fog-xml
    fog-vmfusion (0.1.0)
      fission
      fog-core
    fog-voxel (0.1.0)
      fog-core
      fog-xml
    fog-xml (0.1.3)
      fog-core
      nokogiri (>= 1.5.11, < 2.0.0)
    formatador (0.2.5)
    fuzzyurl (0.9.0)
    gssapi (1.3.0)
      ffi (>= 1.0.1)
    gyoku (1.3.1)
      builder (>= 2.1.2)
    hashie (3.6.0)
    highline (1.7.10)
    httpclient (2.8.3)
    iniparse (1.4.4)
    ipaddress (0.8.3)
    jmespath (1.4.0)
    kitchen-ec2 (1.3.2)
      aws-sdk (~> 2)
      excon
      multi_json
      retryable (~> 2.0)
      test-kitchen (~> 1.4, >= 1.4.1)
    kitchen-sync (2.2.0)
      net-sftp
      test-kitchen (>= 1.0.0)
    knife-stalenodes (1.2.0)
      chef
    knife-windows (1.9.6)
      winrm (~> 2.1)
      winrm-elevated (~> 1.0)
    libyajl2 (1.2.0)
    license-acceptance (1.0.13)
      pastel (~> 0.7)
      tomlrb (~> 1.2)
      tty-box (~> 0.3)
      tty-prompt (~> 0.18)
    little-plugger (1.1.4)
    logging (2.2.2)
      little-plugger (~> 1.1)
      multi_json (~> 1.10)
    logify (0.2.0)
    mime-types (3.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2016.0521)
    mini_portile2 (2.4.0)
    mixlib-archive (0.4.1)
      mixlib-log
    mixlib-authentication (1.4.2)
    mixlib-cli (1.7.0)
    mixlib-config (2.2.4)
    mixlib-install (3.11.26)
      mixlib-shellout
      mixlib-versioning
      thor
    mixlib-log (1.7.1)
    mixlib-shellout (2.4.4)
    mixlib-versioning (1.2.12)
    multi_json (1.14.1)
    necromancer (0.5.1)
    net-scp (2.0.0)
      net-ssh (>= 2.6.5, < 6.0.0)
    net-sftp (2.1.2)
      net-ssh (>= 2.6.5)
    net-ssh (4.2.0)
    net-ssh-gateway (2.0.0)
      net-ssh (>= 4.0.0)
    net-ssh-multi (1.2.1)
      net-ssh (>= 2.6.5)
      net-ssh-gateway (>= 1.2.0)
    net-telnet (0.1.1)
    nokogiri (1.10.7)
      mini_portile2 (~> 2.4.0)
    nori (2.6.0)
    ohai (8.24.1)
      chef-config (>= 12.5.0.alpha.1, < 14)
      ffi (~> 1.9)
      ffi-yajl (~> 2.2)
      ipaddress
      mixlib-cli
      mixlib-config (~> 2.0)
      mixlib-log (>= 1.7.1, < 2.0)
      mixlib-shellout (~> 2.0)
      plist (~> 3.1)
      systemu (~> 2.6.4)
      wmi-lite (~> 1.0)
    pastel (0.7.3)
      equatable (~> 0.6)
      tty-color (~> 0.5)
    plist (3.5.0)
    proxifier (1.0.3)
    public_suffix (4.0.3)
    rack (2.1.1)
    rainbow (2.2.2)
      rake
    rake (13.0.1)
    retryable (2.0.4)
    rspec (3.9.0)
      rspec-core (~> 3.9.0)
      rspec-expectations (~> 3.9.0)
      rspec-mocks (~> 3.9.0)
    rspec-core (3.9.1)
      rspec-support (~> 3.9.1)
    rspec-expectations (3.9.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.9.0)
    rspec-its (1.3.0)
      rspec-core (>= 3.0.0)
      rspec-expectations (>= 3.0.0)
    rspec-mocks (3.9.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.9.0)
    rspec-support (3.9.2)
    rspec_junit_formatter (0.2.3)
      builder (< 4)
      rspec-core (>= 2, < 4, != 2.12.0)
    rubyntlm (0.6.2)
    rubyzip (1.2.1)
    serverspec (2.41.5)
      multi_json
      rspec (~> 3.0)
      rspec-its
      specinfra (~> 2.72)
    sfl (2.3)
    specinfra (2.82.7)
      net-scp
      net-ssh (>= 2.7)
      net-telnet (= 0.1.1)
      sfl
    stove (5.2.0)
      chef-api (~> 0.5)
      logify (~> 0.2)
    strings (0.1.8)
      strings-ansi (~> 0.1)
      unicode-display_width (~> 1.5)
      unicode_utils (~> 1.4)
    strings-ansi (0.2.0)
    syslog-logger (1.6.8)
    systemu (2.6.5)
    test-kitchen (1.25.0)
      license-acceptance (~> 1.0, >= 1.0.11)
      mixlib-install (~> 3.6)
      mixlib-shellout (>= 1.2, < 3.0)
      net-scp (>= 1.1, < 3.0)
      net-ssh (>= 2.9, < 5.0)
      net-ssh-gateway (>= 1.2, < 3.0)
      thor (~> 0.19)
      winrm (~> 2.0)
      winrm-elevated (~> 1.0)
      winrm-fs (~> 1.1)
    thor (0.20.3)
    tomlrb (1.2.9)
    trollop (2.9.10)
    tty-box (0.5.0)
      pastel (~> 0.7.2)
      strings (~> 0.1.6)
      tty-cursor (~> 0.7)
    tty-color (0.5.0)
    tty-cursor (0.7.0)
    tty-prompt (0.20.0)
      necromancer (~> 0.5.0)
      pastel (~> 0.7.0)
      tty-reader (~> 0.7.0)
    tty-reader (0.7.0)
      tty-cursor (~> 0.7)
      tty-screen (~> 0.7)
      wisper (~> 2.0.0)
    tty-screen (0.7.0)
    unicode-display_width (1.6.1)
    unicode_utils (1.4.0)
    uuidtools (2.1.5)
    vagrant-cachier (1.2.1)
    winrm (2.3.4)
      builder (>= 2.1.2)
      erubi (~> 1.8)
      gssapi (~> 1.2)
      gyoku (~> 1.0)
      httpclient (~> 2.2, >= *******)
      logging (>= 1.6.1, < 3.0)
      nori (~> 2.0)
      rubyntlm (~> 0.6.0, >= 0.6.1)
    winrm-elevated (1.1.2)
      erubi (~> 1.8)
      winrm (~> 2.0)
      winrm-fs (~> 1.0)
    winrm-fs (1.3.3)
      erubi (~> 1.8)
      logging (>= 1.6.1, < 3.0)
      rubyzip (~> 1.1)
      winrm (~> 2.0)
    wisper (2.0.1)
    wmi-lite (1.0.5)

PLATFORMS
  ruby

DEPENDENCIES
  awsutils (= 2.1.7)
  chef-zero (= 5.3.2)
  dry-inflector (= 0.1.2)
  et-knife-ec2!
  kitchen-ec2 (= 1.3.2)
  kitchen-sync (= 2.2.0)
  knife-stalenodes (= 1.2.0)
  mime-types (= 3.1)
  mime-types-data (= 3.2016.0521)
  mixlib-archive (= 0.4.1)
  mixlib-cli (= 1.7.0)
  mixlib-config (= 2.2.4)
  ohai (= 8.24.1)
  rubyzip (= 1.2.1)
  stove (= 5.2.0)
  vagrant-cachier (= 1.2.1)

BUNDLED WITH
   1.12.5
