To fix a StackLevelTooDeep issue.  You need to comment out the pagination logic in the query.rb.

# may/will need to change based on chef-VERSION

cd /opt/chef/embedded/lib/ruby/gems/2.3.0/gems/chef-12.22.5/lib/chef/search/

vi query.rb

# comment out this block of code, line 104 usually

next_start = response["start"] + (args_h[:rows] || response["rows"].length)
unless next_start >= response["total"]
  args_h[:start] = next_start
  search(type, query, args_h, &block)
end

run chef-client
